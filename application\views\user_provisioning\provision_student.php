<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('student/student_menu');?>">Student Menu</a></li>
    <li><a href="<?php echo site_url('student/Student_controller/index/');?>">Student Index</a></li>
    <li><a href="<?php echo site_url('student/Student_controller/addMoreStudentInfo/'.$student_uid);?>">Student Detail</a></li>
    <li>Provision Parent Credentials</li>
</ul>
<hr>
<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('student/Student_controller/addMoreStudentInfo/'.$student_uid);?>">
                            <span class="fa fa-arrow-left"></span>
                        </a> 
                        <?php if (!empty($stdData)) { ?>
                            Provision Parent Credentials Details of <?php echo '<strong>' . strtoupper($stdData->stdName) . '</strong> (Class/Sections: ' . $stdData->classSection . ') (Admission No: '. $stdData->admission_no.')'?>
                        <?php } else { ?>
                            Provision Parent Credentials Details
                        <?php } ?>
                    </h3> 
                </div>
            </div>
            <div class="card-body">
                <div class="stdudentData tableFixHead">
                    <div class="no-data-display"><i class="fa fa-spin fa-spinner"></i> Loading...</div>
                </div>
                <form enctype="multipart/form-data" method="post" id="send_provision_credentials" action="" class="form-horizontal" >
                    <input type="hidden" name="callFrom" id="callFrom" value="individualStudent">
                    <input type="hidden" name="student_id" id="student_id" value="<?php echo $student_uid?>">
                    <div id="summary" class="modal fade" role="dialog">
                        <div class="modal-dialog" style="width:80%;margin: auto;">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">Check the numbers and confirm</h4>
                                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                                </div>
                                <div id="numberBody" class="modal-body table-responsive" style="overflow-y:auto;height:500px;">
                                    <div>
                                        <h4 id="warnMsg"></h4>
                                    </div>
                                    <div id="modal-loader" style="display: none; text-align: center;">
                                        <img src="<?php echo base_url('assets/img/ajax-loader.gif'); ?>" style="width:400px; height:400px;">
                                    </div>
                                    <table class="table" id="dynamic-content" width="100%"></table>
                                </div>
                                <div class="modal-footer">
                                    <span id="credit-err" class="text-danger">Not enough credits to send sms</span>
                                    <button type="button" id="cancelModal" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                    <button type="button" id="confirmBtn" onclick="form_submit()" class="btn btn-secondary mt-0">Confirm</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<style>
.tableFixHead {
    overflow-y: auto;
    height: 35rem;
}
.tableFixHead thead th {
    position: sticky;
    top: -2px;
}
table {
    border-collapse: collapse;
    width: 100%;
}
th,
td {
    padding: 8px 16px;
    border: 1px solid #ccc;
}
th {
    background: #eee;
}
.medium {
    width: 40%;
    margin: auto;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function check_all(check, value) {
    if (value == 'send') {
        isCheckedBySend(check);
    } else if (value == 're_send') {
        isCheckedByReSend(check);
    } else if (value == 'deactivate') {
        isCheckedByDeactivate(check);
    }
}
function isCheckedBySend(check) {
    if ($(check).is(':checked')) {
        $('.sendCheck').prop('checked', true);
        $('#sendButton').prop('disabled', false);
        $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop({
            disabled: true,
            checked: false
        });
    } else {
        $('.sendCheck').prop('checked', false);
        $('#sendButton').prop('disabled', true);
        $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled', false);
    }
}
function isCheckedByReSend(check) {
    if ($(check).is(':checked')) {
        $('.re_sendCheck').prop('checked', true);
        $('#re-sendButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll').prop({
            disabled: true,
            checked: false
        });
    } else {
        $('.re_sendCheck').prop('checked', false);
        $('#re-sendButton').prop('disabled', true);
        $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled', false);
    }
}
function isCheckedByDeactivate(check) {
    if ($(check).is(':checked')) {
        $('.deactivate_sendCheck').prop('checked', true);
        $('#deactivateButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll').prop({
            disabled: true,
            checked: false
        });
    } else {
        $('.deactivate_sendCheck').prop('checked', false);
        $('#deactivateButton').prop('disabled', true);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll').prop('disabled', false);
    }
}
function check_smsIndividual() {
    if ($('.sendCheck:checked').length > 0) {
        $('#sendButton').prop('disabled', false);
        $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop({
            disabled: true,
            checked: false
        });
    } else {
        $('.sendCheck').prop('checked', false);
        $('#sendButton').prop('disabled', true);
        $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled', false);
    }
}
function check_reSMSIndividual() {
    if ($('.re_sendCheck:checked').length > 0) {
        $('#re-sendButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll').prop({
            disabled: true,
            checked: false
        });
    } else {
        $('.re_sendCheck').prop('checked', false);
        $('#re-sendButton').prop('disabled', true);
        $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled', false);
    }
}
function check_deactivateIndividual() {
    if ($('.deactivate_sendCheck:checked').length > 0) {
        $('#deactivateButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll').prop({
            disabled: true,
            checked: false
        });
    } else {
        $('.deactivate_sendCheck').prop('checked', false);
        $('#deactivateButton').prop('disabled', true);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll').prop('disabled', false);
    }
}
function send_provision_credentials() {
    var pids = [];
    $('.sendCheck:checked').each(function() {
        var methodType = $('#method_type_indiv_' + $(this).val()).val();
        if (methodType == 'sms') {
            pids.push($(this).val() + '_' + $('#method_type_indiv_' + $(this).val()).val());
        }
        if (methodType == 'email' && $('#parent-email' + $(this).val()).html() != '') {
            pids.push($(this).val() + '_' + $('#method_type_indiv_' + $(this).val()).val());
        }
    });
    if (pids.length <= 0) {
        return false;
    }
    $('#summary').modal('show');
    sendProvisionLink(pids, 'activation');
    $("#process").val('activation');
    $("#smsType").val('send');
}
function re_send_provision_credentials() {
    var pids = [];
    $('.re_sendCheck:checked').each(function() {
        var methodType = $('#method_type_indiv_' + $(this).val()).val();
        if (methodType == 'sms') {
            pids.push($(this).val() + '_' + $('#method_type_indiv_' + $(this).val()).val());
        }
        if (methodType == 'email' && $('#parent-email' + $(this).val()).html() != '') {
            pids.push($(this).val() + '_' + $('#method_type_indiv_' + $(this).val()).val());
        }
    });
    if (pids.length <= 0) {
        return false;
    }
    $('#summary').modal('show');
    sendProvisionLink(pids, 'activation');
    $("#process").val('activation');
    $("#smsType").val('send');
}
function sendProvisionLink(pids, provision = 'activation', alreadyProv = 0) {
    $('#modal-loader').show();
    if (pids.length > 0) {
        $('#dynamic-content').html('');
        $("#warnMsg").html('');
        $.ajax({
                url: '<?php echo site_url('parent_activation/getPreview_credentials'); ?>',
                type: 'POST',
                data: {
                    'pids': pids,
                    'process': provision
                },
                dataType: 'html'
            })
            .done(function(data) {
                var data = $.parseJSON(data);
                var previewData = data.preview;
                var credits_available = data.credits_available;
                var html = '';
                $('#dynamic-content').html('Loading data...');
                if (previewData.length == 0) {
                    html += '<h4>No data</h4>';
                } else {
                    html +=
                        '<thead><tr><th>#</th><th style="width:30%;">Name</th><th>Number</th><th>Message</th></tr></thead><tbody>';
                    for (var i = 0; i < previewData.length; i++) {
                        var escapedMessage = previewData[i].message.replace(/"/g, '&quot;');
                        html += '<tr>';
                        html += '<td>' + (i + 1) + '</td>';
                        html += '<td>' + previewData[i].name + '</td>';
                        html += '<td>' + previewData[i].message_by + '</td>';
                        html += '<td>' + previewData[i].message + '</td>';
                        html += '</tr>';
                        $("#send_provision_credentials").append(`
                            <input type="hidden" name="codes[${previewData[i].pid}]" value="${previewData[i].code}">
                            <input type="hidden" name="messages[${previewData[i].std_id}_${previewData[i].pid}_${previewData[i].relation_type}_${previewData[i].user_id}_${previewData[i].send_type}]" value="${escapedMessage}">
                        `);
                    }
                    html += '</tbody></table>';
                }
                $('#dynamic-content').html(html);
                if (alreadyProv > 0) {
                    $("#warnMsg").html(alreadyProv + ' already provisioned numbers are not taken.');
                }
                $('#modal-loader').hide();
                if (credits_available == 1) {
                    $("#confirmBtn").attr('disabled', false);
                    $("#credit-err").hide();
                } else {
                    $("#confirmBtn").attr('disabled', true);
                    $("#credit-err").show();
                }
            })
            .fail(function() {
                $('#dynamic-content').html(
                    '<i class="glyphicon glyphicon-info-sign"></i> Something went wrong, Please try again...');
                $('#modal-loader').hide();
            });
    } else {
        $("#warnMsg").html('');
        if (alreadyProv > 0) {
            $("#warnMsg").html(alreadyProv + ' already provisioned member(s) is/are not shown here.');
        }
        $("#confirmBtn").hide();
        $('#dynamic-content').html('<i class="glyphicon glyphicon-info-sign"></i> Please select students...');
    }
}
function deactivate_provision_credentials() {
    var userIds = [];
    $('.deactivate_sendCheck:checked').each(function() {
        userIds.push($(this).val());
    });
    if (userIds.length <= 0) {
        Swal.fire({
            icon: 'info',
            title: 'No Users Selected',
            text: 'Please select at least one user to deactivate.'
        });
        return false;
    }
    Swal.fire({
        title: 'Deactivating Users',
        html: `You are deactivating <b>${userIds.length}</b> user(s). Are you sure?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes',
        cancelButtonText: 'No',
        confirmButtonColor: '#28a745', // Green
        cancelButtonColor: '#dc3545',  // Red
        customClass: {
            popup: 'medium'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo site_url('parent_activation/deactivate_provision_credentials_by_user_id'); ?>',
                type: 'POST',
                data: {
                    'userIds': userIds
                }
            })
            .done(function(data) {
                if (data) {
                    fetchAndBuildTable(student_id);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Something went wrong while deactivating users.'
                    });
                }
            })
            .fail(function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to send request to the server.'
                });
            });
        }
    });
}

function reset_password(userid, username){
    Swal.fire({
        title: 'Reset to Default Password?',
        html: `<b>User name:</b> ${username}<br><b>Password:</b> welcome123`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes',
        cancelButtonText: 'No',
        confirmButtonColor: '#28a745', // Green
        cancelButtonColor: '#dc3545',  // Red
        customClass: {
            popup: 'medium'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo site_url('parent_activation/reset_default_password_user_id'); ?>',
                type: 'post',
                data: {
                    userId: userid
                },
                success: function(data) {
                    if (data) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Password Reset',
                            text: 'Password reset successfully.',
                            confirmButtonColor: '#28a745'
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Reset Failed',
                            text: 'Failed to reset password.',
                            confirmButtonColor: '#dc3545'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'Server Error',
                        text: 'Something went wrong while resetting.',
                        confirmButtonColor: '#dc3545'
                    });
                }
            });
        }
    });
}
function form_submit() {
    var action = "<?php echo site_url('parent_activation/send_parent_provision_credentials') ?>";
    $("#confirmBtn").val('Please Wait...');
    $("#confirmBtn").attr('disabled', true);
    $('#send_provision_credentials').attr('action', action);
    $('#send_provision_credentials').submit();
}
$(document).ready(function(){
    var student_id = '<?php echo $student_uid; ?>';
    fetchAndBuildTable(student_id);
    $(document).on('change', '#send_method_selector', function () {
        const value = $(this).val();
        $('.method_type_dropdown').val(value);
    });
});
function fetchAndBuildTable(student_id) {
    $.ajax({
        url: '<?php echo site_url('parent_activation/get_credentials_student_data_by_id'); ?>',
        type: 'POST',
        data: { student_id: student_id },
        dataType: 'json',
        success: function(data) {
            buildProvisionTable(data);
        },
        error: function() {
            $('.stdudentData').html('<div class="no-data-display">Failed to load data.</div>');
        }
    });
}
function buildProvisionTable(stdData) {
    let html = `<table class="table table-bordered" id="student_provision">
        <thead>
            <tr>
                <th>#</th>
                <th>Student Name</th>
                <th>Relation</th>
                <th>Parent Name</th>
                <th>Mobile Number</th>
                <th>Email</th>
                <th style="width:10%">
                    <select class="form-control" id="send_method_selector" name="send_method_type">
                        <option value="sms">SMS</option>
                        <option value="email">Email</option>
                    </select>
                </th>
                <th>Status</th>
                <th>
                    <input type="button" onclick="send_provision_credentials()" id="sendButton" disabled class="btn btn-primary" value="Send">
                    <br><br>Send <input type="checkbox" name="selectAll" value="send" onclick="check_all(this, value)" id="sendAll" class="check">
                </th>
                <th>
                    <input type="button" onclick="re_send_provision_credentials()" id="re-sendButton" disabled class="btn btn-primary" value="Re-Send">
                    <br><br>Re-send <input type="checkbox" name="selectAll" value="re_send" onclick="check_all(this, value)" id="re_sendAll" class="check">
                </th>
                <th>
                    <input type="button" onclick="deactivate_provision_credentials()" id="deactivateButton" disabled class="btn btn-primary" value="Deactivate">
                    <br><br>Deactivate <input type="checkbox" name="selectAll" value="deactivate" onclick="check_all(this, value)" id="deactivateAll" class="check">
                </th>
                <th>Reset password</th>
            </tr>
        </thead>
        <tbody>`;
    if (!stdData || stdData.length === 0) {
        html += `<tr><td colspan="13" class="text-center">No data found</td></tr>`;
    } else {
        for (let i = 0; i < stdData.length; i++) {
            const student = stdData[i];
            let accountStatus = 1;
            let activeDisabled = student.Active == 1 ? 1 : 0;
            html += `<tr>
                <td>${i + 1}</td>
                <td>${student.studentName}</td>
                <td>${student.relation_type}</td>
                <td>${student.parentName}</td>
                <td>${student.mobile}</td>
                <td id="parent-email${student.pid}">${student.email}</td>`;
            if (student.oldUid != null && student.userId != student.oldUid) {
                html += `<td>${student.siblings.map(sib => `<p>${sib.stdName} (${sib.aSection})</p>`).join('')}</td>`;
            } else {
                html += `<td>
                    <select class="form-control method_type_dropdown" name="select_send_options" id="method_type_indiv_${student.pid}">
                        <option value="sms">SMS</option>
                        <option value="email">Email</option>
                    </select>
                </td>`;
            }
            let loggedStatus = '';
            if (student.oldUid != null && student.userId != student.oldUid) {
                loggedStatus = 'Sibling Connected';
            } else if (student.loggedin_atleast_once == 1) {
                loggedStatus = '<button type="button" class="btn btn-info btn-xs">Logged-in</button>';
            } else if (student.Active == 1) {
                loggedStatus = '<button type="button" class="btn btn-warning btn-xs">Activated</button>';
            } else {
                accountStatus = 0;
                loggedStatus = '<button type="button" class="btn btn-danger btn-xs">Not activated</button>';
            }
            html += `<td>${loggedStatus}</td>`;
            if (student.oldUid != null && student.userId != student.oldUid) {
                html += `<td>Sibling connected</td>`;
            } else {
                if (activeDisabled) {
                    html += `<td>${student.loggedin_atleast_once == 1 ? '<font color="green">Logged-in</font>' : '<font color="orange">Activated</font>'}</td>`;
                } else {
                    html += `<td><input type="checkbox" onclick="check_smsIndividual()" name="send_credentials" value="${student.pid}" class="sendCheck"></td>`;
                }
            }
            if (student.oldUid != null && student.userId != student.oldUid) {
                html += `<td>Sibling connected</td>`;
            } else {
                if (activeDisabled) {
                    html += `<td>${student.loggedin_atleast_once == 1
                        ? '<font color="green">Logged-in</font>'
                        : `<input type="checkbox" onclick="check_reSMSIndividual()" name="re_send_credentials" value="${student.pid}" class="re_sendCheck">`
                    }</td>`;
                } else {
                    html += `<td>Not Activated</td>`;
                }
            }
            if (student.oldUid != null && student.userId != student.oldUid) {
                html += `<td>Sibling connected</td>`;
            } else if(accountStatus == 1) {
                html += `<td><input type="checkbox" onclick="check_deactivateIndividual()" name="deactivate_credentials" value="${student.userId}" class="deactivate_sendCheck"></td>`;
            } else {
                html += `<td>Not Activated</td>`;
            }
            if (student.oldUid != null && student.userId != student.oldUid) {
                html += `<td>Sibling connected</td>`;
            } else {
                if (activeDisabled) {
                    html += `<td><input type="button" onclick="reset_password('${student.userId}','${student.username}')" id="reset_password" value="Reset to default" class="btn btn-primary"/></td>`;
                } else {
                    html += `<td>Not Activated</td>`;
                }
            }
            html += '</tr>';
        }
    }
    html += `</tbody></table>`;
    $('.stdudentData').html(html);
}
</script>