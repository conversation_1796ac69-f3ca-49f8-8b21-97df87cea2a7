<?php 
    $schoolLogoSetting = $this->settings->getSetting('school_logo');
    if (!empty($schoolLogoSetting)) {
        $schoolLogo = base_url($schoolLogoSetting);
    } else {
        $schoolLogo = base_url('assets/img/nextelement_logo.png');
    }

    $school_name = $this->settings->getSetting('school_name');
    if (empty($school_name)) {
        $school_name = 'Nextelement';
    }
?>
<div class="dashboard-header">
    <img src="<?php echo $schoolLogo; ?>" alt="School Logo" class="school-logo">
    <div class="school-name">
       <?php echo htmlspecialchars($school_name); ?>
    </div>
</div>

<div class="welcome-section d-flex align-items-center" style="gap: 1rem;">
    <img id="student_profile_photo" src="<?php echo base_url('assets/img/icons/profile.png'); ?>" alt="Profile" class="dashboard-avatar studentphoto" style="width:60px; height:60px; object-fit:cover; cursor:pointer;">
    <div>
        <div class="welcome-title" style="font-size:1.2rem;">
            Hey, <span id="studentName"></span> 👋
        </div>
        <div class="welcome-sub" style="font-size:1rem; color:#888;">
            Grade 1A
        </div>
    </div>
</div>

<marquee class="scrolltexty" behavior="scroll" direction="left" style="margin: 2px 2rem;">
   
</marquee>

<div id="flash_capture" class="modal fade" tabindex="-1" aria-labelledby="flashCaptureLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content" style="border-radius: 8px;">
            <div class="modal-header">
                <h5 class="modal-title" id="flashCaptureLabel">Important Note</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="color: #e04b4a; opacity: 1;"></button>
            </div>
            <div class="modal-body">
                <p style="font-size: 18px;" id="news"></p>
                <p>
                    <b>Published On:</b> <span id="published"></span>
                    <button type="button" class="btn btn-primary float-end" data-bs-dismiss="modal">Got it</button>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Feature Grid -->
<div class="feature-grid">

<?php if($this->settings->isParentModuleEnabled('CIRCULARS_V2')) ?>
    <a href="#" class="text-center">
        <div class="feature-tile">
            <?php  $this->load->view('svg_icons/parent/circular.svg') ?>
        </div>
       <span class="feature-label">Circulars</span>
    </a>

<?php if($this->settings->isParentModuleEnabled('SCHOOL_CALENDAR')) ?>

    <a href="#" class="text-center">
        <div class="feature-tile">
            <?php  $this->load->view('svg_icons/parent/calendar.svg') ?>
        </div>
       <span class="feature-label">Calendar</span>
    </a>


<?php if($this->settings->isParentModuleEnabled('FEESV3')) ?>

    <a href="#" class="text-center">
        <div class="feature-tile">
            <?php  $this->load->view('svg_icons/parent/fees.svg') ?>
        </div>
       <span class="feature-label">Fees</span>
    </a>

<?php if($this->settings->isParentModuleEnabled('STUDENT_DAY_ATTENDANCE_V2')) ?>
    <a href="#" class="text-center">
        <div class="feature-tile">
            <?php  $this->load->view('svg_icons/parent/consent_form.svg') ?>
        </div>
        <span class="feature-label">Attendance</span>
    </a>

<?php if($this->settings->isParentModuleEnabled('GALLERY')) ?>
    <a href="#" class="text-center">
        <div class="feature-tile">
            <?php  $this->load->view('svg_icons/parent/gallery.svg') ?>
        </div>
    <span class="feature-label">Gallery</span>
    </a>
    
<?php if($this->settings->isParentModuleEnabled('TRANSPORT')) ?>
    <a href="#" class="text-center">
        <div class="feature-tile">
            <?php  $this->load->view('svg_icons/parent/transport.svg') ?>
        </div>
        <span class="feature-label">Transportation</span>
    </a>


    <a href="#" class="text-center">
        <div class="feature-tile">
            <?php  $this->load->view('svg_icons/parent/talk_to_us.svg') ?>
        </div>
        <span class="feature-label">Talk to us</span>
    </a>

     <a href="#" class="text-center">
        <div class="feature-tile">
            <?php  $this->load->view('svg_icons/parent/support.svg') ?>
        </div>
        <span class="feature-label">Help & Support</span>
    </a>
</div>



<script>
let student_id = '<?php echo $studentId ?>';


let flashNewsPermission = '<?php echo $this->settings->isParentModuleEnabled('FLASH_NEWS') ?>';
$(document).ready(function(){
    onloadgetStudentData();
});
function onloadgetStudentData(){
    showLoading();
     $.ajax({
        url: '<?php echo site_url('parent/dashboard_controller/get_student_data') ?>',
        type: 'POST',
        data: {'student_id':student_id},
        success: function(response) {
            console.log('AJAX Success - student Response received:', response);
            handleStudentData(response);
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', error, xhr, status);
        },
        complete: function() {
            // Re-enable search button
            hideLoading();
        }
    });

}
let profile_confirmed = 0;
let isSiblingConnected = 0;
let stdYearId = 0;
let profile_status = 0;
let profile_confirmed_date = 0;
let profile_status_changed_date = 0;
function handleStudentData(response){
    var student_data = JSON.parse(response);
    $('#studentName').text(student_data.student_name);
    $('.welcome-sub').text(student_data.class_section);
    $('.studentphoto').attr('src', student_data.student_photo);
    profile_confirmed = student_data.profile_confirmed;
    isSiblingConnected = student_data.has_sibling;
    stdYearId = student_data.stdYearId;
    profile_status = student_data.profile_status,
    profile_confirmed_date =  student_data.profile_confirmed_date,
    profile_status_changed_date = student_data.profile_status_changed_date

    if(flashNewsPermission == 1){
        getflashNewsdata(student_data.class_section_id, student_data.class_name, student_data.section_name,  student_data.board);
    }
}

function getflashNewsdata(class_section_id, class_name, section_name, board){
    $.ajax({
        url: '<?php echo site_url('parent/dashboard_controller/getflashNewsdata') ?>',
        type: 'POST',
        data: {'class_section_id':class_section_id, 'class_name':class_name, 'section_name':section_name, 'board':board},
        success: function(response) {
            console.log('AJAX Success - flash Response received:', response);
            handleFlashNews(response);
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', error, xhr, status);
        },
        complete: function() {
            // Re-enable search button
            $('#search').prop('disabled', false).html('Get Report');
            hideLoading();
        }
    });
}

function handleFlashNews(response){
    var flash_data = JSON.parse(response);
    var is_enforce =flash_data.is_enforce;
    var flash_news =flash_data.flash_news;
    var enforce_reading =flash_data.enforce_reading;
    if(is_enforce == 1){
        function jsonUnEscape(str) {
            return str.replace(/\\n/g, "\n").replace(/\\r/g, "\r").replace(/\\t/g, "\t");
        }
        $("#news").html(jsonUnEscape(enforce_reading.flash_content));
        $("#published").html(moment(enforce_reading.published_on).format('DD-MM-YYYY'));
        $('#flash_capture').modal({
            backdrop: 'static',
            keyboard: false
        });
        $("#flash_capture").modal('show');
    }else{
        var html = '';
        for (let i = 0; i < flash_news.length; i++) {
            let news = flash_news[i];
            let index = i + 1;
            let dateStr = '';
            if (!news.hasOwnProperty('enforce_reading')) {
                dateStr = moment(news.start_date).format('DD MMM') + ' - ';
            }
            html += '<b style="font-size: 20px; color: green ">' + index + '. ' + dateStr + news.flash_content + '.       </b>';
        }
        // Update the marquee content dynamically
        $('.scrolltexty').html(html);
    }
  
}
$('#student_profile_photo').on('click', function() {
    var studentName = $('#studentName').text();
    var classSection = $('.welcome-sub').text();
    var studentPhoto = $('#student_profile_photo').attr('src');

    // Prepare data object
    var data = {
        name: studentName,
        classSection: classSection,
        student_id: student_id,
        photoUrl: studentPhoto,
        profile_confirmed: profile_confirmed,
        isSiblingConnected: isSiblingConnected,
        stdYearId: stdYearId,
        profile_status: profile_status,
        profile_confirmed_date: profile_confirmed_date,
        profile_status_changed_date: profile_status_changed_date
    };

    // Encode the data as a base64 JSON string
    var encodedData = btoa(unescape(encodeURIComponent(JSON.stringify(data))));
    // Redirect with encoded data as a single query param
    window.location.href = '<?php echo site_url('profile') ?>' + '?data=' + encodeURIComponent(encodedData);
});

function showLoading(){

}

function hideLoading(){
    
}
</script>