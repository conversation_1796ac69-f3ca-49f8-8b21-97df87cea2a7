<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard_controller extends CI_Controller {
	private $yearId;

	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
			redirect('auth/login', 'refresh');
		}
		if (!$this->authorization->isModuleEnabled('PARENTS_LOGIN')) {
			redirect('parentdashboard', 'refresh');
		}

		// Load required models and libraries
		$this->load->model('parent/parent_model');
		$this->load->model('dashboard_model');
		$this->load->model('feesv2/fees_student_model');
		$this->load->model('attendance_day_v2/attendance_day_v2_model');
		$this->load->model('parentv2/circular_model');
		$this->load->model('calenderevents_model');
		$this->load->library('filemanager');
		$this->yearId = $this->acad_year->getAcadYearId();
	}

	public function index($student_id = '') {
		 $student_id = $this->input->get('student_id');
		if ($student_id) {
			$data['studentId'] = $student_id;
		} else {
			$data['studentId'] = $this->parent_model->getStudentIdOfLoggedInParent();
		}
		$data['main_content'] = 'dashboard/parent_v3/dashboard';
		$this->load->view('dashboard/parent_v3/inc/index', $data);
	}

	public function get_student_data(){
		$student_id = $this->input->post('student_id');
		$result =  $this->parent_model->get_student_datails($student_id);
		echo json_encode($result);
	}

	public function getflashNewsdata(){
		$class_section_id = $this->input->post('class_section_id');
		$class_name = $this->input->post('class_name');
		$section_name = $this->input->post('section_name');
		$board = $this->input->post('board');
		$this->load->model('flash_model');
		//Get Flash news data
        $is_enforce = 0;
		$flash_news = $this->flash_model->get_flash_news_for_section_v2($class_section_id, $class_name, $section_name, $board);
		$enforce_reading = array();
		if(!empty($flash_news)){
			foreach ($flash_news as $flash_obj) {
				if ($flash_obj->enforce_reading == '1') {
					$is_enforce = 1;
					$enforce_reading = $flash_obj;
				}
			}
		}
		echo json_encode(array('flash_news'=>$flash_news, 'is_enforce'=>$is_enforce, 'enforce_reading'=>$enforce_reading));
	}

	public function profile() {
		// Decode base64-encoded JSON data if provided via GET parameter 'data'
		$encodedData = $this->input->get('data');
		if ($encodedData) {
			$json = urldecode(base64_decode($encodedData));
			$decoded = json_decode($json, true);
			if (is_array($decoded)) {
				$data = array_merge($data ?? [], $decoded);
			}
		}
		$data['student_data'] = $data;
		$data['main_content'] = 'dashboard/parent_v3/profile';
		$this->load->view('dashboard/parent_v3/inc/index', $data);
	}

	public function getConnectedSiblingData(){
		$currentStudentId = $this->input->post('currentStudentId');
		$result =  $this->parent_model->getSiblingdata($currentStudentId);
		echo json_encode($result);
	}

	public function profile_detail($callFrom = ''){
		$data['callFrom'] = $callFrom;

		$data['studentData'] = [];
		// Handle encoded data from URL parameters
		$encodedData = $this->input->get('data');
		if ($encodedData) {
			$json = urldecode(base64_decode($encodedData));
			$decoded = json_decode($json, true);
			if (is_array($decoded)) {
				$data['studentData'] = array_merge($data['studentData'] ?? [], $decoded);
			}
		}
		// Get student ID - either from decoded data or from logged in parent
		$studentId = isset($data['student_id']) ? $data['student_id'] : $this->parent_model->getStudentIdOfLoggedInParent();
		$data['student_id'] = $studentId;
	
		// Get comprehensive student data
		$this->config->load('form_elements');
		$data['parent_columns'] = $parentColumns = $this->config->item('parent_side_columns');

		// Get display columns from settings, with fallback to default
		$displayColumnsJson = isset($this->config->schoolSettings['parent_profile_display_columns']['value'])
			? $this->config->schoolSettings['parent_profile_display_columns']['value']
			: '[]';
		$data['display_columns'] = json_decode($displayColumnsJson, true);
		
		$displayParentColumns = [];
		foreach ($data['parent_columns'] as $key => $value) {
			 if (in_array($value['column_name'], $data['display_columns'])) {
				$displayParentColumns[$value['tabs']][] = $value;
			}
		}

		$data['displayList'] = $displayParentColumns;
		$data['main_content'] = 'dashboard/parent_v3/profile_detail';
		$this->load->view('dashboard/parent_v3/inc/index', $data);
	}

	/**
	 * AJAX endpoint to get student details by ID
	 */
	public function getStudentDetailsById(){
		$studentId = $this->input->post('student_id');
		$listDataInput = $this->input->post('listDataInput');

		if (!$studentId) {
			$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		}

		if (!$studentId) {
			echo json_encode(array('error' => 'Student ID not found'));
			return;
		}

		try {
			$tablColumn = '';
			if (is_array($listDataInput)) {
				$columns = [];
				foreach ($listDataInput as $item) {
					if (!empty($item['table_column']) && !empty($item['field'])) {
						$columns[] = $item['table_column'] . ' as ' . $item['field'];
					}
				}
				$tablColumn = implode(', ', $columns);
			}

			if (empty($tablColumn)) {
				echo json_encode(array('error' => 'No columns specified'));
				return;
			}
			$result = $this->parent_model->getStudentDataById($studentId, $tablColumn);
			
			if (!$result) {
				echo json_encode(array('error' => 'Student data not found'));
				return;
			}

			// Process photo fields to generate proper URLs
			$result = $this->processPhotoFieldsInResult($result, $listDataInput);

			echo json_encode($result);

		} catch (Exception $e) {
			log_message('error', 'Error in getStudentDetailsById: ' . $e->getMessage());
			echo json_encode(array('error' => 'Internal server error'));
		}
	}

	/**
	 * Process photo fields to generate proper URLs
	 */
	private function processPhotoFieldsInResult($result, $listDataInput) {
		if (!is_object($result)) {
			return $result;
		}

		foreach ($listDataInput as $item) {
			if (isset($item['field']) && isset($result->{$item['field']})) {
				// Check if this is a photo field (contains 'PHOTO' in field name)
				if (strpos($item['field'], 'PHOTO') !== false) {
					$photoPath = $result->{$item['field']};
					if (!empty($photoPath)) {
						$result->{$item['field']} = $this->filemanager->getFilePath($photoPath);
					} else {
						$result->{$item['field']} = base_url('assets/img/icons/profile.png');
					}
				}
			}
		}

		return $result;
	}


	/**
	 * Get profile display configuration from settings
	 */
	private function getProfileDisplayConfiguration() {
		$config = array();

		// Get enabled profile fields from settings
		if (!empty($this->config->schoolSettings['parent_profile_display_columns']['value'])) {
			$config['enabled_fields'] = json_decode($this->config->schoolSettings['parent_profile_display_columns']['value'], true);
		} else {
			// Default configuration if not set
			$config['enabled_fields'] = array(
				'STUDENT_NAME', 'STUDENT_PHOTO', 'ADMISSION_NO', 'CLASS_SECTION',
				'STUDENT_DOB', 'STUDENT_GENDER', 'STUDENT_EMAIL', 'STUDENT_MOBILE_NUMBER'
			);
		}

		// Get field labels and display order
		$config['field_labels'] = $this->getFieldLabels();
		$config['field_groups'] = $this->getFieldGroups();

		return $config;
	}

	/**
	 * Load additional profile data based on configuration
	 */
	private function loadAdditionalProfileData($data, $studentId) {
		$config = $data['profileDisplayConfig'];
		$enabledFields = $config['enabled_fields'];

		// Check if we need to load address data
		if (in_array('STUDENT_ADDRESS', $enabledFields) || in_array('FATHER_ADDRESS', $enabledFields) || in_array('MOTHER_ADDRESS', $enabledFields)) {
			$data = $this->loadAddressData($data, $studentId);
		}

		// Check if we need to load parent data
		if (array_intersect(['FATHER_NAME', 'FATHER_EMAIL', 'FATHER_CONTACT_NO', 'FATHER_PHOTO'], $enabledFields)) {
			$data['fatherData'] = $this->parent_model->getFatherDetails($studentId);
		}

		if (array_intersect(['MOTHER_NAME', 'MOTHER_EMAIL', 'MOTHER_CONTACT_NO', 'MOTHER_PHOTO'], $enabledFields)) {
			$data['motherData'] = $this->parent_model->getMotherDetails($studentId);
		}

		// Check if we need to load guardian data
		if (array_intersect(['GUARDIAN_NAME', 'GUARDIAN_EMAIL', 'GUARDIAN_CONTACT_NO', 'GUARDIAN_PHOTO'], $enabledFields)) {
			$data['show_guardian'] = 1;
			$data['guardianData'] = $this->parent_model->getGuardianDetails($studentId);
		} else {
			$data['show_guardian'] = 0;
		}

		// Load electives if enabled
		if (in_array('ELECTIVES', $enabledFields)) {
			$data['electives'] = $this->parent_model->getElectives($studentId);
		}

		// Load stops data for pickup information
		if (in_array('STUDENT_STOP', $enabledFields)) {
			$stops = $this->parent_model->getFeesStops();
			$data['stops'] = array();
			foreach ($stops as $key => $stop) {
				$data['stops'][$stop->id] = $stop->name;
			}
		}

		return $data;
	}

	/**
	 * Load address data for student, father, mother
	 */
	private function loadAddressData($data, $studentId) {
		// Student address
		$student_address_types = $this->settings->getSetting('student_address_types', 1, 1);
		$sAddress = [];
		if (!empty($student_address_types)) {
			foreach ($student_address_types as $key => $address) {
				$sAddress[$address] = $this->parent_model->getStudent_Address_Details($studentId, $key);
			}
		}
		$data['studentAddress'] = $sAddress;

		// Father address
		if (isset($data['fatherData'])) {
			$father_address_types = $this->settings->getSetting('father_address_types');
			$fAddress = [];
			if (!empty($father_address_types)) {
				foreach ($father_address_types as $key => $address) {
					$fAddress[$address] = $this->parent_model->getFather_Address_Details($data['fatherData']->id, $key);
				}
			}
			$data['fatherAddress'] = $fAddress;
		}

		// Mother address
		if (isset($data['motherData'])) {
			$mother_address_types = $this->settings->getSetting('mother_address_types');
			$mAddress = [];
			if (!empty($mother_address_types)) {
				foreach ($mother_address_types as $key => $address) {
					$mAddress[$address] = $this->parent_model->getFather_Address_Details($data['motherData']->id, $key);
				}
			}
			$data['motherAddress'] = $mAddress;
		}

		return $data;
	}

	/**
	 * Get field labels for display
	 */
	private function getFieldLabels() {
		return array(
			'STUDENT_NAME' => 'Student Name',
			'STUDENT_PHOTO' => 'Photo',
			'ADMISSION_NO' => 'Admission Number',
			'ENROLLMENT_NUMBER' => 'Enrollment Number',
			'ALPHA_ROLL_NUMBER' => 'Alpha Roll Number',
			'CLASS_SECTION' => 'Class / Section',
			'STUDENT_DOB' => 'Date of Birth',
			'STUDENT_GENDER' => 'Gender',
			'STUDENT_EMAIL' => 'Email ID',
			'STUDENT_MOBILE_NUMBER' => 'Mobile Number',
			'STUDENT_BLOOD_GROUP' => 'Blood Group',
			'STUDENT_NATIONALITY' => 'Nationality',
			'STUDENT_RELIGION' => 'Religion',
			'STUDENT_CASTE' => 'Caste',
			'CATEGORY' => 'Category',
			'STUDENT_MOTHER_TONGUE' => 'Mother Tongue',
			'STUDENT_STOP' => 'Stop',
			'STUDENT_PICKUP_MODE' => 'Pickup Mode',
			'STUDENT_ADDRESS' => 'Address',
			'STUDENT_HOUSE' => 'House',
			'COMBINATION' => 'Combination',
			'FATHER_NAME' => 'Father Name',
			'FATHER_EMAIL' => 'Father Email',
			'FATHER_CONTACT_NO' => 'Father Contact',
			'MOTHER_NAME' => 'Mother Name',
			'MOTHER_EMAIL' => 'Mother Email',
			'MOTHER_CONTACT_NO' => 'Mother Contact',
			'GUARDIAN_NAME' => 'Guardian Name',
			'GUARDIAN_EMAIL' => 'Guardian Email',
			'GUARDIAN_CONTACT_NO' => 'Guardian Contact',
			'ELECTIVES' => 'Electives',
			'FAMILY_PHOTO' => 'Family Photo',
			'STUDENT_REMARKS' => 'Remarks'
		);
	}

	/**
	 * Get field groups for organized display
	 */
	private function getFieldGroups() {
		return array(
			'student_basic' => array(
				'title' => 'Student Information',
				'icon' => 'fa-user',
				'fields' => array('STUDENT_NAME', 'STUDENT_PHOTO', 'ADMISSION_NO', 'ENROLLMENT_NUMBER', 'ALPHA_ROLL_NUMBER', 'CLASS_SECTION')
			),
			'student_personal' => array(
				'title' => 'Personal Details',
				'icon' => 'fa-id-card',
				'fields' => array('STUDENT_DOB', 'STUDENT_GENDER', 'STUDENT_BLOOD_GROUP', 'STUDENT_NATIONALITY', 'STUDENT_RELIGION', 'STUDENT_CASTE', 'CATEGORY', 'STUDENT_MOTHER_TONGUE')
			),
			'student_contact' => array(
				'title' => 'Contact Information',
				'icon' => 'fa-phone',
				'fields' => array('STUDENT_EMAIL', 'STUDENT_MOBILE_NUMBER', 'PREFFERED_CONTACT_NUMBER')
			),
			'student_academic' => array(
				'title' => 'Academic Information',
				'icon' => 'fa-graduation-cap',
				'fields' => array('STUDENT_HOUSE', 'COMBINATION', 'ELECTIVES')
			),
			'student_transport' => array(
				'title' => 'Transport Information',
				'icon' => 'fa-bus',
				'fields' => array('STUDENT_STOP', 'STUDENT_PICKUP_MODE')
			),
			'student_address' => array(
				'title' => 'Address Information',
				'icon' => 'fa-map-marker',
				'fields' => array('STUDENT_ADDRESS')
			),
			'father_info' => array(
				'title' => 'Father Information',
				'icon' => 'fa-male',
				'fields' => array('FATHER_NAME', 'FATHER_EMAIL', 'FATHER_CONTACT_NO', 'FATHER_PHOTO', 'FATHER_ADDRESS')
			),
			'mother_info' => array(
				'title' => 'Mother Information',
				'icon' => 'fa-female',
				'fields' => array('MOTHER_NAME', 'MOTHER_EMAIL', 'MOTHER_CONTACT_NO', 'MOTHER_PHOTO', 'MOTHER_ADDRESS')
			),
			'guardian_info' => array(
				'title' => 'Guardian Information',
				'icon' => 'fa-users',
				'fields' => array('GUARDIAN_NAME', 'GUARDIAN_EMAIL', 'GUARDIAN_CONTACT_NO', 'GUARDIAN_PHOTO')
			),
			'additional_info' => array(
				'title' => 'Additional Information',
				'icon' => 'fa-info-circle',
				'fields' => array('FAMILY_PHOTO', 'STUDENT_REMARKS')
			)
		);
	}


	/**
	 * Get comprehensive student data based on configured columns
	 */
	private function getComprehensiveStudentData($studentId, $parentColumns, $displayColumns) {
		$yearId = $this->acad_year->getAcadYearId();

		// Build dynamic select query based on parent_columns configuration
		$selectFields = array();
		$joinTables = array();

		foreach ($parentColumns as $column) {
			if (in_array($column['column_name'], $displayColumns) && !empty($column['table_column_name'])) {
				$selectFields[] = $column['table_column_name'] . ' as ' . $column['column_name'];

				// Track required joins based on table prefixes
				$this->addRequiredJoins($column['table_column_name'], $joinTables);
			}
		}

		if (empty($selectFields)) {
			return null;
		}

		// Build the query
		$this->db_readonly->select(implode(', ', $selectFields));
		$this->db_readonly->from('student_admission sa');
		$this->db_readonly->join('student_year sy', 'sa.id = sy.student_admission_id');

		// Add dynamic joins based on required tables
		foreach ($joinTables as $join) {
			$this->db_readonly->join($join['table'], $join['condition'], $join['type']);
		}

		$this->db_readonly->where('sa.id', $studentId);
		$this->db_readonly->where('sy.acad_year_id', $yearId);

		$result = $this->db_readonly->get()->row_array();

		if ($result) {
			// Process file URLs for photo fields
			$result = $this->processPhotoFields($result, $parentColumns);
		}

		return $result;
	}

	/**
	 * Add required joins based on table column references
	 */
	private function addRequiredJoins($tableColumn, &$joinTables) {
		// Define join mappings based on table prefixes
		$joinMappings = array(
			'c.' => array('table' => 'class c', 'condition' => 'sy.class_id = c.id', 'type' => 'left'),
			'cs.' => array('table' => 'class_section cs', 'condition' => 'sy.class_section_id = cs.id', 'type' => 'left'),
			'cmc.' => array('table' => 'class_master_combinations cmc', 'condition' => 'sy.combination_id = cmc.id', 'type' => 'left'),
		);

		foreach ($joinMappings as $prefix => $joinInfo) {
			if (strpos($tableColumn, $prefix) !== false) {
				$joinKey = $joinInfo['table'];
				if (!isset($joinTables[$joinKey])) {
					$joinTables[$joinKey] = $joinInfo;
				}
			}
		}
	}

	/**
	 * Process photo fields to generate proper URLs
	 */
	private function processPhotoFields($result, $parentColumns) {
		foreach ($parentColumns as $column) {
			if ($column['data_input'] === 'file' && isset($result[$column['column_name']])) {
				$photoPath = $result[$column['column_name']];
				if (!empty($photoPath)) {
					$result[$column['column_name']] = $this->filemanager->getFilePath($photoPath);
				} else {
					$result[$column['column_name']] = base_url('assets/img/icons/profile.png');
				}
			}
		}

		return $result;
	}

	/**
	 * Filter student data based on enabled configuration
	 */
	private function filterStudentDataByConfig($studentData, $enabledColumns) {
		if (empty($enabledColumns)) {
			return $studentData; // Return all data if no configuration
		}

		$filtered = new stdClass();

		// Map of field names to student data properties
		$fieldMapping = array(
			'STUDENT_NAME' => 'stdName',
			'STUDENT_PHOTO' => 'picture_url',
			'ADMISSION_NO' => 'admissionNo',
			'ENROLLMENT_NUMBER' => 'enrollment_number',
			'ALPHA_ROLL_NUMBER' => 'alpha_rollnum',
			'CLASS_SECTION' => array('className', 'sectionName'),
			'STUDENT_DOB' => 'dob',
			'STUDENT_GENDER' => 'gender',
			'STUDENT_EMAIL' => 'student_email',
			'STUDENT_MOBILE_NUMBER' => 'student_mobile_no',
			'STUDENT_BLOOD_GROUP' => 'blood_group',
			'STUDENT_NATIONALITY' => 'nationality',
			'STUDENT_RELIGION' => 'religion',
			'STUDENT_CASTE' => 'caste',
			'CATEGORY' => 'category',
			'STUDENT_MOTHER_TONGUE' => 'mother_tongue',
			'STUDENT_STOP' => 'stop',
			'STUDENT_PICKUP_MODE' => 'pickup_mode',
			'STUDENT_HOUSE' => 'student_house',
			'COMBINATION' => 'combination_name',
			'STUDENT_REMARKS' => 'student_remarks'
		);

		// Copy enabled fields
		foreach ($enabledColumns as $field) {
			if (isset($fieldMapping[$field])) {
				$mapping = $fieldMapping[$field];
				if (is_array($mapping)) {
					// Handle composite fields like CLASS_SECTION
					$values = array();
					foreach ($mapping as $prop) {
						if (isset($studentData->$prop)) {
							$values[] = $studentData->$prop;
						}
					}
					$filtered->$field = implode(' / ', array_filter($values));
				} else {
					// Handle single fields
					if (isset($studentData->$mapping)) {
						$filtered->$field = $studentData->$mapping;
					}
				}
			}
		}

		// Always include essential fields for functionality
		$essentialFields = array('id', 'stdYearId', 'profile_status', 'profile_confirmed', 'profile_confirmed_date', 'profile_status_changed_date');
		foreach ($essentialFields as $field) {
			if (isset($studentData->$field)) {
				$filtered->$field = $studentData->$field;
			}
		}

		// Include nested data if enabled
		if (in_array('STUDENT_ADDRESS', $enabledColumns) && isset($studentData->studentAddress)) {
			$filtered->studentAddress = $studentData->studentAddress;
		}

		if (in_array('FATHER_ADDRESS', $enabledColumns) && isset($studentData->fatherAddress)) {
			$filtered->fatherAddress = $studentData->fatherAddress;
		}

		if (in_array('MOTHER_ADDRESS', $enabledColumns) && isset($studentData->motherAddress)) {
			$filtered->motherAddress = $studentData->motherAddress;
		}

		if (in_array('ELECTIVES', $enabledColumns) && isset($studentData->electives)) {
			$filtered->electives = $studentData->electives;
		}

		return $filtered;
	}

	public function info_for_desktop_mobile() {
		$data['main_content'] = 'dashboard/parent_v3/info_message';
		$this->load->view('dashboard/parent_v3/inc/index', $data);
	}
		
}

?>
