<?php $this->load->view("procurement/service_delivery_challan/utils.php") ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript">
    const poDetails = <?php echo $allApprovedPOs ?>;

    $(document).ready(() => {
        // fetchPODetails(); // Pre-fill on page load
        $("#poNumber").change(fetchPODetails); // Also bind change event

        $(".po-items-container").hide();
        $(".po-milestones-container").hide();
    });

    let lastFetchedPOId = null;
    let currentRequestType = null;

    function fetchPODetails() {
        const poId = $("#poNumber").val();

        if (poId === lastFetchedPOId) return; // Prevent duplicate call

        lastFetchedPOId = poId;

        const selectedPO = poDetails[poId];

        if (selectedPO) {
            let { vendor_name, department_name, request_type } = selectedPO;

            $("#vendor").val(vendor_name);
            $("#department").val(department_name);

            // here w'll get to know what to bring whether items or milestones
            if (request_type === "Service Milestones") {
                currentRequestType = "milestone";

                getMilestonesForSDCFromPO(poId);

                $(".po-items-container").hide();
                $(".po-milestones-container").show();
                $('#po-items-table').html(''); // Clear items table
            } else {
                currentRequestType = "item";

                getItemsForSDCFromPO(poId);

                $(".po-milestones-container").hide();
                $(".po-items-container").show();
                $('#po-milestones-table').html(''); // Clear milestones table
            }
        } else {
            $("#vendor").val('Vendor name will be shown here...');
            $("#department").val('Department name will be shown here...');
            $('#po-items-table').html('');
            $('#po-milestones-table').html('');
            currentRequestType = null;
        }
    }

    function renderPOItems(items) {
        let html = `
        <table class="table table-striped table-hover table-bordered">
            <thead class="thead-dark">
                <tr>
                    <th>#</th>
                    <th>Item Name</th>
                    <th>Expected Qty</th>
                    <th>Delivered Qty</th>
                    <th>Accepted Qty</th>
                </tr>
            </thead>
            <tbody>
    `;

        items.forEach((item, index) => {
            html += `
            <tr>
                <td>${index + 1}</td>
                <td data-item-id="${item.item_id}">${item.item_name}</td>
                <td>${item.remaining_qty}</td>
                <td><input type="number" name="delivered_qty[${item.item_id}]" class="form-control" min="0" max="${item.remaining_qty}"></td>
                <td><input type="number" name="accepted_qty[${item.item_id}]" class="form-control" min="0" max="${item.remaining_qty}"></td>
            </tr>
        `;
        });

        html += `
            </tbody>
        </table>
    `;

        $('#po-items-table').html(html);
    }

    function getItemsForSDCFromPO(poId) {
        if (!poId) {
            return;
        }

        $.ajax({
            url: "<?php echo site_url('procurement/Requisition_controller_v2/getItemsForSDCFromPO') ?>",
            method: 'POST',
            data: { poId },
            success: function (response) {
                response = JSON.parse(response);

                if (response.length) {
                    renderPOItems(response);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Failed',
                        text: 'Failed to fetch PO items.'
                    });
                }
            },
            error: function (xhr, status, error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Error fetching PO items.'
                });
            }
        });
    }

    function renderPOMilestones(milestones) {
        let html = `
        <table class="table table-striped table-hover table-bordered">
            <thead class="thead-dark">
                <tr>
                    <th>#</th>
                    <th>Milestone Name</th>
                    <th>Description</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
    `;

        milestones.forEach((milestone, index) => {
            html += `
            <tr>
                <td>${index + 1}</td>
                <td>${milestone.milestone_name}</td>
                <td>${milestone.milestone_desc
                    ? (milestone.milestone_desc.length > 20
                        ? milestone.milestone_desc.substring(0, 20) + "..."
                        : milestone.milestone_desc)
                    : "-"
                }
                </td>
                <td>
                    <select name="" id="">
                        <option value="${milestone.status}" selected data-milestone-id="${milestone.milestone_id}">${milestone.status}</option>
                        <option value="Completed" data-milestone-id="${milestone.milestone_id}">Completed</option>
                    </select>
                </td>
            </tr>
        `;
        });

        html += `
            </tbody>
        </table>
    `;

        $('#po-milestones-table').html(html);
    }

    function getMilestonesForSDCFromPO(poId) {
        if (!poId) {
            return;
        }

        $.ajax({
            url: "<?php echo site_url('procurement/Requisition_controller_v2/getMilestonesForSDCFromPO') ?>",
            method: 'POST',
            data: { poId },
            success: function (response) {
                response = JSON.parse(response);

                if (response.length) {
                    renderPOMilestones(response);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Failed',
                        text: 'Failed to fetch PO items.'
                    });
                }
            },
            error: function (xhr, status, error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Error fetching PO items.'
                });
            }
        });
    }

    function collectChallanData() {
        const poId = $("#poNumber").val();
        const qualityRemarks = $("#qualityRemarks").val();
        const otherRemarks = $("#otherRemarks").val();

        // Validation: PO must be selected
        if (!poId) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Please select a Purchase Order.'
            });
            return null;
        }

        // Validation: Remarks required
        // if (!qualityRemarks || !otherRemarks) {
        //     Swal.fire({
        //         icon: 'error',
        //         title: 'Error',
        //         text: 'Please fill in both Quality Remarks and Other Remarks.'
        //     });
        //     return null;
        // }

        let data = {
            poId,
            requestType: currentRequestType,
            qualityRemarks,
            otherRemarks,
        };

        if (currentRequestType === "milestone") {
            // Collect milestones data only
            let milestones = [];
            let milestoneSelected = false;
            $('#po-milestones-table tbody tr').each(function () {
                const selectElem = $(this).find('select');
                const status = selectElem.val();
                // Get milestone id from selected option
                const milestoneId = selectElem.find('option:selected').data('milestone-id');
                if (status === "Completed") {
                    milestones.push({
                        milestone_id: milestoneId,
                    });
                    milestoneSelected = true;
                }
            });
            data.milestones = milestones;
        } else {
            // Collect items data only
            let items = [];
            let valid = true;
            $('#po-items-table tbody tr').each(function () {
                // Get item id from data-item-id attribute
                const itemId = $(this).find('td[data-item-id]').data('item-id');
                const deliveredQty = $(this).find('input[name^="delivered_qty"]').val();
                const acceptedQty = $(this).find('input[name^="accepted_qty"]').val();
                const maxQty = parseFloat($(this).find('input[name^="delivered_qty"]').attr('max'));

                // Validation: delivered and accepted qty must be numbers and within range
                if (deliveredQty === "" || acceptedQty === "" || isNaN(deliveredQty) || isNaN(acceptedQty)) {
                    return true; //skip this iteration
                }

                if (parseFloat(deliveredQty) <= 0 || parseFloat(acceptedQty) <= 0 ||
                    parseFloat(deliveredQty) > maxQty || parseFloat(acceptedQty) > maxQty) {
                    valid = false;
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Delivered/Accepted quantity cannot be less than 0 or exceed expected quantity.'
                    });
                    return false; // break loop
                }

                if (acceptedQty >= 1) {
                    items.push({
                        item_id: itemId,
                        delivered_qty: deliveredQty,
                        accepted_qty: acceptedQty
                    });
                }
            });
            if (!valid) return null;
            data.items = items;
        }

        return data;
    }

    function saveChallan() {
        const challanData = collectChallanData();

        if (!challanData) return; // Validation failed

        if (challanData.requestType === "milestone") {
            if (!challanData.milestones || challanData.milestones.length <= 0) {
                return Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Please mark at least one milestone as Completed before proceeding.'
                });
            }
        } else {
            if (!challanData.items || challanData.items.length <= 0) {
                return Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Please accept at least 1 item before proceeding.'
                });
            }
        }

        $.ajax({
            url: "<?php echo site_url('procurement/Requisition_controller_v2/saveServiceDeliveryChallan') ?>",
            method: 'POST',
            data: challanData,
            success: function (response) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: 'Challan saved successfully!'
                }).then(() => {
                    window.location.href = "<?php echo site_url('procurement/requisition_controller_v2/service_delivery_challans'); ?>"
                });
                // Optionally, redirect or reset form
            },
            error: function (xhr, status, error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to save challan.'
                });
            }
        });
    }
</script>