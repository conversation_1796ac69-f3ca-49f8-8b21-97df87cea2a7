<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2');?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/service_contract_controller/service_contract_dashboard');?>">Service Contract Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/service_contract_controller/service_contracts');?>">View Service Contracts</a></li>
    <li>Add Service Contract</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div style="width: 100%;" class="d-flex justify-content-between">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('procurement/service_contract_controller/service_contracts') ?>">
                        <span class="fa fa-arrow-left"></span>
                        </a> 
                        Add Service Contract
                    </h3>
                </div>
            </div>
        </div>
        <div class="panel-body">





        <div class="col-md-12">
                            <div class="container">
                                <div class="stepper">
                                        <div class="step-line"></div>
                                        <div class="step" data-step="1" >
                                                <div class="step-circle circle-1" style="background: green;">o</div>
                                                <div>Details</div>
                                        </div>
                                        <div class="step" data-step="2" >
                                                <div class="step-circle circle-2" >○</div>
                                                <div>Vendor</div>
                                        </div>
                                        <div class="step" data-step="3" >
                                                <div class="step-circle circle-3">○</div>
                                                <div>Recurring Billing</div>
                                        </div>
                                        <!-- <div class="step" data-step="4" >
                                                <div class="step-circle circle-4">○</div>
                                                <div>Payment Details</div>
                                        </div> -->
                                        <div class="step" data-step="5" >
                                                <div class="step-circle circle-5">○</div>
                                                <div>Approvers</div>
                                        </div>
                                        <div class="step" data-step="6" >
                                                <div class="step-circle circle-6">○</div>
                                                <div>Attachments</div>
                                        </div>
                                </div>

                                <div class="card p-4 col-md-12">
                                        <div id="step-1" class="step-content">
                                                <!-- Details starts here -->
                                                <div id="div-scrollable-1" class="details-container container gap-div" style="height: 300px;">
                                                        <h4>Details</h4>
                                                        <form id="step-1-form">
                                                        <input type="hidden" name="service_contract_master_id" class="service_contract_master_id" value="0">
                                                        <input type="hidden" name="basic_details_add_edit" class="basic_details_add_edit" value="Add">
                                                                <div class="form-row">
                                                                        <div class="form-group col-md-6">
                                                                                <label for="contract_number">Service Contract No.<font
                                                                                                color="red">*</font>
                                                                                </label>
                                                                                <input type="text" class="form-control"
                                                                                        id="contract_number"
                                                                                        name="contract_number"
                                                                                        value="CONT-<?php echo $acadYear->acad_year; ?>-<?php
                                                                                         $nextId = intval($currentMaxCantractId->currentRunningId) + 1;
                                                                                         $eightDigitId = str_pad($nextId, 10, '0', STR_PAD_LEFT);
                                                                                         echo $eightDigitId; 
                                                                                         ?>"
                                                                                        readonly>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="">Requester<font
                                                                                                color="red">*</font>
                                                                                </label>
                                                                                <input type="text" class="form-control"
                                                                                        id=""
                                                                                        name=""
                                                                                        value="<?php echo $requester->requesterName; ?>"
                                                                                        readonly>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="contract_date">Contract Date
                                                                                        <font color="red">*</font>
                                                                                </label>
                                                                                <input type="text" class="form-control"
                                                                                        placeholder="DD-MM-YYYY"
                                                                                        name="contract_date"
                                                                                        id="contract_date"
                                                                                        minlength="10"
                                                                                        maxlength="10"
                                                                                        required>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="contract_type">Contract Type<font
                                                                                                color="red">*</font>
                                                                                </label>
                                                                                <select class="form-control"
                                                                                        name="contract_type"
                                                                                        id="contract_type"
                                                                                        required>
                                                                                        <option value='Service'>Service</option>
                                                                                        <option value='SaaS'>SaaS</option>
                                                                                        <option value='AMC'>AMC</option>
                                                                                        <option value='Utility'>Utility</option>
                                                                                </select>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="start_date">Contract Start Date <font color="red">*</font></label>
                                                                                <input type="text" class="form-control"
                                                                                        placeholder="DD-MM-YYYY"
                                                                                        name="start_date"
                                                                                        id="start_date"
                                                                                        minlength="10"
                                                                                        styll="z-index: 90;"
                                                                                        maxlength="10"
                                                                                        required>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label
                                                                                        for="end_date">Contract End Date <font color="red">*</font></label>
                                                                                        <input type="text" class="form-control"
                                                                                        placeholder="DD-MM-YYYY"
                                                                                        name="end_date"
                                                                                        id="end_date"
                                                                                        minlength="10"
                                                                                        maxlength="10"
                                                                                        required>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="department">Department <font color="red">*</font></label>
                                                                                <select name="department" id="department"
                                                                                        class="form-control"
                                                                                        required>
                                                                                        <option value="">Select..
                                                                                        </option>
                                                                                        <?php
                                                                                            if(!empty($departments)) {
                                                                                                foreach($departments as $key => $val) {
                                                                                                    echo "<option value='$val->id'>$val->department</option>";
                                                                                                }
                                                                                            }
                                                                                        ?>
                                                                                </select>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="renewal_remainder">Renewal Remainder (in days before)
                                                                                </label>
                                                                                <input type="number" class="form-control"
                                                                                        placeholder="Ex: 30"
                                                                                        name="renewal_remainder"
                                                                                        id="renewal_remainder"
                                                                                        minlength="1"
                                                                                        maxlength="3" min="0">
                                                                        </div>
                                                                        <!-- <div class="form-group col-md-6">
                                                                                <label for="currency">Currency</label>
                                                                                <select name="currency" id="currency"
                                                                                        class="form-control">
                                                                                        <option value="">Select..</option>
                                                                                        <option value="">INR</option>
                                                                                        <option value="">Dollar</option>
                                                                                </select>
                                                                        </div> -->
                                                                        
                                                                    </div>
                                                                <!-- </div> -->
                                                        </form>
                                                </div>
                                                <div class="col-md-12">
                                                        <button type="button" onclick="save_basic_details(this, 'step-1', 1)" class="btn btn-dark pull-right">Save & Next</button>
                                                </div>
                                        </div>
                                        <div id="step-2" class="step-content hidden">
                                                <!-- Vendor list goes here -->
                                                <div id="div-scrollable-2" class="product-list-container container gap-div"  style="overflow: auto; height: 300px;">
                                                        <h4>Vendor Details</h4>
                                                        <form id="step-2-form">
                                                                <input type="hidden" name="service_contract_master_id" class="service_contract_master_id" value="0">
                                                                <input type="hidden" name="vendor_add_edit" class="vendor_add_edit" value="Add">
                                                                <div class="form-row">
                                                                <div class="form-group col-md-6">
                                                                                <label for="vendor_id">Select Vendor<font
                                                                                                color="red">*</font>
                                                                                </label>
                                                                                <select name="vendor_id" id="vendor_id"
                                                                                        class="form-control select2"
                                                                                        onchange="onchange_vendor()"
                                                                                        required
                                                                                        >
                                                                                        <option value="">Select 
                                                                                        </option>
                                                                                        <?php
                                                                                            if(!empty($vendors)) {
                                                                                                foreach($vendors as $key => $val) {
                                                                                                    echo "<option value='$val->id'>$val->vendor_name</option>";
                                                                                                }
                                                                                            }

                                                                                        ?>
                                                                                </select>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="proc_im_category_id">Select Service Category<font
                                                                                                color="red">*</font>
                                                                                </label>
                                                                                <select name="proc_im_category_id" id="proc_im_category_id"
                                                                                        class="form-control select2"
                                                                                        required
                                                                                        >
                                                                                        <option value="">Select 
                                                                                        </option>
                                                                                </select>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="vendor_code">Vendor Code
                                                                                </label>
                                                                                <input type="text" class="form-control"
                                                                                        id="vendor_code"
                                                                                        name=""
                                                                                        value="-"
                                                                                        readonly>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="vendor_contact_person">Contact Person
                                                                                </label>
                                                                                <input type="text" class="form-control"
                                                                                        id="vendor_contact_person"
                                                                                        name=""
                                                                                        value="-"
                                                                                        readonly>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="vendor_email">Email
                                                                                </label>
                                                                                <input type="text" class="form-control"
                                                                                        id="vendor_email"
                                                                                        name=""
                                                                                        value="-"
                                                                                        readonly>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="vendor_phone">Phone
                                                                                </label>
                                                                                <input type="text" class="form-control"
                                                                                        id="vendor_phone"
                                                                                        name=""
                                                                                        value="-"
                                                                                        readonly>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="vendor_gst_vat_no">GST/VAT No.
                                                                                </label>
                                                                                <input type="text" class="form-control"
                                                                                        id="vendor_gst_vat_no"
                                                                                        name=""
                                                                                        value="-"
                                                                                        readonly>
                                                                        </div>
                                                                        <div class="form-group col-md-12">
                                                                                <label for="vendor_address">Vendor Address
                                                                                </label>
                                                                                <textarea rows="6" readonly class="form-control" name="" id="vendor_address">-</textarea>
                                                                        </div>
                                                                </div>
                                                        </form>
                                                </div>

                                                <div class="col-md-12">
                                                        <button type="button" onclick="save_vendor_details(this, 'step-2', 2)" class="btn btn-dark pull-right">Save & Next</button>
                                                        <button style="margin-right: 10px;" type="button" onclick="reach_at_prev_tab(this, 'step-1')" class="btn btn-outline-secondary pull-right">Previous</button>
                                                </div>
                                        </div>
                                        <div id="step-3" class="step-content hidden">
                                                <!-- Recurring billing goes here -->
                                                <div id="div-scrollable-3" class="milestones-container container gap-div" style="overflow: auto; height: 300px;">
                                                        <h4>Recurring Billing</h4>
                                                        <div class="empty-state center-div" id="empty-state-3">
                                                                <div>
                                                                        <div id="products-list">
                                                                                <h5>No Services Found</h5>
                                                                                <p class="text-muted">Please add Service
                                                                                        to see</p>
                                                                        </div>

                                                                        <button type="button" class="btn btn-dark"
                                                                                data-toggle="modal"
                                                                                data-target="#addProductsModal"
                                                                                onclick="show_add_modal()">
                                                                                <i class="fas fa-plus"></i> Add New
                                                                                Item
                                                                        </button>
                                                                </div>
                                                        </div>
                                                        <div id="service_items_div" style="display: none;">
                                                                <form id="step-3-form">
                                                                <input type="hidden" id="isItemsAdded" value="No">
                                                                <input type="hidden" name="service_contract_master_id" class="service_contract_master_id" value="0">
                                                                <input type="hidden" name="recurring_add_edit" class="recurring_add_edit" value="Add">
                                                                <table class="table table-bordered table-responsive" style="">
                                                                        <thead class="thead-dark">
                                                                                <tr>
                                                                                        <th>Service</th>
                                                                                        <th>Service Frequency</th>
                                                                                        <th>Unit Cost</th>
                                                                                        <th>Total Cost</th>
                                                                                        <th>Service Description</th>

                                                                                        <th>Payment Mode</th>
                                                                                        <th>Payment Frequency</th>
                                                                                        <th>Expense Category</th>
                                                                                        <th>Expense Sub-category</th>
                                                                                        <th>Payment Policy</th>


                                                                                        <!-- 
                                                                                        <th>Action</th> 
                                                                                        -->
                                                                                </tr>
                                                                        </thead>
                                                                        <tbody id="service_items_tbody">
                                                                                <tr class="tr_class_remover_item">
                                                                                        <td colspan="11" class="text-center">Items not added</td>
                                                                                </tr>
                                                                        </tbody>
                                                                </table>
                                                                </form>
                                                                <button onclick="show_add_modal()" class="btn btn-secondary" style="border-radius: 5px;"><span class="fa fa-plus"></span>Add Services</button>
                                                        </div>
                                                </div>
                                                <div class="col-md-12">
                                                        <button type="button" onclick="save_service_items(this, 'step-3', 3)" class="btn btn-dark pull-right">Save & Next</button>
                                                        <button style="margin-right: 10px;" type="button" onclick="reach_at_prev_tab(this, 'step-2')" class="btn btn-outline-secondary pull-right">Previous</button>
                                                </div>
                                        </div>


                                        <div id="step-4" class="step-content hidden">
                                                <div id="div-scrollable-4" class="milestones-container container gap-div" style="overflow: auto; height: 300px;">
                                                        <h4>Payment Details</h4>
                                                        <form id="step-4-form">
                                                        <input type="hidden" name="service_contract_master_id" class="service_contract_master_id" value="0">
                                                        <div id="payment_details_div">
                                                                
                                                        </div>
                                                        </form>
                                                </div>
                                                <div class="col-md-12">
                                                        <button type="button" onclick="save_payment_details(this, 'step-4', 4)" class="btn btn-dark pull-right">Save & Next</button>
                                                        <button style="margin-right: 10px;" type="button" onclick="reach_at_prev_tab(this, 'step-3')" class="btn btn-outline-secondary pull-right">Previous</button>
                                                </div>
                                        </div>


                                        <div id="step-5" class="step-content hidden">
                                                <div id="div-scrollable-5" class="milestones-container container gap-div" style="overflow: auto; height: 300px;">
                                                        <h4>Approvers Details</h4>
                                                        <div class="col-md-12" id="approvers_div">

                                                        </div>
                                                </div>
                                                <div class="col-md-12">
                                                        <button type="button" onclick="save_approver_details(this, 'step-5', 5)" class="btn btn-dark pull-right">Next</button>
                                                        <button style="margin-right: 10px;" type="button" onclick="reach_at_prev_tab(this, 'step-3')" class="btn btn-outline-secondary pull-right">Previous</button>
                                                </div>
                                        </div>

                                        <div id="step-6" class="step-content hidden">
                                                <!-- Additional Attachements Section -->
                                                <div id="div-scrollable-6" class="milestones-container container gap-div" style="overflow: auto; height: 340px;">
                                                        <div class="">
                                                                <h5 class="font-weight-bold">Additional Attachements</h5>
                                                                <div class="empty-state col-md-6" id="attachments-list-empty" style="">
                                                                        <div class="col-md-12" style="height: 30px;"></div>
                                                                        <div>
                                                                                <div id="attachments-list">
                                                                                        <h5>No Attachments Found</h5>
                                                                                        <p class="text-muted">Please add Attachments to see</p>
                                                                                </div>
                                                                                <font color="" class="text-warning">Please use the right-side form to add attachments</font>
                                                                        </div>
                                                                </div>
                                                                <div id="additional_attachements_div" class="col-md-6" style="display: none; ">
                                                                        <form id="step-6-form">
                                                                        <input type="hidden" name="service_contract_master_id" class="service_contract_master_id" value="0">
                                                                        <table class="table table-bordered">
                                                                                <thead class="thead-dark">
                                                                                        <tr>
                                                                                                <th>File Name</th>
                                                                                                <th>Size</th>
                                                                                                <th>Remarks</th>
                                                                                                <th>Action</th>
                                                                                        </tr>
                                                                                </thead>
                                                                                <tbody id="additional_attachements_tbody">
                                                                                        <tr class="tr_class_remover">
                                                                                                <td colspan="4" class="text-center">
                                                                                                No documents found
                                                                                                </td>

                                                                                        </tr>
                                                                                </tbody>
                                                                        </table>
                                                                        </form>
                                                                </div>
                                                                <div class="col-md-6">
                                                                        <div id="add_additional_attachements_div" style="padding-left: 7px; overflow: auto; border-left: 3px dashed gray;">
                                                                                <form id="docs_form">
                                                                                <input type="hidden" name="service_contract_master_id" class="service_contract_master_id" value="0">
                                                                                <div class="form-group col-md-12">
                                                                                        <label for="additional_description_notes">Remarks
                                                                                        </label>
                                                                                        <textarea rows="3"  class="form-control" name="additional_description_notes" id="additional_description_notes"></textarea>
                                                                                </div>
                                                                                <div class="form-group col-md-12">
                                                                                        <label for="additional_attachements">Upload File<font color="red">*</font></label>
                                                                                        <div class="file-upload-container">
                                                                                                <div class="file-upload-box" id="dropZone">
                                                                                                        <i class="fas fa-cloud-upload-alt"></i> <!-- Font Awesome icon, include FA if needed -->
                                                                                                        <div>Drag & drop your file here or <span class="browse-link">browse file [pdf only] [max size= 10MB]</span></div>
                                                                                                        <div class="file-name" id="fileName">No file selected</div>
                                                                                                        <input type="file" id="additional_attachements" name="additional_attachements" required accept=".pdf">
                                                                                                </div>
                                                                                        </div>
                                                                                </div>
                                                                                </form>
                                                                                <div class="col-md-12" style="height: 5px;"></div>
                                                                                <div class="col-md-12">
                                                                                        <button style="" id="add_doc" type="Button" class="btn btn-dark col-md-12" onclick="add_additional_notes(this, 'close')">Add Document</button>
                                                                                </div>
                                                                        </div>
                                                                </div>
                                                        </div>
                                                </div>
                                                <div class="col-md-12">
                                                        <button type="button" onclick="save_and_close(this)" class="btn btn-dark pull-right">Submit</button>
                                                        <button style="margin-right: 10px;" type="button" onclick="reach_at_prev_tab(this, 'step-5')" class="btn btn-outline-secondary pull-right">Previous</button>
                                                </div>
                                        </div>
                                </div>
                                <div class="d-flex justify-content-between mt-3 col-md-12">
                                        <!-- <button id="prevBtn" class="btn btn-outline-secondary"
                                                disabled>Previous</button>
                                        <button id="nextBtn" class="btn btn-dark">Next</button> -->
                                </div>
                        </div>
        </div>


            


        </div>
    </div>
</div>


<style>
    .file-upload-container {
        width: 100%;
        margin: 10px 0;
    }
    
    .file-upload-box {
        border: 2px dashed #ccc;
        border-radius: 5px;
        height: 100px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 20px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
        position: relative;
    }
    
    .file-upload-box:hover {
        border-color: #999;
        background-color: #f9f9f9;
    }
    
    .file-upload-box i {
        font-size: 24px;
        color: #666;
        margin-bottom: 10px;
    }
    
    .file-upload-box .browse-link {
        color: #0066cc;
        text-decoration: underline;
        cursor: pointer;
    }
    
    .file-upload-box input[type="file"] {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        opacity: 0;
        cursor: pointer;
    }
    
    .file-upload-box .file-name {
        margin-top: 10px;
        font-size: 14px;
        color: #333;
    }
</style>


<!-- Modal for add contract items -->
<div class="modal fade" id="add_contract_item_modal" role="dialog" style="width:75%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px; width: 1200px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="add_contract_item_modal_h4">Add a Service</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
        <div class="modal-body" style="">
            <div id="add_contract_item_div" style="height: 300px; overflow: auto; padding-right: 7px;">
                <form id="add_contract_item_div_form_validate" data-parsley-validate method="post">
                                                                        <div class="form-group col-md-6">
                                                                                <label for="item_id">Select Service<font
                                                                                                color="red">*</font>
                                                                                </label>
                                                                                <select name="item_id" id="item_id"
                                                                                        class="form-control select2"
                                                                                        required
                                                                                        >
                                                                                        <option value="">Select 
                                                                                        </option>
                                                                                </select>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="service_frequency" class="">Service Frequency<font
                                                                                                color="red">*</font>
                                                                                </label>
                                                                               <select name="service_frequency" id="service_frequency" class="form-control" onchange="calculate_total_cost(this)" required>
                                                                                       <option value="Monthly">Monthly</option>
                                                                                       <option value="Quarterly">Quarterly</option>
                                                                                       <option value="Yearly">Yearly</option>
                                                                                       <option value="Ad-hoc">Ad-hoc</option>
                                                                               </select>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="service_frequency_unit_cost">Unit Frequency Cost<font
                                                                                                color="red">*</font>
                                                                                </label>
                                                                                <input type="text" class="form-control"
                                                                                        id="service_frequency_unit_cost"
                                                                                        name="service_frequency_unit_cost"
                                                                                        value="0"
                                                                                        required
                                                                                        onkeyup="calculate_total_cost(this)"
                                                                                        onchange="calculate_total_cost(this)"
                                                                                        >
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="yearly_cost">Yearly Cost
                                                                                </label>
                                                                                <input type="text" class="form-control"
                                                                                        id="yearly_cost"
                                                                                        name="yearly_cost"
                                                                                        value="0"
                                                                                        readonly
                                                                                        >
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="service_total_cost">Total Cost<font
                                                                                                color="red">*</font>
                                                                                </label>
                                                                                <input type="text" class="form-control"
                                                                                        id="service_total_cost"
                                                                                        name="service_total_cost"
                                                                                        value="0"
                                                                                        readonly
                                                                                        >
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="service_description">Remarks
                                                                                </label>
                                                                                <textarea rows="4"  class="form-control" name="service_description" id="service_description"></textarea>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="payment_mode" class="">Payment Mode<font color="red">*</font>
                                                                                </label>
                                                                                <select name="payment_mode[]" id="payment_mode" class="form-control" required>
                                                                                        <option value="Bank Transfer">Bank Transfer</option>
                                                                                        <option value="Credit Card">Credit Card</option>
                                                                                        <option value="Cheque">Cheque</option>
                                                                                </select>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="payment_frequency" class="">Payment Frequency<font color="red">*</font>
                                                                                </label>
                                                                                <select name="payment_frequency[]" id="payment_frequency" class="form-control" required>
                                                                                        <option value="Monthly">Monthly</option>
                                                                                        <option value="Quarterly">Quarterly</option>
                                                                                        <option value="Yearly">Yearly</option>
                                                                                </select>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="expense_category_id" class="">Expense Category<font color="red">*</font>
                                                                                </label>
                                                                                <select onchange="onchangeExpenseCategory()" name="expense_category_id[]" id="expense_category_id" class="form-control" required>
                                                                                        <option value="">Select...</option>
                                                                                <?php
                                                                                        if(!empty($expenseCategory)) {
                                                                                                foreach($expenseCategory as $key => $val) {
                                                                                                        echo "<option value='$val->id'>$val->category_name</option>";
                                                                                                }
                                                                                        }
                                                                                ?>
                                                                                </select>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="expense_sub_category_id" class="">Expense Sub-Category<font color="red">*</font>
                                                                                </label>
                                                                                <select name="expense_sub_category_id[]" id="expense_sub_category_id" class="form-control" required>
                                                                                        <option value="">Select...</option>
                                                                                </select>
                                                                        </div>
                                                                        <div class="form-group col-md-12">
                                                                                <label for="payment_rules_and_policy">Terms of Payment
                                                                                </label>
                                                                                <textarea rows="4" class="form-control" name="payment_rules_and_policy[]" id="payment_rules_and_policy"></textarea>
                                                                        </div>





                </form>
            </div>
        </div>
        <div class="modal-footer">
                <button type="Button" class="btn btn-outline-secondary" onclick="add_contract_items(this, 'close')">Save & Close</button>
                <button type="button" class="btn btn-dark" onclick="add_contract_items(this, 'add more')">Save & Add More</button>
        </div>
    </div>
</div>



<!-- Modal for add additional attachements -->
<!-- <div class="modal fade" id="add_additional_attachements_modal" role="dialog" style="min-width:1200px;margin:auto;top:0%" data-backdrop="static" aria-labelledby="" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px; width: 1200px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="add_additional_attachements_modal_h4">Additional Attachements</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
        <div class="modal-body" style="">
            <div id="add_additional_attachements_div" style="height: 300px; overflow: auto; padding-right: 7px;">
                <form id="docs_form">
                <input type="hidden" name="service_contract_master_id" class="service_contract_master_id" value="0">
                <div class="form-group col-md-6">
                        <label for="additional_description_notes">Remarks
                        </label>
                        <textarea rows="6"  class="form-control" name="additional_description_notes" id="additional_description_notes"></textarea>
                </div>
                <div class="form-group col-md-6">
                        <label for="additional_attachements">Upload File<font color="red">*</font></label>
                        <input type="file" class="form-control"
                                id="additional_attachements"
                                name="additional_attachements" required>
                </div>
                </form>
            </div>
        </div>
        <div class="modal-footer">
                <button type="Button" class="btn btn-success" onclick="add_additional_notes(this, 'close')">Save & Close</button>
        </div>
    </div>
</div> -->




<!-- Modal for show progress for attachements -->
<!-- <div class="modal fade" id="show_attachements_progress_modal" role="dialog" style="width:70%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px; width: 1200px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="">Uploading File...</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
        <div class="modal-body" style="">
                <div class="col-md-12">
                <div id="show_progress_color_modal" style="height: 15px; background: lightgreen; width: 1%;"><div>
                </div>
                       <div class="col-md-12" style="height: 20px;"></div>
                <div class="pull-right" id="show_progress_percentage_modal" style="text-align: right; font-weight: bold; color: black;"><div>
        </div>
    </div>
</div> -->




<!-- Script -->
 <?php
        $this->load->view('procurement/service_contract_view/__script_add_service_contract.php');
 ?>





<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
        .center-div {
                display: flex;
                justify-content: center;
                align-items: center;
        }

        .gap-div {
                margin: auto auto 6rem;
                background-color: #fff;
                padding: 2rem;
        }

        .gap-div-top {
                margin-top: 2rem;
        }

        /* UI for stepped component */
        .stepper {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 20px;
                position: relative;
        }

        .step {
                text-align: center;
                position: relative;
                z-index: 1;
        }

        .step-circle {
                width: 30px;
                height: 30px;
                border-radius: 50%;
                background: gray;
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 5px;
                position: relative;
                z-index: 2;
        }

        .active-step {
                background: black;
        }

        .step-line {
                position: absolute;
                top: 15px;
                left: 0;
                width: 100%;
                height: 4px;
                background: gray;
                z-index: 1;
        }

        .hidden {
                display: none;
        }

        div#div-scrollable-6::-webkit-scrollbar {
        width: 12px; /* Adjust as needed */
        }

        /* Style the scrollbar track */
        div#div-scrollable-6::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        }

        /* Customize the scrollbar thumb appearance */
        div#div-scrollable-6::-webkit-scrollbar-thumb {
        background: #eee; /* Adjust the color as desired */
        }

        div#div-scrollable-6 {
        scrollbar-width: thin;
        }




        div#div-scrollable-5::-webkit-scrollbar {
        width: 12px; /* Adjust as needed */
        }

        /* Style the scrollbar track */
        div#div-scrollable-5::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        }

        /* Customize the scrollbar thumb appearance */
        div#div-scrollable-5::-webkit-scrollbar-thumb {
        background: #eee; /* Adjust the color as desired */
        }

        div#div-scrollable-5 {
        scrollbar-width: thin;
        }



        div#div-scrollable-4::-webkit-scrollbar {
        width: 12px; /* Adjust as needed */
        }

        /* Style the scrollbar track */
        div#div-scrollable-4::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        }

        /* Customize the scrollbar thumb appearance */
        div#div-scrollable-4::-webkit-scrollbar-thumb {
        background: #eee; /* Adjust the color as desired */
        }

        div#div-scrollable-4 {
        scrollbar-width: thin;
        }



        div#div-scrollable-3::-webkit-scrollbar {
        width: 12px; /* Adjust as needed */
        }

        /* Style the scrollbar track */
        div#div-scrollable-3::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        }

        /* Customize the scrollbar thumb appearance */
        div#div-scrollable-3::-webkit-scrollbar-thumb {
        background: #eee; /* Adjust the color as desired */
        }

        div#div-scrollable-3 {
        scrollbar-width: thin;
        }




        div#div-scrollable-2::-webkit-scrollbar {
        width: 12px; /* Adjust as needed */
        }

        /* Style the scrollbar track */
        div#div-scrollable-2::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        }

        /* Customize the scrollbar thumb appearance */
        div#div-scrollable-2::-webkit-scrollbar-thumb {
        background: #eee; /* Adjust the color as desired */
        }

        div#div-scrollable-2 {
        scrollbar-width: thin;
        }




        div#div-scrollable-1::-webkit-scrollbar {
        width: 12px; /* Adjust as needed */
        }

        /* Style the scrollbar track */
        div#div-scrollable-1::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        }

        /* Customize the scrollbar thumb appearance */
        div#div-scrollable-1::-webkit-scrollbar-thumb {
        background: #eee; /* Adjust the color as desired */
        }

        div#div-scrollable-1 {
        scrollbar-width: thin;
        }
</style>






