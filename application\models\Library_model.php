<?php
  defined('BASEPATH') OR exit('No direct script access allowed');
            
    class Library_model extends CI_Model {
    private $yearId;
    private $current_branch;
    public function __construct(){
      parent::__construct();
      $this->yearId = $this->acad_year->getAcadYearID();
      $this->current_branch = $this->authorization->getCurrentBranch();
    }

// Master Details
 	public function submit_data_admin(){

    $data = array(
        'member_type'   => $this->input->post('member_type'),
        'hold_period'   => $this->input->post('hold_period'), 
        'fine_per_day' => $this->input->post('fine_per_day'), 
        'num_books' => $this->input->post('num_books'),  
        'book_type' => json_encode($this->input->post('book_type')), 
        'libraries' => json_encode($this->input->post('libraries')), 
        'display_color' => $this->input->post('displayColor'), 
        'created_by' => $this->authorization->getAvatarId()
       );
    return $this->db->insert('library_master',$data);
  }

 	public function get_info_admin_data(){
    $this->db->order_by('id','desc');
    $result = $this->db->get('library_master')->result();
    $books_type = $this->settings->getSetting('books_type');

    foreach ($result as $key => &$val) {
        $books = json_decode($val->book_type);
        $bType = '';

        foreach ($books_type as $type) {
            if (!empty($books)) {
                foreach ($books as $data) {
                    if ($type->value == $data) {
                        if (!empty($bType)) {
                            $bType .= ', ';
                        }
                        $bType .= $type->name;
                    }
                }
            }
        }

        $val->bookTypes = $bType;
    }

    foreach ($result as $key => &$val) {
        $cards_count = $this->db->select('count(id) as total_count')
                                ->from('library_cards')
                                ->where('master_id', $val->id)
                                ->get()
                                ->row();

        $val->cards_count = !empty($cards_count) ? $cards_count->total_count : 0;
    }

    return $result;
}

	public function get_info_edit_idby_admindata($id){
   $this->db->where('id',$id);
   return $this->db->get('library_master')->row();
	}

 	public function update_data_admin($id){
    $data = array(
      'member_type'   => $this->input->post('member_type'),
      'hold_period'      => $this->input->post('hold_period'), 
      'fine_per_day' => $this->input->post('fine_per_day'), 
      'num_books' => $this->input->post('num_books'),
      'book_type' => json_encode($this->input->post('book_type')),
      'display_color' => $this->input->post('displayColor'),   
      'libraries' => json_encode($this->input->post('libraries')),   
      'last_modified_by' => $this->authorization->getAvatarId()
    );
      $this->db->where('id',$id);
    return $this->db->update('library_master',$data);
  }

  public function delete_admin_data($id){
	   $this->db->where('id',$id);
	   return $this->db->delete('library_master');
	}



// Book Details
 	public function submit_data_book_details($path){  


    $contains = $this->input->post('contains');
    $total_numberof_copies = $this->input->post('total_numberof_copies');
    $data = array(
        'book_type'             => $this->input->post('book_type'), 
        'libraries'             => $this->input->post('libraries'), 
        'category'              => $this->input->post('book_category'), 
        'subject'               => $this->input->post('book_subject'),
        'book_title'            => $this->input->post('book_title'),
        'b_sub_title'           => $this->input->post('book_sub_title'),
        'b_book_keyword'        => $this->input->post('book_keyword'),
        'series'                => $this->input->post('series'),
        'volume'                => $this->input->post('volume'),
        'volume_number'         => $this->input->post('volume_number'),
        'language'              => $this->input->post('language'), 
        'author'                => $this->input->post('author'),
        'edition'               => $this->input->post('edition'), 
        'publisher_name'        => $this->input->post('book_publisher_name'),
        'yearof_publishing'     => $this->input->post('yearof_publishing'),
        'placeof_publishing'     => $this->input->post('placeof_publishing'),
        'pages'                 => $this->input->post('book_pages'),
        // 'total_numberof_copies' => $total_numberof_copies,
        'remarks'                => $this->input->post('remarks'), 
        'source'                => $this->input->post('book_source'), 
        'description'           => $this->input->post('description'),
        'shelf_no_of'           => $this->input->post('shelf_no'),
        'call_number'           => $this->input->post('call_number'),
        'isbn'                  => $this->input->post('isbn_no'),
        'issn_no'               => $this->input->post('issn_no'),
        'supplier'              => $this->input->post('vendor'),
        'bill_no'               => $this->input->post('bill_no'),
        'bill_no_date'             => $this->input->post('bill_date'),
        'book_course'            => $this->input->post('book_course'),
        'b_copies'              => $this->input->post('total_numberof_copies'),
        'file_url'              => ($path['file_name'] == '') ? null : $path['file_name'],
        'contains'              => ($contains == '') ? null : $contains,
        'last_modified_by'      => $this->authorization->getAvatarId(),
       );
    $this->db->insert('library_books',$data);
    $lastId =  $this->db->insert_id();
    $data = array();
    for ($i=1; $i <= $total_numberof_copies ; $i++) { 
        $data[] = array(
          'book_id'=>$lastId,
          'costof_book'=>$this->input->post('costof_book'),
          'date_of_accession'=>date('Y-m-d',strtotime($this->input->post('date_of_accession'))),
          'rack_name'=>$this->input->post('location_book'),
          'currency'=>$this->input->post('currency'),
          'b_invoice_no'=>$this->input->post('invoice_no'),
          'volume'    => $this->input->post('volume')
        );
    }
    $this->db->insert_batch('library_books_copies',$data);
    return $lastId;
  }

	public function get_barcode_details($id){
    $this->db->where('book_id',$id);
    $result =  $this->db->get('library_books_copies')->result();
    foreach ($result as $key => $val) {
      $lbcId[] = $val->id;
    }
    return $lbcId;
	}

  
  public function get_books_detailsbyId($id){

    $this->db->where('id',$id);
    $books = $this->db->get('library_books')->row();

    $copies = $this->db->select('lb.id, lbc.date_of_accession,lbc.costof_book, count(lbc.id) as nCopies, lbc.b_invoice_no, lbc.currency, lbc.rack_name, lbc.access_code')
    ->from('library_books lb')
    ->where('lb.id',$id)
    ->join('library_books_copies lbc','lb.id=lbc.book_id')
    ->get()->row();
    $last_row=$this->db->select('access_code')->order_by('id',"desc")->get('library_books_copies')->row();
    $books->nCopies = $copies->nCopies;
    $books->date_of_accession = $copies->date_of_accession;
    $books->costof_book = $copies->costof_book;
    $books->b_invoice_no = $copies->b_invoice_no;
    $books->currency = $copies->currency;
    $books->location_book = $copies->rack_name;
    $books->last_row = $last_row->access_code;
    return $books;

  }

  public function get_infoIdByBookDetails($id){  
    // $this->db->where('id',$id);
    // $books = $this->db->get('library_books')->row();
    $copies = $this->db->select('lbc.id')
    ->from('library_books lb')
    ->where('lb.id',$id)
    ->join('library_books_copies lbc','lb.id=lbc.book_id')
    ->get()->result();
    // $books->nCopies = $copies->nCopies;

    return $copies;     
  } 

  public function get_add_cartCount(){
    $this->db->select('count(id) as count');
    $this->db->from('books_cart');
   return $this->db->get()->row()->count;
  }
  public function get_allpopNames(){
            $this->db->distinct();
            $this->db->select('category,series,subject,source,publisher_name,book_course');
            $this->db->order_by('publisher_name', 'ASC');
    return  $this->db->get('library_books')->result();
  }

  public function delete_booksdetailsall($id){
    $this->db->where('id',$id);
    $this->db->update('library_books', array('soft_delete'=> 1));

    $lbCopiesStatus[] = array(
      'book_id'=>$id,
      'status'=>'Rejected'
    );
    $this->db->update_batch('library_books_copies',$lbCopiesStatus,'book_id');
    return  $this->db->affected_rows();
  }

  public function update_data_book_detailsbyId($id,$path){
    $bookType = $this->input->post('book_type');
    $contains  = $this->input->post('contains ');
    $total_numberof_copies  = $this->input->post('total_numberof_copies');
    $this->db->trans_start();
    $data = array(
        'book_type'             => $this->input->post('book_type'), 
        'libraries'             => $this->input->post('libraries'), 
        'category'              => $this->input->post('book_category'), 
        'subject'               => $this->input->post('book_subject'),
        'book_title'            => $this->input->post('book_title'),
        'b_sub_title'           => $this->input->post('book_sub_title'),
        'b_book_keyword'        => $this->input->post('book_keyword'),
        'series'                => $this->input->post('series'),
        'volume'                => $this->input->post('volume'),
        'language'              => $this->input->post('language'), 
        'author'                => $this->input->post('author'),
        'edition'               => $this->input->post('edition'), 
        'publisher_name'        => $this->input->post('book_publisher_name'),
        'yearof_publishing'     => $this->input->post('yearof_publishing'),
        'pages'                 => $this->input->post('book_pages'),
        // 'total_numberof_copies' => $total_numberof_copies,
        'source'                => $this->input->post('book_source'), 
        'description'           => $this->input->post('description'),
        'shelf_no_of'           => $this->input->post('shelf_no'),
        'call_number'           => $this->input->post('call_number'),
        'isbn'                  => $this->input->post('isbn_no'),
        'issn_no'               => $this->input->post('issn_no'),
        'supplier'              => $this->input->post('vendor'),
        'remarks'              => $this->input->post('remarks'),
        'volume_number'              => $this->input->post('volume_number'),
        'placeof_publishing'              => $this->input->post('placeof_publishing'),
        'bill_no'               => $this->input->post('bill_no'),
        'bill_no_date'             => $this->input->post('bill_date'),
        'book_course'            => $this->input->post('book_course'),
        'b_copies'              => $this->input->post('total_numberof_copies'),
        'file_url'              => ($path['file_name'] == '') ? null : $path['file_name'],
        'contains'              => ($contains == '') ? null : $contains,
        'last_modified_by' => $this->authorization->getAvatarId()
       );
    
      if($path['file_name'] != '') {
        $data = array_merge($data,['file_url' => $path['file_name']]);
      } 

    $this->db->where('id',$id);
    $this->db->update('library_books',$data);
    
    $u_data = array();
    for ($i=1; $i <= $total_numberof_copies ; $i++) {
      $u_data[] = array(
        'book_id'=>$id,
        'costof_book'=>$this->input->post('costof_book'),
        'date_of_accession'=>date('Y-m-d',strtotime($this->input->post('date_of_accession'))),
        'rack_name'=>$this->input->post('location_book'),
        'currency'=>$this->input->post('currency'),
        'b_invoice_no'=>$this->input->post('invoice_no'),
        'volume'    => $this->input->post('volume')
      );
    }

     $this->db->update_batch('library_books_copies',$u_data,'book_id');
     $this->db->trans_complete(); 
     return ($this->db->trans_status() === FALSE)? FALSE:TRUE;

  }

  public function update_access_CodeId($a_id,$copiesId){
    
    $data = array();
    foreach ($copiesId as $key => $val) {
        $data[] = array(
          'access_code'=>$a_id[$key],
          'id'=>$val
        );
    }
    return $this->db->update_batch('library_books_copies',$data,'id');

  }


  public function update_access_code_manually($acc_code,$book_id){
    $result = $this->db->select('id')
    ->from('library_books_copies')
    ->where('book_id', $book_id)
    ->get()->result();
    $data = array();
    foreach ($result as $key => $val) {
      $data[] = array(
        'access_code'=>$acc_code[$key],
        'id'=>$val->id
      );
    }  
    // foreach ($acc_code as $key => $code) {
    //   $data[] = array(
    //     'access_code'=>$code,
    //     'book_id'=>$book_id
    //   );
    // }
    return $this->db->update_batch('library_books_copies',$data,'id');
  }

  public function getbooksqrCodeDetailsAll($bookid){
      $iv = substr(hash('sha256', 'cbc790d23ef09d14'), 0, 16);
      $bookqrCode = openssl_decrypt(base64_decode($bookid), 'aes-256-cbc', '846546546', 0, $iv);
          $this->db->where('access_code',$bookqrCode);
    return $this->db->get('library_books_copies')->row();
  }

  public function disabled_books_report(){
   return $this->db->select('lb.id, lb.book_title,lb.author,lb.language,lbc.access_code')
    ->from('library_books lb')
    ->join('library_books_copies lbc','lb.id=lbc.book_id')
    ->where('lbc.status', 'Delete')
    ->get()->result();
  }

  public function get_qrCodeDetails($id){
    $libr_master = $this->db->select("lb.*, lbc.access_code, lb.id, ifnull(lb.book_type, '-') as book_type, ifnull(lb.category, '-') as category, ifnull(lb.language, '-') as language, ifnull(lb.author, '-') as author, ifnull(lb.yearof_publishing, '-') as yearof_publishing, ifnull(lb.publisher_name, '-') as publisher_name, ifnull(lb.description, '-') as description, ifnull(lb.book_title, '-') as book_title, ifnull(lb.series, '-') as series, ifnull(lb.volume, '-') as volume, ifnull(lb.subject, '-') as subject, ifnull(lb.location_book, '-') as location_book, ifnull(lb.source, '-') as source, ifnull(lb.contains, '-') as contains, ifnull(lb.edition, '-') as edition, ifnull(lb.isbn, '-') as isbn, ifnull(lb.pages, '-') as pages, ifnull(lb.call_number, '-') as call_number, ifnull(lb.supplier, '-') as supplier, ifnull(lb.b_author2, '-') as b_author2, ifnull(lb.b_sub_title, '-') as b_sub_title, ifnull(lb.shelf_no_of, '-') as shelf_no_of, ifnull(lb.book_course, '-') as book_course, ifnull(lb.volume_number, '-') as volume_number, lbc.costof_book, UPPER(ifnull(lbc.currency,'')) as currency")
    ->from('library_books lb')
    ->join('library_books_copies lbc','lb.id=lbc.book_id')
    ->where('lb.id',$id)
    ->get()->row();
    
    $last_row=$this->db->select('access_code')->order_by('id',"desc")->get('library_books_copies')->row();
    $libr_master->last_row = $last_row->access_code;
    // echo $books_type->last_row ; 
   $books_type = $this->settings->getSetting('books_type');
    foreach ($books_type as $key => $type) {
      if ($type->value == $libr_master->book_type) {
        $libr_master->bookType=$type->name;

      }   
    }
  
    $libr_copies = $this->db->select('*')
    ->from('library_books_copies')
    ->where('book_id',$id)
    ->get()->result();

    $libr_master->copies = $libr_copies;
    return $libr_master;
  }

  public function access_code_updatedby_aId(){
   $aId =  $this->input->post('accessId');
   $access_code =  $this->input->post('access_code');
   foreach ($access_code as $k => $val) {
      $a_data[] = array(
        'id'=>$aId[$k],
        'access_code'=>$val,
        'status'=>$this->input->post('status')[$k],
        'remarks'=>$this->input->post('remarks')[$k],
      );
   }
   //echo "<pre>"; print_r($a_data); die();
   return $this->db->update_batch('library_books_copies',$a_data, 'id');
  }

  public function add_more_copiesofbooksId($bookId){
    $tnCopies = $this->input->post('total_numberof_copies');
    $data = array();
    for ($i=1; $i <= $tnCopies ; $i++) { 
        $data[] = array(
          'book_id'=>$bookId,
          'costof_book'=>$this->input->post('costof_book'),
          'date_of_accession'=>date('Y-m-d',strtotime($this->input->post('date_of_accession')))
        );
    }

    $this->db->insert_batch('library_books_copies',$data);
    $insertId = $this->db->insert_id();
    $count = count($data) + $insertId;
    $sName = $this->settings->getSetting('school_short_name');
    while ($insertId < $count) {
      $len = strlen((string)$insertId);
        $digits = 'LB';
        for ($i = 6 - $len;$i > 0; $i--) { 
          $digits .= '0';
        }
        $digits .= $insertId;
        $a_code[] = strtoupper($sName).$digits;
        $copiesId[] = $insertId;
        $insertId++;
    }

    $data = array();
    foreach ($copiesId as $key => $val) {
        $data[] = array(
          'access_code'=>$a_code[$key],
          'id'=>$val
        );
    }
    return $this->db->update_batch('library_books_copies',$data,'id');
  }

  public function access_code_updated($lbcId){
    $aId =  $this->input->post('access_code');
    $status =  $this->input->post('status');
    $remarks =  $this->input->post('remarks_new');
    // echo "<pre>"; print_r($remarks); die(); exit();
    $a_data = array(
        'access_code'=>$aId,
        'status'=>$status,
        'remarks'=>$remarks,
      );
      $this->db->where('id',$lbcId);
   return $this->db->update('library_books_copies',$a_data);
  }

  // Library Cards
  public function get_all_type_ofCards(){
   return $this->db->select('id,member_type')->from('library_master')->get()->result();
  }

  public function get_selected_typewise(){
  return  $this->db->select("staff_master.id as staff_id, concat(ifnull(first_name,''), ' ' ,ifnull(last_name,'')) as staff_name")
    ->from('staff_master')
    //->where('staff_master.id not in (select stake_holder_id from library_cards where master_id ='.$member_type.')')
    ->where('staff_master.status','2') // 2 Approved Staff
    ->order_by('first_name','ASC')
    ->get()->result();
  }
  
  public function get_student_typewise($classId){

    return $this->db->select("sd.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as student_name")
    ->from('student_year sy')
    ->join('student_admission sd','sy.student_admission_id=sd.id')
    ->where('sy.acad_year_id',$this->yearId)
    ->where('sy.class_id',$classId)
    ->where('admission_status','2')
    ->where('sy.promotion_status!=', '4')
    ->where('sy.promotion_status!=', '5')
    ->order_by('sd.first_name','ASC')
    ->get()->result();
  }

  public function insert_library_card_details(){
    $stake_holder_type = $this->input->post('stake_holder_type');
    $member_type_id = $this->input->post('member_type_id');
    $stake_holder_id = $this->input->post('stake_holder_id');
    $number_of_cards = $this->input->post('number_of_cards');
    
      $this->db->distinct();
      $this->db->select('stake_holder_id');
      $this->db->where('master_id',$member_type_id);
      $this->db->where('stake_holder_type',$stake_holder_type);
    $query =  $this->db->get('library_cards')->result();
    $old_Staff = array();
    foreach ($query as $key => $val) {
      $old_Staff[] = $val->stake_holder_id;
    }

    $data =array();
    foreach ($stake_holder_id as $k => $val) {
      if (!in_array($val, $old_Staff)) {
        $data[] = array(
        'stake_holder_id' =>  $val,
        'stake_holder_type' =>  $stake_holder_type,
        'master_id'  =>  $member_type_id,
        'status'  =>1,
        );
      }
    }
    if(!empty($data)){
      for ($i=1; $i <= $number_of_cards ; $i++) {
        $insertData = $data;
        foreach ($insertData as $key => $value) {
          if (!in_array($value, $old_Staff)) {
            $insert_val[] = $value;
          }
        }
      }
    }else{
        return 0;
    }
   
    return $this->db->insert_batch('library_cards',$insert_val);

  }

  public function update_card_access_codeAll(){

    $result = $this->db->select('id')->get('library_cards')->result();
    $sName = $this->settings->getSetting('school_short_name');
    foreach ($result as $key => $val) {
      $len = strlen((string)$val->id);
      $digits = 'LC';
      for ($i = 6 - $len; $i > 0; $i--) { 
        $digits .= '0';
      }
      $digits .= $val->id;
      $a_code[] = strtoupper($sName).$digits;
      $lcId[] = $val->id;
    }
  
    $rData = array();
    foreach ($lcId as $k => $value) {
        $rData[] = array('id'=>$value,'card_access_code'=>$a_code[$k]);
    }
    return $this->db->update_batch('library_cards',$rData,'id');
  }

  public function get_selected_member_typewise($member_type,$type){

    $this->db->distinct();
    $result = $this->db->select('stake_holder_type, stake_holder_id')
      ->from('library_cards')
      ->where('master_id',$member_type)
      ->get()->result();
    if (empty($result)) {
      return 0;
    }

    $stake_holder = array();
    foreach ($result as $key => $val) {
      $stake_holder[] = $val->stake_holder_id;
    } 
    if ($type == 'staff') {
      
    $s_result =  $this->db->select("id as stId, concat(ifnull(first_name,''), ' ' ,ifnull(last_name,'')) as s_name")
      ->from('staff_master')
      ->where_in('id',$stake_holder)
      ->get()->result();
 
    }elseif ($type == 'student') {

    $s_result = $this->db->select("sd.id as stId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as s_name")
    ->from('student_year sy')
    ->join('student_admission sd','sy.student_admission_id=sd.id')
    ->where_in('sd.id',$stake_holder)
    ->get()->result();
    }

    return $s_result;

  }

  public function check_access_code_validation($access_codeId){
    $res=$this->db->query("select * from library_books_copies where access_code ='$access_codeId'")->num_rows();
    return $res;    
  }

  public function delete_book_access_code($a_id){
    $this->db->where('id',$a_id);
  return $this->db->delete('library_books_copies');
  }

  public function get_class_info(){
    $class_sec = $this->db->select('c.id as classID ,c.class_name,cs.id as secID,cs.section_name')
                    ->from('class c')
                    ->join('class_section cs','cs.class_id=c.id')
                    ->where('acad_year_id',$this->yearId)
                    ->get()->result();
    return $class_sec;
  }

  public function get_class_names(){
    $class_sec = $this->db->select('c.id,c.class_name')
                    ->from('class c')
                    ->where('acad_year_id',$this->yearId)
                    ->where('is_placeholder !=',1)
                    ->get()->result();
    return $class_sec;
  }

  public function get_staffLibrary(){
    $this->db->select("id as staff_id, concat(ifnull(first_name,''), ' ' ,ifnull(last_name,'')) as name");
    $this->db->where('status','2'); // 2 Approved Staff
    $staffs = $this->db->get('staff_master')->result();

    $libraryCards = $this->db->select('lc.id,lc.stake_holder_id,lm.member_type,lc.card_access_code')
                    ->from('library_cards lc')
                    ->join('library_master lm','lc.master_id=lm.id')
                    ->where('status',1)
                    ->where('lc.stake_holder_type','staff')
                    ->get()->result();
    
    foreach ($staffs as $key => &$staf) {
      $stafLbr = '';
      $stafcard = '';
      $stafcardDetails = array();
      foreach ($libraryCards as $key => $card) {
        if ($card->stake_holder_id == $staf->staff_id) {
          if (!empty($stafLbr)) $stafLbr.=', ';
          $stafLbr .= $card->member_type;
          $stafcard .= $card->card_access_code;
        }
      }
       if (empty($stafLbr)) $stafLbr = 'Not issued';
        $staf->library_id = $stafLbr; 
        $staf->libcard = $stafcard; 
    }
    return $staffs;
  }

  public function get_student_library($secID){

    $student =  $this->db->select("sd.id as stdId, sd.admission_no,concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as name,c.id as classID ,c.class_name,cs.id as secID,cs.section_name")
    ->from('student_year sy')
    ->join('student_admission sd','sy.student_admission_id=sd.id')
    ->join('class c','sy.class_id=c.id')
    ->join('class_section cs','sy.class_section_id=cs.id')
    ->where('sy.acad_year_id',$this->yearId)
    ->where('sy.class_section_id',$secID)
    ->where('admission_status','2')
    ->where('sy.promotion_status!=', '4')
    ->where('sy.promotion_status!=', '5')
    ->get()->result();

    $libraryCards = $this->db->select('lc.id,lc.stake_holder_id,lm.member_type,lc.card_access_code')
        ->from('library_cards lc')
        ->join('library_master lm','lc.master_id=lm.id')
        ->where('status',1)
        ->where('lc.stake_holder_type','student')
        ->get()->result();
    
    foreach ($student as $key => &$std) {
      $stdLbr = '';
      // $counts = '';
      $stafcardDetails = array();
      foreach ($libraryCards as $key => $card) {
        if ($card->stake_holder_id == $std->stdId) {
          
          if (!empty($stdLbr)) $stdLbr.=' , ';
          $stdLbr .='<span id="span_'.$card->id.'">'. $card->card_access_code;
          $stdLbr.=' <a  href="javascript:void(0)" onclick="remove_assinged_card('.$card->id.')" class="panel-remove"><span style="color:red; font-size:20px;" class="glyphicon glyphicon-remove"></span></a></span>';
        }
       
      }
       if (empty($stdLbr)) $stdLbr = 'Not issued';
        $std->library_id = $stdLbr; 
   
    }
    return $student;
  }

  public function getlibrDetailsbyStaffId($stafId){

    $staffs =  $this->db->select("id, concat(ifnull(first_name,''), ' ' ,ifnull(last_name,'')) as name")
    ->where('status','2') // 2 Approved Staff
    ->where('id',$stafId)
    ->get('staff_master')->row();

    $libraryCards = $this->db->select('lc.id,lc.card_access_code,lc.stake_holder_id,lm.member_type,lc.stake_holder_type')
        ->from('library_cards lc')
        ->join('library_master lm','lc.master_id=lm.id')
        ->where('lc.status',1)
        ->where('lc.stake_holder_id',$stafId)
        ->where('lc.stake_holder_type','staff')
        ->get()->result();

    $staffs->libr_details = $libraryCards;
    return $staffs;
  }

  public function getlibrDetailsbyStdId($stdId){

    $student = $this->db->select("sd.id, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as name")
    ->from('student_year sy')
    ->join('student_admission sd','sy.student_admission_id=sd.id')
    ->where('sy.acad_year_id',$this->yearId)
    ->where('sd.id',$stdId)
    ->where('admission_status','2')
    ->where('sy.promotion_status!=', '4')
    ->where('sy.promotion_status!=', '5')
    ->get()->row();

    $libraryCards = $this->db->select('lc.id,lc.card_access_code,lc.stake_holder_id,lm.member_type,lc.stake_holder_type')
        ->from('library_cards lc')
        ->join('library_master lm','lc.master_id=lm.id')
        ->where('lc.status',1)
        ->where('lc.stake_holder_id',$stdId)
        ->where('lc.stake_holder_type','student')
        ->get()->result();

    $student->libr_details = $libraryCards;
    return $student;
  }

  public function insert_librarycards_individual($id,$type){
    $number_of_cards = $this->input->post('number_of_cards');

    for ($i=1; $i <= $number_of_cards ; $i++) { 
      $data[] = array(
        'stake_holder_id' =>  $id,
        'stake_holder_type' => $type,
        'master_id'  =>  $this->input->post('member_type_id'),
        'status'  =>1,
      ); 
    }   
    //echo "<pre>"; print_r($data); die();
    return $this->db->insert_batch('library_cards',$data);
  }

  public function delete_libraryCards($lbId){
    $this->db->where('id',$lbId);
    return $this->db->delete('library_cards');
  }

  public function get_books_details($book_accessid,$bType){
    if (empty($book_accessid)) {
      return false;
    }
      $result = $this->db->select('id,book_id')->from('library_books_copies')->where('access_code',$book_accessid)->get()->row();
      // echo "<pre>"; print_r($result); die();
    if (empty($result)) {
      return 3;
    }
    $bAcess_Id = $result->book_id;
    $res = $this->db->select("lbd .*, ifnull(lbd.volume,'') as b_volume, lqc.id as qrId, lqc.access_code, lqc.costof_book as book_price, UPPER(ifnull(lqc.currency,'')) as currency")
        ->from('library_books_copies lqc')
        ->where('lqc.access_code',$book_accessid)
        ->where_in('lbd.book_type',$bType)
        ->join('library_books lbd','lqc.book_id=lbd.id')
        ->where('lqc.id not in (select book_access_id from library_transacation where book_access_id="'.$bAcess_Id.'" and status=1)')
        ->get()->row();
    if (empty($res)) {
      $tran = $this->db->select('book_access_id')
      ->from('library_transacation')
      ->where('book_access_id',$bAcess_Id)
      ->get()->row();
      if (!empty($tran)) {
        return 2;
      } else {
        return 0;
      }
    }
   $books_type = $this->settings->getSetting('books_type');
      foreach ($books_type as $key => $type) {
        if ($type->value == $res->book_type) {
          $res->bookType=$type->name;
        }   
      }
    return $res;
  }

  public function clearAll_card_data(){
          $this->db->where('id!=0');
    return $this->db->delete('books_cart');
  }
  public function get_books_detailsbyaccessId($book_accessid){
    if (empty($book_accessid)) {
      return 0;
    }
    $res = $this->db->select('lbd.book_type')
        ->from('library_books_copies lqc')
        ->where('lqc.access_code',$book_accessid)
        ->where('lqc.status','Available')
        ->join('library_books lbd','lqc.book_id=lbd.id')
        ->get()->row();
        // echo "<pre>"; print_r($this->db->last_query()); die();
    if (empty($res)) {
      return 3;
    }
    $books_type = $this->settings->getSetting('books_type');
      foreach ($books_type as $key => $type) {
        if ($type->value == $res->book_type) {
          $bookType=$type->name;
        }   
      }
      // echo "<pre>"; print_r($bookType); die();
    return $bookType;
  }

  public function get_library_student_data($aId){    
    return $this->db->select("lc.card_access_code, lm.member_type, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as sName, libraries, sy.picture_url")
    ->from('student_year sy')
    ->join('student_admission sd','sy.student_admission_id=sd.id')
    ->where('admission_status','2')
    ->where('sy.promotion_status!=', '4')
    ->where('sy.promotion_status!=', '5')
    ->where('acad_year_id',$this->yearId)
    ->where('sd.identification_code',$aId)
    ->where('lc.stake_holder_type','student')
    ->join('library_cards lc','sd.id=lc.stake_holder_id')
    ->join('library_master lm','lc.master_id=lm.id')
    ->get()->result();


  }

  public function get_library_staff_data($aId){
   return $this->db->select("lc.card_access_code,lm.member_type,concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as sName, libraries, sm.picture_url")
   // return $this->db->select("lc.card_access_code,lm.member_type")
      ->from('staff_master sm')
      ->where('sm.identification_code',$aId)
      ->where('sm.status',2)
      ->where('lc.stake_holder_type','staff')
      ->join('library_cards lc','sm.id=lc.stake_holder_id')
      ->join('library_master lm','lc.master_id=lm.id')
      ->get()->result();
  }

  public function get_library_multi_card_data($aId){

    // $lc = $this->db->select("lc.id, lc.stake_holder_id as stakeholderId, lc.stake_holder_type,card_access_code,concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as sName")
    $lc = $this->db->select("lc.id, lc.stake_holder_id as stakeholderId, lc.stake_holder_type,card_access_code")
    ->from('library_cards lc')
    ->where('lc.card_access_code',$aId)
    ->get()->row();
    if (!empty($lc)) {
      $this->db->select("lc.card_access_code,lm.member_type,concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as sName, libraries, sy.picture_url");
      $this->db->from('library_cards lc');
      $this->db->where('lc.stake_holder_id',$lc->stakeholderId);
      $this->db->where('lc.stake_holder_type',$lc->stake_holder_type);
      if ($lc->stake_holder_type == 'staff') {
        $this->db->join('staff_master sm','lc.stake_holder_id=sm.id');
        $this->db->join('staff_master sy','lc.stake_holder_id=sy.id');
      }else{
        $this->db->join('student_admission sm','lc.stake_holder_id=sm.id');
        $this->db->join('student_year sy','sm.id=sy.student_admission_id');
        $this->db->where('sy.acad_year_id',$this->yearId);
      }
      $this->db->join('library_master lm','lc.master_id=lm.id');
      $result =  $this->db->get()->result();
      return $result;
    }

  }
  public function get_library_card_data($aId){
    $lc = $this->db->select("lc.id, lc.stake_holder_id as stakeholderId, lc.stake_holder_type,card_access_code")
      ->from('library_cards lc')
      ->where('lc.card_access_code',$aId)
      ->get()->row();

    if (!empty($lc)) {    
        $this->db->select('lc.id as lbId, sm.id as stakeholderId, lm.fine_per_day, lm.hold_period, lm.member_type, lm.num_books, lm.book_type, sm.first_name, lt .*, lbd.book_title, lc.card_access_code,lbd.book_type as bType,lqc.access_code as access_code ');
        $this->db->from('library_cards lc');
        // $this->db->where('sm.id',$lc->stakeholderId);
        $this->db->where('lc.card_access_code',$lc->card_access_code);
        $this->db->where('lc.stake_holder_type',$lc->stake_holder_type);
        if ($lc->stake_holder_type == 'staff') {
          $this->db->join('staff_master sm','lc.stake_holder_id=sm.id','left');
        }else{
           $this->db->join('student_admission sm','lc.stake_holder_id=sm.id','left');
        }
        $this->db->join('library_transacation lt','lc.id=lt.lbr_access_id and lt.status=1','left');
        $this->db->join('library_master lm','lc.master_id=lm.id','left');
        $this->db->join('library_books_copies lqc','lt.book_access_id =lqc.id','left');
        $this->db->join('library_books lbd','lqc.book_id=lbd.id','left');
      $result =  $this->db->get()->result();
      $books_type = $this->settings->getSetting('books_type');
      
      if (!empty($result)) { 
        foreach ($result as $index => &$val) {
            $books = json_decode($val->book_type);

            if (is_array($books)) {
                $bType = '';
                foreach ($books_type as $typeKey => $type) {
                    foreach ($books as $bookKey => $data) {
                        if ($type->value == $data) {
                            if (!empty($bType)) $bType .= ', ';
                            $bType .= $type->name;
                        }
                    }
                }
                $val->typeof_book_allowed = $bType;
            } else {
                $val->typeof_book_allowed = '';
            }
        }
      }

      return $result;
    }
      
  }
  public function get_libraryid_details_fine_view($aId){
    $sName = $this->settings->getSetting('school_short_name');
    $strReplce = str_replace(strtoupper($sName), "", $aId);
    $cardName = substr($strReplce, 0,2);
    switch ($cardName) {
      case 'SD':
        $result = $this->get_library_student_data($aId);
        break;
      case 'ST':
        $result = $this->get_library_staff_data($aId);
        break;
      case 'LC':
        $result = $this->get_library_multi_card_data($aId);
        break;
    }
    $selectedLbId =  $this->session->userdata('libraries');
    if(!empty($result)){
        foreach ($result as $key => $val) {
          $lbJson = json_decode($val->libraries);
          foreach ($lbJson as $key => $lbId) {
            if ($lbId == $selectedLbId ) {
              continue;
            }else{
              return 2;
            }
          }
        }
    }else{
      $result='';
    }
    
    return $result;
  
  }

 public function get_libraryid_details($aId, $selectedLbId) {
    $sName = $this->settings->getSetting('school_short_name');
    $strReplce = str_replace(strtoupper($sName), "", $aId);
    $cardName = substr($strReplce, 0, 2);
    $id_code = [];

    if(strpos($aId, 'student_url_qr_code')){
      $id_code = $this->get_library_student_data($aId);
      if (!empty($id_code)) {
          $result = $this->get_library_card_data($id_code[0]->card_access_code);
      }
    }else{

      switch ($cardName) {
        case 'SD':
            $id_code = $this->get_library_student_data($aId);
            if (!empty($id_code)) {
                $result = $this->get_library_card_data($id_code[0]->card_access_code);
            }
            break;
        case 'ST':
            $id_code = $this->get_library_staff_data($aId);
            if (!empty($id_code)) {
                $result = $this->get_library_card_data($id_code[0]->card_access_code);
            }
            break;
        case 'LC':
            $id_code = $this->get_library_multi_card_data($aId);
            $result = $this->get_library_card_data($aId);
            break;
      }
    }

    if (empty($id_code)) {
        return 2;
    }

    $lbJson = [];
    $libriesArry = [];
    foreach ($id_code as $key => $val) {
        $lbJson = json_decode($val->libraries);
        foreach ($lbJson as $key => $val) {
            $libriesArry[$val] = $val;
        }
    }

    $found = 0;
    foreach ($libriesArry as $key => $lbId) {
        if ($lbId == $selectedLbId) {
            $found = 1;
            break;
        }
    }

    if ($found) {
        return array('lbr' => $result, 'multi_card' => $id_code);
    } else {
        return 2;
    }
}

  public function insert_issuebook_details($accessId,$book_accessId,$issue_date){

    foreach ($book_accessId as $key => $bA_Id) {
      $data[] = array(
        'lbr_access_id'=>$accessId,
        'book_access_id'=>$bA_Id,
        'issue_date'=>date('Y-m-d',strtotime($issue_date)),
        'status'=>'1',
        'created_by'=>$this->authorization->getAvatarId() // created by ,
      );
    }
    return $this->db->insert_batch('library_transacation',$data);
  }

  public function update_issuebook_details($returnId){
    $data = array();
    foreach ($returnId as $key => $id) {
      $data[] = array(
        'id'=>$id,
        'return_date'=>date('Y-m-d',strtotime($this->input->post('return_date'))),
        'status'=>2,
      );
    }
   
   return $this->db->update_batch('library_transacation',$data,'id');

    // if (!empty($update)) {
    //   $fData = array();
    //   foreach ($fAmount as $key => $fA) {
    //     if ($fA != 0) {
    //       $fData[] = array(
    //         'lbr_card_id'=>$accessId,
    //         'book_acccess_id'=>$book_accessId[$key],
    //         'fine_amount'=>$fA,
    //         'created_by'=>$this->authorization->getAvatarId(),
    //         'transaction_id'=>$returnId[$key],
    //       );
    //     }
    //   }
    //   if (empty($fData)) {
    //    return $update;
    //   }
    //   return $this->db->insert_batch('library_fine_transaction',$fData);
    // }

  }
  public function get_library_transcationbyReturnId($lbr_card_id){
 
    $lc_card = $this->db->select('stake_holder_id,stake_holder_type')
    ->from('library_cards')
    ->where('id',$lbr_card_id)
    ->get()->row();

    if ($lc_card->stake_holder_type == 'staff') {
      $this->db->select('sm.identification_code as sCode');
      $this->db->from('staff_master sm');
      $this->db->where('sm.id',$lc_card->stake_holder_id);
    }else{
      $this->db->select("sd.identification_code as sCode");
      $this->db->from('student_year sy');
      $this->db->join('student_admission sd','sy.student_admission_id=sd.id');
      $this->db->where('admission_status','2');
      $this->db->where('sy.promotion_status!=', '4');
      $this->db->where('sy.promotion_status!=', '5');
      $this->db->where('sd.id',$lc_card->stake_holder_id);
    }
    $result =  $this->db->get()->row();
   return $result;
  }

  public function insert_fine_transacation($returnId,$id_code,$fine_amount){
    $fData = array();
    foreach ($fine_amount as $k => $amount) {
      if ($amount != 0) {
        $fData[] = array(
          'trans_id'=>$returnId[$k],
          'identification_code'=>$id_code,
          'amount'=>$fine_amount[$k],
          'created_by'=>$this->authorization->getAvatarId()
        );
      }
    }
    if (empty($fData)) {
      return true;
    }
    return $this->db->insert_batch('library_fine_transaction',$fData);
    
  }
  public function get_staff_all(){
    $this->db->select("id as staff_id, concat(ifnull(first_name,''), ' ' ,ifnull(last_name,'')) as name");
    $this->db->where('status','2'); // 2 Approved Staff
    return $this->db->get('staff_master')->result();
  }

  public function insert_qrCodestaff($qrCode,$staffId){
    foreach ($qrCode as $key => $code) {
      $data[] = array(
         'id'=>$staffId[$key],
        'identification_code'=>$code,
      );
    }
    return $this->db->update_batch('staff_master',$data,'id');
  }

  public function get_s_qrCodeDetails($staffId){

    return $this->db->select("sm.identification_code as qr_code, concat(ifnull(first_name,''), ' ' ,ifnull(last_name,'')) as name")
      ->from('staff_master sm')
      ->where_in('id',$staffId)
      ->get()->result();
  }

  public function get_all_class(){
    return $this->db->select("c.id as cId,cs.id as csId, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as cls_sec")
    ->from('class c')
    ->where('c.acad_year_id',$this->yearId)
    ->join('class_section cs','c.id=cs.class_id')
    ->get()->result();
  }

  public function get_student_all($sectionId){

    return $this->db->select("sd.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as name, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as cls_sec")
      ->from('student_year sy')
      ->join('student_admission sd','sy.student_admission_id=sd.id')
      ->where('admission_status','2')
      ->where('sy.promotion_status!=', '4')
      ->where('sy.promotion_status!=', '5')
      ->where('sy.class_section_id',$sectionId)
      ->where('sy.acad_year_id',$this->yearId)
      ->join('class_section cs','sy.class_section_id=cs.id')
      ->join('class c','cs.class_id=c.id')
      ->order_by('sd.first_name')
      ->get()->result();

  }

  public function get_parent_of_student_all($sectionId){
     return  $this->db->select("sd.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as name, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as cls_sec,p.id as pId")
      ->from('student_year sy')
      ->join('student_admission sd','sy.student_admission_id=sd.id')
      ->join('parent p','sd.id=p.student_id')
      ->where('admission_status','2')
      ->where('sy.promotion_status!=', '4')
      ->where('sy.promotion_status!=', '5')
      ->where('sy.class_section_id',$sectionId)
      ->where('sy.acad_year_id',$this->yearId)
      ->join('class_section cs','sy.class_section_id=cs.id')
      ->join('class c','cs.class_id=c.id')
      ->get()->result();

  }

  public function get_section_parent_of_student($sectionId, $relation_type){
    return  $this->db->select("sd.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as name, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as cls_sec,p.id as pId, concat(ifnull(p.first_name,''), ' ' ,ifnull(p.last_name,'')) as pname")
      ->from('student_year sy')
      ->join('student_admission sd','sy.student_admission_id=sd.id')
      ->join('parent p','sd.id=p.student_id')
      ->join('student_relation sr','p.id=sr.relation_id')
      ->where('sr.relation_type',$relation_type)
      ->where('admission_status','2')
      ->where('sy.promotion_status!=', '4')
      ->where('sy.promotion_status!=', '5')
      ->where('sy.class_section_id',$sectionId)
      ->where('sy.acad_year_id',$this->yearId)
      ->join('class_section cs','sy.class_section_id=cs.id')
      ->join('class c','cs.class_id=c.id')
      ->get()->result();
  }

  public function get_parent_qrCodeDetails($pIds){
    return  $this->db->select("concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as name, p.id as pId, concat(ifnull(p.first_name,''), ' ' ,ifnull(p.last_name,'')) as pname, p.identification_code, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as cls_sec,")
    ->from('parent p')
    ->join('student_admission sd','p.student_id=sd.id')
    ->join('student_year sy','sd.id=sy.student_admission_id')
    ->join('class_section cs','sy.class_section_id=cs.id')
    ->join('class c','sy.class_id=c.id')
    ->where_in('p.id',$pIds)
    ->get()->result();
  }
  public function insert_qrCodestudent($qrCode,$stdId){

    $data = array();
    foreach ($qrCode as $key => $code) {
      $result = $this->db->select('identification_code')
      ->from('student_admission')
      ->where('id',$stdId[$key])
      ->get()->row();
      if(empty($result->identification_code)){
        $data[] = array(
          'id'=>$stdId[$key],
         'identification_code'=>$code,
       );
      }else{
        $data[] = array(
          'id'=>$stdId[$key],
         'identification_code'=>$result->identification_code,
       );
      }
    }
    return $this->db->update_batch('student_admission',$data,'id');
  }

  public function insert_student_url_qr_code($qrCode,$stdId){
    $data = array();
    foreach ($qrCode as $stdId => $code) {
      $result = $this->db->select('identification_code')
      ->from('student_admission')
      ->where('id',$stdId)
      ->like('identification_code','student_url_qr_code')
      ->get()->row();
      $urlupdate = base_url().'student_url_qr_code/'.$code;
      if(!empty($result->identification_code)){
        $urlupdate = $result->identification_code; 
      }
      $data[] = array(
        'id'=> $stdId,
        'identification_code' => $urlupdate
      );
    }
    return $this->db->update_batch('student_admission',$data,'id');
  }

  public function insert_qrCodeparent($qrCode,$pIds)
  {
    $data = array();
    foreach ($qrCode as $key => $code) {
      $data[] = array(
         'id'=>$pIds[$key],
        'identification_code'=>$code,
      );
    }
    return $this->db->update_batch('parent',$data,'id');
  }

  public function get_stu_qrCodeDetails($stdId){

    return  $this->db->select("sd.id as stdId, sd.identification_code as iCode, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as name, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as cls_sec, sy.picture_url, sd.admission_no")
      ->from('student_year sy')
      ->join('student_admission sd','sy.student_admission_id=sd.id')
      ->where('admission_status','2')
      ->where('sy.promotion_status!=', '4')
      ->where('sy.promotion_status!=', '5')
      ->where_in('sd.id',$stdId)
      ->where('sy.acad_year_id',$this->yearId)
      ->join('class_section cs','sy.class_section_id=cs.id')
      ->join('class c','cs.class_id=c.id')
      ->get()->result();

  }

  public function get_libr_cards_staff($staff_student_id,$userType){
    return $this->db->select("lc.id,stake_holder_type, card_access_code,lm.member_type,num_books, sum(case when lt.status = 1 then 1 else 0 end) as borrowed")
      ->from('library_cards lc')
      ->where('lc.stake_holder_id',$staff_student_id)
      ->where('lc.stake_holder_type',$userType)
      ->where('lc.status',1)
      ->join('library_master lm','lc.master_id=lm.id')
      ->join('library_transacation lt','lc.id=lt.lbr_access_id','left')
      ->group_by('lc.card_access_code')
      ->get()->result();
  }

  public function _due_date_list_find($staff_lbr_cards){

      foreach ($staff_lbr_cards as $key => &$val) {
        $issueDate = $val->issue_date;
        $cDate = date('Y-m-d');
        $dayCount = $val->hold_period;
        // $due_date[$val->transId] = date('Y-m-d',strtotime($issueDate) + (24*3600*$dayCount));
        $due_date = date('Y-m-d',strtotime($issueDate) + (24*3600*$dayCount));
        $val->due_date = $due_date;
      }

     return $staff_lbr_cards;
  }

  public function get_libr_books_cardWise($lbrId, $userType){
    $master =  $this->db->select("lc.id,lc.card_access_code,lm.member_type,num_books, sm.identification_code, lft.amount as amount, lft1.amount as paidAmount")
    ->from('library_cards lc')
    ->where('lc.id',$lbrId)
    ->join('library_master lm','lc.master_id=lm.id')
    ->join('staff_master sm','lc.stake_holder_id=sm.id and lc.stake_holder_type = "$userType"','left')
    ->join('library_fine_transaction lft','sm.identification_code=lft.identification_code and lft.trans_id IS NOT NULL','left')
    ->join('library_fine_transaction lft1','sm.identification_code=lft1.identification_code and lft1.trans_id IS NULL','left')
    ->get()->row();

    $today = date('Y-m-d');

    $tanscation_list = $this->db->select("lc.id, lc.card_access_code, lm.member_type, lm.num_books, date_format(lt.issue_date,'%d-%m-%Y') as issue_date, date_format(lt.return_date,'%d-%m-%Y') as return_date, date_format(date_add(lt.issue_date, INTERVAL lm.hold_period DAY), '%d-%m-%Y') as due_date, if(datediff(ifnull(lt.return_date, CURRENT_DATE()),  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)) > 0 , datediff(ifnull(lt.return_date, CURRENT_DATE()),  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)), 0) as dayDiff , if(datediff(ifnull(lt.return_date, CURRENT_DATE()),  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)) > 0 , datediff(ifnull(lt.return_date, CURRENT_DATE()),  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)), 0) * lm.fine_per_day as fineAmount, lb.book_title, lb.author, lb.subject")
    ->from('library_cards lc')
    ->where('lc.id',$lbrId)
    ->join('library_master lm','lc.master_id=lm.id')
    ->join('library_transacation lt','lc.id=lt.lbr_access_id','left')
    ->join('library_books_copies lbc','lt.book_access_id=lbc.id')
    ->join('library_books lb','lbc.book_id=lb.id')
    ->get()->result();
    $master->trans_list = $tanscation_list;
    return $master;
    // echo "<pre>"; print_r($master);
    //  die();
  }

  public function get_libr_books_all_tx($lbrId, $userType){
    $master =  $this->db->select("lc.id,lc.card_access_code,lm.member_type,num_books, sm.identification_code, lft.amount as amount, lft1.amount as paidAmount")
    ->from('library_cards lc')
    ->where_in('lc.id',$lbrId)
    ->join('library_master lm','lc.master_id=lm.id')
    ->join('staff_master sm','lc.stake_holder_id=sm.id and lc.stake_holder_type = "$userType"','left')
    ->join('library_fine_transaction lft','sm.identification_code=lft.identification_code and lft.trans_id IS NOT NULL','left')
    ->join('library_fine_transaction lft1','sm.identification_code=lft1.identification_code and lft1.trans_id IS NULL','left')
    ->get()->result();

    $today = date('Y-m-d');
    $lbrIdquery= implode(',', $lbrId);

    $tanscation_list = $this->db->select("lc.id, lc.card_access_code, lm.member_type, lm.num_books, date_format(lt.issue_date,'%d-%m-%Y') as issue_date, date_format(lt.return_date,'%d-%m-%Y') as return_date, date_format(date_add(lt.issue_date, INTERVAL lm.hold_period DAY), '%d-%m-%Y') as due_date, if(datediff(ifnull(lt.return_date, CURRENT_DATE()),  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)) > 0 , datediff(ifnull(lt.return_date, CURRENT_DATE()),  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)), 0) as dayDiff , if(datediff(ifnull(lt.return_date, CURRENT_DATE()),  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)) > 0 , datediff(ifnull(lt.return_date, CURRENT_DATE()),  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)), 0) * lm.fine_per_day as fineAmount, lb.book_title, lb.author, lb.subject")
    ->from('library_cards lc')
    ->where_in('lc.id',$lbrId)
    ->join('library_master lm','lc.master_id=lm.id')
    ->join('library_transacation lt',"lc.id=lt.lbr_access_id",'left')
    ->join('library_books_copies lbc','lt.book_access_id=lbc.id')
    ->join('library_books lb','lbc.book_id=lb.id')
    ->order_by('lt.id','desc')
    ->get()->result();
    $transactionArry =[];
    foreach ($tanscation_list as $key => $value) {
      $transactionArry[$value->id] = $value;
    }
    
    foreach ($master as $key => &$m) {
      if(array_key_exists($m->id, $transactionArry))
        $m->trans_list = $tanscation_list;
    }
    return $master;
    // echo "<pre>"; print_r($master);
    //  die();
  }

  public function get_libr_books_staff_all($staffId){

    $staff = $this->db->select("sm.identification_code")
    ->from('staff_master sm')
    ->where('sm.id',$staffId)
    ->get()->row();
    $staff_lbr_cards = $this->db->select("lc.id as lbrcardId, lc.card_access_code,lm.member_type")
    ->from('library_cards lc')
    ->join('library_master lm','lc.master_id=lm.id')
    ->where('lc.stake_holder_type','staff')
    ->where('lc.stake_holder_id',$staffId)
    ->get()->result();

    $lbr_CardId = array();
    foreach ($staff_lbr_cards as $key => $cardId) {
      $lbr_CardId[]=$cardId->lbrcardId;
    }

    $lbrTranscation = $this->db->select("date_format(lt.issue_date,'%d-%m-%Y') as issue_date,lt.status,lb.book_title,author,lft.amount,lbr_access_id,lm.hold_period")
    ->from('library_transacation lt')
    ->join('library_fine_transaction lft','lt.id=lft.trans_id','left')
    ->join('library_books_copies lbc','lt.book_access_id=lbc.id')
    ->join('library_books lb','lbc.book_id=lb.id')
    ->join('library_cards lc','lt.lbr_access_id=lc.id')
    ->join('library_master lm','lc.master_id=lm.id')
    ->order_by('lt.id','desc')
    ->where_in('lt.lbr_access_id',$lbr_CardId)
    ->get()->result();

    $lbr_trans_due_date = $this->_due_date_list_find($lbrTranscation);
    
    // echo "<pre>"; print_r($staff);

    $amount_paid =  $this->db->select('sum(amount) as amount_paid')
    ->from('library_fine_transaction lft')
    ->where('identification_code',$staff->identification_code)
    ->where('trans_id is NULL')
    ->get()->row()->amount_paid;

    $amount =  $this->db->select('sum(amount) as amount')
    ->from('library_fine_transaction lft')
    ->where('identification_code',$staff->identification_code)
    ->where('trans_id is NOT NULL')
    ->get()->row()->amount;

    $total_balance = $amount + $amount_paid;

    foreach ($staff_lbr_cards as $key => &$val) {
        foreach ($lbr_trans_due_date as $key => $v) {
          if ($val->lbrcardId == $v->lbr_access_id) {
            $val->trans[] = $v;
            $val->balance = $total_balance;
          } 
        }
    }
    return $staff_lbr_cards;
  }

  public function get_staff_student_names(){

    $staff =  $this->db->select("sm.id as stafId, concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as s_name, sm.identification_code as id_number")
    ->from("staff_master sm")
    ->where('status',2)
    ->get()->result();

    $student =  $this->db->select(" concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as s_name, cs.section_name, c.class_name, sd.identification_code as id_number")
      ->from('student_year sy')
      ->join('student_admission sd','sy.student_admission_id=sd.id')
      ->where('admission_status','2')
      ->where('sy.promotion_status!=', '4')
      ->where('sy.promotion_status!=', '5')
      ->where('sy.acad_year_id',$this->yearId)
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->join('class c','sy.class_id=c.id','left')
      ->get()->result();

    return array('staff'=>$staff, 'student'=>$student);

  }

  public function insert_import_booksdata($input){

    $form_input = (array)$input;
    $data = array(
      //'date_of_accession'     => date('Y-m-d',strtotime($this->input->post('date_of_accession'))) , 
        'book_type'             => $form_input['book_type'], 
        'category'              => $form_input['category'], 
        'book_title'            => $form_input['book_title'],
        'series'                => $form_input['series'],
        'volume'                => $form_input['volume'],
        'subject'               => $form_input['subject'],
        'language'              => $form_input['language'], 
        'author'                => $form_input['author'], 
        'yearof_publishing'     => $form_input['yearof_publishing'],
        'publisher_name'        => $form_input['publisher_name'], 
        'location_book'         => $form_input['location_book'], 
        'source'                => $form_input['source'], 
        'description'           => $form_input['description'],
        'contains'              => $form_input['contains'] ,
        'last_modified_by' => $this->authorization->getAvatarId()
    );
    $this->db->insert('library_books',$data);
    $lastId =  $this->db->insert_id();

    if (empty($form_input['date_of_accession'])) {
      $form_input['date_of_accession'] == null;
    }

    $data = array(
      'book_id'=>$lastId,
      'access_code' => $form_input['access_code'],
      'costof_book' => $form_input['costof_book'],
      'remarks' => $form_input['remarks'],
      'date_of_accession' => $form_input['date_of_accession'],
    );
   
   return $this->db->insert('library_books_copies',$data);
  }

  public function update_import_booksdata($input){
    $form_input = (array)$input;
    
    $this->db->like('book_title', $form_input['book_name']);
    $res = $this->db->get('library_books')->row();

    $data = array(
      'book_id'=>$res->id,
      'access_code' => $form_input['access_code'],
      'costof_book' => $form_input['costof_book'],
      'remarks' => $form_input['remarks'],
      'date_of_accession' => $form_input['date_of_accession'],
    );
  return $this->db->insert('library_books_copies',$data);
  }


  // North hill bulk insert
  public function bulk_insert_northhill_books_data($input){
    $form_input = (array)$input;
  
    $bookType = $this->input->post('book_type');
    $contains  = $this->input->post('contains ');
    $total_numberof_copies  = $this->input->post('total_numberof_copies');
    $this->db->trans_start();
    $data = array(
        //'date_of_accession'     => date('Y-m-d',strtotime($this->input->post('date_of_accession'))) , 
        'book_type'             => $form_input['book_type'], 
        'category'              => $form_input['category'], 
        'book_title'            => $form_input['book_title'],
        'language'              => $form_input['language'], 
        'author'                => $form_input['author'], 
        'yearof_publishing'     => $form_input['yearof_publishing'],
        'publisher_name'        => $form_input['publisher_name'], 
        'last_modified_by' => $this->authorization->getAvatarId()
       );
    $this->db->insert('library_books',$data);
    $booksId = $this->db->insert_id();

    $u_data = array();
    for ($i=1; $i <= $form_input['number_of_copies'] ; $i++) {
      $u_data[] = array(
        'book_id'=>$booksId,
        'costof_book'=> $form_input['cost_of_book'],
        'date_of_accession'=>null
      );
    }

     $this->db->insert_batch('library_books_copies',$u_data);
     $this->db->trans_complete();
     return ($this->db->trans_status() === FALSE)? FALSE:$booksId;
  }

  public function insert_import_data($input) {
    $input = (array)$input;
    $data = array(
        'book_type'             => $input['book_type'], 
        'category'              => $input['category'], 
        'book_title'            => $input['book_title'],
        'language'              => $input['language'], 
        'author'                => $input['author'], 
        'yearof_publishing'     => $input['yearof_publishing'],
        'publisher_name'        => $input['publisher_name'], 
        'series' => $input['series'],
        'edition' => $input['edition'],
        'isbn' => $input['isbn'],
        'call_number' => $input['call_number'],
        'supplier' => $input['supplier'],
        'bill_no_date' => $input['bill_no_date'],
        'acc_no' => $input['acc_no'],
        'subject' => $input['subject'],
        'location_book' => $input['location_book'],
        'source' => $input['source'],
        'last_modified_by' => $this->authorization->getAvatarId()
       );
    $this->db->insert('library_books',$data);
    return $this->db->insert_id();
  }

  public function insert_import_book_copies($input) {
    $input = (array)$input;
      $data = array(
        'book_id'     => $input['book_id'],
        'costof_book' => $input['costof_book'],
        'date_of_accession'=>$input['date_of_accession'],
      );
      return $this->db->insert('library_books_copies',$data);
  }


  public function get_librar_databyisbn($isbn_number){
    $this->db->select('lb.id, lb.book_title, lb.b_sub_title, lb.book_type, lb.author, lb.publisher_name, lbc.status');
    $this->db->from('library_books lb');
    $this->db->join('library_books_copies lbc', 'lb.id=lbc.book_id');
    $this->db->where('lb.soft_delete!=',1);
    $this->db->where('lb.isbn', $isbn_number);
    $this->db->order_by('lb.id','desc');
    $this->db->limit(500);
    $books = $this->db->get()->result();
   return $this->_get_librar_databyname($books);
  }

  public function get_librar_databybook($books_title){
    $this->db->select('lb.id, lb.book_title, lb.b_sub_title, lb.book_type, lb.author, lb.publisher_name, lbc.status');
    $this->db->from('library_books lb');
    $this->db->join('library_books_copies lbc', 'lb.id=lbc.book_id');
    $this->db->where('lb.soft_delete!=',1);
    $this->db->where("lb.book_title like '%$books_title%'");
    $this->db->order_by('lb.id','desc');
    $this->db->group_by('lb.id');
    $this->db->limit(500);
    $books = $this->db->get()->result();
   return $this->_get_librar_databyname($books);
  }

  public function get_librar_databyauthor($book_author){
    $this->db->select('lb.id, lb.book_title, lb.b_sub_title, lb.book_type, lb.author, lb.publisher_name, lbc.status');
    $this->db->from('library_books lb');
    $this->db->join('library_books_copies lbc', 'lb.id=lbc.book_id');
    $this->db->or_where("lb.author like '%$book_author%'");
    $this->db->order_by('lb.id','desc');
    $this->db->where('lb.soft_delete!=',1);
    $this->db->limit(500);
    $books = $this->db->get()->result();
    return $this->_get_librar_databyname($books);
  }

  public function get_librar_databypublisher($publisher_name){
    $this->db->select('lb.id, lb.book_title, lb.b_sub_title, lb.book_type, lb.author, lb.publisher_name, lbc.status');
    $this->db->from('library_books lb');
    $this->db->join('library_books_copies lbc', 'lb.id=lbc.book_id');
    $this->db->where("lb.publisher_name like '%$publisher_name%'");
    $this->db->order_by('lb.id','desc');
    $this->db->where('lb.soft_delete!=',1);
    $this->db->limit(500);
    $books = $this->db->get()->result();
    return $this->_get_librar_databyname($books);
  }

  public function get_new_arrivals($new_arrivals_date){
    $fromDate = $new_arrivals_date;
    $toDate = date('Y-m-d');

    $this->db->select('lb.id, lb.book_title, lb.b_sub_title, lb.book_type, lb.author,lb.publisher_name,lbc.id as lbcId, lbc.status');
    $this->db->from('library_books lb');
    $this->db->where('lb.soft_delete!=',1);
    if ($fromDate && $toDate) {
      $this->db->where('date_format(lbc.date_of_accession,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    }
    $this->db->join('library_books_copies lbc','lb.id=lbc.book_id');
    $books =  $this->db->get()->result();
    return $this->_get_librar_databyname($books);
  }

  public function get_librar_books_databyId($accessId){
    $this->db->select('lb.id, lb.book_title, lb.b_sub_title, lb.book_type, lb.author,lb.publisher_name,lbc.id as lbcId, lbc.status');
    $this->db->from('library_books lb');
    $this->db->where('lb.soft_delete!=',1);
    $this->db->where('lbc.access_code',$accessId);
    $this->db->join('library_books_copies lbc','lb.id=lbc.book_id');
    $this->db->group_by('lb.id');
    $books =  $this->db->get()->result();
    return $this->_get_librar_databyname($books);

  }

  public function default_newly_added_librar_books(){

    $this->db->select('id, book_title, b_sub_title, book_type, author,publisher_name');
    $this->db->where('soft_delete!=',1);
    $this->db->order_by('id','desc');
    $this->db->limit(50);
    $books = $this->db->get('library_books')->result();
   return $this->_get_librar_databyname($books);

  }

  public function get_filter_serachby_bookslist($books_title,$book_author,$publisher_name){
    $this->db->select('id, book_title, book_type, author,publisher_name');
    if ($books_title) {
      $this->db->or_where("book_title like '%$books_title%'");
    }
    if ($book_author) {
      $this->db->or_where("author like '%$book_author%'");
    }
    if ($publisher_name) {
      $this->db->or_where("publisher_name like '%$publisher_name%'");
    }
    $this->db->order_by('id','desc');
    $this->db->limit(500);
    $books = $this->db->get('library_books')->result();
    return $this->_get_librar_databyname($books);
  }
  private function _get_librar_databyname($books){
  
    if (empty($books)) {
      return false;
    }

    $booksId = array();
    foreach ($books as $key => $val) {
      $booksId[] = $val->id;
    }


    $lbcCount = $this->db->select('lbc.book_id, count(lbc.id) as tCopies')
    ->from('library_books_copies lbc')
    ->where_in('lbc.book_id',$booksId)
    ->where('lbc.status','Available')
    ->where("lbc.id IN (select book_access_id from library_transacation where status=1)")
    ->order_by('book_id','asc')
    ->group_by('book_id')
    ->get()->result();

    $booksCount = $this->db->select('lbc.book_id, count(lbc.id) as tbookCopies')
    ->from('library_books_copies lbc')
    ->where_in('lbc.book_id',$booksId)
    ->where('lbc.status','Available')
    ->order_by('book_id','asc')
    ->group_by('book_id')
    ->get()->result();

    if (!empty($booksCount)) {
      foreach ($booksCount as $key => $bCount) {
         $bCount->bCopies = $bCount->tbookCopies;
         if (!empty($lbcCount)) {
           foreach ($lbcCount as $key => $tcount) {
             if ($bCount->book_id == $tcount->book_id) {
               $bCount->bCopies = $bCount->tbookCopies - $tcount->tCopies;
             }
           }
         }
      }
    }
    foreach ($books as $key => &$book) {
      foreach ($booksCount as $key => $count) {
        if ($book->id == $count->book_id) {
          $book->tbookCopies = $count->tbookCopies;
          $book->bCopies = $count->bCopies;
        } 
      }
    }
  return $books;
  }

  public function get_lbrCards_master_details($userType){
    $avatarId = $this->authorization->getAvatarId();
    $result = $this->db->select('lm.book_type')
    ->from('avatar a')
    ->join('library_cards lc','lc.stake_holder_id=a.stakeholder_id')
    ->where('stake_holder_type',$userType)
    ->join('library_master lm','lc.master_id=lm.id')
    ->get()->row();
    if (!empty($result)) {
       $bType = json_decode($result->book_type);
    }
    return $bType;
  }
  private function _library_transaction_data($transaction){
      $staff = array();
      $student = array();
      foreach ($transaction as $key => $val) {
        if ($val->stake_holder_type =='staff') {
          $staffId[] = $val->stake_holder_id;
        }else{
          $stdId[] = $val->stake_holder_id;
        }
      }

      if (!empty($staffId)) {
        $staff = $this->db_readonly->select("id as stake_holder_id, concat(ifnull(first_name,''), ' ' ,ifnull(last_name,'')) as  first_name, first_name, ' ' as sectionName, 'Staff' as type, 'staff' as stake_holder_type")
        ->from('staff_master')
        ->where_in('id',$staffId)
        ->get()->result();
      }
    
      if (!empty($stdId)) {

        $student =  $this->db_readonly->select("sd.id as stake_holder_id, concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as  first_name,  concat(ifnull(cs.class_name,''), '' ,ifnull(cs.section_name,'')) as sectionName, 'Student' as type, 'student' as stake_holder_type ")
        ->from('student_year sy')
        ->join('student_admission sd','sy.student_admission_id=sd.id')
        ->join('class_section cs','sy.class_section_id=cs.id')
        ->where('admission_status','2')
        ->where('sy.promotion_status!=', '4')
        ->where('sy.promotion_status!=', '5')
        ->where('sy.acad_year_id',$this->yearId)
        ->where_in('sd.id',$stdId)        
        ->get()->result();
      }
  
      $staff_std =  array_merge($staff,$student);

     foreach ($transaction as $key => &$trns) {
        foreach ($staff_std as $key => $data) {
          if ($data->stake_holder_id == $trns->stake_holder_id && $data->stake_holder_type == $trns->stake_holder_type) {
            $trns->name = $data->first_name.' - '.$data->sectionName.'<br>('.$data->type.')';
            $trns->secName = $data->sectionName;
          }
        }
      }
    
     $books_type = $this->settings->getSetting('books_type');
      foreach ($transaction as $key => &$val) {
        $bType = '';
        foreach ($books_type as $key => $type) {
            if ($type->value == $val->book_type) {
              if (!empty($bType)) $bType.=', ';
                $bType .=$type->name;
              }
            $val->typeof_book_allowed = $bType;
          }
        }
    return $transaction;
  }

  public function get_today_transactionfor_lbr(){
    $lbrId = $this->session->userdata('libraries');

    $transaction = $this->db->select("lt.id as ltId, lt.status, date_format(issue_date,'%d-%m-%Y') as issue_date,date_format(return_date,'%d-%m-%Y') as return_date, lc.stake_holder_id, lc.stake_holder_type, lc.card_access_code, lb.book_type, lb.book_title,lm.member_type,lbc.access_code as book_id, lb.author, ifnull(lb.book_course,'') as course")
    ->from('library_transacation lt')
    ->where('lt.status','1')
    ->where('date_format(lt.issue_date,"%d-%m-%Y")', date('d-m-Y'))
    ->join('library_cards lc','lt.lbr_access_id=lc.id')
    ->join('library_master lm','lc.master_id=lm.id')
    ->join('library_books_copies lbc','lt.book_access_id=lbc.id')
    ->join('library_books lb','lbc.book_id=lb.id')
    ->where('lb.libraries',$lbrId)
    ->get()->result();
   return $this->_library_transaction_data($transaction);

  }

  public function get_transactionfor_lbr_all(){
     $transaction = $this->db->select("lt.id as ltId, lt.status, date_format(issue_date,'%d-%m-%Y') as issue_date,date_format(return_date,'%d-%m-%Y') as return_date, lc.stake_holder_id, lc.stake_holder_type, lc.card_access_code, lb.book_type, lb.book_title,lm.member_type, lbc.access_code as book_id, lb.author, ifnull(lb.book_course,'') as course")
    ->from('library_transacation lt')
    ->join('library_cards lc','lt.lbr_access_id=lc.id')
    ->join('library_master lm','lc.master_id=lm.id')
    ->join('library_books_copies lbc','lt.book_access_id=lbc.id')
    ->join('library_books lb','lbc.book_id=lb.id')
    ->get()->result();
    //echo "<pre>"; print_r($transaction); die();
   return $this->_library_transaction_data($transaction);
  }

  public function get_transactionfor_lbr_daywise($from_date, $to_date, $type){
    $lbrId = $this->session->userdata('libraries');

    // echo "<pre>"; print_r($lbrId); die();
    $fromDate = date('Y-m-d',strtotime($from_date));
    $toDate =date('Y-m-d',strtotime($to_date));


    $this->db_readonly->select("lt.id as ltId, (case when lt.status = 1 then 'Issue'  else 'Return' end) as status, lc.stake_holder_id, lc.stake_holder_type, ifnull(date_format(issue_date,'%d-%m-%Y'),'') as issue_date, ifnull(date_format(return_date,'%d-%m-%Y'),'') as return_date, lc.card_access_code, lb.book_type, lb.book_title,lm.member_type, lbc.access_code as book_id, lb.author, ifnull(lb.book_course,'') as course, date_format(lt.created_on,'%d-%m-%Y') as created_date, (case when lt.status = 1 then ifnull(date_format(issue_date,'%d-%m-%Y'),'')  else ifnull(date_format(return_date,'%d-%m-%Y'),'') end) as issue_return_date");
    $this->db_readonly->from('library_transacation lt');
    if ($type == 1) {
      $this->db_readonly->where('date_format(lt.issue_date,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    }else if($type == 2){
      $this->db_readonly->where('date_format(lt.return_date,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    }
    // if ($type) {
    //   $this->db_readonly->where('lt.status',$type);
    // }

    $this->db_readonly->join('library_cards lc','lt.lbr_access_id=lc.id');
    $this->db_readonly->join('library_master lm','lc.master_id=lm.id');
    $this->db_readonly->join('library_books_copies lbc','lt.book_access_id=lbc.id');
    $this->db_readonly->join('library_books lb','lbc.book_id=lb.id');
    $this->db_readonly->where('lb.libraries',$lbrId);
    $transaction =  $this->db_readonly->get()->result();
    return $this->_library_transaction_data($transaction);
  }

  public function get_transcation_report($bookId){
    $this->db_readonly->select("lt.id as ltId, (case when lt.status = 1 then 'Issue'  else 'Return' end) as status, lc.stake_holder_id, lc.stake_holder_type, ifnull(date_format(issue_date,'%d-%m-%Y'),'') as issue_date, ifnull(date_format(return_date,'%d-%m-%Y'),'') as return_date, lc.card_access_code, lb.book_type, lb.book_title,lm.member_type, lbc.access_code as book_id, lb.author, ifnull(lb.book_course,'') as course, date_format(lt.created_on,'%d-%m-%Y') as created_date, (case when lt.status = 1 then ifnull(date_format(issue_date,'%d-%m-%Y'),'')  else ifnull(date_format(return_date,'%d-%m-%Y'),'') end) as issue_return_date");
    $this->db_readonly->from('library_transacation lt');
    

    $this->db_readonly->join('library_cards lc','lt.lbr_access_id=lc.id');
    $this->db_readonly->join('library_master lm','lc.master_id=lm.id');
    $this->db_readonly->join('library_books_copies lbc','lt.book_access_id=lbc.id');
    $this->db_readonly->join('library_books lb','lbc.book_id=lb.id');
    $this->db_readonly->where('lb.id',$bookId);
    $transaction =  $this->db_readonly->get()->result();
    // echo "<pre>"; print_r($transaction); die();
    return $this->_library_transaction_data($transaction);
  }

  public function get_bulk_book_data(){
   return $this->db->select('lb.book_title, lbc.access_code')
    ->from('library_books lb')
    ->order_by('lb.book_title','asc')
    ->join('library_books_copies lbc','lb.id=lbc.book_id')
    ->get()->result();
  }


  public function insert_access_number_forprint($lbcId){
    $this->db->select('count(id) as count');
    $this->db->from('books_cart');
    $counts =  $this->db->get()->row()->count;
    
    // $array_count = count($lbcId);
    // $tCount =  $counts + $array_count;
    // if ($tCount > 40) {
    //   return 2;
    // }
  
    foreach ($lbcId as $key => $val) {
      $data[] = array('lbc_id'=>$val, 'avatar_id'=>$this->authorization->getAvatarId());
    }
   
    $this->db->where_in('lbc_id',$lbcId);
    $res = $this->db->get('books_cart')->result();
    if (!empty($res)) {
      return 0;
    }
    return $this->db->insert_batch('books_cart',$data);
  }


  

  public function edit_accessnumberById($lbcid){
    return $this->db->select('lbc.*, lb.id as lbId,lbc.remarks, lb.book_title, author, language, category,  subject, lb.volume, contains, edition')
    ->from('library_books_copies lbc')
    ->where('lbc.id',$lbcid)
    ->join('library_books lb','lb.id=lbc.book_id')
    ->get()->row();
  }

  public function get_lbr_fine_details($id_code,$limit){
    if (empty($id_code)) {
      return array();
    }
    $trans = $this->db->select('sum(case when trans_id is NULL then amount else 0 end ) as amtPaid, sum(case when trans_id is NOT NULL then amount else 0 end ) as tobeCollected')
    ->from('library_fine_transaction lft')
    ->where('identification_code',$id_code)
    ->get()->row();

    $total_balance = $trans->tobeCollected + $trans->amtPaid;

     $this->db->select('lft.id,lft.amount,lb.book_type,lb.book_title, (case when lft.trans_id is NULL then date_format(lft.created_on, "%d-%m-%Y") else null  end) as paidDate, (case when lft.trans_id is NULL then "Cash Paid" else (case when lft.transaction_mode is not NULL then "Damage / lost " else "Delayed return" end) end) as typeStatus, lft.payment_type')
    ->from('library_fine_transaction lft')
    ->where('identification_code',$id_code)
    ->join('library_transacation lt','lft.trans_id=lt.id','left')
    ->join('library_books_copies lbc','lt.book_access_id=lbc.id','left')
    ->join('library_books lb','lbc.book_id=lb.id','left');
    // ->order_by('lft.id','desc');
    if ($limit) {
     $this->db->limit($limit);
    }
    $result = $this->db->get()->result();
    $paymentType = $this->settings->getSetting('lbr_payment_modes');
    foreach ($result as $key => $val) {
      if (!empty($val->payment_type)) {
         foreach ($paymentType as $key => $type) {
          if ($type->value == $val->payment_type) {
            $val->pType = strtoupper($type->name);
          }
        }
      }else{
        $val->pType = $val->typeStatus;
      }
    }
    return array('transaction'=> $result,'balance'=>$total_balance);

  }

  public function get_librarycar_identification_code($accessId){  

   $lc_card =  $this->db->select('stake_holder_id,stake_holder_type')
    ->from('library_cards')
    ->where('card_access_code',$accessId)
    ->get()->row();

    if ($lc_card->stake_holder_type == 'staff') {
      $this->db->select('sm.identification_code as sCode');
      $this->db->from('staff_master sm');
      $this->db->where('sm.id',$lc_card->stake_holder_id);
    }else{

        $this->db->select("sd.identification_code as sCode");
        $this->db->from('student_year sy');
        $this->db->join('student_admission sd','sy.student_admission_id=sd.id');
        $this->db->where('admission_status','2');
        $this->db->where('sy.promotion_status!=', '4');
        $this->db->where('sy.promotion_status!=', '5');
        $this->db->where('sy.acad_year_id',$this->yearId);
        $this->db->where_in('sd.id',$lc_card->stake_holder_id);
    }
    $result =  $this->db->get()->row()->sCode;

      // echo "<pre>"; print_r($result); die();

   return $result;

  }
  public function get_identification_code_fine($accessId){

    $sName = $this->settings->getSetting('school_short_name');
    $strReplce = str_replace(strtoupper($sName), "", $accessId);
    $cardName = substr($strReplce, 0,2);  


    switch ($cardName) {
      case 'SD':
        $identification_code = $accessId;
        break;
      case 'ST':
        $identification_code = $accessId;
        break;
      case 'LC':
        $sCode = $this->get_librarycar_identification_code($accessId);

        $identification_code = $sCode;
        break;
    }

    if (empty($identification_code)) {
     return array();
    }
    return $identification_code;
  }

  public function insert_fine_amountwith_id_code($id_code){
    // $this->db->trans_start();
    $data = array(
      'identification_code'=> $id_code,
      'amount'=>'-'.$this->input->post('t_amount'),
      'remarks'=> $this->input->post('remarks'),
      'payment_type'=>$this->input->post('payment_type'),
      'created_by'=>$this->authorization->getAvatarId(),
    );
    return $this->db->insert('library_fine_transaction',$data);

  
   
  }

  public function get_lbr_due_amount_list(){
    
  }

  private function _fint_transcation_due($result,$flag=0){

    $idCode = array();
    foreach ($result as $key => $val) {
      $idCode[] = $val->identification_code;
    }

    $this->db->select('identification_code, sum(case when trans_id is NOT NULL then amount else 0 end ) as amount, sum(case when trans_id is NULL then ABS(amount)  else 0 end ) as paid, sum(case when trans_id is NOT NULL then amount else 0 end ) + sum(case when trans_id is NULL then amount else 0 end ) as balance ');
    $this->db->from('library_fine_transaction');
    $this->db->where_in('identification_code',$idCode);
    $this->db->group_by('identification_code');
    $amount = $this->db->get()->result();

    foreach ($amount as $key => &$res) {
      foreach ($result as $key => $v) {
        if ($res->identification_code == $v->identification_code ) {
          $res->sName = $v->sName;
          if($flag)
          $res->clssSection = $v->sectionName;
        }
      }
    }
    
    // usort($amount, function($a, $b) { 
    //     echo "<pre>"; print_r($a);
    //   return $a->clssSection - $b->clssSection;}
    // );

    return $amount;
  }


  public function get_studet_duelist($classSection){

    $s_result = $this->db->select("sd.identification_code, concat(ifnull(sd.first_name,''), ' ', ifnull(sd.last_name,'')) as sName, concat(ifnull(c.class_name,''), ' ', ifnull(cs.section_name,'')) as sectionName")
    ->from('library_fine_transaction lft')
    ->join('student_admission sd','lft.identification_code=sd.identification_code')
    ->join('student_year sy','sd.id=sy.student_admission_id')
    ->join('class c','c.id=sy.class_id')
    ->join('class_section cs','cs.id=sy.class_section_id','left')
    ->where_in('sy.class_section_id',$classSection)
    ->where('admission_status','2')
    ->where('sy.promotion_status!=', '4')
    ->where('sy.promotion_status!=', '5')
    ->where('sy.acad_year_id',$this->yearId)
    ->group_by('lft.identification_code')
    ->order_by('c.class_name,cs.section_name,sd.first_name')
    ->get()->result();
    if (empty($s_result)) {
      return 0;
    }
    
    return $this->_fint_transcation_due($s_result,1);
  }

  public function get_staff_duelist_fine(){
    $staf_res = $this->db->select("sm.identification_code, concat(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) as sName")
    ->from('library_fine_transaction lft')
    ->join('staff_master sm','lft.identification_code=sm.identification_code')
    ->group_by('lft.identification_code')
    ->get()->result();
    if (empty($staf_res)) {
      return 0;
    }
    return $this->_fint_transcation_due($staf_res);

  }

  public function get_classandsection_detils(){
    $this->db->select('c.id as clsId,cs.id as sectionId, c.class_name as class, cs.section_name as class_sections');
    $this->db->from('class c');
    $this->db->where('c.acad_year_id',$this->yearId);
    if($this->current_branch) {
      $this->db->where('branch_id',$this->current_branch);
    }
    $this->db->join('class_section cs','cs.class_id=c.id','left');
  return $this->db->get()->result();
  }

  public function get_classwise_student_list($class_section){
    $class = $class_section[0];
    $section = $class_section[1];
   
    $result = $this->db->select('sd.id,sd.first_name,sd.admission_no,c.class_name as class,cs.section_name as std_section,sd.identification_code')
    ->from('student_admission sd')
    ->join('student_year sy','sd.id=sy.student_admission_id')
    ->where('admission_status','2') // 2 Aproved Student
    ->where('sy.promotion_status!=', '4')
    ->where('sy.promotion_status!=', '5')
    ->join('class c','c.id=sy.class_id')
    ->join('class_section cs','cs.id=sy.class_section_id','left')
    ->where('sy.class_id',$class)
    ->where('sy.class_section_id',$section)
    ->order_by('sd.first_name', 'ASC')
    ->get()->result();
    return $result;
  }

  public function get_studet_not_return_list($due_cross='', $classSection='',$book_accessid){
    
    $lbrId = $this->session->userdata('libraries');

    $this->db->select("lt.id as transId, date_format(lt.issue_date,'%d-%m-%Y') as issue_date,  date_format(lt.return_date,'%d-%m-%Y') as return_date, lt.status, lc.card_access_code, lb.book_title,author, lm.member_type,hold_period,lm.fine_per_day, lbc.access_code as book_id, ifnull(lb.book_course,'') as course, max(sy.id) as student_year_id, s.id as sId, concat(ifnull(s.first_name,''), ' ' ,ifnull(s.last_name,'')) as s_name,")
    ->from('library_transacation lt')
    ->join('library_cards lc','lt.lbr_access_id=lc.id')
    ->join('library_books_copies lbc','lt.book_access_id=lbc.id')
    ->join('library_books lb','lbc.book_id=lb.id')
    ->where('lb.libraries',$lbrId)

    ->join('student_admission s','lc.stake_holder_id=s.id')
    ->join('student_year sy','s.id=sy.student_admission_id')

    // ->join('class c','sy.class_id=c.id','left')
    // ->join('class_section cs','sy.class_section_id=cs.id','left')

    ->join('library_master lm','lc.master_id=lm.id')
    ->where('lc.stake_holder_type','student');
    $this->db->where('lt.status',1);
    if ($classSection) {
      $this->db->where_in('sy.class_section_id',$classSection);
    }
    if ($book_accessid) {
      $this->db->where('lbc.access_code',$book_accessid);
    }
    $this->db->group_by('lbc.id');
    $transcat_list_all = $this->db->get()->result();
    foreach ($transcat_list_all as $key => $val) {
      $classSectionData = $this->_library_student_clas_section_details($val->student_year_id);
      $val->class_section = $classSectionData->class_section;
    }
  return  $this->_return_pending_list($transcat_list_all, $due_cross);

  }

  private function _library_student_clas_section_details($student_year_id){
    $this->db->select("concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as class_section");
    $this->db->from('student_year sy');
    $this->db->join('class c','sy.class_id=c.id','left');
    $this->db->join('class_section cs','sy.class_section_id=cs.id','left');
    $this->db->where('sy.id',$student_year_id);
    return $this->db->get()->row();
  }

  private function _return_pending_list($transcat_list_all, $due_cross){
      $transcation = array();
      foreach ($transcat_list_all as $key => $val) {
        $issueDate = $val->issue_date;
        $returndate = $val->return_date;

        $datediff = date_diff(date_create($returndate) ,date_create($issueDate));
        $dayCount_return = $datediff->format('%a');
        // if ($val->hold_period < $dayCount_return) {
          if ($returndate=='') {
            $returndate = date('Y-m-d');
          }
          $dayCount = $val->hold_period;
          // $after_dayCount = $dayCount - $dayCount_return;
          // $due_date[$val->transId] = date('Y-m-d',strtotime($issueDate) + (24*3600*$dayCount));
          $due_date = date('Y-m-d',strtotime($issueDate) + (24*3600*$dayCount));
          $ts1 = strtotime($returndate);
          $ts2 = strtotime($due_date);
          $datediff = $ts1 - $ts2;
          $days_exceeded=0;
          if ($val->hold_period < $dayCount_return) {
            $days_exceeded=($datediff / (60*60*24));
          }
          $perday_fine=$val->fine_per_day *  $days_exceeded;

          if ($due_cross) {
             if ($returndate > $due_date) {
               $val->due_date = date('d-m-Y',strtotime($due_date));
               $val->fine = $perday_fine;
               $val->days_exceeded = $days_exceeded;
              array_push($transcation, $val);
            }
          }else{
              $val->due_date = date('d-m-Y',strtotime($due_date));
              $val->fine = $perday_fine;
              $val->days_exceeded = $days_exceeded;
              array_push($transcation, $val);
          }
        // }else{
        //   $val->due_date = '';
        //   $val->fine = 0;
        //   $val->days_exceeded = 0;
        //   array_push($transcation, $val);
        // }
      }
     return $transcation;
  }

  public function get_staff_not_retur_list($due_cross='',$classSection='',$book_accessid){
    $lbrId = $this->session->userdata('libraries');

    $this->db->select("lt.id as transId, date_format(lt.issue_date,'%d-%m-%Y') as issue_date, date_format(lt.return_date,'%d-%m-%Y') as return_date,  lt.status, lc.card_access_code, lb.book_title,author,sm.id as sId, concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as s_name, lm.member_type,hold_period,lm.fine_per_day,'' as class_section, lbc.access_code as book_id, ifnull(lb.book_course,'') as course ")
    ->from('library_transacation lt')
    ->join('library_cards lc','lt.lbr_access_id=lc.id')
    ->join('library_books_copies lbc','lt.book_access_id=lbc.id')
    ->join('library_books lb','lbc.book_id=lb.id')
    ->where('lb.libraries',$lbrId) 
    ->join('staff_master sm','lc.stake_holder_id=sm.id')
    ->join('library_master lm','lc.master_id=lm.id')
    ->where('lc.stake_holder_type','staff');
    if ($book_accessid) {
      $this->db->where('lbc.access_code',$book_accessid);
    }
    // if ($due_cross) {
      $this->db->where('lt.status',1);
    // }
    $transcat_list_all = $this->db->get()->result();
  return  $this->_return_pending_list($transcat_list_all, $due_cross);
  }

  public function get_data_bulk_books_serach($book_accessid,$lbrcardId){
    $lbrId = $this->session->userdata('libraries');
    $trans = $this->db->select('count(lt.id) as countTrans')
    ->from('library_transacation lt')
    ->where('lbr_access_id',$lbrcardId)
    ->where('status',1)
    ->get()->row();

    $lbr_details = $this->db->select('lm.num_books,lm.book_type')
    ->from('library_cards lc')
    ->where('lc.id',$lbrcardId)
    ->where('stake_holder_type','student')
    ->join('library_master lm','lc.master_id=lm.id')
    ->get()->row();
  
    $lbr_details->remaing = $lbr_details->num_books - $trans->countTrans;
    $lbrBtype = json_decode($lbr_details->book_type); 
    $result = $this->db->select('lb.book_title,lb.author, lb.book_type,lt.id as transId, lt.status,lt.issue_date,lm.hold_period,lm.fine_per_day,lbc.access_code,lbc.id as qrId')
    ->from('library_books_copies lbc')
    ->join('library_books lb','lbc.book_id=lb.id')
    ->where('lb.libraries',$lbrId)
    ->join('library_transacation lt','lbc.id=lt.book_access_id','left')
    ->join('library_cards lc','lt.lbr_access_id=lc.id','left')
    ->join('library_master lm','lc.master_id=lm.id','left')
    ->where_in('lb.book_type',$lbrBtype)
    ->where('lbc.access_code',$book_accessid)
    ->order_by('lt.id','desc')
    ->get()->row();
    if (empty($result)) {
      return 0; // Info :can't be issued book
    }

   $books_type = $this->settings->getSetting('books_type');
    foreach ($books_type as $key => $val) {
      if ($val->value == $result->book_type) {
        $result->bType = $val->name;
        if ($result->status == 1) {
          $result->status = 'Return';
        }else{
          $result->status = 'Issue';
        }
      }  
    }
    $result->remaing_books = $lbr_details->remaing;
    return $result;
  }

  public function get_student_lc_idBy_iCode($stdId){
    $result = $this->db->select('id as lcId')
    ->from('library_cards')
    ->where('stake_holder_id',$stdId)
    ->where('stake_holder_type','student')
    ->get()->row();
    if (empty($result)) {
      return 0;
    } else {
      return $result->lcId;
    }

  }

  public function get_student_accessId_idBy_iCode($lbrId){
    $result = $this->db->select('card_access_code as accessId')
    ->from('library_cards')
    ->where('id',$lbrId)
    ->where('stake_holder_type','student')
    ->get()->row();
    if (empty($result)) {
      return 0;
    } else {
      return $result->accessId;
    }
  }

  public function issue_transcation_table_insert(){
    
    $book_status = isset($_POST['book_status']) ? $_POST['book_status'] : null;
    $lbrcardId = isset($_POST['lbrcardId']) ? $_POST['lbrcardId'] : null ;
    $book_accessId = isset($_POST['book_accessId']) ? $_POST['book_accessId'] : null;

    if (!is_array($book_status) || empty($book_status)) {
      return false;
    }
    
    if (empty($lbrcardId) || empty($book_accessId)) {
      return false;
    }

    $this->db->where_in('book_access_id',$book_accessId);
    $this->db->where('status',1);
    $query = $this->db->get('library_transacation')->result();

    $id_code = $this->db->select('s.identification_code')
    ->from('library_cards lc')
    ->where('lc.stake_holder_type','student')
    ->join('student_admission s','lc.stake_holder_id=s.id')
    ->where('lc.id',$lbrcardId)
    ->get()->row()->identification_code;
    $insert = [];
    $fData = [];
    $update = [];
    foreach ($book_status as $key => $status) {
      if ($status == 'Return') {
      $transId = $_POST['transId'];
      $amount = $_POST['amount'];
        $update = array();
        foreach ($query as $key => $val) {
           $update[] = array(
            'id'=>$val->id,
            'return_date'=>date('Y-m-d'),
            'status'=>2,
          );
        }
     
        $fData = array();
        foreach ($amount as $k => $amt) {
          if ($amt != 0) {
            $fData[] = array(
              'trans_id'=>$transId[$k],
              'identification_code'=>$id_code,
              'amount'=>$amt,
              'created_by'=>$this->authorization->getAvatarId()
            );
          }
        }
      } else{
        $querR = array();
        foreach ($query as $key => $val) {
          $querR[] = $val->book_access_id;
        }
        $arrayDif =array_diff($book_accessId,$querR);
        $insert = array();
        foreach ($arrayDif as $key => $val) {
          $insert[] = array(
            'lbr_access_id'=>$lbrcardId,
            'book_access_id'=>$val,
            'issue_date'=>date('Y-m-d'),
            'status'=>'1',
            'created_by'=>$this->authorization->getAvatarId()
          );
        }
      }
    }
    if (!empty($insert)) {
      $result = $this->db->insert_batch('library_transacation',$insert);
    }
    if (!empty($fData)) {
      $result = $this->db->insert_batch('library_fine_transaction',$fData);
    }
    if (!empty($update)) {
      $result = $this->db->update_batch('library_transacation',$update,'id');
    }
    return $result; 
  }
  public function return_transcation_table_upate($returnId){
      $data = array();
    foreach ($returnId as $key => $id) {
      $data[] = array(
        'id'=>$id,
        'return_date'=>date('Y-m-d'),
        'status'=>2,
      );
    }
   
   return $this->db->update_batch('library_transacation',$data,'id');
  }

  public function get_library_transcation_id($bId){

    $result = $this->db->select('id')
    ->from('library_transacation')
    ->where_in('book_access_id',$bId)
    ->get()->result();
    $trasId = array();
    foreach ($result as $key => $val) {
      $trasId[] = $val->id;
    }
    return $trasId;
  }

  // TODO : return UI change after finish 1st ver
  public function get_book_accessId_return_books($accessId){
    
  }



  public function get_librar_books_summary(){
    $lbId =  $this->session->userdata('libraries');
    // echo $lbId; die();
    $books_type = $this->settings->getSetting('books_type');
    $bookType = [];
    $booksName = [];
    foreach ($books_type as $key => $type) {
      $bookType[] = $type->value;
      $booksName[$type->value] = $type->name;
    }
    
    $tBooks = $this->db->select("book_type, sum(Case When (lbc.status='Available') Then 1 Else 0 End) as numberof_books, sum(Case When (lbc.status='Damage') Then 1 Else 0 End) as damage, round(sum(ifnull(lbc.costof_book,0)),2) as cost_of_the_book ")
    ->from('library_books lb')
    ->where('lb.libraries',$lbId)
    ->where_in('lb.book_type',$bookType)
    ->join('library_books_copies lbc','lb.id=lbc.book_id','left')
    ->group_by('book_type')
    ->get()->result();

    $tbook_issued = $this->db->select("book_type, sum(Case When (lt.status='1') Then 1 Else 0 End) as issued")
    ->from('library_books lb')
    ->where('lb.libraries',$lbId)
    ->where_in('lb.book_type',$bookType)
    ->join('library_books_copies lbc','lb.id=lbc.book_id')
    ->join('library_transacation lt','lbc.id=lt.book_access_id','left')
    ->group_by('book_type')
    ->get()->result();




    $trans_overdue_date = $this->db->select("lt.book_access_id, date_format(lt.issue_date,'%d-%m-%Y') as issue_date,lm.hold_period")
    ->from('library_transacation lt')
    ->where('lt.status','1')
    ->join('library_cards lc','lt.lbr_access_id=lc.id')
    ->join('library_master lm','lc.master_id=lm.id')
    ->get()->result();

    $overdue_booksId = array();
    foreach ($trans_overdue_date as $key => $val) {
      $issueDate = $val->issue_date;
      $cDate = date('Y-m-d');
      $dayCount = $val->hold_period;
      // $due_date[$val->transId] = date('Y-m-d',strtotime($issueDate) + (24*3600*$dayCount));
      $due_date = date('Y-m-d',strtotime($issueDate) + (24*3600*$dayCount));
      if ($cDate > $due_date) {          
        array_push($overdue_booksId, $val->book_access_id);
      } 
    }

    if (!empty($overdue_booksId)) {
      $overDue = $this->db->select('book_type,count(book_id) as overDue')
      ->from('library_transacation lt')
      ->join('library_books_copies lbc','lt.book_access_id=lbc.id','left')
      ->join('library_books lb','lbc.book_id=lb.id')
      ->where('lb.libraries',$lbId)
      ->where_in('lt.book_access_id',$overdue_booksId)
      ->where_in('lb.book_type',$bookType)
      ->where('lt.status','1')
      ->group_by('book_type')
      ->get()->result();
    } 
    $nBooks = [];
    if (!empty($tBooks)) {
    if (!empty($tbook_issued)) {
       foreach ($tBooks as $key => $val) {
       foreach ($tbook_issued as $key => $val1) {
        $nBooks['# of books in library'][$val->book_type] = $val->numberof_books -  $val1->issued;
        $nBooks['# of books damaged'][$val->book_type] = $val->damage;
        $temp[$val->book_type] = $val->numberof_books;
        $cost[$val->book_type] = $val->cost_of_the_book;
      }}}
    }else{
        $nBooks['# of books in library'][] = 0;
        $nBooks['# of books damaged'][] = 0;
        $temp[] = 0;
        $cost[] = 0;
    }

    if (!empty($tbook_issued)) {
      foreach ($tbook_issued as $key => $val) {
        $nBooks['# of books issued'][$val->book_type] = $val->issued;
      }
    } else {
      $nBooks['# of books issued'][] = '-';
    }

   
    if (!empty($overDue)) {
      foreach ($overDue as $key => $val) {
        $nBooks['# of books overdue'][$val->book_type] = $val->overDue;
      }
    } else {
      $nBooks['# of books overdue'][] = '-';
    }
      $nBooks['Total no of books'] = 0;
    if (!empty($temp)) {
      $nBooks['Total no of books'] = $temp;
    }
     $nBooks['Cost of the Total Books'] = 0;
    if (!empty($cost)) {
      $nBooks['Cost of the Total Books'] = $cost;
    }
    return array('Name'=>$booksName,'result'=>$nBooks);

  }

  public function get_class_section_all(){
    return $this->db->select("c.id as cId, cs.id as csId, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection")
    ->from('class c')
    ->where('c.acad_year_id',$this->yearId)
    ->join('class_section cs','c.id=cs.class_id')
    ->where('cs.is_placeholder!=1')
    ->get()->result();
  }

  public function get_class_section_studetn_data($cId, $csId){

    $prefix_student_name = $this->settings->getSetting('prefix_student_name');
    $prefix_order_by = $this->settings->getSetting('prefix_order_by');

    $order_by = 'sd.first_name';
    if ($prefix_order_by == "roll_number") {
      $order_by = 'sy.roll_no';
    }else if($prefix_order_by == "enrollment_number"){
      $order_by = 'sd.enrollment_number';
    }else if($prefix_order_by == "admission_number"){
      $order_by = 'sd.admission_number';
    }else if($prefix_order_by == "alpha_rollnum"){
      $order_by = 'sy.alpha_rollnum';
    }

		if ($prefix_student_name == "roll_number") {
      $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as sName";
    } else if ($prefix_student_name == "enrollment_number") {
      $std_name = "CONCAT(ifnull(sd.enrollment_number, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as sName";
    } else if ($prefix_student_name == "admission_number") {
      $std_name = "CONCAT(ifnull(sd.admission_no, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as sName";
    } else if ($prefix_student_name == "alpha_rollnum") {
        $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as sName";
    }else {
      $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as sName";
    }  

    return $this->db->select("sd.id as sId, $std_name, sd.identification_code,'student' as type ")

    ->from('student_admission sd')
    ->join('student_year sy','sd.id=sy.student_admission_id')
    ->where('sy.acad_year_id',$this->yearId)
    ->where('sy.class_id',$cId)
    ->where('sy.class_section_id',$csId)
    ->where('sd.admission_status',2)
    ->where('sy.promotion_status!=', '4')
    ->where('sy.promotion_status!=', '5')
    ->order_by($order_by)
    ->get()->result();
  }

  public function _get_staff_data_details($sId){
    # code...
  }
  public function _get_student_data_details($sId){
      return $this->db->select("s.id as sId, concat(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as sName, s.identification_code,'student' as type ")
      ->from('student_admission s')
      ->where('s.id',$sId)
      ->get()->row();
  }
  public function get_all_lbr_details_byId_Type($sId,$type){
    switch ($type) {
      case 'staff':
       $result = $this->_get_staff_data_details($sId);
        break;
       case 'student':
       $result = $this->_get_student_data_details($sId);
        break;   
    }
    
    $lbr_details =  $this->db->select('lc.id as lcId,stake_holder_type,master_id,card_access_code,member_type,num_books,book_type')
      ->from('library_cards lc')
      ->join('library_master lm','lc.master_id=lm.id')
      ->where('stake_holder_id',$sId)
      ->where('stake_holder_type',$type)
      ->where('status',1)
      ->get()->result();

    $books_type = $this->settings->getSetting('books_type');
    foreach ($lbr_details as $key => &$val) {
      $bType = json_decode($val->book_type);
      $bookType = '';
      foreach ($bType as $key => $b) {
        foreach ($books_type as $key => $type) {
          if ($type->value == $b) {
            if (!empty($bookType)) $bookType.=', ';
              $bookType .= $type->name;
          }
           $val->typeof_book_allowed = $bookType;
        }
      }
    } 
    $result->lbr = $lbr_details;
    return $result;
  }

  public function get_user_selection_libraryCard($sId,$type){
    return $this->db->select('lc.id as lcId,stake_holder_type,master_id,card_access_code,member_type,num_books,book_type')
    ->from('library_cards lc')
    ->join('library_master lm','lc.master_id=lm.id')
    ->where('stake_holder_id',$sId)
    ->where('stake_holder_type',$type)
    ->where('status',1)
    ->get()->result();
  }

  public function get_staff_all_library(){
    return $this->db->select("id as sId, concat(ifnull(first_name,''),' ',ifnull(last_name,'')) as sName, identification_code,'staff' as type")
    ->from('staff_master')
    ->where('status', 2)
    ->order_by('first_name','asc')
    ->get()->result();
  }

 
  public function fetch_lbrIdwise_data($type,$userId){
    $lbrId =  $this->session->userdata('libraries');

    $today = date('Y-m-d');
    $this->db->select("lc.id,lc.card_access_code,lm.member_type,num_books, concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as sName,  sm.identification_code");
    $this->db->from('library_cards lc');

    // if ($lbId != 'all') {
    //  $this->db->where('lc.id',$lbId);
    // }

    $this->db->join('library_master lm','lc.master_id=lm.id');

    if ($type == 'staff') {
      $this->db->join('staff_master sm','lc.stake_holder_id=sm.id and lc.stake_holder_type = "staff"');
      $this->db->where('sm.id',$userId);
    }elseif ($type == 'student') {
      $this->db->join('student_admission sm','lc.stake_holder_id=sm.id and lc.stake_holder_type = "student"');
      $this->db->where('sm.id',$userId);
    }
    $master = $this->db->get()->row();

    $this->db->select('sum(case when trans_id is NOT NULL then amount else 0 end ) + sum(case when trans_id is NULL then amount else 0 end ) as tobeCollected, sum(case when trans_id is NULL then amount else 0 end ) as collected');
    $this->db->from('library_fine_transaction');
    $this->db->where('identification_code',$master->identification_code);
    $fTrans = $this->db->get()->row();
    
    $this->db->select("lt.id as trnsId, lc.id,lc.card_access_code,lm.member_type,num_books, date_format(lt.issue_date,'%d-%m-%Y') as issue_date, date_format(date_add(lt.issue_date, INTERVAL lm.hold_period DAY), '%d-%m-%Y') as due_date, if(datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)) > 0 , datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)), 0) as dayDiff , if(datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)) > 0 and lt.status=1, datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)), 0) * lm.fine_per_day as fineAmount, lb.book_title, lb.author, lb.subject,  ifnull(date_format(lt.return_date,'%d-%m-%Y'), 'Not return') as returnDate, ifnull(lft.amount,'0.00') as returnAmount, lt.status as trnsStatus, lbc.access_code as book_id, ifnull(lb.book_course,'') as course ");
    $this->db->from('library_cards lc');
    // if ($lbId != 'all') {
    //   $this->db->where('lc.id',$lbId);
    // }

    if ($type == 'staff') {
      $this->db->join('staff_master sm','lc.stake_holder_id=sm.id and lc.stake_holder_type = "staff"','left');
      $this->db->where('sm.id',$userId);
    }elseif ($type == 'student') {
      $this->db->join('student_admission sm','lc.stake_holder_id=sm.id and lc.stake_holder_type = "student"','left');
      $this->db->where('sm.id',$userId);
    }

    $this->db->join('library_master lm','lc.master_id=lm.id');
    $this->db->join('library_transacation lt','lc.id=lt.lbr_access_id','left');
    $this->db->join('library_fine_transaction lft','lt.id=lft.trans_id','left');
    $this->db->join('library_books_copies lbc','lt.book_access_id=lbc.id');
    $this->db->join('library_books lb','lbc.book_id=lb.id');
    $this->db->where('lb.libraries',$lbrId);
    $tanscation_list = $this->db->get()->result();

    $master->trans_list = $tanscation_list;
    $master->ftAmount = $fTrans;
    return $master;
  }

  public function _damage_ui_books_list_std($idCode){

    $today = date('Y-m-d');
    $this->db->select("lt.id as trnsId, lc.id,lc.card_access_code,lm.member_type,num_books, date_format(lt.issue_date,'%d-%m-%Y') as issue_date, date_format(date_add(lt.issue_date, INTERVAL lm.hold_period DAY), '%d-%m-%Y') as due_date, if(datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)) > 0 , datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)), 0) as dayDiff , if(datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)) > 0 and lt.status=1, datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)), 0) * lm.fine_per_day as fineAmount, lb.book_title, lb.author, lb.subject,  ifnull(lt.return_date, 'Not return') as returnDate, lt.status as trnsStatus, concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as sName,  sm.identification_code,lbc.costof_book as bPrice");
    $this->db->from('student_admission sm');
    $this->db->where('sm.identification_code',$idCode);
    $this->db->join('library_cards lc','sm.id=lc.stake_holder_id');
    $this->db->where('lc.stake_holder_type','student');
    $this->db->join('library_master lm','lc.master_id=lm.id');
    $this->db->join('library_transacation lt','lc.id=lt.lbr_access_id');
    $this->db->where('lt.status','1');
    $this->db->join('library_books_copies lbc','lt.book_access_id=lbc.id');
    $this->db->join('library_books lb','lbc.book_id=lb.id');
    return $this->db->get()->result();
  }

  public function _damage_ui_books_list_staff($idCode){
    $today = date('Y-m-d');
    $this->db->select("lt.id as trnsId, lc.id,lc.card_access_code,lm.member_type,num_books, date_format(lt.issue_date,'%d-%m-%Y') as issue_date, date_format(date_add(lt.issue_date, INTERVAL lm.hold_period DAY), '%d-%m-%Y') as due_date, if(datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)) > 0 , datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)), 0) as dayDiff , if(datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)) > 0 and lt.status=1, datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)), 0) * lm.fine_per_day as fineAmount, lb.book_title, lb.author, lb.subject,  ifnull(lt.return_date, 'Not return') as returnDate,lt.status as trnsStatus, concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as sName,  sm.identification_code, lbc.costof_book as bPrice");
    $this->db->from('staff_master sm');
    $this->db->where('sm.identification_code',$idCode);
    $this->db->join('library_cards lc','sm.id=lc.stake_holder_id');
    $this->db->where('lc.stake_holder_type','staff');
    $this->db->join('library_master lm','lc.master_id=lm.id');
    $this->db->join('library_transacation lt','lc.id=lt.lbr_access_id');
    $this->db->where('lt.status','1');
    $this->db->join('library_books_copies lbc','lt.book_access_id=lbc.id');
    $this->db->join('library_books lb','lbc.book_id=lb.id');
    return $this->db->get()->result();
    
  }


  public function library_return_books_damage_collections($idCode){

    $sName = $this->settings->getSetting('school_short_name');
    $strReplce = str_replace(strtoupper($sName), "", $idCode);
    $cardName = '';
    if (!empty($strReplce)){
      $cardName = substr($strReplce, 0,2);
    }

    switch ($cardName) {
      case 'SD':
        $result = $this->_damage_ui_books_list_std($idCode);
        break;
      case 'ST':
        $result = $this->_damage_ui_books_list_staff($idCode);
        break;
    }
    if (empty($result)) {
     return array();
    }
    return $result;
  }

  public function update_issue_damage_book_details($returnId,$fAmount,$bPrice){

    if (!empty($this->input->post('cheque_number'))) {
        $cheque_dd_nb_cc_dd_number = $this->input->post('cheque_number');
      }elseif(!empty($this->input->post('dd_number'))){
        $cheque_dd_nb_cc_dd_number = $this->input->post('dd_number');
      }elseif(!empty($this->input->post('nb_rn'))){
        $cheque_dd_nb_cc_dd_number = $this->input->post('nb_rn');
      }elseif(!empty($this->input->post('cd_no'))){
        $cheque_dd_nb_cc_dd_number = $this->input->post('cd_no');
      }else{
        $cheque_dd_nb_cc_dd_number = null;
      }

    $this->db->trans_start();

    $data = array();
    foreach ($returnId as $key => $id) {
      $bccid_row =  $this->db_readonly->select('book_access_id')
    ->from('library_transacation')
    ->where('id',$id)
    ->get()->row();

    $bccid = $bccid_row->book_access_id;

    $data[] = array(
        'id'=>$bccid,
        'status'=>$this->input->post('damage'),
      );

    }

    $this->db->update_batch('library_books_copies',$data, 'id');

    $data = array();
    foreach ($returnId as $key => $id) {
      $data[] = array(
        'id'=>$id,
        'return_date'=>date('Y-m-d'),
        'status'=>2,
      );
    }
    $this->db->update_batch('library_transacation',$data,'id');

    $bData = array();
    foreach ($bPrice as $k => $amount) {
        $bData[] = array(
          'trans_id'=>$returnId[$k],
          'identification_code'=>$this->input->post('idCode'),
          'amount'=>$amount,
          'transaction_mode'=>$this->input->post('damage'),
          'created_by'=>$this->authorization->getAvatarId(),
          'remarks'=> $this->input->post('remarks'),
          'payment_type'=>$this->input->post('payment_type'),
          'bank_name'=>$this->input->post('bank_name'),
          'bank_branch'=>$this->input->post('branch_name'),
          'cheque_or_dd_date'=>date('Y-m-d',strtotime($this->input->post('bank_date'))),
          'cheque_dd_nb_cc_dd_number'=>$cheque_dd_nb_cc_dd_number

        );
    }    
    if (empty($bData)) {
      return 0;
    }

    $this->db->insert_batch('library_fine_transaction',$bData);

    // $pData = array(
    //   'identification_code'=> $this->input->post('idCode'),
    //   'amount'=>'-'.$this->input->post('t_amount'),
    //   'remarks'=> $this->input->post('remarks'),
    //   // 'transaction_mode'=>$this->input->post('damage'),
    //   'payment_type'=>$this->input->post('payment_type'),
    //   'created_by'=>$this->authorization->getAvatarId(),
    // );
    // $this->db->insert('library_fine_transaction',$pData);

    $this->db->trans_complete();
    $result =  $this->db->trans_status();
    if (!empty($result)) {
      $fData = array();
      foreach ($fAmount as $k => $amount) {
        if ($amount != 0) {
          $fData[] = array(
            'trans_id'=>$returnId[$k],
            'identification_code'=>$this->input->post('idCode'),
            'amount'=>$fAmount[$k],
            'created_by'=>$this->authorization->getAvatarId()
          );
        }
      }
      if (empty($fData)) {
        return 2;
      }
    return $this->db->insert_batch('library_fine_transaction',$fData);
    }else{
      return 0;
    }

  }

  public function get_type_of_accessId($accessId){
    return  $this->db->select('stake_holder_id,stake_holder_type')
    ->from('library_cards')
    ->where('id',$accessId)
    ->get()->row();
  }

  public function sent_sms_for_issue_books_staff($accessId,$book_accessId){
    $today = date('Y-m-d');
    $this->db->select("lt.id as trnsId, lc.id,lc.card_access_code,lm.member_type,num_books, date_format(lt.issue_date,'%d-%m-%Y') as issue_date, date_format(date_add(lt.issue_date, INTERVAL lm.hold_period DAY), '%d-%m-%Y') as due_date, if(datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)) > 0 , datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)), 0) as dayDiff , if(datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)) > 0 and lt.status=1, datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)), 0) * lm.fine_per_day as fineAmount, lb.book_title, lb.author, lb.subject,  ifnull(lt.return_date, 'Not return') as returnDate,lt.status as trnsStatus, lbc.costof_book as bPrice,sm.contact_number as staff_number, sm.id as staffId");
    $this->db->from('library_cards lc');
    $this->db->join('staff_master sm','lc.stake_holder_id=sm.id');
    $this->db->where('lc.stake_holder_type','staff');
    $this->db->join('library_master lm','lc.master_id=lm.id');
    $this->db->join('library_transacation lt','lc.id=lt.lbr_access_id');
    $this->db->where('lt.status','1');
    $this->db->where_in('lt.book_access_id',$book_accessId);
    $this->db->where('lt.lbr_access_id',$accessId);
    $this->db->join('library_books_copies lbc','lt.book_access_id=lbc.id');
    $this->db->join('library_books lb','lbc.book_id=lb.id');
    return $this->db->get()->result();
  }

  public function get_messageformSMSTable($category, $templateName){
    $result = $this->db->select('stay.content')
      ->from('sms_template_new stn')
      ->where('stn.category',$category)
      ->where('stn.name',$templateName)
      ->join('sms_templates_acad_year stay','stn.id=stay.sms_templates_id')
      ->get()->row();
      if (empty($result)) {
        return array();
      }
      return $result->content;
  }
  public function sent_sms_for_issue_books_student($accessId,$book_accessId){

    $today = date('Y-m-d');
    $this->db->select("lt.id as trnsId, lc.id,lc.card_access_code,lm.member_type,num_books, date_format(lt.issue_date,'%d-%m-%Y') as issue_date, date_format(date_add(lt.issue_date, INTERVAL lm.hold_period DAY), '%d-%m-%Y') as due_date, if(datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)) > 0 , datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)), 0) as dayDiff , if(datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)) > 0 and lt.status=1, datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)), 0) * lm.fine_per_day as fineAmount, lb.book_title, lb.author, lb.subject,  ifnull(lt.return_date, 'Not return') as returnDate,lt.status as trnsStatus, lbc.costof_book as bPrice,sf.mobile_no as father_number, sm.mobile_no as mother_number,s.id as stdId, concat(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as stdName");
    $this->db->from('library_cards lc');
    $this->db->join('student_admission s','lc.stake_holder_id=s.id');
    $this->db->where('lc.stake_holder_type','student');
    $this->db->join('student_relation sr','s.id=sr.std_id');
    $this->db->where('sr.relation_type','Father');
    $this->db->join('student_relation sr1','s.id=sr1.std_id');
    $this->db->where('sr1.relation_type','Mother');
    $this->db->join('parent sf','sr.relation_id=sf.id');
    $this->db->join('parent sm','sr1.relation_id=sm.id');
    $this->db->join('library_master lm','lc.master_id=lm.id');
    $this->db->join('library_transacation lt','lc.id=lt.lbr_access_id');
    $this->db->where('lt.status','1');
    $this->db->where_in('lt.book_access_id',$book_accessId);
    $this->db->where('lt.lbr_access_id',$accessId);
    $this->db->join('library_books_copies lbc','lt.book_access_id=lbc.id');
    $this->db->join('library_books lb','lbc.book_id=lb.id');
    return $this->db->get()->result();
  }

  public function sent_sms_for_return_books_staff($rId){
     $today = date('Y-m-d');
    $this->db->select("lt.id as trnsId, lc.id,lc.card_access_code,lm.member_type,num_books, date_format(lt.issue_date,'%d-%m-%Y') as issue_date, date_format(date_add(lt.issue_date, INTERVAL lm.hold_period DAY), '%d-%m-%Y') as due_date, if(datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)) > 0 , datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)), 0) as dayDiff , if(datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)) > 0 and lt.status=1, datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)), 0) * lm.fine_per_day as fineAmount, lb.book_title, lb.author, lb.subject,  ifnull(lt.return_date, 'Not return') as returnDate,lt.status as trnsStatus, lbc.costof_book as bPrice,sm.contact_number as staff_number, sm.id as staffId");
    $this->db->from('library_cards lc');
    $this->db->join('staff_master sm','lc.stake_holder_id=sm.id');
    $this->db->where('lc.stake_holder_type','staff');
    $this->db->join('library_master lm','lc.master_id=lm.id');
    $this->db->join('library_transacation lt','lc.id=lt.lbr_access_id');
    // $this->db->where('lt.status','1');
    $this->db->where_in('lt.id',$rId);
    $this->db->join('library_books_copies lbc','lt.book_access_id=lbc.id');
    $this->db->join('library_books lb','lbc.book_id=lb.id');
    return $this->db->get()->result();
  }

  public function sent_sms_for_return_books_student($rId){
        $today = date('Y-m-d');
    $this->db->select("lt.id as trnsId, lc.id,lc.card_access_code,lm.member_type,num_books, date_format(lt.issue_date,'%d-%m-%Y') as issue_date, date_format(date_add(lt.issue_date, INTERVAL lm.hold_period DAY), '%d-%m-%Y') as due_date, if(datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)) > 0 , datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)), 0) as dayDiff , if(datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)) > 0 and lt.status=1, datediff('$today',  date_add(lt.issue_date, INTERVAL lm.hold_period DAY)), 0) * lm.fine_per_day as fineAmount, lb.book_title, lb.author, lb.subject,  ifnull(lt.return_date, 'Not return') as returnDate,lt.status as trnsStatus, lbc.costof_book as bPrice,sf.mobile_no as father_number, sm.mobile_no as mother_number, s.id as stdId, concat(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as stdName");
    $this->db->from('library_cards lc');
    $this->db->join('student_admission s','lc.stake_holder_id=s.id');
    $this->db->where('lc.stake_holder_type','student');
    $this->db->join('student_relation sr','s.id=sr.std_id');
    $this->db->where('sr.relation_type','Father');
    $this->db->join('student_relation sr1','s.id=sr1.std_id');
    $this->db->where('sr1.relation_type','Mother');
    $this->db->join('parent sf','sr.relation_id=sf.id');
    $this->db->join('parent sm','sr1.relation_id=sm.id');
    $this->db->join('library_master lm','lc.master_id=lm.id');
    $this->db->join('library_transacation lt','lc.id=lt.lbr_access_id');
    // $this->db->where('lt.status','1');
    $this->db->where_in('lt.id',$rId);
    $this->db->join('library_books_copies lbc','lt.book_access_id=lbc.id');
    $this->db->join('library_books lb','lbc.book_id=lb.id');
    return $this->db->get()->result();

  }

  public function get_stock_check_data(){
    $this->db->select('lsc.*, a.friendly_name');
    $this->db->join('avatar a', 'a.id = lsc.created_by', 'left');
    $this->db->order_by('lsc.id', 'DESC');
    return $this->db->get('library_stock_check_master lsc')->result_array();
  }
  public function save_stock_check(){
    $data = array(
      'name' => $_POST['stock_check_name'],
      'start_date' => date("Y-m-d", strtotime($_POST['start_date'])),
      'created_by' => $_POST['created_by']
    );
    return $this->db->insert('library_stock_check_master', $data);
  }


  public function book_stock_update($access_code, $check_id, $status, $checked_by,$missing_remark){
    $data = array(
      'access_code' => $access_code,
      'check_id' => $check_id,
      'status' => $status,
      'checked_by' => $checked_by,
      'missing_remark'=>$missing_remark
    );

    if($status == 0){
      $data['status'] = "Discarded";
    }
    if($status == 1){
      $data['status']  = "Verified";
    }
    
    $this->db->select('COUNT(access_code) as c');
    $this->db->where('access_code', $access_code);
    $this->db->where('STATUS', 'Available');
    $result = $this->db->get('library_books_copies')->row_array()['c'];
    if($result == 1){
      if($status == 0){
        $data1 = array(
          'status' => "Discarded",
          'discarded_date' => date("Y-m-d"),
          'missing_remark' => $missing_remark
        );
        $this->db->where('access_code', $access_code);
        $this->db->update('library_books_copies', $data1);
      }
      return (int)$this->db->insert('library_stock_check_books', $data);
    }  
    else{
      $this->db->select('COUNT(access_code) as c');
      $this->db->where('access_code', $access_code);
      
      $result = $this->db->get('library_books_copies')->row_array()['c'];
      if($result == 0){
        return -2; //Invalid Access Code
      
      }
      return -1;  // Book already checked in
    }
  }  
  public function complete_checking($id){
    $this->db->set('end_date', date("Y-m-d"));
    $this->db->where('id', $id);
    return  $this->db->update('library_stock_check_master');
  }

  public function stock_check_report_ids($check_id){

    $result = $this->db->select('id')
    ->from('library_stock_check_books')
    ->where('check_id',$check_id)
    ->order_by('checked_on', 'desc')
    ->get()->result();
    $stockIds = [];
    foreach ($result as $key => $res) {
      array_push($stockIds, $res->id);
    }
    return $stockIds;

  }

  public function stock_report_data($stock_check_ids){
    $result = $this->db->select("id, access_code, check_id, checked_by, status, ifnull(missing_remark,'') as missing_remark, checked_on")
    ->from('library_stock_check_books')
    ->where_in('id',$stock_check_ids)
    ->order_by('checked_on', 'desc')
    ->get()->result();

    $accessCodArry = [];
    foreach ($result as $key => $val) {
      array_push($accessCodArry, $val->access_code);
    }

    $booktitle = $this->db->select('lbc.access_code, lb.book_title')
    ->from('library_books_copies lbc')
    ->join('library_books lb', 'lbc.book_id = lb.id')
    ->where_in('lbc.access_code',$accessCodArry)
    ->get()->result();
    $booktitleArry = [];
    foreach ($booktitle as $key => $val) {
      $booktitleArry[$val->access_code] = $val->book_title;
    }
    foreach ($result as $key => $val) {
      $val->book_title = '';
      if (array_key_exists($val->access_code, $booktitleArry)) {
        $val->book_title = $booktitleArry[$val->access_code];
      }
      $val->checked_by = $this->get_staff_name_from_avatar_id($val->checked_by);
      $val->checked_on = date('d-m-Y H:i a', strtotime($val->checked_on)); 
    }
    return $result;

  }

  public function stock_report($id){
    $result = $this->db->select('*')
    ->from('library_stock_check_books')
    ->where('check_id',$id)
    ->order_by('checked_on', 'desc')
    ->get()->result();

    $accessCodArry = [];
    foreach ($result as $key => $val) {
      array_push($accessCodArry, $val->access_code);
    }

    $booktitle = $this->db->select('lbc.access_code, lb.book_title')
    ->from('library_books_copies lbc')
    ->join('library_books lb', 'lbc.book_id = lb.id')
    ->where_in('lbc.access_code',$accessCodArry)
    ->get()->result();
    $booktitleArry = [];
    foreach ($booktitle as $key => $val) {
      $booktitleArry[$val->access_code] = $val->book_title;
    }
    foreach ($result as $key => $val) {
      $val->book_title = '';
      $val->checked_by = '';
      if (array_key_exists($val->access_code, $booktitleArry)) {
        $val->book_title = $booktitleArry[$val->access_code];
      }
      $val->checked_by = $this->get_staff_name_from_avatar_id($val->checked_by);
    }
    $result =json_decode(json_encode($result,true),true);
    return $result;

    // $this->db->select('lsc.*, lb.book_title, a.friendly_name as checked_by');
    // $this->db->join('library_books_copies lbc', 'lsc.access_code = lbc.access_code', 'LEFT');
    // $this->db->join('library_books lb', 'lbc.book_id = lb.id', 'LEFT');
    // $this->db->join('avatar a', 'a.id = lsc.checked_by', 'left');
    // $this->db->where('lsc.check_id', $id);
    // $this->db->order_by('checked_on', 'desc');
    // return $this->db->get('library_stock_check_books lsc')->result_array();
  }

  private function get_staff_name_from_avatar_id($avatarId) {
    $collected = $this->db_readonly->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staffName')
        ->from('staff_master sm')
        ->join('avatar a', 'sm.id=a.stakeholder_id')
        ->where('a.avatar_type', '4') // 4 avatar type staff        
        ->where('a.id',$avatarId)
        ->get()->row();
    if (!empty($collected)) {
      return $collected->staffName;
    }else{
      return 'Admin';
    }
}

  public function last_five_records_lsc($id){

    $result = $this->db->select('*')
    ->from('library_stock_check_books')
    ->where('check_id',$id)
    ->order_by('checked_on', 'desc')
    ->limit(5)
    ->get()->result();
    if(empty($result)){
      return 0;
    }
    $accessCodArry = [];
    foreach ($result as $key => $val) {
      array_push($accessCodArry, $val->access_code);
    }

    $booktitle = $this->db->select('lbc.access_code, lb.book_title')
    ->from('library_books_copies lbc')
    ->join('library_books lb', 'lbc.book_id = lb.id')
    ->where_in('lbc.access_code',$accessCodArry)
    ->get()->result();
    $booktitleArry = [];
    foreach ($booktitle as $key => $val) {
      $booktitleArry[$val->access_code] = $val->book_title;
    }
    foreach ($result as $key => $val) {
      $val->book_title = '';
      if (array_key_exists($val->access_code, $booktitleArry)) {
        $val->book_title = $booktitleArry[$val->access_code];
      }
    }
    $result =json_decode(json_encode($result,true),true);
    return $result;

    // $this->db->select('lsc.*, lb.book_title');
    // $this->db->join('library_books_copies lbc', 'lsc.access_code = lbc.access_code', 'LEFT');
    // $this->db->join('library_books lb', 'lbc.book_id = lb.id', 'LEFT');
    // $this->db->where('lsc.check_id', $id);
    // $this->db->order_by('checked_on', 'desc');
    // $this->db->limit(5);
    // return $this->db->get('library_stock_check_books lsc')->result_array();
  }


  public function getBookDetails($access_code, $check_id){
    $this->db->select('COUNT(access_code) as c');
    $this->db->where('access_code', $access_code);
    $this->db->where('check_id', $check_id);
    $count = $this->db->get('library_stock_check_books')->row_array()['c'];
    if($count)
      return -1;
    $this->db->select('COUNT(access_code) as count1');
    $this->db->where('access_code', $access_code);
    $count1 = $this->db->get('library_books_copies')->row_array()['count1'];
    if($count1 == 0)
      return $count1;

    $this->db->select('lb.book_title');
    $this->db->join('library_books lb', 'lbc.book_id = lb.id', 'LEFT' );
    $this->db->where('access_code', $access_code);
    return $this->db->get('library_books_copies lbc')->row_array()['book_title'];
  }


  public function getBookDetails1($access_code) {
    $this->db->select('lsc.*, lb.book_title');
    $this->db->join('library_books_copies lbc', 'lsc.access_code = lbc.access_code', 'LEFT');
    $this->db->join('library_books lb', 'lbc.book_id = lb.id', 'LEFT');
    $this->db->where('lsc.access_code', $access_code);
    return $this->db->get('library_stock_check_books lsc')->row();
  }
  
  public function stock_report_missing_books_data(){
    $result = $this->db->select("lbc.access_code, lbc.missing_remark, lbc.status, lb.book_title")
    ->from('library_books_copies lbc')
    ->join('library_books lb', 'lbc.book_id = lb.id')
    ->where('lbc.access_code not in (select access_code FROM library_stock_check_books)')
    ->group_by('lbc.book_id')
    ->get()->result();
    return $result;

  }

  public function missing_stock_report($id){
    $sql ="select library_books_copies.access_code,library_books_copies.missing_remark, library_books_copies.status, library_books.book_title 
    from library_books_copies 
    LEFT JOIN library_books ON library_books.id = library_books_copies.book_id 
    where library_books_copies.status = 'Available' AND library_books_copies.access_code 
    NOT IN (SELECT library_stock_check_books.access_code FROM library_stock_check_books) ;"; 
    return $this->db->query($sql)->result();
  }
  
  public function get_check_name($id){
    $this->db->select("name");
    $this->db->where("id", $id);
    return $this->db->get("library_stock_check_master")->row_array()['name'];
  }

  public function report_missing($check_id, $access_code,$missing_remark){
    $data = array(
      "access_code" => $access_code,
      'check_id' => $check_id, 
      'status' => 'Missing',
      'checked_by' =>  $this->authorization->getAvatarId(),
      'missing_remark' => $missing_remark
    );
    
    $this->db->trans_start();
    

    $this->db->where('access_code', $access_code);
    $this->db->insert('library_stock_check_books', $data);
    
    $this->db->set('discarded_date', date("Y-m-d"));
    $this->db->set('status', "Missing");

    $this->db->where('access_code', $access_code);
    $this->db->update('library_books_copies');
    return $this->db->trans_complete();
  }

  public function  no_of_unchecked_books($id){
    $sql ="select count(id) as missing from library_books_copies where status='Available' and access_code not in (select access_code from library_stock_check_books where check_id=$id)";
    return $this->db->query($sql)->row()->missing;
  }

  public function get_libraries_details(){
    return $this->db->get('libraries')->result_array();
  }

  public function add_libraries_name(){

    $data = array(
      "name" => $this->input->post('library_name')
    );
    return $this->db->insert('libraries', $data);
  }

  public function delete_libraries_name($id) {
    
    $check = $this->db_readonly->select("id, libraries")
        ->from("library_books")
        ->where("libraries", $id)
        ->get() 
        ->result();

 
    if (count($check) > 1) {
        return false; 
    } else {
        
        $this->db->where('id', $id);
        return $this->db->delete('libraries'); 
    }
}

  public function get_copies_map_entery($id){
    $libr_master = $this->db->select('book_title')
    ->from('library_books')
    ->where('id',$id)
    ->get()->row();

    $libr_copies = $this->db->select("*, date_format(date_of_accession,'%d-%m-%Y') as date_of_accession")
    ->from('library_books_copies')
    ->where('book_id',$id)
    ->get()->result();

    $libr_master->copies = $libr_copies;
    return $libr_master;
   } 

  public function update_access_number_to_lb_copies($access_code){

    $aData=[];
    foreach ($access_code as $id => $val) {
      $aData[] = array(
        'id'=>$id,
        'access_code'=>$val
      );
    }
    $this->db->update_batch('library_books_copies',$aData,'id');
    return  $this->db->affected_rows();
  }

  public function insert_last_running_number(){
    $this->db->where('cache_key','library_book_id_running_number');
    $this->db->update('cache',array('cache_value'=>$this->input->post('to')));
    return $this->db->affected_rows();
  }

  // public function get_last_number_to_generate($id){
  //   return $this->db->get_where('cache',array('cache_key'=>'lbr_qr'))->row();
  // }

  public function get_last_number(){
    return $this->db->where('cache_key','library_book_id_running_number')->get('cache')->row();
  }

  public function get_fine_daily_tx_data($from_date, $to_date){
    $this->db->select("date_format(lft.created_on,'%d-%m-%Y') as fdate, (case when trans_id is NULL then ABS(amount) else 0 end ) as amtPaid, lft.identification_code, remarks, payment_type")
    ->from('library_fine_transaction lft')
    ->where('lft.trans_id IS NULL');
    if ($from_date && $to_date) {
      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));
     $this->db->where('date_format(lft.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    }
    $result = $this->db->get()->result();
    $sName = $this->settings->getSetting('school_short_name');
    $data = [];
    foreach ($result as $key => $val) {
      $strReplce = str_replace(strtoupper($sName), "", $val->identification_code);
      $cardName = substr($strReplce, 0,2);
      switch ($cardName) {
        case 'SD':
          $result = $this->_get_studet_detils($val->identification_code);
          break;
        case 'ST':
          $result = $this->_get_staff_details($val->identification_code);
          break;
      }
      $finedata = new stdClass();
      $finedata->fDate = $val->fdate;
      $finedata->amount = $val->amtPaid;
      $finedata->remarks = $val->remarks;
      $finedata->payment_type = $val->payment_type;
      $finedata->identification_code = $val->identification_code;
      $finedata->name = $result->s_name .' (' .$result->name. ' )'.' '.'('.$result->clsSection.')';
      $data[] = $finedata;
    }
    return $data;
  }


  private function _get_studet_detils($idCode){
    return $this->db->select("'stduent' as name,  sd.id as stId, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as clsSection, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as s_name")
    ->from('student_year sy')
    ->join('student_admission sd','sy.student_admission_id=sd.id')
    ->join('class c','sy.class_id=c.id')
    ->join('class_section cs','sy.class_section_id=cs.id','left')
    ->where('sd.identification_code',$idCode)
    ->get()->row();
  }

  private function _get_staff_details($idCode){
    return $this->db->select("'staff' as name, ' ' as clsSection,  id as stId, concat(ifnull(first_name,''), ' ' ,ifnull(last_name,'')) as s_name")
    ->from('staff_master')
    ->where('identification_code',$idCode)
    ->get()->row();
  }

  private function _get_issued_books_all(){
    //Step 1: Get all the books that are issued. (lib trans, lib card, lib master)
    
    $result = $this->db->select('lm.fine_per_day, lm.hold_period, lt.issue_date,  lc.stake_holder_type, lc.stake_holder_id, lt.book_access_id')
      ->from('library_transacation lt')
      ->where('lt.status','1')
      ->join('library_cards lc','lt.lbr_access_id = lc.id')
      ->join('library_master lm','lc.master_id=lm.id')
      ->get()->result();
      return $result;
  }

  private function _get_return_date_reminder_details($issuedBooks){
    $data = new stdClass();
    $data->oneDayPriorReminderData = [];
    $data->lateReminderData = [];
    foreach ($issuedBooks as $key => $value) {
      $result = $this->_get_exceeded_days($value);

      if ($result->days_exceeded == -1){
        array_push($data->oneDayPriorReminderData, $result);
        continue;
      } 

      if ($result->days_exceeded > 0) {
        array_push($data->lateReminderData, $result);
        continue;
      }
    }
    return $data;
  }

  private function _get_exceeded_days($result){
    $issueDate = $result->issue_date;
    $cDate = date('Y-m-d');
    $dayCount = $result->hold_period;
    $due_date = date('Y-m-d',strtotime($issueDate) + (24*3600*$dayCount));
    $ts1 = strtotime($cDate);
    $ts2 = strtotime($due_date);
    $datediff = $ts1 - $ts2;
    $result->days_exceeded=($datediff / (60*60*24));
    return $result;
  }

  private function _get_student_books_details($reminderStdData){

    return $this->db->select('sa.first_name')
    ->from('student_admission sa')
    ->where('sa.id',$reminderStdData->stake_holder_id)
    ->get()->row();

  }

  private function _get_staff_books_details($reminderStdData){

    return $this->db->select('sm.first_name')
    ->from('staff_master sm')
    ->where('sm.id',$reminderStdData->stake_holder_id)
    ->get()->row();

  }
  private function _get_issued_book_details($book_access_id){
    return $this->db->select('lb.book_title')
    ->from('library_books_copies lbc')
    ->where('lbc.id',$book_access_id)
    ->join('library_books lb','lbc.book_id=lb.id')
    ->get()->row();
  }

  public function reminder_student_return_books_data(){
    $lbr_return_reminder_sms = $this->settings->getSetting('library_return_reminder_sms');
    
    if ($lbr_return_reminder_sms){
      //Step 1: Get all the books that are issued. (lib trans, lib card, lib master)
      $issuedBooks = $this->_get_issued_books_all();

      //Step 2: Figure out the books whose return date is tomorrow. (for loop)
      $reminderData = $this->_get_return_date_reminder_details($issuedBooks);

      //Step 3: For each book of Step 2, get student OR staff detail and book title. 
      $student_ids = [];
      $staff_ids = [];
      if (!empty($reminderData->lateReminderData)) {
        $returnMessage = $this->settings->getSetting('library_late_reminder_notification');
         foreach ($reminderData->lateReminderData as $key => $data) {
          $book = $this->_get_issued_book_details($data->book_access_id);

          $fine_amount = $data->days_exceeded * $data->fine_per_day;
          $message = $returnMessage;
          $message = str_replace('%%book_title%%', $book->book_title, $message);
          $message = str_replace('%%issue_date%%', date('d-m-Y',strtotime($data->issue_date)), $message);
          $message = str_replace('%%fine_amount%%',$fine_amount, $message);

          if ($data->stake_holder_type === 'student') {
            $returnData = $this->_get_student_books_details($data);
            $student_ids[$data->stake_holder_id] = $message;
          }

          if ($data->stake_holder_type === 'staff') {
            $returnData = $this->_get_staff_books_details($data);
            $staff_ids[$data->stake_holder_id] = $message;
          }
        }
      }
      
      if (!empty($reminderData->oneDayPriorReminderData)) {
        $returnMessage = $this->settings->getSetting('library_reminder_notification');
         foreach ($reminderData->oneDayPriorReminderData as $key => $data) {
          $book = $this->_get_issued_book_details($data->book_access_id);

          $fine_amount = $data->days_exceeded * $data->fine_per_day;
          $message = $returnMessage;
          $message = str_replace('%%book_title%%', $book->book_title, $message);
          $message = str_replace('%%issue_date%%', date('d-m-Y',strtotime($data->issue_date)), $message);
          $message = str_replace('%%fine_amount%%',$fine_amount, $message);

          if ($data->stake_holder_type === 'student') {
            $returnData = $this->_get_student_books_details($data);
            $student_ids[$data->stake_holder_id] = $message;
          }

          if ($data->stake_holder_type === 'staff') {
            $returnData = $this->_get_staff_books_details($data);
            $staff_ids[$data->stake_holder_id] = $message;
          }
        }
      }
      if(empty($student_ids) && empty($staff_ids)) {
        return 0;
      }
      //Step 4: Call SMS API.
      if(!empty($student_ids))
        $this->_send_sms_reminder_for_student($student_ids);
      if(!empty($staff_ids))
      $this->_send_sms_reminder_for_staff($staff_ids);
      return 1;
    }
  }
  private function _send_sms_reminder_for_student($student_ids){
    $input_arr = array();
    $this->load->helper('texting_helper');
    $input_arr['student_id_messages'] = $student_ids;
    $input_arr['source'] = 'Library return books reminder';
    $text_send_to = 'Both';
    $input_arr['mode'] = 'notification';
    $input_arr['send_to'] = $text_send_to;
    $success = sendUniqueText($input_arr);
    if($success['success'] != ''){
      $insId1 = 1;
    } else {
      $insId1 = 0;
    }
    return $insId1;
  }

  private function _send_sms_reminder_for_staff($staff_ids){
    $input_arr = array();
    $this->load->helper('texting_helper');
    $input_arr['staff_id_messages'] = $staff_ids;
    $input_arr['source'] = 'Library return books reminder';
    $text_send_to = 'Both';
    $input_arr['mode'] = 'notification';
    $input_arr['send_to'] = $text_send_to;
    $success = sendUniqueText($input_arr);
    if($success['success'] != ''){
      $insId1 = 1;
    } else {
      $insId1 = 0;
    }
    return $insId1;
  }

  public function GetStaffPhotos($staff_ids) {
    return $this->db_readonly->select("id, CONCAT(ifnull(first_name,''),' ',ifnull(last_name,'')) as staff_name, picture_url")->get('staff_master')->result();
  }

  public function GetStudentParentPhotos($student_ids, $profile_confirmed) {
    $sql1 = '';
    if ($profile_confirmed == 'true') {
        $sql1 = 'and sy.profile_confirmed = "Yes"';
      }  
    $std_ids = implode(",", $student_ids);
    $sql = "select sa.id as student_id, CONCAT(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name, CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parent_name, sy.high_quality_picture_url as student_photo, p.id as parent_id, p.high_quality_picture_url as parent_photo, sr.relation_type, CONCAT(cs.class_name,'',cs.section_name) as class_section 
            from student_admission sa 
            join student_year sy on sa.id=sy.student_admission_id 
            join class_section cs on cs.id=sy.class_section_id 
            join parent p on p.student_id=sa.id 
            join student_relation sr on sr.relation_id=p.id 
            where sa.id in ($std_ids) and sy.acad_year_id=$this->yearId ".$sql1." order by sa.first_name";
            
    $result = $this->db_readonly->query($sql)->result();
    $prefix = $this->filemanager->getFilePath('');
    $students = array();
    foreach ($result as $res) {
      if(!array_key_exists($res->student_id, $students)) {
        $students[$res->student_id] = array();
        $students[$res->student_id]['id'] = $res->student_id;
        $students[$res->student_id]['name'] = $res->student_name;
        $students[$res->student_id]['class_section'] = $res->class_section;
        $students[$res->student_id]['photo'] = ($res->student_photo)?($prefix.$res->student_photo):'';
        $students[$res->student_id]['parents'] = array();
      }
      $students[$res->student_id]['parents'][] = array(
        'id' => $res->parent_id,
        'name' => $res->parent_name,
        'photo' => ($res->parent_photo)?($prefix.$res->parent_photo):'',
        'relation' => $res->relation_type
      );
    }

    $data = array();
    foreach ($students as $std) {
      $data[] = $std;
    }
    return $data;
  }

   public function update_issuebook_detailsbyId($returnId, $return_date){
    $data = array(
      'return_date'=>date('Y-m-d',strtotime($return_date)),
      'status'=>2,
    );
    $this->db->where('id',$returnId);
    return $this->db->update('library_transacation',$data);

  }

  public function renewal_issuebook_details($returnId, $return_date, $issue_date){

    $this->db->trans_start();
    $rdata = array(
      'return_date'=>date('Y-m-d',strtotime($return_date)),
      'status'=>2,
    );
    $this->db->where('id',$returnId);
    $this->db->update('library_transacation',$rdata);

    $this->db->where('id',$returnId);
    $query = $this->db->get('library_transacation')->row();
    $issue_data = array(
      'lbr_access_id'=>$query->lbr_access_id,
      'book_access_id'=>$query->book_access_id,
      'issue_date'=>date('Y-m-d',strtotime($issue_date)),
      'status'=>'1',
      'created_by'=>$this->authorization->getAvatarId() // created by ,
    );
    $this->db->insert('library_transacation',$issue_data);
    return $this->db->trans_complete();

  }

  public function update_borrow_returnbook_details($returnId, $return_date){
    $data = array();
    foreach ($returnId as $key => $id) {
      $data[] = array(
        'id'=>$id,
        'return_date'=>date('Y-m-d',strtotime($return_date)),
        'status'=>2,
      );
    }
   
   return $this->db->update_batch('library_transacation',$data,'id');

  }

  public function get_student_qr_codes_list(){
    return $this->db->select('identification_code')
    ->from('student_admission sa')
    ->join('student_year sy','sy.student_admission_id=sa.id')
    ->where('sy.acad_year_id',$this->yearId)
    ->get()->result();
  }

  public function check_access_number_exit_in_db_value($value){
    $result = $this->db->where('access_code',$value)->get('library_books_copies');
    if ($result->num_rows() > 0) {
       return 1;
    }else {
      return 0;
    }
  }


  public function get_book_export_data($columns, $from_date, $to_date, $book_status) {
    // Always ensure required fields from 'library_books_copies' are included
    if (!in_array('access_code', $columns)) {
        $columns[] = 'access_code';
    }
    if (!in_array('volume', $columns)) {
        $columns[] = 'volume';
    }

    // Build selected columns with proper table aliases
    $selectedColumns = '';
    foreach ($columns as $field) {
        if ($field === 'volume') {
            $selectedColumns .= 'lbc.volume AS volume, ';
        } elseif ($field === 'access_code') {
            $selectedColumns .= 'lbc.access_code AS access_code, ';
        } elseif ($field === 'costof_book') {
          $selectedColumns .= 'lbc.costof_book AS costof_book, ';
      } else {
            $selectedColumns .= 'lb.' . $field . ', '; // default to library_books
        }
    }
    $selectedColumns = rtrim($selectedColumns, ', ');

    // Build the query
    $this->db->select($selectedColumns)
             ->from('library_books lb')
             ->join('library_books_copies lbc', 'lb.id = lbc.book_id', 'LEFT');

    // Apply date and status filters if valid
    if ($from_date !== '01-01-2001' && $to_date !== '12-31-9999') {
        $fromDate = date('Y-m-d', strtotime($from_date));
        $toDate = date('Y-m-d', strtotime($to_date));
        
        $this->db->where("lbc.date_of_accession BETWEEN '$fromDate' AND '$toDate'");
      }
      $this->db->where('lbc.status', $book_status);
     
      // Execute query
    $result = $this->db->get()->result();

    // Convert book_type value to readable label, only if it's selected
    if (in_array('book_type', $columns) && !empty($result)) {
        $books_type = $this->settings->getSetting('books_type');
        foreach ($result as $key => $book) {
            foreach ($books_type as $type) {
                if ($type->value == $book->book_type) {
                    $result[$key]->book_type = $type->name;
                    break;
                }
            }
        }
    }

    return $result;
}


  public function getVendorList(){
    $this->db_readonly->select('id, vendor_name');
    $this->db_readonly->from('procurement_vendor_master');
    return $this->db_readonly->get()->result(); 
  }

  public function get_library_borrow_detailsby_access_number($book_access_number)  {
    $books = $this->db_readonly->select('id')
    ->from('library_books_copies')
    ->where('access_code',$book_access_number)
    ->get()->row();
    if(!empty($books)){
      $transctions =  $this->db_readonly->select('lc.card_access_code')
      ->from('library_transacation lt')
      ->where('lt.status',1)
      ->where('lt.book_access_id',$books->id)
      ->join('library_cards lc','lt.lbr_access_id=lc.id')
      ->get()->row();
      if(!empty($transctions)){
        return $transctions->card_access_code;
      }else{
        return 0;
      }
    }else{
      return 0;
    }
  }

  public function get_libraryid_rfid_details($aId, $selectedLbId){  

    $id_code = $this->get_library_student_rfid_data($aId);
    if (!empty($id_code)) {
      $result = $this->get_library_card_data($id_code[0]->card_access_code);
    }

  // echo "<pre>"; print_r($result);die();

    $lbJson = [];
    $libriesArry = [];
    foreach ($id_code as $key => $val) {
      $lbJson = json_decode($val->libraries);
      foreach ($lbJson as $key => $val) {
        $libriesArry[$val] = $val;
      }
    }
    $found = 0;
    foreach ($libriesArry as $key => $lbId) {
      if ($lbId == $selectedLbId) {
        $found = 1;
        break;
      }
    }
    if($found){
      return array('lbr'=>$result,'multi_card'=> $id_code);
    }else{
      return 2;
    }
    
  }

  public function get_library_student_rfid_data($aId){    
    return $this->db->select("lc.card_access_code, lm.member_type, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as sName, libraries, sy.picture_url, sd.rfid_number")
    ->from('student_year sy')
    ->join('student_admission sd','sy.student_admission_id=sd.id')
    ->where('admission_status','2')
    ->where('sy.promotion_status!=', '4')
    ->where('sy.promotion_status!=', '5')
    ->where('acad_year_id',$this->yearId)
    ->where('sd.rfid_number',$aId)
    ->where('lc.stake_holder_type','student')
    ->join('library_cards lc','sd.id=lc.stake_holder_id')
    ->join('library_master lm','lc.master_id=lm.id')
    ->get()->result();
  }

  public function get_qrCodeDetails_add_edit(){
    $result = $this->db->select('access_code')->order_by('id', 'desc')->get('library_books_copies')->row();
    if ($result) {
        $accessCode = $result->access_code;
        return $accessCode;
    } else {
        return "No access code found"; 
    }
  }

  public function get_library_access_code_damage_lost($access_code){
    return $this->db->select("lc.card_access_code")
    ->from('library_transacation lt')
    ->join('library_cards lc','lt.lbr_access_id=lc.id')
    ->join('library_books_copies lbc','lbc.id=lt.book_access_id')
    ->where('lbc.access_code',$access_code)
    ->get()->row();
  }

  public function get_damage_lost_report($from_date, $to_date, $selectType){
    $fromDate = date('Y-m-d', strtotime($from_date));
    $toDate = date('Y-m-d', strtotime($to_date));

    $result = $this->db->select("lb.book_title, lbc.status, IFNULL(lft.remarks, '') as remarks, lft.amount, lft.transaction_mode, lc.stake_holder_type, lc.stake_holder_id, lbc.costof_book, lbc.access_code, lft.created_by as fine_created_by, date_format(lft.created_on, '%d-%m-%Y %h:%i %p') as created_on")
        ->from('library_books lb')
        ->join('library_books_copies lbc', 'lb.id=lbc.book_id', 'left')
        ->join('library_transacation lt', 'lbc.id=lt.book_access_id', 'left')
        ->join('library_fine_transaction lft', 'lt.id=lft.trans_id', 'left')
        ->join('library_cards lc', 'lt.lbr_access_id=lc.id', 'left')
        ->join('staff_master sm', 'sm.id=lft.created_by', 'left')
        ->where("date_format(lft.created_on, '%Y-%m-%d') BETWEEN '{$fromDate}' AND '{$toDate}'")
        ->where('lbc.status', $selectType)
        ->get()->result();

    foreach ($result as $key => $val) {
        switch ($val->stake_holder_type) {
            case 'student':
                $classSection = $this->_get_class_sections_student_name($val->stake_holder_id);
                $val->student_name_sections = $classSection ? $classSection->classSection : '';
                break;
            case 'staff':
                $teacherName = $this->_get_staff_name($val->stake_holder_id);
                $val->student_name_sections = $teacherName ? $teacherName->teacher_name : '';
                break;
        }
    }

    foreach ($result as $item) {
          $staffName = $this->get_staff_name_from_avatar_id_damage($item->fine_created_by);
          $item->fine_created_by = $staffName;
        }

    return $result;
  }


  public function _get_class_sections_student_name($source_id){
    $this->db->select("CONCAT(IFNULL(c.class_name,''), ' ' ,IFNULL(cs.section_name,'')) as classSection, CONCAT(IFNULL(sa.first_name,''), ' ' ,IFNULL(sa.last_name,'')) as student_name");
    $this->db->from('student_admission sa');
    $this->db->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId");
    $this->db->join('class_section cs', 'sy.class_section_id=cs.id');
    $this->db->join('class c', "sy.class_id=c.id and c.acad_year_id=$this->yearId");
    $this->db->where('sa.id', $source_id);
    $result = $this->db->get()->row();

    if ($result) {
        $result->classSection = $result->student_name . ' - ' . $result->classSection;
    }

    return $result;
  }

  public function _get_staff_name($source_id){
      $this->db->select("CONCAT(IFNULL(first_name,''), ' ' ,IFNULL(last_name,'')) as teacher_name");
      $this->db->from('staff_master');
      $this->db->where('id', $source_id);
      $result = $this->db->get()->row();
      return $result;
  }

  private function get_staff_name_from_avatar_id_damage($fine_created_by) {
    $collected = $this->db->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as created_staffName')
        ->from('staff_master sm')
        ->join('avatar a', 'sm.id=a.stakeholder_id')
        ->where('a.avatar_type', '4') // 4 avatar type staff        
        ->where('a.id',$fine_created_by)
        ->get()->row();
        if (!empty($collected)) {
          return $collected->created_staffName;
        }else{
          return 'Admin';
        }
    }
    
public function upload_excel_to_erp($insertedIds,$group_by,$db_table){
  $sql = "select *, COALESCE(COUNT(*), 0) as numbr_of_copies from ($db_table) where id in ($insertedIds) group by ($group_by)";
  $result = $this->db->query($sql)->row();
   $this->db->trans_start();  
    $data = array(
      'book_type'             =>  1, 
      'category'              => (!isset($result->category) || $result->category == '')? null : $result->category,  
      'language'              => (!isset($result->language) || $result->language == '')? null : $result->language, 
      'author'                => (!isset($result->author) || $result->author == '')? null : $result->author,
      'yearof_publishing'     => (!isset($result->yearof_publishing) || $result->yearof_publishing == '')? null : $result->yearof_publishing,
      'publisher_name'        => (!isset($result->publisher_name) || $result->publisher_name == '')? null : $result->publisher_name,
      'created_on'            => (!isset($result->created_on) || $result->created_on == '')? null : date("Y-m-d", strtotime($result->created_on)),
      'description'           => (!isset($result->description) || $result->description == '')? null : $result->description,
      'modified_on'           => (!isset($result->modified_on) || $result->modified_on == '')? null : date("Y-m-d", strtotime($result->modified_on)),
      'book_title'            => (!isset($result->book_title) || $result->book_title == '')? null : $result->book_title,
      'series'                => (!isset($result->series) || $result->series == '')? null : $result->series,
      'volume'                => (!isset($result->volume) || $result->volume == '')? null : $result->volume,
      'subject'               => (!isset($result->subject) || $result->subject == '')? null : $result->subject,
      'location_book'         => (!isset($result->location_book) || $result->location_book == '')? null : $result->location_book,
      'source'                => (!isset($result->source) || $result->source == '')? null : $result->source, 
      'contains'              => (!isset($result->contains) || $result->contains == '')? null : $result->contains,
      'edition'               => (!isset($result->edition) || $result->edition == '')? null : $result->edition, 
      'acc_no'                => (!isset($result->acc_no) || $result->acc_no == '')? null : $result->acc_no, 
      'isbn'                  => (!isset($result->isbn) || $result->isbn == '')? null : $result->isbn,
      'pages'                 => (!isset($result->pages) || $result->pages == '')? null : $result->pages,
      'call_number'           => (!isset($result->call_number) || $result->call_number == '')? null : $result->call_number,
      'supplier'              => (!isset($result->supplier) || $result->supplier == '')? null : $result->supplier,
      'bill_no_date'          => (!isset($result->bill_no_date) || $result->bill_no_date == '')? null : $result->bill_no_date,
      'soft_delete'           => (!isset($result->soft_delete) || $result->soft_delete == '')? null : $result->soft_delete,
      'libraries'             => (!isset($result->libraries) || $result->libraries == '')? null : $result->libraries,
      'b_author2'             => (!isset($result->b_author2) || $result->b_author2 == '')? null : $result->b_author2,
      'b_sub_title'           => (!isset($result->b_sub_title) || $result->b_sub_title == '')? null : $result->b_sub_title,
      'b_book_keyword'        => (!isset($result->b_book_keyword) || $result->b_book_keyword == '')? null : $result->b_book_keyword,
      'b_copies'              => (!isset($result->numbr_of_copies) || $result->numbr_of_copies == '')? null : $result->numbr_of_copies,
      'shelf_no_of'           => (!isset($result->shelf_no_of) || $result->shelf_no_of == '')? null : $result->shelf_no_of,
      'issn_no'               => (!isset($result->issn_no) || $result->issn_no == '')? null : $result->issn_no,
      'book_course'           => (!isset($result->book_course) || $result->book_course == '')? null : $result->book_course,
      'bill_no'               => (!isset($result->bill_no) || $result->bill_no == '')? null : $result->bill_no,
      'placeof_publishing'    => (!isset($result->placeof_publishing) || $result->placeof_publishing == '')? null : $result->placeof_publishing,
      'volume_number'         => (!isset($result->volume_number) || $result->volume_number == '')? null : $result->volume_number,
      'remarks'               => (!isset($result->remarks) || $result->remarks == '')? null : $result->remarks,
      'last_modified_by'      => $this->authorization->getAvatarId()
    );
    //$this->db->where('id',$id);
    $this->db->insert('library_books',$data);
    
    $u_data = array();
    for ($i=1; $i <= $result->numbr_of_copies ; $i++) {
      $u_data[] = array(
        'book_id'            => $result->id,
        'costof_book'        => (!isset($result->costof_book) || $result->costof_book == '')? null : $result->costof_book,
        'access_code'        => 1,
        'date_of_accession'  => (!isset($result->date_of_accession) || $result->date_of_accession == '')? null : date("Y-m-d", strtotime($result->date_of_accession)),
        'rack_name'          => (!isset($result->rack_name) || $result->rack_name == '')? null : $result->rack_name,
        'currency'           => (!isset($result->currency) || $result->currency == '')? null : $result->currency,
        'b_invoice_no'       => (!isset($result->b_invoice_no) || $result->b_invoice_no == '')? null : $result->b_invoice_no,
        'volume'             => (!isset($result->volume) || $result->volume == '')? null : $result->volume,
      );
    }

     $this->db->insert_batch('library_books_copies',$u_data,'book_id');
     $this->db->trans_complete(); 
     return ($this->db->trans_status() === FALSE)? FALSE:TRUE;

}

        public function insert_csv_library_data(){
          $input = $this->input->post();
          // unset($input['status']);
          // echo '<pre>'; print_r($input); die();          
          $listColumns =  $this->db->list_fields('library_books');
          $dataArry = array();
          foreach ($listColumns as $columnName) {
             if(isset($input[$columnName])){
               $dataArry[$columnName] = $input[$columnName];
             }
          }
          unset($dataArry['id']);
          $this->db->where('book_title',$input['book_title']);
          $query = $this->db->get("library_books")->row();

          if(!empty($query)){
              $this->db->where('book_title',$input['book_title']);
              $this->db->update('library_books', $dataArry);
              $insertId = $query->id;
          }else{
            $this->db->insert('library_books',$dataArry);
            $insertId = $this->db->insert_id();
          }

          $listColumnsCopies =  $this->db->list_fields('library_books_copies');

          $dataCopiesArry = array('book_id'=>$insertId);
          foreach ($listColumnsCopies as $column_name) {
             if(isset($input[$column_name])){
               $dataCopiesArry[$column_name] = $input[$column_name];
             }
          }
          unset($dataCopiesArry['id']);
          $this->db->insert('library_books_copies',$dataCopiesArry);
          return $insertId;
        }

        public function save_export_books_filters($data_arr){
          $filter_name= $data_arr['filterName'];
          unset($data_arr['filterName']);
          $insert_arr= array(
            'saved_report_name' => $filter_name,
            'acad_year_id' => $this->yearId,
            'filters_selected' => json_encode($data_arr),
            'master_report_name' => 'lib_export_report',
            'created_by' => $this->authorization->getAvatarStakeHolderId(),
            'created_on' => date('Y-m-d H:i:s')
        );
        return $this->db->insert('predefined_reports', $insert_arr);
        }
        public function get_saved_filters(){
          $data = $this->db->select('id,saved_report_name,filters_selected,master_report_name,created_by,created_on,acad_year_id')
          ->from('predefined_reports')
          ->where('master_report_name','lib_export_report')
          ->get()->result();
            return $data;

        }
        public function get_saved_filter_data($id){
          $data = $this->db->select('id,saved_report_name,filters_selected,master_report_name,created_by,created_on,acad_year_id')
          ->from('predefined_reports')
          ->where('id',$id)
          ->get()->result();
          return $data;

        }

       

       


        public function remove_mutiple_lib_cards($lib_card_id){
          $query = $this->db->select('lc.id,lt.lbr_access_id')
          ->from('library_cards lc')
          ->join('library_transacation lt','lt.lbr_access_id=lc.id','left')
          ->where('lc.id',$lib_card_id)
          ->get()->row();
         if ($query && is_null($query->lbr_access_id)) {
              $this->db->where('id', $lib_card_id)->delete('library_cards');
              return 1;
          }
            return 0;

          }

          public function get_access_number_from_receipt_book($receipt_book_id){
            $this->load->library('fee_library');
            $receipt_book = $this->db->select('*')->from('feev2_receipt_book')->where('id',$receipt_book_id)->get()->row();
            if (!empty($receipt_book)) {
                $receipt_number =  $this->fee_library->receipt_format_get_update($receipt_book);
            }else{
                $receipt_number =  0;
            }

            if(!empty($receipt_number)){
              $this->db->where('id',$receipt_book_id);
              $this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));
            }

            return $receipt_number;
          }

          public function getRfid_DetailsRange($userType, $fromDate, $toDate) {
            $fromDate='';
            $toDate='';
            $this->db->select("id, access_id,usertype,status,checked_time")
                     ->from("library_check_in_logs");
        
            if ($userType != '-1') {
                $this->db->where("usertype", $userType);
            }
        
            if ($fromDate && $toDate) {
                $this->db->where('DATE_FORMAT(checked_time, "%Y-%m-%d") >=', $fromDate);
                $this->db->where('DATE_FORMAT(checked_time, "%Y-%m-%d") <=', $toDate);
            }
        
            $getDetails = $this->db->get(); 
            $result = $getDetails->result(); 

            foreach ($result as &$entry) {
              $user_details = '';
      
              if ($entry->usertype === 'staff') {
                  $user_details = $this->db->select("
                                      sm.id as stafId,
                                      CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) as s_name,
                                      sm.identification_code as id_number")
                              ->from("staff_master sm")
                              ->where('sm.identification_code', $entry->access_id)
                              ->get()
                              ->row();
              } elseif ($entry->usertype === 'student') {
                  $user_details = $this->db->select("
                                      sd.id as studId,
                                      CONCAT(IFNULL(sd.first_name, ''), ' ', IFNULL(sd.last_name, '')) as s_name,
                                      sd.identification_code as id_number")
                              ->from('student_year sy')
                              ->join('student_admission sd', 'sy.student_admission_id = sd.id')
                              ->join('class_section cs', 'sy.class_section_id = cs.id', 'left')
                              ->join('class c', 'sy.class_id = c.id', 'left')
                              ->where('admission_status', '2')
                              ->where('sy.promotion_status !=', '4')
                              ->where('sy.promotion_status !=', '5')
                              ->where('sd.identification_code', $entry->access_id)
                              ->get()
                              ->row();
              }
      
              $entry->checked_time = date("d-M-Y h:i A", strtotime($entry->checked_time));
      
              if ($user_details) {
                  $entry->s_name = $user_details->s_name;
              } else {
                  $entry->s_name = "-";
              }
          }
            return $result; 
        }

        public function manualCheckout($id) {
          $current_time = date("Y-m-d H:i:s");
      
          $existingRecord = $this->db->select("access_id, usertype")
                                     ->from("library_check_in_logs")
                                     ->where("id", $id)
                                     ->get()
                                     ->row();
      
          if ($existingRecord) {
              $data = array(
                  'access_id'   => $existingRecord->access_id,
                  'usertype'    => $existingRecord->usertype,
                  'checked_time'=> $current_time,
                  'status'      => 'checkout'
              );
      
              return $this->db->insert('library_check_in_logs', $data);
          }
      
          return false; 
      }
    }
?>
