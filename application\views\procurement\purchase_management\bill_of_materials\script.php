<style>
    .summery-container {
        display: flex;
        justify-content: right;
    }

    .summery-content {
        /* position: fixed; */
        bottom: 0;
        width: 100%;
        height: 1rem;
        background: #e0d0d0;
        /* color: #eae7e7; */
        font-size: 14px;
        display: flex;
        justify-content: right;
        align-items: center;
        padding: 2.5rem;
        padding-left: 4rem;
    }

    .summery-content-first {
        /* position: fixed; */
        bottom: 0;
        width: 100%;
        height: 1rem;
        background: #e0d0d0;
        /* color: #eae7e7; */
        font-size: 14px;
        display: flex;
        justify-content: left;
        align-items: center;
        padding: 2.5rem;
        padding-left: 4rem;
    }


    /* new css */
    .modal-dialog {
        width: 80%;
        margin: auto;
    }

    .modal {
        overflow-y: auto;
    }

    .modal-header {
        position: relative;
    }

    .close {
        font-size: 34px;
        color: red;
        position: absolute;
        right: 10px;
    }

    tr:hover {
        background: #F1EFEF;
    }

    .row_background_color {
        background: #7f848780;
    }

    .dt-buttons {
        font-size: 14px;
        background: "red";
    }

    td>a>i {
        text-decoration: none;
        font-size: 16px;
        color: #191818;
        padding: 2px 5px;
    }

    .dataTables_wrapper .dt-buttons {
        float: right;
    }

    .dataTables_filter input {
        background-color: #f2f2f2;
        border: 1px solid #ccc;
        border-radius: 4px;
        margin-right: 5px;
    }

    .dataTables_wrapper .dataTables_filter {
        float: right;
        text-align: left;
        width: unset;
    }

    .dataTables_filter {
        position: absolute;
        right: 20%;
    }

    .dt-buttons {
        position: absolute;
        right: 15px;
    }

    .centerBtn {
        position: absolute;
        overflow: none;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }

    .inputItemsAdd {
        /* css for add items */
        height: 250px;
        border-top: 4px solid #c3b4b4;
        overflow: auto;
        /* padding: 15px 11px */
    }

    /* data table filters */
    /* .dataTables_filter{
        position: relative !important;
    } */

    /* #bomDetails_filter {
        position: absolute;
        right: 3%;
    } */

    .swal2-popup {
        width: 45%;
        margin: auto;
    }

    @media only screen and (min-width:1404px) {
        .dataTables_filter {
            position: absolute;
            right: 15%;
        }
    }

    @media only screen and (min-width:1734px) {
        .dataTables_filter {
            position: absolute;
            right: 11%;
        }
    }

    #billOfMaterialsTable {
        overflow: hidden;
    }

    .sidebar {
        position: absolute;
        height: 61%
    }

    /* .modal-dialog {
        width: 100%;
    } */

    iframe {
        width: 100%;
        height: 100%;
    }

    .fa-trash-o:hover {
        /* z-index: 100; */
        color: #F1EFEF !important;
    }

    /* .top-nav-bar {
        position: absolute;
        top: 0;
        right: 1rem;
    } */
</style>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
    integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
    crossorigin="anonymous"></script>

<script src="https://cdn.jsdelivr.net/npm/parsleyjs"></script>

<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script type="text/javascript">
    var totalIndentItems = 0;
    var indentId = <?php echo json_encode($indentId); ?>;

    window.localStorage.setItem("indentId", indentId);
    // inseting data into bom-detals
    showIndentItems(indentId);

    const loggedInStafId = "<?php echo $this->authorization->getAvatarStakeHolderId(); ?>";
    const isAdmin = "<?php echo $this->authorization->isAuthorized('INDENT.ADMIN'); ?>";

    const canSelectQuotation = "<?php echo json_encode($canSelectQuotation); ?>";

    var totalApproxAmount = 0;
    var totalItems = 0;
    var budgetCategoryId = 0;

    var indentItemsKart = [];

    var originalIndentPrice = 0;

    var expenseSubCategoryId = 0;

    var totalIndentPrice = 0;

    if (isAdmin == 1) {
        $("#createBomBtntn").show();
    } else {
        $("#createBomBtntn").hide();
    }

    // triggring current tab
    $(document).ready(function () {
        const params = new URLSearchParams(window.location.search);
        const activeTab = params.get('tab');

        if (activeTab) {
            const btn = document.querySelector(`button[data-tab="${activeTab}"]`);
            if (btn) {
                btn.click();
            }

            if (activeTab == "indent-details") {
                $(".summery-content").show();
            } else {
                $(".summery-content").hide();
            }
        } else {
            const firstBtn = document.querySelector(".content button");
            if (firstBtn) {
                firstBtn.click();
            }
        }
    });

    // add event lister on nav-tabs to listen on every tab when clicked
    $(".content").on("click", function (e) {
        let btn = e.target.closest("button");

        if (btn) {
            e.preventDefault();

            let tabName = btn.dataset.tab;

            if (tabName) {
                const newUrl = `${window.location.pathname}?tab=${tabName}`;
                window.history.pushState({ path: newUrl }, '', newUrl);

                if (tabName == "indent-details") {
                    $(".summery-content").show();
                } else {
                    $(".summery-content").hide();
                }
            }
        }
    });

    const generateMessage = function (msg) {
        let msgTemplate = `
            <div style="color:red;text-align:center;
                color: #000;
                border: 2px solid #fffafa;
                text-align: center;
                border-radius: 6px;
                position: relative;
                margin-left: 14px;
                padding: 10px;
                font-size: 14px;
                margin-top: 15px;
                background: #ebf3ff;">
                    ${msg}
                </div>`;

        return msgTemplate;
    }

    const status = {
        0: "Pending",
        1: "Approved",
        2: "Rejected",
        3: "Request sent for modification",
        4: "Sent request for Indent approval",
        5: "Sent request for quotation approval",
        6: "Approved by Indent approvers",
        7: "Request sent for modification from quotation approver",
    };

    const statusIcon = {
        0: "glyphicon-time",
        1: "thumbs-up",
        2: "thumbs-down",
        3: "repeat",
        4: "glyphicon-user",
        5: "glyphicon-user",
        6: "thumbs-up",
        7: "repeat",
    }

    function callupdateItemQty(itemId, indentId, newQTY) {
        // update in the item qty in db
        $.ajax({
            url: "<?php echo site_url('procurement/Requisition_controller_v2/update_indent_item_qty') ?>",
            type: "POST",
            data: { "itemId": itemId, "newQTY": newQTY },
            success: function (res) {
                if (+res) {
                    updateUI(indentId);

                    Swal.fire({
                        title: "Updated!",
                        text: "Successfully saved the changes.",
                        icon: "success"
                    });
                } else {
                    Swal.fire({
                        title: "Error!",
                        text: "Something went wrong.",
                        icon: "error"
                    });
                }
            }
        });
    }

    function updateItemQty(itemId, indentId, currentQTY) {
        try {
            const newQTY = document.querySelector(`#newItemQTY_${itemId}`).value;

            const currentClickedItem = document.querySelector(`#item_id_${itemId}`);
            const { itemName } = currentClickedItem.dataset;

            if (+newQTY) {
                const html = `<div data-item-id="${itemId}" data-item-name="${itemName}" data-indent-id="${indentId}" id="item_id_${itemId}" name="item_id_${itemId}" onclick="edit_item_qty('${itemId}','${indentId}','${+newQTY}')">${new Intl.NumberFormat(window.navigator.language).format(+newQTY)}</div>`;

                currentClickedItem.innerHTML = html;

                callupdateItemQty(itemId, indentId, newQTY);
            } else {
                Swal.fire({
                    title: "Are you sure?",
                    html: `You want to delete the item <span style="text-decoration: underline;font-size: large;font-weight: 700;">${itemName}</span> ?`,
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Yes, delete it!"
                }).then((result) => {
                    if (result.isConfirmed) {
                        callupdateItemQty(itemId, indentId, newQTY);
                        currentClickedItem.closest("tr").remove();
                    } else {
                        const html = `<div data-item-id="${itemId}" data-item-name="${itemName}" data-indent-id="${indentId}" id="item_id_${itemId}" name="item_id_${itemId}" onclick="edit_item_qty('${itemId}','${indentId}','${+currentQTY}')">${new Intl.NumberFormat(window.navigator.language).format(+currentQTY)}</div>`;

                        currentClickedItem.innerHTML = html;
                    }
                });
            }

            updateUI(indentId);
        } catch (err) {

        }
    }

    function updateUI(indentId) {
        showIndentApprovalTable(indentId);
        showQuotationApprovalTable(indentId);
        showIndentItems(indentId);
        updateBadgeStatus(indentId);
    }

    let editItemButtonInserted = false;
    function edit_item_qty(itemId, indentId, currentQTY) {
        // 1. select clicked item QTY td box
        const currentClickedItem = document.querySelector(`#item_id_${itemId}`);
        currentClickedItem.removeAttribute("onclick");

        // 2. clear the childs from the current item qty td
        currentClickedItem.innerHTML = "";
        // 3. insert an number inbox with the current qty and Update button
        let html = `
            <input class="form-control" type="text" maxlength="3" autocomplete="off" name="newItemQTY_${itemId}" id="newItemQTY_${itemId}" value="${currentQTY}" step="1" min="0" style="font-size: 13px;line-height: 19px;background-color: #fff;height: 31px;padding: 3px 7px;height: 23px;width: 61px;display:inline-block;margin-right:8%;">
        `;

        html += `
            <span style="text-decoration: underline;cursor:pointer;" onclick="updateItemQty('${itemId}','${indentId}','${currentQTY}')">Update</span>
        `;
        currentClickedItem.innerHTML = html;

        // 4. when clicked on update -> then all the childs should be clear and updated QTY should show up in the current item QTY td
    }


    // <button id="deleteButton" data-indent-id="${BOMItems[0]['indentId']}" style="display:${+BOMItems[0]['status'] !== 0 && +BOMItems[0]['status'] !== 3 && "none" || ""}" onclick="deleteItem('${BOMItems[0]['indentId']}')" type="button" class="btn btn-default"><span class="glyphicon glyphicon-trash" aria-hidden="true"></span> Delete</button>

    //     <button id="statusButton" data-indent-id="${BOMItems[0]['indentId']}" type="button" class="btn btn-default" style="position: absolute;right: 1rem;top: 0rem;">
    //     <span class="badge text-uppercase" style="font-size: 1.2rem; padding: 0.5rem 1rem; background-color: #6f42c1; color: white;">
    //         <span class="glyphicon glyphicon-${statusIcon[BOMItems[0]['status']]}" aria-hidden="true"></span> ${status[+BOMItems[0]['status']]}
    //     </span>
    // </button>

    const generateIndentDetailsTable = function (BOMItems, active_quotation) {
        if (!BOMItems?.length) {
            return generateMessage("Data not found");
        }

        let table = `
        <div class="container-fluid top-nav-bar" style="padding-top: 12px; padding-bottom: 1rem; display: ${isAdmin == 1 ? "block" : "none"};">
            <div class="cts-toolbar btn-toolbar" role="toolbar">
                <button id="active_quotation" 
                        style="display: ${active_quotation ? "block" : "none"};" 
                        data-indent-id="${BOMItems[0]['indentId']}" 
                        type="button" 
                        class="btn btn-default">
                    ${active_quotation ? `Selected Vendor : ${active_quotation}` : ""}
                </button>

                <button style="display: ${(+BOMItems[0]['status'] !== 0 && +BOMItems[0]['status'] !== 3) ? "none" : ""};"
                        data-toggle="modal" 
                        data-target="#add_items_modal" 
                        data-indent-id="${BOMItems[0]['indentId']}" 
                        data-subcategory-id="${BOMItems[0]['subcategory_id']}" 
                        data-category-id="${BOMItems[0]['categoryId']}" 
                        type="button" 
                        class="btn btn-default">
                    <span class="glyphicon glyphicon-add" aria-hidden="true"></span> Add Items
                </button>

                <button id="sendForApprovalBtn"
                        data-indent-id="${BOMItems[0]['indentId']}" 
                        style="display: ${(+BOMItems[0]['status'] !== 0 && +BOMItems[0]['status'] !== 3) ? "none" : ""};" 
                        onclick="sendForIndentApproval('${BOMItems[0]['indentId']}')" 
                        type="button" 
                        class="btn btn-default">
                    <span class="glyphicon glyphicon-shopping-cart" aria-hidden="true"></span> Send For Indent Approval
                </button>
                
                <button id="generatePDFBtn"
                        data-indent-id="${BOMItems[0]['indentId']}" 
                        style="display: ${[6, 7].includes(+BOMItems[0]['status']) ? "" : "none"};" 
                        onclick="generatePDF('${BOMItems[0]['indentId']}')" 
                        type="button" 
                        class="btn btn-default">
                    <span class="glyphicon glyphicon-print" aria-hidden="true"></span> Generate PDF for quotation
                </button>

                <button id="uploadPDFBtn"
                        data-toggle="modal" 
                        data-target="#upload_pdf_modal" 
                        data-indent-id="${BOMItems[0]['indentId']}" 
                        style="display: ${[6, 7].includes(+BOMItems[0]['status']) ? "" : "none"};" 
                        onclick="showUploadPDFModal('${BOMItems[0]['indentId']}')" 
                        type="button" 
                        class="btn btn-default">
                    <span class="glyphicon glyphicon-upload" aria-hidden="true"></span> Upload RFQ
                </button>`;

        if (active_quotation) {
            table += `
                <button id="sendQuotationApprovalBtn"
                        data-indent-id="${BOMItems[0]['indentId']}" 
                        style="display: ${[6, 7].includes(+BOMItems[0]['status']) ? "" : "none"};" 
                        onclick="sendForQuotationApproval('${BOMItems[0]['indentId']}')" 
                        type="button" 
                        class="btn btn-default">
                    <span class="glyphicon glyphicon-shopping-cart" aria-hidden="true"></span> Send Request For Quotation Approval
                </button>`;
        }

        table += `
            </div>
        </div>

        <table name="BOMItems" id="bomDetails" class="table table-bordered">
            <thead>
                <tr>
                    <th>#</th>
                    <th>ITEM NAME</th>
                    <th>CATEGORY</th>
                    <th>UNITS</th>
                    <th>QTY</th>
                    <th>APPROX UNIT PRICE</th>
                    <th>TOTAL AMOUNT</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>`;

        BOMItems.forEach((item, index) => {
            const quantity = Number(item.quantity);
            const totalPrice = quantity * Number(item.unit_price);

            totalItems += quantity;
            totalApproxAmount += totalPrice;

            table += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${item.item_name}</td>
                    <td>${item.category_name}</td>
                    <td>${item.unit_of_measurement || "-"}</td>
                    <td>
                        <div data-item-name="${item.item_name}" 
                            data-item-id="${item.id}" 
                            data-indent-id="${item.indentId}" 
                            id="item_id_${item.id}" 
                            name="item_id_${item.id}"
                            ${(+BOMItems[0]['status'] === 0 || +BOMItems[0]['status'] === 3) ? `onclick="edit_item_qty(${item.id}, ${item.indentId}, ${item.quantity})"` : ""}>
                            ${new Intl.NumberFormat(window.navigator.language).format(quantity)}
                            ${(+BOMItems[0]['status'] === 0 || +BOMItems[0]['status'] === 3) ? `<i class="glyphicon glyphicon-pencil" style="font-size: 12px; margin-left: 4px;"></i>` : ""}
                        </div>
                    </td>
                    <td>${formatCurrency(item.unit_price, "INR")}</td>
                    <td>${formatCurrency(totalPrice, "INR")}</td>
                    <td>`;

            if (+BOMItems[0]['status'] === 0 || +BOMItems[0]['status'] === 3) {
                table += `
                        <button class="btn btn-danger" onclick="removeItem('${item.id}', '${item.item_name}', '${item.indentId}')">
                            Remove
                        </button>`;
            } else {
                table += `-`;
            }

            table += `
                    </td>
                </tr>`;
        });

        table += `
            </tbody>
        </table>`;

        return table;
    };


    function generateQuotationApprovalTable(bomItems) {
        if (bomItems?.length) {
            let table = `<table name="BOMItems" id="financeApproval" class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>ITEM NUMBER</th>
                                        <th>ITEM NAME</th>
                                        <th>CATEGORY</th>
                                        <th>QTY</th>
                                        <th>UNITS</th>
                                    </tr>
                                        </thead>
                                        <tbody>
                                        `;

            bomItems.forEach((b, i) => {
                table += `
                                        <tr>
                                        <td>${++i}</td>
                                        <td>${b.item_name}</td>
                                        <td>${b.category_name}</td>
                                        <td>${new Intl.NumberFormat(window.navigator.language).format(+b.quantity)}</td>
                                        <td>${b.unit_of_measurement}</td>
                        </tr>
                        `;
            })

            table += `</tbody>
                </table>`;
            return table;
        } else {
            return generateMessage("No remaining approvals!");
        }
    }

    function formatCurrency(amount, currency = "INR") {
        return Intl.NumberFormat("en-In", {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 2
        }).format(amount)
    }

    function formatNumber(number) {
        return new Intl.NumberFormat('en-IN', { maximumSignificantDigits: 3 }).format(
            number,
        )
    }

    function summery(referenceContainer, totalItems, totalAmount) {
        $(`.${referenceContainer} .summery-container`).remove();

        if (totalAmount > 0) {
            return $(`.${referenceContainer}`).append(`<div style="" class="summery-container">
            <div class="summery-content" style="display:none;">
                <div style="margin-right: 3rem;">${formatNumber(totalItems)} Items</div>
                <div>Total Amount ${formatCurrency(totalAmount)}</div>
            </div>
        </div>`);
        }
    }

    async function showingIndentItems(indentId) {
        try {
            let BOMItems;
            await $.ajax({
                url: "<?php echo site_url('procurement/Requisition_controller_v2/get_indent_items') ?>",
                type: "POST",
                data: { "indents_master_id": indentId },
                success: function (data) {
                    BOMItems = JSON.parse(data);
                    indentItemsKart = BOMItems?.bill_of_material_items;
                    const [firstItem] = BOMItems?.bill_of_material_items || [];
                    expenseSubCategoryId = firstItem?.expense_sub_category_id ?? null;
                }
            })
            return BOMItems;
        } catch (err) {
            console.log(err);
            throw err;
        }
    }

    function addDataTable(tableId, downloadReportName) {
        const reportName = `${downloadReportName}_report_${new Date().toLocaleString('default', { month: 'short' }) + " " + new Date().getDate() + " " + new Date().getFullYear()}_${new Date().getHours() + "" + new Date().getMinutes()}`;


        const table = $(`#${tableId}`).DataTable({
            "language": {
                "search": "",
                "searchPlaceholder": "Enter Search..."
            },
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "pageLength": 10,
            dom: 'lBfrtip',
            buttons: [
                {
                    extend: 'excelHtml5',
                    text: 'Excel',
                    filename: reportName,
                    className: 'btn btn-info'
                },
                {
                    extend: 'csvHtml5',
                    text: 'CSV',
                    filename: reportName,
                    className: 'btn btn-info'
                },
                {
                    extend: 'pdfHtml5',
                    text: 'PDF',
                    filename: reportName,
                    className: 'btn btn-info'
                }
            ]
        });
    }

    async function showIndentItems(indentId, bomName) {
        totalApproxAmount = 0;
        totalItems = 0;
        // To Do : show bom added items
        // 1. get bom master id

        // 2. bring bom master data based on id and show inmodal box in table format
        try {
            const BOMItems = await showingIndentItems(indentId);

            totalIndentItems = BOMItems.bill_of_material_items.length;

            const bomDetailsTable = generateIndentDetailsTable(BOMItems.bill_of_material_items, BOMItems.active_quotation);

            $("#show-bom-details").html(bomDetailsTable);

            totalIndentPrice = totalApproxAmount;

            summery("card", totalItems, totalApproxAmount);

            // look datatable
            // addDataTable("bomDetails", "bill_of_material_items");
        } catch (err) {
            console.log(err.message);
            return Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Something went wrong!",
            });
        }
    }

    function deleteIndent(indentId, bomName) {
        Swal.fire({
            title: "Are you sure?",
            text: `You want to delete the ${bomName}?`,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes, delete it!"
        }).then((result) => {
            if (result.isConfirmed) {
                // delete query goes here

                Swal.fire({
                    title: "Deleted!",
                    text: "Your Indent has been deleted.",
                    icon: "success"
                });
            }
        });
    }

    async function approveRequestedIndent(indentId, approvalType, approverMasterId, isItLastDocument, approvalRemarks) {
        let response;
        try {
            await $.ajax({
                url: "<?php echo site_url('procurement/Requisition_controller_v2/approve_indent'); ?>",
                type: "POST",
                data: { "indentId": indentId, "approvalType": approvalType, "approverMasterId": approverMasterId, "isItLastDocument": isItLastDocument, "approvalRemarks": approvalRemarks, "budgetCategoryId": budgetCategoryId, "totalApproxAmount": totalApproxAmount },
                success: function (data) {
                    approvers = data;
                }
            });
            return approvers;
        } catch (er) {

            throw err;
        }
    }

    async function getBudgetCategories() {
        let categories = [];
        try {
            await $.ajax({
                url: "<?php echo site_url('procurement/Requisition_controller_v2/getBudgetCategories'); ?>",
                type: "POST",
                data: {},
                success: function (res) {
                    categories = JSON.parse(res);
                }
            })
            return categories;
        } catch (err) {
            throw new Error(err.message);
        }
    }

    async function getBudgetDetails() {
        // const indentMasterId = window.localStorage.getItem("indentId");

        try {
            const res = await $.ajax({
                url: "<?php echo site_url('procurement/Requisition_controller_v2/getBudgetDetailsForIndentInFirstLevelFinancialApproval'); ?>",
                type: "POST",
                data: { expenseSubCategoryId }
            });

            const budgetDetails = JSON.parse(res);

            if (!Object.entries(budgetDetails).length) {
                return {
                    html: `<div style="margin-top:10px; color:red;"><b>Note: Budget Details not available. Please proceed only if justified.</b></div>`,
                    isBudgetSufficient: false
                };
            }

            let isBudgetSufficient = false;
            const total = budgetDetails.total_amount;
            const available = budgetDetails.total_available_amount;
            if (total !== null && total !== undefined && available !== null && available !== undefined && !isNaN(total) && !isNaN(available)) {
                isBudgetSufficient = Number(total) <= Number(available);
            }

            const html = `
            <div style="overflow-x:auto;">
                <table style="width:100%; border-collapse: collapse; text-align: left; font-size: 14px;">
                    <thead>
                        <tr style="background-color: #f2f2f2;">
                            <th style="padding: 8px; border: 1px solid #ddd;">Expense Category Name</th>
                            <th style="padding: 8px; border: 1px solid #ddd;">Expense Sub-Category Name</th>
                            <th style="padding: 8px; border: 1px solid #ddd;">Amount Allocated</th>
                            <th style="padding: 8px; border: 1px solid #ddd;">Amount Available</th>
                            <th style="padding: 8px; border: 1px solid #ddd;">Amount Requisition</th>
                            <th style="padding: 8px; border: 1px solid #ddd;">Amount PO</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;">${budgetDetails.category_name ?? '—'}</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${budgetDetails.sub_category ?? '—'}</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${budgetDetails.total_allocated_amount ?? '—'}</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${budgetDetails.total_available_amount ?? '—'}</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${budgetDetails.total_reserved_for_indent ?? '—'}</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${budgetDetails.total_reserved_for_po ?? '—'}</td>
                        </tr>
                    </tbody>
                </table>
                ${!isBudgetSufficient ? '<div style="margin-top:10px; color:red;"><b>Note: This indent exceeds the current budget. Please proceed only if justified.</b></div>' : ''}
            </div>
        `;

            return {
                html,
                isBudgetSufficient
            };

        } catch (err) {
            throw new Error(err.message);
        }
    }

    async function showBudgetDetails(indentId, approvalType, approverMasterId, isItLastDocument) {
        const { html, isBudgetSufficient } = await getBudgetDetails();

        Swal.fire({
            title: `Budget Details <br> Approval requested for Amount: ${formatCurrency(totalIndentPrice)}`,
            html: html,
            showCancelButton: true,
            // showConfirmButton: isBudgetSufficient,
            confirmButtonText: 'Approve',
            cancelButtonText: 'Close',
            width: 600, // Optional: sets overall modal width
            reverseButtons: true
        }).then(async (result) => {
            if (result.isConfirmed) {
                await approveIndent(indentId, approvalType, approverMasterId, isItLastDocument);
            }
        });
    }

    async function approveIndent(indentId, approvalType, approverMasterId, isItLastDocument) {
        budgetCategoryId = $("#budgetCategoryId").val();

        if (Number(budgetCategoryId) <= 0) {
            return Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Please select budget to proceed!",
            });
        }

        Swal.fire({
            title: "Are you sure?",
            text: "You won't be able to revert this!",
            html: `<textarea required class="form-control" name="approva_remarks" id="approval-remarks" style="width: 70%;margin: auto;font-style: italic;font-size: large;" placeholder="Enter approval remarks"></textarea>`,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes, approve!"
        }).then(async function (result) {
            if (result.isConfirmed) {
                try {
                    const approvalRemarks = $("#approval-remarks").val();
                    if (!approvalRemarks) {
                        return Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Remarks cannot be empty!",
                        }).then(e => {
                            approveIndent(indentId, approvalType, approverMasterId, isItLastDocument);
                        })
                    }
                    const isApproved = await approveRequestedIndent(indentId, approvalType, approverMasterId, isItLastDocument, approvalRemarks);

                    if (isApproved == 0) {
                        return Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Something went wrong!",
                        });
                    } else if (isApproved == -1) {
                        return Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Please upload and select a quotaion to proceed!",
                        });
                    }

                    Swal.fire({
                        title: "Approved!",
                        // text: "Your Indent has been approved!",
                        icon: "success",
                    }).then(e => {
                        // console.log(e);
                        updateUI(indentId);
                    });
                } catch (err) {
                    return Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text: "Something went wrong!",
                    });
                }
            }
        });
    }

    async function rejectRequestedIndent(indentId, approvalType, approverMasterId, isItLastDocument, approvalRemarks) {
        let response;
        try {
            await $.ajax({
                url: "<?php echo site_url('procurement/Requisition_controller_v2/reject_indent'); ?>",
                type: "POST",
                data: { "indentId": indentId, "approvalType": approvalType, "approverMasterId": approverMasterId, "isItLastDocument": isItLastDocument, "approvalRemarks": approvalRemarks },
                success: function (data) {
                    approvers = data;
                }
            });
            return approvers;
        } catch (er) {
            throw err;
        }
    }

    async function rejectIndent(indentId, approvalType, approverMasterId, isItLastDocument) {
        Swal.fire({
            title: "Are you sure?",
            html: `<textarea required class="form-control" name="approva_remarks" id="approval-remarks" style="width: 70%;margin: auto;font-style: italic;font-size: large;" placeholder="Enter rejected remarks"></textarea>`,
            icon: "warning",
            showCancelButton: true,
            cancelButtonText: 'Close',
            confirmButtonText: "Reject!",
            reverseButtons: true
        }).then(async function (result) {
            if (result.isConfirmed) {
                try {
                    const approvalRemarks = $("#approval-remarks").val();
                    if (!approvalRemarks) {
                        return Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Remarks cannot be empty!",
                        }).then(e => {
                            rejectIndent(indentId, approvalType, approverMasterId, isItLastDocument);
                        });
                    }

                    const isRejected = await rejectRequestedIndent(indentId, approvalType, approverMasterId, isItLastDocument, approvalRemarks);

                    if (!isRejected) {
                        return Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Something went wrong!",
                        });
                    }

                    Swal.fire({
                        title: "Rejected!",
                        // text: "Your Indent has been rejected!",
                        icon: "success",
                    }).then(e => {
                        updateUI(indentId);
                    });
                } catch (err) {
                    return Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text: "Something went wrong!",
                    });
                }
            }
        });
    }

    async function sendRequestForModification(indentId, approvalType, approverMasterId, isItLastDocument, approvalRemarks) {
        let response;
        try {
            await $.ajax({
                url: "<?php echo site_url('procurement/Requisition_controller_v2/request_for_modification'); ?>",
                type: "POST",
                data: { "indentId": indentId, "approvalType": approvalType, "approverMasterId": approverMasterId, "isItLastDocument": isItLastDocument, "approvalRemarks": approvalRemarks },
                success: function (data) {
                    response = data;
                }
            });
            return response;
        } catch (er) {
            throw err;
        }
    }

    async function requestForModification(indentId, approvalType, approverMasterId, isItLastDocument) {
        Swal.fire({
            title: "Are you sure?",
            html: `<textarea required class="form-control" name="approva_remarks" id="approval-remarks" style="width: 70%;margin: auto;font-style: italic;font-size: large;" placeholder="Enter modification remarks"></textarea>`,
            icon: "warning",
            showCancelButton: true,
            cancelButtonText: 'Close',
            confirmButtonText: "Send for modification!",
            reverseButtons: true
        }).then(async function (result) {
            if (result.isConfirmed) {
                try {
                    const approvalRemarks = $("#approval-remarks").val();
                    if (!approvalRemarks) {
                        return Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Remarks cannot be empty!",
                        }).then(e => {
                            requestForModification(indentId, approvalType, approverMasterId, isItLastDocument);
                        });
                    }

                    const response = await sendRequestForModification(indentId, approvalType, approverMasterId, isItLastDocument, approvalRemarks);
                    if (!response) {
                        return Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Something went wrong!",
                        });
                    }

                    Swal.fire({
                        title: "Modification!",
                        text: "Sent for modification!",
                        icon: "success",
                    }).then(e => {
                        // this is to re-check for the status 4
                        updateUI(indentId);
                    });
                } catch (err) {
                    return Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text: "Something went wrong!",
                    });
                }
            }
        });
    }

    function generateApproversTable(approvers, indentId, approvalType) {
        if (approvers?.length) {
            let table = `<table name="BOMItems" id="deptApproval" class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Staff Name</th>
                                        <th>Approver Type</th>
                                        <th>Remarks</th>
                                        <th>Actions</th>
                                    </tr>
                                        </thead>
                                        <tbody>
                                        `;

            approvers.forEach((approver, i) => {
                table += `
                                        <tr>
                                        <td>${i + 1}</td>
                                        <td>${approver.name}</td>
                                        <td>${approver.is_financial_approver ? "Financial Approver" : `Level ${i + 1} Approver`}</td>
                                        <td>${approver.approval_remarks || "NA"}</td>`;

                if (+approver.action_done === 1) {
                    table += `<td>${status[approver.status]}</td>`;
                } else if (+approver.display_options === 1) {

                    // if loggedin staff active
                    if (+loggedInStafId === +approver.staff_id) {
                        table += `<td>
                            <div class="container-fluid" style="padding-top: 12px;">
                                <div class="cts-toolbar btn-toolbar" role="toolbar">`;

                        if (approver.is_financial_approver) {
                            table += `<button id="deleteButton" data-indent-id="${indentId}" onclick="showBudgetDetails('${indentId}','${approvalType}','${approver.approver_master_id}','${approver.is_it_last_document}')" type="button" class="btn btn-default"><span class="glyphicon glyphicon-thumbs-up" aria-hidden="true"></span>Approve</button>`;
                        } else {
                            table += `<button id="deleteButton" data-indent-id="${indentId}" onclick="approveIndent('${indentId}','${approvalType}','${approver.approver_master_id}','${approver.is_it_last_document}')" type="button" class="btn btn-default"><span class="glyphicon glyphicon-thumbs-up" aria-hidden="true"></span>Approve</button>`;
                        }

                        table += `<button id="deleteButton" data-indent-id="${indentId}" onclick="rejectIndent('${indentId}','${approvalType}','${approver.approver_master_id}','${approver.is_it_last_document}')" type="button" class="btn btn-default"><span class="glyphicon glyphicon-thumbs-down" aria-hidden="true"></span>Reject</button>
                                    
                                    <button id="deleteButton" data-indent-id="${indentId}" onclick="requestForModification('${indentId}','${approvalType}','${approver.approver_master_id}','${approver.is_it_last_document}')" type="button" class="btn btn-default"><span class="glyphicon glyphicon-repeat" aria-hidden="true"></span>Request for modification</button>
                                </div>
                            </div>
                        </td>`;
                    } else {
                        // if other than loggedin staff active
                        table += `<td>
                            <div class="container-fluid" style="padding-top: 12px;">
                                <div class="cts-toolbar btn-toolbar" role="toolbar">
                                    <button disabled type="button" class="btn btn-default"><span class="glyphicon glyphicon-thumbs-up" aria-hidden="true"></span>Approve</button>
                                    
                                    <button disabled id="deleteButton" type="button" class="btn btn-default"><span class="glyphicon glyphicon-thumbs-down" aria-hidden="true"></span>Reject</button>
                                    
                                    <button disabled id="deleteButton" type="button" class="btn btn-default"><span class="glyphicon glyphicon-repeat" aria-hidden="true"></span>Request for modification</button>
                                </div>
                            </div>
                        </td>`;
                    }
                } else {
                    if (+approver.bom_master_status === 2 || +approver.bom_master_status === 3) {
                        table += `<td>Not applicable</td>`;
                    } else {
                        table += `<td>waiting for previous approval</td>`;
                    }
                }

                table += `</tr>`;
            })

            table += `</tbody>
                </table>`;
            return table;
        } else {
            return generateMessage("No remaining approvals!");
        }
    }

    async function getIndentApprovers(indentId) {
        let approvers;
        try {
            await $.ajax({
                url: "<?php echo site_url('procurement/Requisition_controller_v2/get_indent_approvers'); ?>",
                type: "POST",
                data: { "indentId": indentId },
                success: function (data) {
                    approvers = JSON.parse(data);
                }
            });

            return approvers;
        } catch (err) {
            throw err;
        }
    }

    async function showIndentApprovalTable(indentId) {
        try {
            const approvers = await getIndentApprovers(indentId);
            const bomApprovalTable = generateApproversTable(approvers, indentId, 1);
            $("#show-bom-approval").html(bomApprovalTable);
        } catch (err) {
            return Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Please select an item to delete!",
            });
        }
    }

    async function getQuotationFiles(indentId) {
        let files;
        try {
            await $.ajax({
                url: "<?php echo site_url('procurement/Requisition_controller_v2/get_quotation_files'); ?>",
                type: "POST",
                data: { "indentId": indentId },
                success: function (data) {
                    files = JSON.parse(data);
                    if (files.length) {
                        originalIndentPrice = files[0].original_indent_price;
                    }
                }
            });

            return files;
        } catch (err) {
            throw err;
        }
    }

    function makeSelectedQuotation(fileId, vendorName, bomMasterId, vendorAmount) {
        Swal.fire({
            title: "Are you sure?",
            text: "You won't be able to revert this!",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes, make it as selected Quotation!"
        }).then(function (result) {
            // console.log(result);
            if (!result.isConfirmed) return;
            try {
                $.ajax({
                    url: "<?php echo site_url('procurement/Requisition_controller_v2/make_quotation_as_selected'); ?>",
                    type: "POST",
                    data: { "fileId": fileId, "bomMasterId": bomMasterId, "vendorName": vendorName, 'vendorAmount': vendorAmount },
                    success: function (res) {
                        if (+res) {
                            const currentBomId = window.localStorage.getItem("indentId");
                            showQuotationFiles(currentBomId);
                            return Swal.fire({
                                icon: "success",
                                title: "Quotation Selected",
                                text: "Your changes has been saved!",
                            });
                        } else {
                            return Swal.fire({
                                icon: "error",
                                title: "Oops...",
                                text: "Please select an item to delete!",
                            });
                        }
                    }
                });
            } catch (err) {
                return Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: "Please select an item to delete!",
                });
            }
        })
    }

    function generateQuotationFilesTable(quotationFiles, indentId) {
        if (quotationFiles?.length) {
            const showAction = isAdmin && +canSelectQuotation;

            let table = `<table name="BOMItems" id="deptApproval" class="table table-bordered">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Vendor Name</th>
                    <th>Price</th>
                    <th>File</th>
                    <th>General Remarks</th>
                    <th>Tech Evaluation Remarks</th>
                    <th>Price Evaluation Remarks</th>
                    <th>Delivery Evaluation Remarks</th>
                    ${showAction ? `<th>Action</th>` : ""}
                </tr>
            </thead>
            <tbody>
        `;

            quotationFiles.forEach((file, i) => {
                table += `
                <tr style="background:${+file.selected_quotation === 1 ? "#aee1ae" : ""}">
                    <td>${i + 1}</td>
                    <td>${file.vendor_name}</td>
                    <td>${formatCurrency(file.price)}</td>
                    <td>
                        <a target="_blank" href="${file.file_path}">View file</a>
                    </td>
                    <td>${file.remarks?.trim() || "-"}</td>
                    <td>${file.tech_evaluation_remarks?.trim() || "-"}</td>
                    <td>${file.price_evaluation_remarks?.trim() || "-"}</td>
                    <td>${file.delivery_evaluation_remarks?.trim() || "-"}</td>
                    ${showAction ? `
                    <td style="background: white;">
                        <button ${+file.selected_quotation === 1 ? "disabled" : ""} 
                                class="btn btn-info" 
                                name="make_selected_quotation" 
                                id="make_selected_quotation" 
                                onclick="makeSelectedQuotation('${file.id}','${file.vendor_name}','${file.indents_master_id}',${file.price})">
                                Make as selected quotation
                        </button>
                    </td>
                    ` : ""}
                </tr>
            `;
            });

            table += `</tbody></table>`;
            return table;
        } else {
            return generateMessage("No documents found. Please add one to see!");
        }
    }



    async function showQuotationFiles(indentId) {
        try {
            const quotationFiles = await getQuotationFiles(indentId);
            // Update the HTML element with the formatted price
            $("#original-indent-price").html(`Original Indent Price: ${formatCurrency(originalIndentPrice)}`);

            const bomApprovalTable = generateQuotationFilesTable(quotationFiles, indentId);
            $("#show-quotation-files").html(bomApprovalTable);
        } catch (err) {
            return Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Something  went wrong!",
            });
        }
    }

    async function getQuotationApprovers(indentId) {
        let approvers;
        try {
            await $.ajax({
                url: "<?php echo site_url('procurement/Requisition_controller_v2/get_quotation_approvers'); ?>",
                type: "POST",
                data: { "indentId": indentId },
                success: function (data) {
                    approvers = JSON.parse(data);
                }
            });

            return approvers;
        } catch (err) {
            throw err;
        }
    }

    async function showQuotationApprovalTable(indentId) {
        try {
            const approvers = await getQuotationApprovers(indentId);
            const bomApprovalTable = generateApproversTable(approvers, indentId, 2);
            $("#show-quotation-approval").html(bomApprovalTable);
        } catch (err) {
            return Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Please select an item to delete!",
            });
        }
    }

    async function getIndentHistory(indentId) {
        let bomLog;
        try {
            await $.ajax({
                url: "<?php echo site_url('procurement/Requisition_controller_v2/get_indent_history'); ?>",
                type: "POST",
                data: { "indentId": indentId },
                success: function (data) {
                    bomLog = JSON.parse(data);
                }
            });

            return bomLog;
        } catch (err) {
            throw err;
        }
    }

    function generateHistoryTable(bomLog) {
        if (bomLog?.length) {
            let table = `<table name="bomLog" id="bomLogTable" class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Action On</th>
                                        <th>Action Type</th>
                                        <th>Action</th>
                                        <th>Action By</th>
                                    </tr>
                                        </thead>
                                        <tbody>
                                        `;

            bomLog.forEach((bomLog, i) => {
                table += `
                                        <tr>
                                        <td>${++i}</td>
                                        <td>${bomLog.action_on}</td>
                                        <td>${bomLog.action_type}</td>
                                        <td>${bomLog.action}</td>
                                        <td>${bomLog.action_by}</td>
                        </tr>
                        `;
            });

            table += `</tbody>
                </table>`;
            return table;
        } else {
            return generateMessage("Found no history. Please create one to see!");
        }
    }

    async function showIndentHistoryTable(indentId) {
        try {
            // bring bom history
            const bomLog = await getIndentHistory(indentId);
            const historyTable = generateHistoryTable(bomLog);
            $("#show-history").html(historyTable);
        } catch (err) {
            return Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Please select an item to delete!",
            });
        }
    }

    function showUploadPDFModal(indentId) {
        $("#indentId").val(indentId);
        $("#upload_pdf_modal").trigger("click");
    }

    function showQuotation(indentId) {
        $.ajax({
            url: "<?php echo site_url('procurement/Requisition_controller_v2/bring_po_qoutation'); ?>",
            type: "POST",
            data: { "indentId": indentId },
            success: function (res) {
                const quotationURL = JSON.parse(res);

                // $("#showing_quotation").html(`<iframe src="${quotationURL}" alt="quotation"></iframe>`);
                // $("#show_pdf_modal").trigger("click");


                Swal.fire({
                    title: "Indent Quotation!",
                    html: `<iframe src="${quotationURL}" alt="quotation"></iframe>`,
                    imageWidth: 400,
                    imageHeight: 200,
                    imageAlt: "Custom image"
                });
            }
        });
    }

    function check_indent_pdf_generated_invoice(indentId) {
        try {
            $.ajax({
                url: '<?php echo site_url('procurement/requisition_controller_v2/check_indent_pdf_generated_invoice') ?>',
                type: 'post',
                data: { 'indentId': indentId, 'invoice_type': 'Invoice' },
                success: function (data) {
                    var res = data.trim();
                    // console.log(res);

                    // $("#generate_button").prop("disabled", false).text("Re-Generate PO");
                    // const downloadButton = `<a id="download_btn" href="<?php //echo site_url('procurement/requisition_controller_v2/download_purchase_order') ?>/${requisition_id}" class="btn btn-success" onclick="download_purchase_order()">Download PO</a>`;

                    // $("#download_button").html(downloadButton);
                    // $("#download_button").show();

                    // if (res) {
                    // clearInterval(waitTimer);
                    // $('#downloadInvoice').show();
                    // $('#downloadInvoice').html('Download <span class="fa fa-cloud-download"></span>');
                    // $('#send_emailInvoice').show();
                    // $('#send_emailInvoice').html('Send  <span class="fa fa-envelope-o"></span>');
                    // $('#generateInvoice').html('Generate Invoice');
                    // }
                }
            });
        } catch (err) {
            throw err;
        }

    }

    // function download_purchase_order(indentId) {
    //     console.log(indentId);
    //     // const downloadButton = `<a disabled id="download_btn" href="<?php //echo site_url('procurement/requisition_controller_v2/download_purchase_order') ?>/${requisition_id}" class="btn btn-success" onclick="download_purchase_order()">Downloading PO</a>`;

    //     // $("#download_button").html(downloadButton);

    //     $.ajax({
    //         url: '<?php //echo site_url('procurement/requisition_controller_v2/download_generated_bom') ?>',
    //         type: 'post',
    //         data: { 'indentId': indentId },
    //         success: function (data) {
    //             console.log(data);
    //             // $("#download_button").prop("disabled",false).text("Download PO");
    //         }
    //     });
    // }

    // generating pdf for quotation
    async function generatePDF(indentId) {
        $("#generatePDFBtn").prop("disabled", true).text('Generating PDF...');
        try {
            await $.ajax({
                url: '<?php echo site_url('procurement/requisition_controller_v2/generate_indent_pdf_for_quotation'); ?>',
                type: "post",
                data: { 'indentId': indentId },
                success: function (res) {
                    $("#generatePDFBtn").prop("disabled", false).text('Generate PDF for quotation');
                    // console.log(res);
                    if (+res) {
                        waitTimer = setInterval(function () { check_indent_pdf_generated_invoice(indentId) }, 5000);
                        Swal.fire({
                            title: "Your Indent is ready!",
                            text: "Your pdf has been generated.",
                            icon: "success",
                            html: `<a id="download_btn" href="<?php echo site_url('procurement/requisition_controller_v2/download_generated_bom') ?>/${indentId}" class="btn btn-success">Download Indent!</a>`,
                            showConfirmButton: false,
                            showCancelButton: true,
                            confirmButtonColor: "#3085d6",
                            cancelButtonColor: "#d33",
                            confirmButtonText: `Ok!`,
                            cancelButtonText: `May be later!`
                        }).then(e => {
                            // console.log(e);
                            // showIndentItems(indentId);
                            // download pdf
                            // await download_purchase_order(indentId);
                        });
                    } else {
                        return Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Something went wrong!",
                        });
                    }


                    // if (res != 0) {
                    // } else {
                    //     $(function () {
                    //         new PNotify({
                    //             title: 'Error',
                    //             text: 'Invoice template not added',
                    //             type: 'error',
                    //         });
                    //     });
                    // }
                }
            });
        } catch (err) {
            return Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Something went wrong!",
            });
        }
    }


    const deleteIndentItem = async function (itemsArray, indentId) {
        let response;
        try {
            // finally submitting all the bom id's to be delete
            await $.ajax({
                url: "<?php echo site_url('procurement/Requisition_controller_v2/delete_individual_indent_items'); ?>",
                type: "POST",
                data: { "bomItemIds": itemsArray, "indentId": indentId },
                success: function (data) {
                    response = data;
                }
            });

            return response;
        } catch (err) {
            console.log(err);
            throw err;
        }
    }

    function bringAllItems() {
        return document.querySelectorAll('input[name="uuid"]:checked');
    }

    async function removeItem(itemId, itemName, indentId) {
        if (totalIndentItems == 1) {
            return Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Cannot delete all items!",
            });
        }

        // confirm user to delete it permanently
        Swal.fire({
            title: `Are you sure you want to remove Item- ${itemName}?`,
            text: "You won't be able to revert this!",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes, delete it!"
        }).then(async function (result) {
            if (result.isConfirmed) {
                try {
                    const deleteSelectedItems = await deleteIndentItem([itemId], indentId);

                    if (!deleteSelectedItems) {
                        return Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Something went wrong!",
                        });
                    }

                    Swal.fire({
                        title: "Deleted!",
                        text: "Your file has been deleted.",
                        icon: "success"
                    }).then(async e => {
                        showIndentItems(indentId);
                    });
                } catch (err) {
                    console.log(err);
                    return Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text: "Something went wrong!",
                    });
                }
            }
        });
    }

    async function deleteItem(indentId) {
        var allSelectedItems = bringAllItems();

        // empty check -> guard clause
        if (!allSelectedItems?.length) {
            return Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Please select an item to delete!",
            });
        }

        // confirm user to delete it permanently
        Swal.fire({
            title: "Are you sure?",
            text: "You won't be able to revert this!",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes, delete it!"
        }).then(async function (result) {
            if (result.isConfirmed) {
                try {
                    const deleteItemsArray = [];
                    allSelectedItems.forEach(i => {
                        deleteItemsArray.push(i.dataset.itemId);
                    });

                    const deleteSelectedItems = await deleteIndentItem(allSelectedItems, indentId);

                    if (!deleteSelectedItems) {
                        return Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Something went wrong!",
                        });
                    }

                    Swal.fire({
                        title: "Deleted!",
                        text: "Your file has been deleted.",
                        icon: "success"
                    }).then(async e => {
                        const currentBom = document.querySelector(".active");
                        const currentBomId = currentBom.dataset.indentId;
                        showIndentItems(currentBomId);
                    });
                } catch (err) {
                    console.log(err);
                    return Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text: "Something went wrong!",
                    });
                }
            }
        });
    }

    // Handle click on "Select all for delete" control
    let check = false;
    function selectAll() {
        const getAllItems = document.querySelectorAll(".bom-check");

        getAllItems.forEach(item => {
            const selectAllCheckBtn = document.querySelector("#select-all");

            selectAllCheckBtn.checked = check ? false : true;
            item.checked = check ? false : true;
        });

        check = !check;
    };


    // Handle click on "Select all for approver" control
    let appCheck = false;
    function selectAllApp() {
        const getAllItems = document.querySelectorAll(".app-check");

        getAllItems.forEach(item => {
            const selectAllCheckBtn = document.querySelector("#select-all-app");

            selectAllCheckBtn.checked = appCheck ? false : true;
            item.checked = appCheck ? false : true;
        });

        appCheck = !appCheck;
    };

    // Handle click on checkbox to set state of "Select all" control
    $('#ctsmail tbody').on('change', 'input[type="checkbox"]', function () {
        // If checkbox is not checked
        if (!this.checked) {
            var el = $('#select-all').get(0);
            // If "Select all" control is checked and has 'indeterminate' property
            if (el && el.checked && ('indeterminate' in el)) {
                // Set visual state of "Select all" control
                // as 'indeterminate'
                el.indeterminate = true;
            }
        }
    });


    // $('#refreshButton').click(function () {
    //     location.reload();
    // });

    async function sendingForDeptApproval(indentId, staffIds) {
        let response;
        try {
            await $.ajax({
                url: "<?php echo site_url('procurement/Requisition_controller_v2/send_for_dept_approval') ?>",
                type: "POST",
                data: { "indentId": indentId, "staffIds": staffIds },
                success: function (data) {
                    // bringAllBOMs();
                    response = data;
                }
            });
            return response;
        } catch (err) {
            throw err;
        }
    }

    async function sendingForQuotationApproval(indentId, staffIds) {
        let response;
        try {
            await $.ajax({
                url: "<?php echo site_url('procurement/Requisition_controller_v2/send_for_quotation_approval') ?>",
                type: "POST",
                data: { "indentId": indentId, "staffIds": staffIds },
                success: function (data) {
                    // bringAllBOMs();
                    response = data;
                }
            });
            return response;
        } catch (err) {
            throw err;
        }
    }

    async function getIndentApproversList(indentId, source = "indent") {
        let response;
        try {
            await $.ajax({
                url: "<?php echo site_url('procurement/Requisition_controller_v2/get_indent_approvers_list') ?>",
                type: "POST",
                data: { "indentId": indentId, "source": source },
                success: function (data) {
                    response = JSON.parse(data);
                }
            });
            return response;
        } catch (err) {
            throw err;
        }
    }

    function generateIndentApproversTable(approverList) {
        if (approverList.length) {
            // construct table here
            let table = `<table name="billApproversList" id="billApproversList" class="table table-bordered" style="text-align:left;">
                                 <thead>
                                    <tr>
                                         <th>#</th>
                                         <th>Approver Name</th>
                                         <th>Approver Type</th>
                                         
                                         <th>Designation</th>
                                    </tr>
                                </thead>
                                <tbody>
                                         `;

            // itertae loop to construct select/delect feature here
            approverList?.forEach((app, i, arr) => {
                table += `
                                    <tr>
                                        <td>${i + 1}</td>
                                        <td>${app.name}</td>
                                        <td>${i + 1 == arr.length && "Financial" || `Level ${i + 1}`} Approver</td>
                                        <td>${app.designation}</td>
                                    </tr>`;
            });

            table += `</tbody>
                 </table>`;
            return table;
        } else {
            // return no data msg here
            return generateMessage("Found no approver, Please add one to show!");
        }
    }

    function getApproverIds(className) {
        const getSelectedApprovers = document.querySelectorAll('input[name="bomApproverList"]:checked');
        try {
            if (getSelectedApprovers?.length) {
                const bomSelectedApproverList = [];
                getSelectedApprovers.forEach(i => {
                    bomSelectedApproverList.push(i.dataset.staffId);
                });

                return bomSelectedApproverList;
            }
        } catch (err) {
            throw err;
        }
    }

    // async function checkApproverAlreadyAssigned(indentId) {
    //     let response;
    //     try {
    //         await $.ajax({
    //             url: "<?php //echo site_url('procurement/Requisition_controller_v2/check_indent_approver_already_assigned') ?>",
    //             type: "POST",
    //             data: { "indentId": indentId },
    //             success: function (data) {
    //                 // bringAllBOMs();
    //                 response = data;
    //             }
    //         });
    //         return response;
    //     } catch (err) {
    //         
    //         throw err;
    //     }
    // }
    const bomStatusDescription = {
        0: "P",     // pending
        1: "A",     // approved
        2: "R",     // rejected
        3: "RFM",   // sent request for modification
        4: "SIA",   // sent for indent approval
        5: "SQA",   // sent for quotation approval
        6: "IA",     // Indent Approved
        7: "RFMQA"     // Request for modification from quotation approver
    };

    const statusBadgeColor = {
        0: "primary",
        1: "success",
        2: "danger",
        3: "warning",
        4: "info",
        5: "info",
        6: "info",
    }

    function updateBadgeStatus(indentId = 0) {
        if (!indentId) return;

        // based on bom id bring active bom status
        $.ajax({
            url: "<?php echo site_url('procurement/Requisition_controller_v2/get_current_indent_staus') ?>",
            type: "POST",
            data: { "indentId": indentId },
            success: function (res) {
                const { status } = JSON.parse(res);
                const badgeStatus = bomStatusDescription[status];
                const badgeColor = statusBadgeColor[status];
                // update bom badge status
                const currentBomBadge = document.querySelector(`#bom-badge-${indentId}`);
                if (currentBomBadge) {
                    currentBomBadge.innerText = badgeStatus;

                    currentBomBadge.classList.remove("badge-primary", "badge-success", "badge-danger", "badge-warning", "badge-info");
                    currentBomBadge.classList.add(`badge-${badgeColor}`);
                }
            }
        });
    }

    async function sendForIndentApproval(indentId) {
        // LEVEL 1 APPROVAL
        try {
            // const isApproverAlreadyAssigned = await checkApproverAlreadyAssigned(indentId);

            // if (+isApproverAlreadyAssigned) {
            //     // console.log(isApproverAlreadyAssigned);
            //     Swal.fire({
            //         title: "Sent For Approval!",
            //         text: "Your Indent has been sent for approval. Please wait for response!",
            //         icon: "success",
            //     }).then(e => {
            //         // console.log(e);
            //         showIndentItems(indentId);
            //         updateBadgeStatus(indentId);
            //     });
            //     return;
            // }
            // 1. bring all the staffs from staff master
            const bomApproversList = await getIndentApproversList(indentId, "indent");
            // 2. construct/display table with select/d-select feature
            const bomApproversTable = generateIndentApproversTable(bomApproversList);

            Swal.fire({
                title: "<strong>Approvers List</strong>",
                html: `${bomApproversTable}`,
                showCloseButton: true,
                showCancelButton: true,
                focusConfirm: false,
                confirmButtonText: `
                    <i class=\"fa fa-thumbs-up\"></i> Send
                `,
                confirmButtonAriaLabel: "Thumbs up, great!",
                cancelButtonText: `
                    <i class=\"fa fa-thumbs-down\"> Cancel </i>
                `,
                cancelButtonAriaLabel: "Thumbs down",
                showConfirmButton: bomApproversList.length > 0 // Hide Send button if no approvers
            }).then(res => {
                if (res.isConfirmed) {
                    let approverIds = [];

                    if (!bomApproversList.length) {
                        return Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Please add approvers to proceed!",
                        }).then(r => {
                            sendForIndentApproval(indentId);
                        });
                    } else {
                        bomApproversList.forEach(approver => {
                            approverIds.push(approver.id);
                        })
                    }
                    // do somethign when selected
                    Swal.fire({
                        title: "Are you sure?",
                        text: "You won't be able to revert this!",
                        icon: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#3085d6",
                        cancelButtonColor: "#d33",
                        confirmButtonText: "Yes, send for approval!"
                    }).then(async function (result) {
                        if (result.isConfirmed) {
                            try {
                                // code for sending approval
                                // 3. then submit names along with confirm msg
                                const isApprovalSent = await sendingForDeptApproval(indentId, approverIds);

                                if (!isApprovalSent) {
                                    return Swal.fire({
                                        icon: "error",
                                        title: "Oops...",
                                        text: "Something went wrong!",
                                    });
                                } else if (isApprovalSent == -1) {
                                    return Swal.fire({
                                        icon: "error",
                                        title: "Oops...",
                                        text: "Please add Financial Approver to proceed!",
                                    });
                                }

                                Swal.fire({
                                    title: "Sent For Approval!",
                                    text: "Your Indent has been sent for approval. Please wait for response!",
                                    icon: "success",
                                }).then(e => {
                                    // console.log(e);
                                    showIndentItems(indentId);

                                    // update the status of the bomin lest hand side menu bar
                                    updateBadgeStatus(indentId);
                                });
                            } catch (err) {
                                return Swal.fire({
                                    icon: "error",
                                    title: "Oops...",
                                    text: "Something went wrong!",
                                });
                            }
                        } else {
                            // sendForIndentApproval(indentId);
                        }
                    });

                } else {
                    // do something if not selected
                }
            })

        } catch (err) {
            throw err;
        }
        // 4. then, store selected ones in the 'procurement_indents_master 's table
        // 5. then, display selected staff names in bom with all the individual opertaions they can perform
    }

    // async function checkApproverAlreadyAssignedForQuotation(indentId) {
    //     let response;
    //     try {
    //         await $.ajax({
    //             url: "<?php //echo site_url('procurement/Requisition_controller_v2/check_indent_quotation_approver_already_assigned') ?>",
    //             type: "POST",
    //             data: { "indentId": indentId },
    //             success: function (data) {
    //                 // bringAllBOMs();
    //                 response = data;
    //             }
    //         });
    //         return response;
    //     } catch (err) {
    //         
    //         throw err;
    //     }
    // }

    async function sendForQuotationApproval(indentId) {
        try {
            // const isApproverAlreadyAssigned = await checkApproverAlreadyAssignedForQuotation(indentId);

            // if (+isApproverAlreadyAssigned) {
            //     console.log(isApproverAlreadyAssigned);
            //     Swal.fire({
            //         title: "Sent For Approval!",
            //         text: "Your Indent has been sent for approval. Please wait for response!",
            //         icon: "success",
            //     }).then(e => {
            //         // console.log(e);
            //         showIndentItems(indentId);
            //         updateBadgeStatus(indentId);
            //     });
            //     return;
            // }
            // 1. bring all the staffs from staff master
            const bomApproversList = await getIndentApproversList(indentId, "RFQ");
            // 2. construct/display table with select/d-select feature
            const bomApproversTable = generateIndentApproversTable(bomApproversList);

            Swal.fire({
                title: "<strong>Approvers List</strong>",
                html: `${bomApproversTable}`,
                showCloseButton: true,
                showCancelButton: true,
                focusConfirm: false,
                confirmButtonText: `
                    <i class=\"fa fa-thumbs-up\"></i> Send
                `,
                confirmButtonAriaLabel: "Thumbs up, great!",
                cancelButtonText: `
                    <i class=\"fa fa-thumbs-down\"> Cancel </i>
                `,
                cancelButtonAriaLabel: "Thumbs down",
                showConfirmButton: bomApproversList.length > 0 // Hide Send button if no approvers
            }).then(res => {
                if (res.isConfirmed) {
                    let approverIds = [];

                    if (!bomApproversList.length) {
                        return Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Please add approvers to proceed!",
                        }).then(r => {
                            sendForIndentApproval(indentId);
                        });
                    } else {
                        bomApproversList.forEach(approver => {
                            approverIds.push(approver.id);
                        })
                    }

                    Swal.fire({
                        title: "Are you sure?",
                        text: "You won't be able to revert this!",
                        icon: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#3085d6",
                        cancelButtonColor: "#d33",
                        confirmButtonText: "Yes, send for approval!"
                    }).then(async function (result) {
                        if (result.isConfirmed) {
                            try {
                                // code for sending approval
                                // 3. then submit names along with confirm msg
                                const isApprovalSent = await sendingForQuotationApproval(indentId, approverIds);

                                if (!isApprovalSent) {
                                    return Swal.fire({
                                        icon: "error",
                                        title: "Oops...",
                                        text: "Something went wrong!",
                                    });
                                } else if (isApprovalSent == -1) {
                                    return Swal.fire({
                                        icon: "error",
                                        title: "Oops...",
                                        text: "Please add Financial Approver to proceed!",
                                    });
                                }

                                Swal.fire({
                                    title: "Sent For Approval!",
                                    text: "Your Quotation has been sent for approval. Please wait for response!",
                                    icon: "success",
                                }).then(e => {
                                    // console.log(e);
                                    showIndentItems(indentId);

                                    // update the status of the bomin lest hand side menu bar
                                    updateBadgeStatus(indentId);
                                });
                            } catch (err) {
                                return Swal.fire({
                                    icon: "error",
                                    title: "Oops...",
                                    text: "Something went wrong!",
                                });
                            }
                        }
                    });

                } else {
                    // do something if not selected
                }
            })

        } catch (err) {
            throw err;
        }
        // 4. then, store selected ones in the 'procurement_indents_master 's table
        // 5. then, display selected staff names in bom with all the individual opertaions they can perform
    }
</script>