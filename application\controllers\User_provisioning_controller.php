<?php defined('BASEPATH') OR exit('No direct script access allowed');

class User_provisioning_controller extends CI_Controller {

	public function __construct()	{
        parent::__construct();

		if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }

        $this->load->model('user_provisioning_model');
        $this->load->model('student/Student_Model');
        $this->config->load('form_elements');
        $this->load->library('bitly');
    }

    public function provision_report() {
        if (!$this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_PARENTS')) {
            redirect('dashboard', 'refresh');
        }
        
        $data['main_content'] = 'user_provisioning/report';
        $this->load->view('inc/template', $data);
    }

    public function getProvisionReport(){
        $report = $this->user_provisioning_model->getProvisionReport($this->input->post('classtype'), $this->input->post('parenttype'));
        echo json_encode($report);
    }

    public function app_logins() {
        $this->load->library('filemanager');
        $this->load->model('student/Student_Model');
        $data['selectedClassId'] = $this->input->post('classId');
        $data['classList'] = $this->Student_Model->getClassNames();
        $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
        $data['studentNames'] = $this->Student_Model->getstudentNames();
        $data['admission_type'] = $this->settings->getSetting('admission_type');
        if ($this->mobile_detect->isMobile()) {
            $data['main_content']    = 'user_provisioning/app_logins_mobile';
        } else {
            $data['main_content']    = 'user_provisioning/app_logins';
        }
        
        // $data['main_content'] = 's3_direct_upload_starter_example.php';
        $this->load->view('inc/template', $data);
    }

    public function getStudentDetails(){
        $mode = $_POST['mode'];
        $admission_type = $_POST['admission_type'];
        switch ($mode) {
        case 'section_id':
            $sectionId = $_POST['sectionId'];
            $stdData = $this->user_provisioning_model->getstdDataByClassSection($sectionId, $admission_type);
            break;
        case 'class_id':
            $classId = $_POST['classId'];
            $stdData = $this->user_provisioning_model->getstdDataByClass($classId, $admission_type);
            break;
        case 'name':
            $name = $_POST['name'];
            $stdData = $this->user_provisioning_model->getstdDataByName($name, $admission_type);
            break;
        case 'ad_no':
            $adNo = $_POST['ad_no'];
            $stdData = $this->user_provisioning_model->getStudentByAdNo($adNo, $admission_type);
            break;
        case 'phone_no':
            $phoneNo = $_POST['phone_no'];
            $stdData = $this->user_provisioning_model->getStudentByPhoneNo($phoneNo, $admission_type);
            break;
            case 'email':
            $email = $_POST['email'];
            $stdData = $this->user_provisioning_model->getStudentByEmail($email, $admission_type);
            break;
        }
        echo json_encode($stdData);
    }
}
