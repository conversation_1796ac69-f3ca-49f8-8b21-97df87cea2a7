<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('student_observation/menu');?>">Student Observation Menu</a></li>
  <li>Mass Observations</li>
</ul>

<div class="col-md-12 col_new_padding">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-9 pl-0" style="margin-top: 5px;">
          <h3 class="card-title panel_title_new_style_staff">
            <a id="back-anchor" class="back_anchor" href="<?php echo site_url('student_observation/menu') ?>" class="control-primary">
              <span class="fa fa-arrow-left"></span>
            </a> 
            Mass Observations
          </h3>
        </div>
      </div>
    </div>
    <div class="card-body">

      <div id="class_section_div" class="col-md-5 form-group " >
        <label class="control-label">Select Class</label>
            <select class="form-control" title="All" name="class_section_id" id="class_section_id">
              <?php foreach ($sectionList as $value) { ?>
                <option value="<?php echo $value->id; ?>"><?php echo $value->class_name; ?></option>
              <?php }?>
            </select>
        </div>

        <div id="category_type_div" class="col-md-5 form-group " >
        <label class="control-label">Select Category</label>
            <select class="form-control" title="All" name="category_type_id" id="category_type_id">
              <?php foreach ($category_types as $value) { ?>
                <option value="<?php echo $value->id; ?>"><?php echo $value->name; ?></option>
              <?php }?>
            </select>
        </div>

        <div id="capacity_type_div" class="col-md-5 form-group">
        <label class="control-label">Observed as </label>
            <select class="form-control" title="All" name="capacity_type_id" id="capacity_type_id">
              <?php foreach ($capacity_types as $value) { ?>
                <option value="<?php echo $value->id; ?>"><?php echo $value->name; ?></option>
              <?php }?>
            </select>
        </div>

      <div class="col-md-2 form-group pt-3">
          <button id="get_button" class="btn btn-primary mt-3" onclick="get_mass_observation_data()">Get</button>
      </div>
      <div id="observation_table"class="panel-body table-responsive hidden-xs">
        </div>
        <div id="submit_observation" class="panel-body table-responsive hidden-xs"></div>
    </div>
    
  </div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script type="text/javascript">
  
  let btn_status;
  let submit_btn;
  $(document).ready(function() {
    btn_status=document.getElementById("get_button");
    // btn_status.disabled = true;
    // btn_status.textContent="Please wait...";

    // get_mass_observation_data();
    // $('.select2').select2();

});

$("#section_id").change(function(){
  var section_id = $("#section_id").val();
  $.post("<?php echo site_url('student_observation/observation/get_student');?>",{section_id:section_id},function(data){
    var resultData=$.parseJSON(data);
    var output1='';
    var stdName = resultData.stdname;

       for (var i=0,j=stdName.length; i < j; i++) {
         output1+='<option value="'+stdName[i].id+'">'+stdName[i].std_name+' </option>';
       }
        $("#student_id").html(output1);
        $('#student_id').selectpicker('refresh');
    });
});

function get_mass_observation_data(){
    btn_status.disabled = true;
    btn_status.textContent="Please wait...";
  var class_section_id = $("#class_section_id").val();
  var category_type_id = $("#category_type_id").val();


  $("#observation_table").html(`<center><div class="spinner-border" role="status">
  <span class="sr-only">Loading...</span>
  </div></center>`);
  
  $.ajax({
    url: '<?php echo site_url('student_observation/observation/get_mass_observation_data'); ?>',
    type: 'post',
    data: {'category_type_id':category_type_id,'class_section_id':class_section_id} ,
    success: function(data) {
      btn_status.disabled = false;
      btn_status.textContent = "Get";
      parsed_data = $.parseJSON(data);
      var observation_data = parsed_data;
      // console.log(observation_data);
      html = construct_observation_table(observation_data.data,observation_data.category);
      $("#observation_table").html(html);
      

      $('#observation_type_table').DataTable( {
        ordering:false,
        paging : false,
        scrollY :'40vh',
        responsive: true,
				"language": {
					"search": "",
					"searchPlaceholder": "Enter Search..."
				},
				dom: 'lBfrtip',
				buttons: [
				]
        	});
          $('.select2').select2();
          $('.search-select-multiple').select2({
              dropdownAutoWidth: true,
              multiple: true,
              width: '100%',
              height: '30px',
              placeholder: "Select",
              allowClear: true
          });
          $('.select2-search__field').css('width', '100%');
          html2=`<center><button class="btn btn-success" id="submit_button" onclick="submit()">Submit</button>&nbsp;<button class="btn btn-warning" onclick="go_back()">Cancel & Go Back</button></center>`;
          $("#submit_observation").html(html2);

    },
    error: function (err) {
        console.log(err);
    }
  });
}

function go_back(){
  confirmed=confirm('Any unsaved data will be lost. Are you sure you want to go back?');
  if(confirmed){
    window.location.href="<?php  echo site_url('student_observation/menu'); ?>";
  }
}

function submit(){
 
    submit_button=document.getElementById("submit_button");
    var class_section_id=$('#class_section_id').val(); 
    var category_type_id=$('#category_type_id').val(); 
    var capacity_type_id=$('#capacity_type_id').val(); 
      
      submit_button.disabled = true;
  submit_button.textContent="Please wait...";

    var tableData = [];

    $('#observation_type_table tbody tr').each(function() {
      var row = $(this);
      var observation = row.find('select, input').val();
      if (observation) {
        var rowData = {
          class_section_id: class_section_id,
          admission_no:  row.data('id'),
          observation: observation,
          capacity_type_id:capacity_type_id,
          category_type_id:category_type_id,
        };
        tableData.push(rowData);
      }
    });
    if(tableData.length>0){
      Swal.fire({
            title: "Do you want to save the changes?",
            showDenyButton: false,
            showCancelButton: true,
            confirmButtonText: "Save",
          }).then((result) => {
            submit_button.disabled = false;
            submit_button.textContent="Submit";
            if (result.isConfirmed) {
              $.ajax({
                  url:  '<?php echo site_url('student_observation/observation/add_mass_observation_data'); ?>',
                  method: 'POST',
                  contentType: 'application/json',
                  data: JSON.stringify(tableData),
                  success: function(response) {
                    submit_button.disabled = false;
                    submit_button.textContent="Submit";
                    get_mass_observation_data();
                  },
                  error: function(error) {
                    alert('Error submitting data');
                  }
                });
              Swal.fire("Saved!", "", "success");
            } else if (result.isDenied) {
              Swal.fire("Changes are not saved", "", "info");
            }
          });
    }else{
      submit_button.disabled = false;
      submit_button.textContent="Submit";
      Swal.fire({
        icon: "error",
        title: "Please Select or Enter Atleast One Observation!",
      });
    }

  }

  function construct_observation_table(observation_data, category) {
  var html = '';

  if (observation_data.length === 0) {
    html += '<h4>No Observations Added</h4>';
  } else {
    html += `
      <table id="observation_type_table" class="table table-bordered display responsive">
        <thead>
          <tr style="white-space: nowrap">
            <th>#</th>
            <th >Student Name</th>
            <th>Class</th>
            <th>Admission Number</th>
            <th style="width:30%">Observation</th>
          </tr>
        </thead>
        <tbody>
    `;

    for (var i = 0; i < observation_data.length; i++) {
      var data = observation_data[i];
      html += `
        <tr data-id="${data.id}">
          <td>${i + 1}</td>
          <td>${data.std_name}</td>
          <td>${data.class_name_}</td>
          <td>${data.admission_no}</td>
          <td>
      `;

      if (category.length !== 0) {
        html += `<select class="form-control select2" id="student_observation_${data.id}" name="student_observation[]" multiple>`;
        for (var j = 0; j < category.length; j++) {
          html += `<option value="${category[j].name}">${category[j].name}</option>`;
        }
        html += `</select>`;
      } else {
        html += `<input type="text" class="form-control">`;
      }

      html += `</td></tr>`;
    }

    html += `</tbody></table>`;
  }

  // Append the generated HTML to the DOM
  $('#observation_table_container').html(html);

  // Initialize select2 for each select element
  for (var i = 0; i < observation_data.length; i++) {
    $('#student_observation_' + observation_data[i].id).select2({
      placeholder: 'Select Observation',
      allowClear: true
    });
  }

  return html;
}

document.getElementById('back-anchor').addEventListener('click', function(event) {
    event.preventDefault(); 
    var confirmBack = confirm("Any unsaved data will be lost. Are you sure you want to go back?");
    if (confirmBack) {
      window.location.href = this.href;
    }
  });


</script>
<style type="text/css">

    ::-webkit-scrollbar {
    width: 10px;
    }

    /* Track */
    ::-webkit-scrollbar-track {
    background: #f1f1f1;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
    background: #888;
    }

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
    background: #555;
    }
    
  .new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
  }
  .widthadjust{
  width:600px;
  margin:auto;
  }

  .modal {
    overflow-y:auto;
  }
  
  .modal-dialog{
    margin: 4% auto;
    width: 80%;
  }
  
  .modal-header{
    position:relative;
  }

  .close{
    font-size: 34px;
    color: red;
    position: absolute;
    right: 10px;
  }
  .dataTables_scrollBody{
    margin-top: -13px;
  }

  tr:hover{
    background: #F1EFEF;
  }

  .row_background_color
  {
    background:#7f848780;
  }

  .dt-buttons{
    font-size: 14px;
    background:"red";
  }
  .dt-buttons{
    margin-right: 3vh;

  }
  .form-horizontal .control-label{
    padding-top: 7px;
    margin-bottom: 0;
    text-align: right;

  }
  td>a>i{
		text-decoration: none;
		font-size: 16px;
		color: #191818;
		padding: 2px 5px;
	}

	.dataTables_wrapper .dt-buttons {
		float: right;
    
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5vh;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}

	.dataTables_filter{
		position:absolute;
		right: 20%;
	}

	.dt-buttons{
		position:absolute;
		right:2vh;
	}

  .validation-message {
      color: red;
      font-size: 12px;
    }

	@media only screen and (min-width:1404px){
		.dataTables_filter{
			position:absolute;
			right: 15%;
		}	
	}

	@media only screen and (min-width:1734px){
		.dataTables_filter{
			position:absolute;
			right: 5vh;
		}	
	}
  
</style>
