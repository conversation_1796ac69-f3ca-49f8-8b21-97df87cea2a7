<div class="success-container" style="margin-top: 3rem;">
    <div class="success-image">
        <?php $this->load->view('svg_icons/application_submitted.svg') ?>
    </div>

    <div class="application-details" id="hideheader"
        style="<?php echo ($receipts_view == 0)?'display:none':'display:block'; ?>">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1 class="application-number">
                    Details of <?= $final_preview->std_name ?>
                </h1>
                <h2 class="success-heading">
                    Applied for Class: <?= $final_preview->grade_applied_for ?>
                </h2>
            </div>
            <div>
                <button id="print" class="btn btn-danger" onclick="window.print()">
                    <span class="glyphicon glyphicon-print" aria-hidden="true"></span> Print Application
                </button>
            </div>
        </div>
    </div>

    <div class="message-box">
        <div id="nextsteps">
            <?php 
                $message = $config_val['final_description'];
                $message = str_replace('%%application_no%%', $final_preview->application_no, $message);
                echo $message;
            ?>
        </div>
    </div>

    <?php $view_path = APPPATH . 'views/admission/receipts/' . $school_short . '.php';
    if (empty($application_html) && file_exists($view_path)) { ?>
        <div id="print_application" style="display:none">
            <?php $this->load->view('admission/receipts/' . $school_short); ?>
        </div>
    <?php } ?>

    <div class="button-group text-center">
        <span style="font-size: 20px; font-weight: 500; display: block;">
            Application <?= $final_preview->application_no ?>
        </span>
        <span style="font-size: 20px; font-weight: 500;">Submitted Successfully</span>
        <p style="font-size: 14px; color: #666; text-align: center; max-width: 600px; margin: 0 auto;">
            Your application is successfully submitted to <?php echo $this->settings->getSetting('school_name') ?>.
            Please make a note of the application number and use it for any further correspondence with the school.
        </p>

        <div class="col-md-12 d-flex justify-content-center mt-3">
            <div class="d-flex flex-wrap justify-content-center align-items-center" style="gap: 10px;">
                <?php if ($final_preview->pdf_status || !empty($application_html)) { ?>
                <button class="btn btn-success btn-sm px-3 py-2" style="padding: 15.5px 32px;font-size:14px"
                    onclick="download_application_form('<?php echo $insert_id ?>','<?php echo $au_id ?>','<?php echo $admission_setting_id ?>')"
                    id="download_appl_btn">
                    Download Application
                </button>
                <?php } else if (!empty($application_html)) { ?>
                <button class="btn btn-success btn-sm px-3 py-2" style="padding: 15.5px 32px;font-size:14px"
                    onclick="generate_application_form('<?php echo $insert_id ?>','<?php echo $au_id ?>','<?php echo $admission_setting_id ?>')"
                    id="generate_appl_btn">
                    Generate Application
                </button>
                <?php } else { ?>
                <button class="btn btn-success btn-sm px-3 py-2" onclick="printDiv('print_application')" style="padding: 15.5px 32px;font-size:14px">
                    Print Application
                </button>
                <?php } ?>

                <?php if ($application_fee_status->payment_status == 'SUCCESS') { ?>
                <button class="btn btn-success btn-sm px-3 py-2" id="stu_print"
                    onclick="print_receipt(<?php echo $final_preview->id ?>)" style="padding: 15.5px 32px;font-size:14px">
                    Download Receipt
                </button>
                <?php } ?>

                <!-- Use div wrapper instead of form for consistent button alignment -->
                <div>
                    <form id="print-form" class="m-0" method="post" enctype="multipart/form-data">
                        <input type="hidden" id="afid" name="lastId"
                            value="<?php if(isset($insert_id)) echo $insert_id ?>">
                        <input type="hidden" id="admission_setting_id" name="admission_setting_id"
                            value="<?= $admission_setting_id; ?>">
                        <button type="button" class="btn btn-warning btn-sm px-3 py-2" onclick="goto_my_application()" style="padding: 15.5px 32px;font-size:14px">
                            Go to Home Page
                        </button>
                    </form>
                </div>
            </div>
        </div>

    </div>
</div>

    <?php
    function numberTowords($num = false)
    {
        $num = str_replace(array(',', ' '), '' , trim($num));
        if(! $num) {
            return false;
        }
        $num = (int) $num;
        $words = array();
        $list1 = array('', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten', 'eleven',
            'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen'
        );
        $list2 = array('', 'ten', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety', 'hundred');
        $list3 = array('', 'thousand', 'million', 'billion', 'trillion', 'quadrillion', 'quintillion', 'sextillion', 'septillion',
            'octillion', 'nonillion', 'decillion', 'undecillion', 'duodecillion', 'tredecillion', 'quattuordecillion',
            'quindecillion', 'sexdecillion', 'septendecillion', 'octodecillion', 'novemdecillion', 'vigintillion'
        );
        $num_length = strlen($num);
        $levels = (int) (($num_length + 2) / 3);
        $max_length = $levels * 3;
        $num = substr('00' . $num, -$max_length);
        $num_levels = str_split($num, 3);
        for ($i = 0; $i < count($num_levels); $i++) {
            $levels--;
            $hundreds = (int) ($num_levels[$i] / 100);
            $hundreds = ($hundreds ? ' ' . $list1[$hundreds] . ' hundred' . ' ' : '');
            $tens = (int) ($num_levels[$i] % 100);
            $singles = '';
            if ( $tens < 20 ) {
                $tens = ($tens ? ' ' . $list1[$tens] . ' ' : '' );
            } else {
                $tens = (int)($tens / 10);
                $tens = ' ' . $list2[$tens] . ' ';
                $singles = (int) ($num_levels[$i] % 10);
                $singles = ' ' . $list1[$singles] . ' ';
            }
            $words[] = $hundreds . $tens . $singles . ( ( $levels && ( int ) ( $num_levels[$i] ) ) ? ' ' . $list3[$levels] . ' ' : '' );
        } //end for loop
        $commas = count($words);
        if ($commas > 1) {
            $commas = $commas - 1;
        }
        return implode(' ', $words);
    }
    function construct_adr_string($line1,$area,$dist,$state,$country,$pincode){
        $address = '';
        if(!empty($line1)){
            $address .= $line1;
        }

        if(!empty($area)){
            $address .= ',' . $area. '<br>';
        }

        if(!empty($dist)){
            $address .= ', ' . $dist;
        }

        if(!empty($state)){
            $address .= ', ' . $state;
        }

        if(!empty($country)){
            $address .= ', ' . $country;
        }

        if(!empty($pincode)){
            $address .= ', Pin-' . $pincode;
        }

        return $address;

    }
?>

    <script type="text/javascript">
    function printDiv(divId) {
        var printContents = document.getElementById(divId).innerHTML;
        var originalContents = document.body.innerHTML;
        document.body.innerHTML = printContents;
        window.print();
        document.body.innerHTML = originalContents;
    }
    function goto_my_application() {
        url = '<?php echo site_url('admissions/home') ?>';
        $('#print-form').attr('action', url);
        $('#print-form').submit();
    }

    function print_receipt(admissionId) {
        $.ajax({
            url: '<?php echo site_url('admission_controller/print_receipt'); ?>',
            type: 'POST',
            data: {
                'admissionId': admissionId
            },
            success: function(data) {
                var successData = data.trim();
                if (successData != 0) {
                    // setInterval(function() { 
                    var downloadUrl = '<?php echo site_url('admission_controller/download_receipt/'); ?>' +
                        admissionId;
                    window.location.href = downloadUrl;
                    // }, 5000);

                } else {
                    generate_application_receipt(admissionId);
                }
            }
        })
    }

    function generate_application_receipt(admissionId) {
        $('#receipt_btn').attr('disabled', 'disabled').html('Please wait..');
        $.ajax({
            url: '<?php echo site_url('admission_controller/generate_print_receipt'); ?>',
            type: 'POST',
            data: {
                'admissionId': admissionId
            },
            success: function(data) {
                setInterval(function() {
                    $('#receipt_btn').removeAttr('disabled').html('Download Receipt');
                    location.reload();
                }, 5000);

                // print_receipt(admissionId);
            }
        })
    }

    function download_application_form(admissionId, au_id, admission_setting_id) {
        $('#download_appl_btn').attr('disabled', 'disabled').html('Please wait..');
        $.ajax({
            url: '<?php echo site_url('admission_controller/check_application_form'); ?>',
            type: 'POST',
            data: {
                'admissionId': admissionId
            },
            success: function(data) {
                var successData = data.trim();
                // console.log(successData)

                if (successData == 1) {
                    var downloadUrl =
                        '<?php echo site_url('admission_controller/download_application_form/'); ?>' +
                        admissionId;
                    window.location.href = downloadUrl;
                    $('#download_appl_btn').removeAttr('disabled').html('Download Application');
                } else {
                    var timesRun = 0;
                    var interval = setInterval(function() {
                        timesRun += 1;
                        if (timesRun === 60) {
                            clearInterval(interval);
                        }
                        generate_application_form(admissionId, au_id, admission_setting_id);
                    }, 5000);

                }
            }
        })
    }

    function generate_application_form(admissionId, au_id, admission_setting_id) {
        $('#generate_appl_btn').attr('disabled', 'disabled').html('Please wait..');
        $.ajax({
            url: '<?php echo site_url('admission_controller/gender_pdf_application_form'); ?>',
            type: 'POST',
            data: {
                'admissionId': admissionId,
                'au_id': au_id,
                'admission_setting_id': admission_setting_id
            },
            success: function(data) {

                var timesRun = 0;
                var interval = setInterval(function() {
                    timesRun += 1;
                    if (timesRun === 60) {
                        clearInterval(interval);
                    }
                    $('#generate_appl_btn').removeAttr('disabled').html('Generate');
                }, 5000);
                // download_application_form(admissionId, au_id, admission_setting_id);
                location.reload();
                // window['wait_timer_'] = setInterval(function() {  }, 5000);
                // setInterval(function() {
                //     $('#generate_appl_btn').removeAttr('disabled').html('Generate');
                // }, 60000);


                // download_application_form(admissionId);
            }
        })
    }
    </script>

    <style>
    .success-container {
        max-width: 800px;
        margin: 40px auto;
        text-align: center;
        padding: 30px;
        /* background: #fff; */
        /* border-radius: 10px; */
        /* box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); */
    }

    .success-image {
        max-width: 250px;
        margin: 0 auto 30px;
    }

    .success-image img {
        width: 100%;
        height: auto;
    }

    .application-details {
        margin-bottom: 30px;
    }

    .application-number {
        font-size: 24px;
        color: #333;
        margin-bottom: 10px;
    }

    .success-heading {
        font-size: 20px;
        color: #666;
        margin-bottom: 20px;
    }

    .message-box {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 30px;
        text-align: left;
    }

    .button-group {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }

    .btn {
        padding: 12px 25px;
        border-radius: 5px;
        font-size: 16px;
        border: none;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .btn i {
        font-size: 18px;
    }

    .btn-success {
        background-color: #28a745;
        color: white;
    }

    .btn-success:hover {
        background-color: #218838;
    }

    .btn-warning {
        background-color: #ffc107;
        color: #000;
        width: 100%;
        max-width: 300px;
    }

    .btn-warning:hover {
        background-color: #e0a800;
    }

    .home-btn {
        margin: 20px auto 0;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    @media (max-width: 768px) {
        .success-container {
            margin: 20px;
            padding: 20px;
        }

        .button-group {
            flex-direction: column;
        }

        .btn {
            width: 100%;
        }
    }
    </style>