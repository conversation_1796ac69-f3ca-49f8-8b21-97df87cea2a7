<style>
    @import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap");

    .report-header-icon,
    .report-header-title {
        color: #161327;
        font-family: Inter, sans-serif;
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 120%;
    }

    .progress-bar {
        background-color: #623CE7 !important;
        border-radius: 2px !important;
    }

    .progress {
        border-radius: 2px !important;
    }

    .grow-select {
        border: 1px solid #dcdcdc;
        background-color: #ffffff !important;
        color: #6b6b6b;
        appearance: none;
        background: url('https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/icons/chevron-down.svg') no-repeat center center/contain;
        background-repeat: no-repeat;
        background-position: right 10px center;
        background-size: 12px;
        cursor: pointer;
        max-width: 270px;
        border-radius: 4px;
    }

    .arrow-mark {
        appearance: none;
        border: 1px solid #dcdcdc;
        background-color: #ffffff !important;
        background: url('https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/icons/chevron-down.svg') no-repeat center center/contain;
        background-repeat: no-repeat;
        background-position: right 10px center;
        background-size: 12px;
    }

    #clear {
        background: transparent;
        color: #623CE7;
        border-radius: 5px;
        padding: 0.75rem 1.5rem;
        font-weight: bold;
        font-size: 15px;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 82px;
        height: 40px !important;
        border: 0px;
    }

    #clear:focus,
    #clear:active {
        box-shadow: 0 0 0 3px rgba(98, 60, 231, 0.3);
    }

    #clear:disabled {
        background: transparent;
        color: #A9A6A9;
        cursor: not-allowed;
    }

    #expbtns {
        all: unset;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.75em 1.5em;
        font-weight: bold;
        border: 2px solid #623CE7;
        color: #623CE7;
        background-color: transparent;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.2s ease;
        gap: 0.5em;
        border-radius: 4px;
    }

    #expbtns:hover {
        background-color: #EFECFD;
    }

    #expbtns:focus {
        outline: none;
        color: #623CE7;
        border: 2px solid #CEC3F8;
    }

    #expbtns:active {
        background-color: #CEC3F8;
        border: 2px solid #623CE7;
        color: #623CE7;
    }

    #expbtns:disabled {
        border-color: #A9A6A9;
        background-color: #DEDCDF;
        color: #c0c0c0;
        cursor: not-allowed;
    }

    #expbtns:disabled::after {
        color: #c0c0c0;
    }

    #expbtns2 {
        all: unset;
        background: #623CE7;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        font-weight: bold;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5em;
        border: 2px solid #623CE7;
    }

    #search {
        background: #623CE7;
        color: white;
        border: none;
        border-radius: 5px;
        padding: 0.75rem 1.5rem;
        font-weight: bold;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 120px;
        height: 40px !important;
    }

    #search:hover,
    #expbtns2:hover {
        background: #462BA4 !important;
    }

    #search:focus,
    #expbtns2:focus {
        background: #623CE7;
        outline: 3px solid #B7A5F4;
        outline-offset: 2px;
    }

    #search:active,
    #expbtns2:active {
        background: #36217F;
    }

    #search:disabled,
    #expbtns2:disabled {
        background: #DEDCDF !important;
        color: #A9A6A9 !important;
        cursor: not-allowed;
        border: 2px solid #a9a6a9 !important;
    }

    .search-box {
        display: flex;
        align-items: center;
        width: 260px;
        height: 40px;
        gap: 8px;
        border-radius: 8px;
        border: 1px solid #DEDCDF;
        background: #F9FAFB;
        box-shadow: 0 2px 8px 0 rgba(16, 24, 40, 0.07);
        padding: 0 14px 0 38px;
        position: relative;
        margin-right: 18px;
        margin-top: 6px;
        transition: box-shadow 0.2s, border-color 0.2s;
    }

    .search-box:focus-within {
        box-shadow: 0 4px 16px 0 rgba(98, 60, 231, 0.10);
        border-color: #623CE7;
    }

    .search-box .bi-search {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 20px;
        color: #d1d1e0;
        pointer-events: none;
        transition: color 0.2s;
    }

    .search-box:focus-within .bi-search {
        color: #623CE7;
    }

    /* Calender Btns */
    .btn-small.btn-primary {
        background: #623CE7 !important;
        border-color: #623CE7 !important;
        color: #fff !important;
    }

    .cancelBtn.btn-small {
        color: #623CE7 !important;
        background: transparent !important;
        border: none !important;
    }

    .input-search {
        font-family: "Inter", sans-serif !important;
        flex: 1;
        border: none;
        background: transparent;
        font-size: 15px;
        color: #161327;
        outline: none;
        line-height: 1.5;
        padding: 0;
    }

    .input-search::placeholder {
        color: #b3b3c6;
        font-size: 14px;
        font-weight: 400;
    }

    /* Container style */
    .dataTables_filter.custom-search-box {
        float: right;
        margin-bottom: 5px;
        margin-right: -17px;
    }

    /* Search box */
    .dataTables_filter .search-box {
        display: flex;
        align-items: center;
        width: 260px;
        height: 40px;
        gap: 8px;
        border-radius: 8px;
        border: 1px solid #DEDCDF;
        background: #F9FAFB;
        box-shadow: 0 2px 8px rgba(16, 24, 40, 0.07);
        padding: 0 14px 0 38px;
        position: relative;
        transition: box-shadow 0.2s, border-color 0.2s;
    }

    .dataTables_filter .search-box:focus-within {
        box-shadow: 0 4px 16px rgba(98, 60, 231, 0.10);
        border-color: #623CE7;
    }

    /* Icon inside input */
    .dataTables_filter .search-box .bi-search {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 20px;
        color: #d1d1e0;
        pointer-events: none;
        transition: color 0.2s;
    }

    /* Change icon color on focus */
    .dataTables_filter .search-box:focus-within .bi-search {
        color: #623CE7;
    }

    /* Input field */
    .dataTables_filter .search-box input {
        flex: 1;
        border: none;
        background: transparent;
        font-size: 15px;
        color: #161327;
        outline: none;
        line-height: 1.5;
        padding: 0;
        font-family: "Inter", sans-serif !important;
    }

    .dataTables_filter .search-box input::placeholder {
        color: #b3b3c6;
        font-size: 14px;
        font-weight: 400;
    }


    table {
        font-family: "Inter", sans-serif !important;
    }

    table {
        font-family: "Inter", sans-serif !important;
        width: 100% !important;
        border-collapse: collapse !important;
        background-color: #ffffff !important;
        /* border-radius: 1.5rem !important; */
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06) !important;
        opacity: 1 !important;
        transition: none !important;
    }

    table thead th {
        /* position: sticky !important; */
        top: 0 !important;
        background-color: #EFECFD !important;
        color: #161327 !important;
        font-size: 12px !important;
        font-weight: 600 !important;
        z-index: 10 !important;
        text-align: left !important;
        padding: 12px 16px !important;
        border-bottom: 1px solid #e5e7eb !important;
        border-top: none !important;
    }

    table>thead>tr>th {
        background: #EFECFD;
        color: #161327;
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
    }

    table th,
    table td {
        padding: 10px 14px !important;
        border-bottom: 1px solid #e5e7eb !important;
        font-size: 11px !important;
        font-weight: 400 !important;
        background: none !important;
    }

    table tbody tr:nth-child(even) {
        background-color: #FBFBFB !important;
    }

    table tbody tr:hover {
        background-color: #F4F4F5 !important;
    }

    table tfoot tr {
        background-color: #f3f4f6 !important;
        font-weight: 500 !important;
    }

    .table-bordered>tbody>tr>td,
    .table-bordered>thead>tr>th {
        border: 0px !important;
    }

    .panel_heading_new_style_staff_border>.row {
        border-bottom: 0px !important;
    }

    .custom-checkbox-label {
        display: flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
        font-family: 'Inter', sans-serif;
    }

    .custom-checkbox {
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        width: 18px;
        height: 18px;
        border-radius: 2px;
        border: 2px solid #B0B1B1;
        background-color: white;
        position: relative;
        cursor: pointer;
        flex-shrink: 0;
        aspect-ratio: 1/1;
    }

    .custom-checkbox:checked {
        background-color: #623CE7;
        border-color: #623CE7;
    }

    .custom-checkbox:checked::after {
        content: '';
        position: absolute;
        top: 35%;
        left: 50%;
        width: 6px;
        height: 10px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: translate(-50%, -50%) rotate(45deg);
    }

    .custom-checkbox-label span {
        color: #161327;
        font-size: 15px;
        font-weight: 500;
        line-height: normal;
    }

    .custom-radio-label {
        display: flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
        font-family: 'Inter', sans-serif;
    }

    .custom-radio {
        /* Use default radio button appearance with custom color */
        width: 16px;
        height: 16px;
        cursor: pointer;
        flex-shrink: 0;
        accent-color: #623CE7;
    }

    .custom-radio-label span {
        color: #161327;
        font-size: 15px;
        font-weight: 500;
        line-height: normal;
    }


    .form-control {
        height: 40px;
    }

    .dropdown-toggle.selectpicker {
        height: 40px !important;
    }

    .bootstrap-select>.dropdown-toggle::after,
    .dropdown-toggle::after {
        content: "";
        display: none !important;
        border: none !important;
        background: none !important;
    }

    .bootstrap-select.btn-group .btn .caret {
        display: none !important;
    }

    .bootstrap-select>.dropdown-toggle::after {
        content: "";
        display: inline-block;
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        width: 1em;
        height: 1em;
        background: none;
    }

    .bootstrap-select>.dropdown-toggle {
        position: relative;
        background: none !important;
    }

    .bootstrap-select>.dropdown-toggle::before {
        content: "";
        display: inline-block;
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        width: 1em;
        height: 1em;
        z-index: 2;
        background-size: 12px;
        background: url('https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/icons/chevron-down.svg') no-repeat center center/contain;
    }

    .fee-type-error .bootstrap-select>.dropdown-toggle::before {
        background: url('https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/icons/chevron-down.svg') no-repeat center center/contain;
        filter: invert(23%) sepia(97%) saturate(7492%) hue-rotate(353deg) brightness(97%) contrast(108%);
    }

    .dataTables_length,
    .dataTables_filter {
        border-bottom: 0px !important;
    }

    #filtersContainer {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        transform-origin: top;
        overflow: visible;
        will-change: transform, opacity, max-height;
        position: relative;
        z-index: 1;
    }

    #filtersContainer.hiding {
        opacity: 0;
        transform: scaleY(0);
        max-height: 0 !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        margin-top: 0 !important;
        margin-bottom: 0 !important;
        border: none !important;
        overflow: hidden;
    }

    #filterToggleIcon {
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    #filterToggleIcon.rotated {
        transform: rotate(-90deg);
    }

    .card-body {
        transition: height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    #filtersContainer .bootstrap-select .dropdown-menu {
        z-index: 9999 !important;
    }

    #filtersContainer .bootstrap-select.open {
        z-index: 9998 !important;
    }

    .bootstrap-select>.dropdown-toggle {
        z-index: 1;
    }

    #branches {
        height: 30px;
    }

    .modal-backdrop.fade {
        z-index: -1;
    }

    /* === Date Range Picker Calendar: Styled Same as tables === */

    .daterangepicker table {
        font-family: "Inter", sans-serif !important;
        width: 100% !important;
        border-collapse: collapse !important;
        background-color: #ffffff !important;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06) !important;
        font-size: 12px !important;
    }

    .daterangepicker th,
    .daterangepicker td {
        padding: 8px 10px !important;
        font-size: 11px !important;
        font-weight: 400 !important;
        color: #161327 !important;
        text-align: center;
        border: none !important;
        background: none !important;
        border-radius: 0px;
    }

    .daterangepicker thead th {
        background-color: #EFECFD !important;
        color: #161327 !important;
        font-weight: 600 !important;
    }

    .daterangepicker td.available:hover,
    .daterangepicker th.available:hover {
        background-color: #F4F4F5 !important;
        cursor: pointer;
    }

    .daterangepicker td.active,
    .daterangepicker td.active:hover {
        background-color: #6D28D9 !important;
        color: white !important;
        font-weight: 600 !important;
    }

    .daterangepicker td.in-range {
        background-color: #EFECFD !important;
        color: #161327 !important;
    }

    .daterangepicker td.start-date,
    .daterangepicker td.end-date {
        background-color: #7C3AED !important;
        color: white !important;
        font-weight: 600 !important;
    }

    .daterangepicker td.off {
        color: #9CA3AF !important;
        pointer-events: none;
        opacity: 0.5;
    }

    .daterangepicker .ranges li.active,
    .daterangepicker .ranges li:hover {
        background: #623ce7;
        border: 1px solid #623ce7;
        color: #fff;
    }

    .form-select {
        height: 40px;
    }

    .buttons-print,
    .buttons-excel,
    .buttons-colvis {
        background-color: #f9f7fe !important;
        border: 0px #f9f7fe !important;
    }

    .dt-search-1 {
        all: unset;

    }

    /* Custom Checkbox Styles for DataTable Column Visibility */
    .dt-button-collection .dt-button {
        display: flex !important;
        align-items: center !important;
        cursor: pointer !important;
        font-family: 'Inter', sans-serif !important;
        padding: 8px 12px !important;
        border: none !important;
        background: none !important;
        text-align: left !important;
        width: 100% !important;
        position: relative !important;
    }

    .dt-button-collection .dt-button:hover {
        background-color: #f8f9fa !important;
    }

    /* Hide any default input checkboxes if they exist */
    .dt-button-collection .dt-button input[type="checkbox"] {
        display: none !important;
    }

    .dt-button-collection .dt-button input[type="checkbox"]:checked {
        background-color: #623CE7 !important;
        border-color: #623CE7 !important;
    }

    .dt-button-collection .dt-button input[type="checkbox"]:checked::after {
        content: '' !important;
        position: absolute !important;
        top: 16% !important;
        left: 80% !important;
        width: 4px !important;
        height: 8px !important;
        border: solid white !important;
        border-width: 0 2px 2px 0 !important;
        transform: translate(-50%, -50%) rotate(45deg) !important;
    }

    .dt-button-collection .dt-button-active:before {
        content: '' !important;
        display: inline-block !important;
        width: 12px !important;
        height: 12px !important;
        border-radius: 2px !important;
        border: 2px solid #623CE7 !important;
        background-color: #623CE7 !important;
        position: relative !important;
        margin-right: 10px !important;
        flex-shrink: 0 !important;
        vertical-align: middle !important;
    }

    .dt-button-collection .dt-button-active:after {
        content: '' !important;
        position: absolute !important;
        left: 16px !important;
        top: 80% !important;
        width: 4px !important;
        height: 8px !important;
        border: solid white !important;
        border-width: 0 2px 2px 0 !important;
        transform: translateY(-50%) rotate(45deg) !important;
        z-index: 1 !important;
    }

    /* Style inactive column buttons */
    .dt-button-collection .dt-button:not(.dt-button-active):before {
        content: '' !important;
        display: inline-block !important;
        width: 12px !important;
        height: 12px !important;
        border-radius: 2px !important;
        border: 2px solid #B0B1B1 !important;
        background-color: white !important;
        position: relative !important;
        margin-right: 10px !important;
        flex-shrink: 0 !important;
        vertical-align: middle !important;
    }

    .dt-button-collection .dt-button span {
        color: #161327 !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        line-height: normal !important;
    }

    .dt-button-collection {
        border-radius: 8px !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        border: 1px solid #e9ecef !important;
        padding: 8px 0 !important;
        min-width: 200px !important;
        max-height: 300px !important;
        overflow-y: auto !important;
    }

    /* Custom scrollbar for column dropdown */
    .dt-button-collection::-webkit-scrollbar {
        width: 6px !important;
    }

    .dt-button-collection::-webkit-scrollbar-track {
        background: #f1f1f1 !important;
        border-radius: 3px !important;
    }

    .dt-button-collection::-webkit-scrollbar-thumb {
        background: #623CE7 !important;
        border-radius: 3px !important;
        transition: background 0.3s ease !important;
    }

    .dt-button-collection::-webkit-scrollbar-thumb:hover {
        background: #4f2db8 !important;
    }

    .dt-button-collection {
        scrollbar-width: thin !important;
        scrollbar-color: #A09EAE #f1f1f1 !important;
    }

    /* Pagination */
    .pagination,
    .page-item,
    .dataTables_paginate {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }

    .pagination .page-link,
    .page-item .page-link,
    .dataTables_paginate .paginate_button {
        text-decoration: none;
        border: none;
        background-color: transparent;
        font-weight: 500;
        font-size: 1.2rem;
        line-height: 1.6;
        letter-spacing: 0.3px;
        color: black;
        border-radius: 5px;
        padding: 0.6rem 1rem;
        transition: all 0.2s ease-in-out;
        -webkit-font-smoothing: antialiased;
    }

    .page-item.active .page-link,
    .page-item.active .page-link:focus,
    .page-item.active .page-link:active,
    .page-item.active .page-link:hover,
    .dataTables_paginate .paginate_button.current,
    .dataTables_paginate .paginate_button.current:focus,
    .dataTables_paginate .paginate_button.current:active,
    .dataTables_paginate .paginate_button.current:hover {
        background-color: #623CE7 !important;
        color: white !important;
        border-radius: 2px;
    }

    .page-item .page-link:hover,
    .dataTables_paginate .paginate_button:hover {
        background-color: #462BA4 !important;
        color: white !important;
        border-radius: 2px;
    }

    .page-item .page-link:focus,
    .dataTables_paginate .paginate_button:focus {
        background-color: #623CE7 !important;
        color: white !important;
        outline: none !important;
        box-shadow: 0 0 0 0.25rem rgba(137, 112, 241, 0.4) !important;
    }

    .page-item .page-link:active,
    .dataTables_paginate .paginate_button:active {
        background-color: #36217F !important;
        color: white !important;
        border-radius: 2px;
    }

    .page-item.disabled .page-link,
    .dataTables_paginate .paginate_button.disabled {
        color: #A9A6A9 !important;
        background-color: transparent !important;
        cursor: default !important;
        pointer-events: none !important;
    }

    .page-item.disabled .page-link.next,
    .page-item.disabled .page-link.previous,
    .page-item.disabled .page-link.first,
    .page-item.disabled .page-link.last,
    .page-item.disabled .page-link.nav-arrow,
    .dataTables_paginate .paginate_button.disabled.first,
    .dataTables_paginate .paginate_button.disabled.previous,
    .dataTables_paginate .paginate_button.disabled.next,
    .dataTables_paginate .paginate_button.disabled.last {
        color: #A9A6A9 !important;
    }

    .page-item .page-link.next,
    .page-item .page-link.previous,
    .page-item .page-link.first,
    .page-item .page-link.last,
    .page-item .page-link.nav-arrow,
    .dataTables_paginate .paginate_button.first,
    .dataTables_paginate .paginate_button.previous,
    .dataTables_paginate .paginate_button.next,
    .dataTables_paginate .paginate_button.last {
        color: #623CE7 !important;
    }

    .page-item:not(.disabled) .page-link.next:hover,
    .page-item:not(.disabled) .page-link.previous:hover,
    .page-item:not(.disabled) .page-link.first:hover,
    .page-item:not(.disabled) .page-link.last:hover,
    .page-item:not(.disabled) .page-link.nav-arrow:hover,
    .dataTables_paginate .paginate_button.first:hover:not(.disabled),
    .dataTables_paginate .paginate_button.previous:hover:not(.disabled),
    .dataTables_paginate .paginate_button.next:hover:not(.disabled),
    .dataTables_paginate .paginate_button.last:hover:not(.disabled) {
        background-color: #462BA4 !important;
        color: white !important;
        border-radius: 2px;
    }

    .page-item .page-link.next:focus,
    .page-item .page-link.previous:focus,
    .page-item .page-link.first:focus,
    .page-item .page-link.last:focus,
    .page-item .page-link.nav-arrow:focus,
    .page-item .page-link.next:active,
    .page-item .page-link.previous:active,
    .page-item .page-link.first:active,
    .page-item .page-link.last:active,
    .page-item .page-link.nav-arrow:active {
        background-color: #623CE7 !important;
        color: white !important;
        border-radius: 2px;
    }

    .dataTables_wrapper .dataTables_info {
        text-align: left;
    }

    .dataTables_wrapper .dataTables_paginate {
        text-align: right !important;
    }

    .dataTables_wrapper .row:last-child {
        margin-top: 10px;
    }

    /* Pagination Count */

    .dt-info {
        display: block;
        font-family: 'Inter', sans-serif;
        font-size: 14px;
        font-weight: 400;
        color: #161327;

        padding: 8px 0;
        text-align: left;
    }


    /* Pagination Length Styles */
    select[id^="dt-length-"] {
        color: #161327;
        font-family: 'Inter', sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;

        width: 88px !important;
        height: 32px !important;
        border-radius: 4px;
        border: 1px solid #DEDCDF;
        background-color: #FFF;

        box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
        padding: 4px 32px 4px 8px;

        appearance: none;
        background: url('https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/icons/chevron-down.svg') no-repeat right 8px center / 12px;
    }

    label[for^="dt-length-"] {
        font-family: 'Inter', sans-serif;
        font-size: 14px;
        font-weight: 400;
        color: #161327;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    select[id^="dt-length-"]:focus {
        box-shadow: none !important;
        border: 1px solid #DEDCDF !important;
    }

    .card-header.panel_heading_new_style_staff_border.border-0 {
        margin-bottom: 20px;
    }

    .col-md-12.mb-10 {
        margin-bottom: 10px;
    }

    /* New Search Box Can Remove Old One above Later */
    input[type="search"].input-search {
        font-family: "Inter", sans-serif !important;
        flex: 1;
        border: none;
        background: transparent;
        font-size: 15px;
        color: #161327;
        outline: none;
        line-height: 1.5;
        padding: 0;
        box-shadow: none;
    }

    input[type="search"].input-search::placeholder {
        color: #b3b3c6;
        font-size: 14px;
        font-weight: 400;
    }

    .search-box {
        display: flex;
        align-items: center;
        width: 263px;
        height: 40px;
        gap: 8px;
        border-radius: 8px;
        border: 1px solid #DEDCDF;
        background: #F9FAFB;
        box-shadow: 0 2px 8px rgba(16, 24, 40, 0.07);
        padding: 0 14px 0 38px;
        position: relative;
        transition: box-shadow 0.2s, border-color 0.2s;
        margin-right: -5px;
    }

    .search-box:focus-within {
        box-shadow: 0 4px 16px rgba(98, 60, 231, 0.10);
        border-color: #623CE7;
    }

    .search-box .bi-search {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 20px;
        color: #d1d1e0;
        pointer-events: none;
        transition: color 0.2s;
    }

    .search-box:focus-within .bi-search {
        color: #623CE7;
    }

    #academic_year {
        height: 30px;
        margin-top: 11px;
    }

    /* Summary Table Styles */
    .container-fluid {
        padding-left: 0;
        padding-right: 0;
    }

    .summary-card {
        width: 100%;
        background: #CEC3F8;
        border-radius: 6px;
        padding: 0;
        margin-bottom: 2rem;
        overflow: hidden;
    }

    .summary-header {
        padding: 1rem;
        text-align: center;
        background: #CEC3F8;
    }

    .summary-wrapper {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: stretch;
        gap: 0;
    }

    .summary-table {
        flex: 1;
        min-width: 300px;
        background: white;
        border-right: none;
    }

    .summary-table:last-child {
        border-right: 1px solid transparent;
    }

    .summary-table table {
        width: 100%;
        height: 100%;
        table-layout: fixed;
        border-collapse: separate !important;
        border-spacing: 0 !important;
        border: none !important;
        background: white;
    }

    .summary-table th,
    .summary-table td {
        padding: 14px 16px;
        vertical-align: middle;
        word-wrap: break-word;
        border: none !important;
    }

    .summary-table thead th {
        font-weight: bold;
        font-size: 14px;
        border-bottom: none !important;
        color: #000;
    }

    .summary-table tfoot th {
        background-color: #EFECFD !important;
        font-weight: bold;
        color: #000;
    }

    .summary-table tbody tr:nth-child(odd) {
        background-color: #FBFBFB;
    }

    /* .summary-table tbody tr:nth-child(even) {
        background-color: white;
    } */

    .summary-table:last-child table {
        border-radius: 0 0 6px 0;
    }

    .summary-table:first-child table {
        border-radius: 0 0 0 6px;
    }

    .summary-card .custom-title {
        color: #161327;
        font-family: 'Inter', sans-serif;
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        text-transform: capitalize;
    }

    .summary-card .custom-dates {
        color: #161327;
        font-family: 'Inter', sans-serif;
        font-size: 18px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        text-transform: capitalize;
    }
</style>