<?php 
	class Payment_controller extends CI_Controller{

		function __construct(){
			parent::__construct();
			$this->load->model('payment_model');
			$this->load->library('payment');
			$this->load->library('payment_payu');
			$this->load->library('payment_jodo');
			$this->load->library('payment_application');
		}

		public function ne_done(){
			//Call the payment callback handler
			$result = $this->payment->payment_callback_ne_handler($_POST);

			$data = $result;
			$data['main_content'] = 'online_payment/payment_done';
			$this->load->view('inc/template', $data);	  
	
			// redirect(site_url('sms/sms_loaded/').$_POST['udf1'].'/'.$_POST['response_code'].'/'.$msg);
			// redirect('parent_controller/parent_initiative'); //to do change function
		}

		public function school_done(){
			// trigger_error("School done called");
			// trigger_error("API response from TraknPay: ");
			// trigger_error(json_encode($_POST));
			//Call the payment callback handler
			$result = $this->payment->payment_callback_school_handler($_POST);

			// trigger_error("Payment Callback handler called");
			// trigger_error(json_encode($result));

			$data = $result;
			if (!$this->ion_auth->logged_in()) {
				// trigger_error('Session data is not set. Setting now!');
			 	$sid = $_POST['udf4'];
		 		$query = $this->db->query("select data from ci_sessions where id = '$sid' ");
   				session_decode($query->row()->data);
			}
			// trigger_error("Session user data - Fees");
		  
			// $this->session->set_userdata('loginstatus', $data['mobile_number']);

			//trigger_error(json_encode($this->session->userdata()));

			// $data['main_content'] = 'online_payment/payment_done';
			$this->load->view('online_payment/payment_done', $data);
		}

		

		public function school_application_fee_done() {
			// trigger_error("School Application fee done called");
			// trigger_error(json_encode($_POST));

			//Call the payment callback handler
			$result = $this->payment_application->payment_callback_school_handler($_POST);
			// echo '<pre>asdf'; print_r($result);die();
			// trigger_error(json_encode($result));

			$data = $result;
		 	if (!isset($_SESSION['loginstatus'])){
			 	// trigger_error('Session data is not set. Setting now!');
			 	$sid = $_POST['udf4'];
		 		$query = $this->db->query("select data from ci_sessions where id = '$sid' ");
   				session_decode($query->row()->data);
		 	}
			// trigger_error("Session user data - Application");
		  
			// $this->session->set_userdata('loginstatus', $data['mobile_number']);

			// trigger_error(json_encode($this->session->userdata()));

			$this->load->view('online_payment/payment_done', $data);
			
			// $data['main_content'] = 'online_payment/payment_done';
			// $this->load->view('admission/inc/template', $data);
		}

		public function jodo_school_callback() {
			// $order_id = 'gAAAAAAAAAAAcy_NwaNloWi3ereDc2Fo8tZEAXJZCKS22GAY_hg-sFVO42zNw4a8J1cuShAxaQavUo-WbXUWeh0gr1ErhN-2Tw==';
			$order_id = $_GET['order_id'];
			$payment_data = $this->payment_jodo->get_payment_details_by_order_wise($order_id);
			$identifier = json_decode($payment_data->data->custom_identifier);
			if (!$this->ion_auth->logged_in()) {
				$sid = $identifier->udf4; //UDF4 is session_id
				$query = $this->db->query("select data from ci_sessions where id = '$sid' ");
				session_decode($query->row()->data);
			}
			//Call the success payment callback handler
			$data = $this->payment_jodo->payment_callback_school_handler($payment_data);
			$data['main_content'] = 'online_payment/payment_done';
			$this->load->view('inc/template', $data);
		}

		public function jodo_school_callback_flex() {
			// trigger_error("API response from jodo flex: ");
      		// trigger_error(json_encode($_POST));
		}


		public function payu_school_success_callback() {
			if (!$this->ion_auth->logged_in()) {
				$sid = $_POST['udf4']; //UDF4 is session_id
				$query = $this->db->query("select data from ci_sessions where id = '$sid' ");
				session_decode($query->row()->data);
			}

			//Call the success payment callback handler
			$data = $this->payment_payu->payment_callback_school_handler($_POST);

			$data['main_content'] = 'online_payment/payment_done';
			$this->load->view('inc/template', $data);
		}

		public function payu_school_failure_callback() {
			if (!$this->ion_auth->logged_in()) {
				$sid = $_POST['udf4'];
				$query = $this->db->query("select data from ci_sessions where id = '$sid' ");
				session_decode($query->row()->data);
			}

			//Call the failure payment callback handler
			$data = $this->payment_payu->payment_callback_school_failure_handler($_POST);
			
			$data['main_content'] = 'online_payment/payu_school_failure_callback_page';
			$this->load->view('inc/template', $data);
		}

		public function payu_school_failure_callback_new_admission_flow() {
			if (!$this->ion_auth->logged_in()) {
				$sid = $_POST['udf4'];
				$query = $this->db->query("select data from ci_sessions where id = '$sid' ");
				session_decode($query->row()->data);
			}

			//Call the failure payment callback handler
			$data = $this->payment_payu->payment_callback_school_failure_handler($_POST);
			
			// $data['main_content'] = 'online_payment/payu_school_failure_callback_page';
			$this->load->view('parent/feesv2_installments/newAdmissionFlow/online_payment_payu_school_failure_callback_page');
		}

		public function payu_school_cancel_callback() {
			if (!$this->ion_auth->logged_in()) {
				$sid = $_POST['udf4'];
				$query = $this->db->query("select data from ci_sessions where id = '$sid' ");
				session_decode($query->row()->data);
			}

			$data['main_content'] = 'online_payment/payu_school_cancel_callback_page';
			$this->load->view('inc/template', $data);
		}

		public function payu_school_cancel_callback_new_admission_flow(){
			if (!$this->ion_auth->logged_in()) {
				$sid = $_POST['udf4'];
				$query = $this->db->query("select data from ci_sessions where id = '$sid' ");
				session_decode($query->row()->data);
			}

			// $data['main_content'] = 'online_payment/payu_school_cancel_callback_page';
			$this->load->view('parent/feesv2_installments/newAdmissionFlow/online_payment_payu_school_cancel_callback_page');
		}

		public function recon_callback() {
			// trigger_error('Recon URL called');
			// trigger_error(json_encode($_POST));
			$this->payment->recon_handler($_POST);
		}

		public function get_settlementdata(){
			if (!$this->ion_auth->logged_in()) {
				redirect('auth/login', 'refresh');
			}
			if (!$this->authorization->isModuleEnabled('FEESV2') || !$this->authorization->isAuthorized('FEESV2.VIEW_ONLINE_TX_REPORT')) {
				redirect('dashboard', 'redirect');
			}

			$date = $_POST['date'];
			// $date = '2019-03-02'; //remove this line
			$result = $this->payment->get_settlementdata($date);
			print_r($result); 
		}

	  public function online_transaction_report(){
			if (!$this->ion_auth->logged_in()) {
				redirect('auth/login', 'refresh');
			}
			if (!$this->authorization->isModuleEnabled('FEESV2') || !$this->authorization->isAuthorized('FEESV2.VIEW_ONLINE_TX_REPORT')) {
				redirect('dashboard', 'redirect');
			}

			// $data['transaction_list'] = $this->payment_model->get_online_transaction_data();	
			$data['main_content'] = 'feesv2/online_transaction_report/index';
			$this->load->view('inc/template', $data);
		}

		public function getTransactionDetailsByStudent(){
		
		

		$mode = $_POST['mode'];
    switch ($mode) {
      case 'std_name':
				$name = $_POST['name'];
				$stdData = $this->payment_model->getTransactionDetailsByStudent($name);
				// $data->stdData = $stdData;
				// $data->isAdmin = $this->authorization->isSuperAdmin();
				echo json_encode($stdData);
        break;
      case 'order_id':
				$order = $_POST['order'];
				$transactionData = $this->payment_model->getTransactionDetailsByOrderId($order);
				echo json_encode($transactionData);
        break;
      case 'transaction_id':
				$transaction = $_POST['transaction'];
				$transactionData = $this->payment_model->getTransactionDetailsByTransactionId($transaction);
				echo json_encode($transactionData);
        break;
      case 'init_date':
				$date = $_POST['date'];
				$date  = date('Y-m-d', strtotime($date));
				$transactionData = $this->payment_model->getTransactionDetailsByDate($date);
				echo json_encode($transactionData);
        break;
			}
		}

		// public function getTransactionDetailsByOrderId(){
			
		// }

		// public function getTransactionDetailsByTransactionId(){
			
		// }
		// public function getTransactionDetailsByDate(){
			
		// }

		public function online_transaction_detail() {
			$opm_id = $_POST['opm_id'];
			$data['tx'] = $this->payment_model->get_transaction_detail($opm_id);
			$data['main_content'] = 'feesv2/online_transaction_report/detail';
			$this->load->view('inc/template', $data);
		}
	
	  public function online_settlement_report() {
			if (!$this->ion_auth->logged_in()) {
				redirect('auth/login', 'refresh');
			}
			// if (!$this->authorization->isModuleEnabled('FEESV2') || !$this->authorization->isAuthorized('FEESV2.VIEW_ONLINE_TX_REPORT')) {
			// 	redirect('dashboard', 'redirect');
			// }
			$data['transaction_dates'] = $this->payment_model->get_trans_dates();
			// echo "<pre>"; print_r($data); die();
			$data['main_content'] = 'feesv2/settlement/index';
			$this->load->view('inc/template_fee', $data);
		}

		public function online_settlement_report_consolidated(){

			if (!$this->ion_auth->logged_in()) {
				redirect('auth/login', 'refresh');
			}

			if (!$this->authorization->isModuleEnabled('FEESV2') || !$this->authorization->isAuthorized('FEESV2.OVERVIEW_ONLINE_SETTLEMENT_REPORT')) {
				redirect('dashboard', 'redirect');
			}

			$data['main_content'] = 'feesv2/settlement/overview_settlement';
			$this->load->view('inc/template', $data);
		}

		public function online_refund_report(){
			$data['main_content'] = 'feesv2/online_transaction_report/refund';
			$this->load->view('inc/template', $data);
		}

		public function get_online_refund_transactions(){
			$mode = $this->input->post('type');
			$getValue = $this->input->post('getValue');
			$result = $this->payment_model->get_online_refund_transaction_data($mode, $getValue);
			echo json_encode($result);
		}


		public function get_daily_transactions(){
			$date = date("Y-m-d", strtotime($_POST['date']));
			$transaction_list = $this->payment_model->get_daily_transactions_model($date);
			$data['trans_amount'] = 0;
			$data['isConfirmed'] = 0;
			$data['transaction_list'] = array();
			$data['component_total'] = array();

			foreach ($transaction_list as $key => $val) {
				if(!array_key_exists($val->acad_year, $data['transaction_list'])) {
					$data['transaction_list'][$val->acad_year] = array();
				}
				if(!array_key_exists($val->id, $data['transaction_list'][$val->acad_year])) {
					$data['trans_amount'] += $val->amount;
					$data['transaction_list'][$val->acad_year][$val->id] = array();
					$data['transaction_list'][$val->acad_year][$val->id]['stdName'] = $val->stdName." (". $val->classSection.")";
					$data['transaction_list'][$val->acad_year][$val->id]['source'] = $val->source;
					$data['transaction_list'][$val->acad_year][$val->id]['admission_no'] = $val->admission_no;
					$data['transaction_list'][$val->acad_year][$val->id]['amount'] = $val->amount;
					$data['transaction_list'][$val->acad_year][$val->id]['concession'] = $val->concession_amount;
					$data['transaction_list'][$val->acad_year][$val->id]['fine_amount'] = $val->fine_amount;
					$data['transaction_list'][$val->acad_year][$val->id]['adjustment_amount'] = $val->adjustment_amount;
					$data['transaction_list'][$val->acad_year][$val->id]['blueprint_name'] = $val->blueprint_name;
					$data['transaction_list'][$val->acad_year][$val->id]['acad_yearId'] = $val->acad_year_id;
					$data['transaction_list'][$val->acad_year][$val->id]['acad_year'] = $val->acad_year;
					$data['transaction_list'][$val->acad_year][$val->id]['installment'] = array();
				}
				if(!array_key_exists($val->insId, $data['transaction_list'][$val->acad_year][$val->id]['installment'])) {
					$data['transaction_list'][$val->acad_year][$val->id]['installment'][$val->insId] = "<strong>".$val->installment_name."</strong>";

					$data['transaction_list'][$val->acad_year][$val->id]['components'][$val->insId] = array();

				}
				if(!array_key_exists($val->acad_year, $data['component_total'])) {
					$data['component_total'][$val->acad_year] = array();
				}
				if(!array_key_exists($val->component_name, $data['component_total'][$val->acad_year])) {
					$data['component_total'][$val->acad_year][$val->component_name] = 0;
				}
				
				if(!in_array($val->component_name, $data['transaction_list'][$val->acad_year][$val->id]['components'][$val->insId])) {
					$data['transaction_list'][$val->acad_year][$val->id]['installment'][$val->insId] .= "<br>".$val->component_name."=".$val->component_amount_paid;
					$data['transaction_list'][$val->acad_year][$val->id]['components'][$val->insId][] = $val->component_name;
					$data['component_total'][$val->acad_year][$val->component_name] += $val->component_amount_paid;
				}
				

				if($val->settlement_status == 'SETTLED')
					$data['isConfirmed'] = 1;

			}
			$settlements = $this->payment->get_settlementdata($date);
			//echo "<pre>"; print_r($settlements); die();
			$data['settlement'] = array();
			// $date = '2019-03-11';
			$data['settlement_amount'] = 0;
			$data['settled_count'] = 0;
			$data['not_settled_count'] = 0;
			if(empty($settlements)) {
				echo (json_encode($data));
				return;
			}
			if(empty($settlements->data)) {
				echo (json_encode($data));
				return;
			}
			foreach ($settlements->data as $key => $val) {
				// echo "<pre>"; print_r($val); 
				if(!empty($val->transaction_start_date)) {
					$startDate = $val->transaction_start_date;
					// echo $startDate;
					if($startDate == $date) {
						$val->settlement_datetime = date('d-m-Y', strtotime($val->settlement_datetime));
						$val->status = '<span style="color:#ff4335;font-size:14px;font-weight:600;">Settlement Pending</span>';
						if($val->completed == 'y')
							$val->status = '<span style="color:#00701a;font-size:14px;font-weight:600;">Settlement Done</span>';
						if($val->bank_reference != NULL) {
							$data['settled_count']++;
						} else {
							$data['not_settled_count']++;
						}
						$data['settlement'][] = $val;
						$data['settlement_amount'] += $val->payout_amount;
					}
				}
			}
			echo (json_encode($data));
		}

		public function get_overview_transactions(){
			$fromdate = date("Y-m-d", strtotime($_POST['fromdate']));
			$toDate = date("Y-m-d", strtotime($_POST['toDate']));
			$schoolName = $this->settings->getSetting('school_short_name');
			$settlements = $this->payment->get_overivew_settlementdata($fromdate, $toDate);

			$data['settlement'] = array();
			$data['settlement_amount'] = 0;
			$data['settled_count'] = 0;
			$data['not_settled_count'] = 0;
			if(empty($settlements->data)){
				echo (json_encode($settlements->error));
				exit;
			}
			foreach ($settlements->data as $key => $val) {

				// if(!empty($val->transaction_start_date)) {

					$val->settlement_datetime = date('d-m-Y', strtotime($val->settlement_datetime));
					$val->status = '<span style="color:#ff4335;font-size:14px;font-weight:600;">Settlement Pending</span>';
					if($val->completed == 'y')
						$val->status = '<span style="color:#00701a;font-size:14px;font-weight:600;">Settlement Done</span>';
					if($val->bank_reference != NULL) {
						$data['settled_count']++;
					} else {
						$data['not_settled_count']++;
					}
					$data['settlement'][] = $val;
					$data['settlement_amount'] += $val->payout_amount;
				// }

			}
			//Get Settlement verification data
			$settlement_id_arr = array();
			foreach ($settlements->data as $settlement_obj) {
				$settlement_id_arr[] = $settlement_obj->settlement_id;
				$tx_details_traknpay = $this->payment->get_settlement_details($settlement_obj->settlement_id);		
				$order_id_array = array();
				$order_id_vendorarray = array();
				if(!empty($tx_details_traknpay)){
					if(!empty($tx_details_traknpay->data)){
						foreach($tx_details_traknpay->data as $val) {
							$order_id_array[] = $val->order_id;
							if(!empty($val->vendor_code)){
								$order_id_vendorarray[$val->vendor_code][] = $val->order_id;
							}
						}
					}
				}
				if($schoolName == 'gjs' || $schoolName == 'vetpuc' || $schoolName == 'sis'){
					$tx_details_se = $this->payment_model->get_tx_details_from_order_accountwise_id($order_id_vendorarray);
				}else{
					$tx_details_se = $this->payment_model->get_tx_details_from_order_id($order_id_array);
				}
				$total_amount_paid = 0;
				$total_tax_not_generated_amount = 0;
				if (!empty($tx_details_se)) {
					foreach ($tx_details_se as $tx) {
						$total_amount_paid += $tx->amount_paid;
						if(empty($tx->receipt_number)){
							$total_tax_not_generated_amount += $tx->amount_paid;
						}
					}
				}
				$settlement_obj->amount_paid = $total_amount_paid;
				$settlement_obj->total_tax_not_generated_amount = $total_tax_not_generated_amount;
			}
			$verification_data = $this->payment_model->get_settlement_verification_data($settlement_id_arr);

			foreach ($settlements->data as &$settlement_obj) {
				foreach ($verification_data as $verification_obj) {
					if ($settlement_obj->settlement_id == $verification_obj->settlement_id) {
						$settlement_obj->verification_status = $verification_obj->verification_status;
						$settlement_obj->remarks = $verification_obj->remarks;
						$settlement_obj->verified_on = $verification_obj->verified_on;
						$settlement_obj->verified_by = $verification_obj->staff_name;
						continue;
					}
				}
			}

			echo (json_encode($data));
		}

		public function submit_settlement_verification() {
			return $this->payment_model->submit_settlement_verification($_POST['settlement_id'], $_POST['settlement_comment']);
		}

		public function get_settlement_details(){
			$settlement_id = $_POST['settlement_id'];
			
			//Get transaction details from traknpay
			$tx_details_traknpay = $this->payment->get_settlement_details($settlement_id);

			$schoolName = $this->settings->getSetting('school_short_name');

			//Get transaction details from schoolelement
			$order_id_array = array();
			$order_id_vendorarray = array();
			foreach($tx_details_traknpay->data as $val) {
				$order_id_array[] = $val->order_id;
				if(!empty($val->vendor_code)){
					$order_id_vendorarray[$val->vendor_code][] = $val->order_id;
				}
			}
			if($schoolName == 'gjs' || $schoolName == 'vetpuc' || $schoolName == 'sis'){
				$data['tx_details_se'] = $this->payment_model->get_tx_details_from_order_accountwise_id($order_id_vendorarray);
			}else{
				$data['tx_details_se'] = $this->payment_model->get_tx_details_from_order_id($order_id_array);
			}
			//Process split json and get vendors details and sum
			$data['vendor_data'] = $this->_process_split_json($data['tx_details_se'] );
			//Replace split with readable text
			$vendor_name_arr = array();
			foreach ($data['vendor_data'] as $vendor) {
				$vendor_name_arr[$vendor->vendor_code] = $vendor->vendor_name;
			}
			foreach ($data['tx_details_se'] as $key => $tx) {
				if (empty($tx->split_json)) {
					continue;
				}
				$split_array = json_decode($tx->split_json);
				$split_vendors = [];
				foreach ($split_array->vendors as $vendor_obj) {
					$new_vendor_obj = new stdClass();
					$new_vendor_obj->vendor_amount = $vendor_obj->split_amount_fixed;
					$new_vendor_obj->vendor_name = $vendor_name_arr[$vendor_obj->vendor_code];

					$split_vendors[] = $new_vendor_obj;
				}
				$data['tx_details_se'][$key]->split_objs = $split_vendors;
			}

			echo (json_encode($data));
		}

		public function _process_split_json($tx_details) {
			$vendor_obj_arr = array();
			foreach ($tx_details as $tx) {
				if (empty($tx->split_json)) {
					continue;
				}
				$split_array = json_decode($tx->split_json);
				foreach ($split_array->vendors as $vendor_obj) {
					if (!isset($vendor_obj_arr[$vendor_obj->vendor_code])) $vendor_obj_arr[$vendor_obj->vendor_code] = 0;
					$vendor_obj_arr[$vendor_obj->vendor_code] += $vendor_obj->split_amount_fixed;
				}
			}

			//Get the vendor details
			$vendor_obj_arr_final = array();
			foreach ($vendor_obj_arr as $vendor_code => $vendor_amount) {
				$vendor_obj = new stdClass();
				$vendor_obj->vendor_code = $vendor_code;
				$vendor_obj->vendor_amount = $vendor_amount;
				$vendor_details = $this->payment->get_vendor_details($vendor_code);
				if (isset($vendor_details->data->vendor_name)) {
					$vendor_obj->vendor_name = $vendor_details->data->vendor_name;
				} else {
					$vendor_obj->vendor_name = 'Not Available';
				}
				$vendor_obj_arr_final[] = $vendor_obj;
			}

			return $vendor_obj_arr_final;
		}

		public function confirmSettlement() {
			$status = $this->payment_model->confirmSettlement();
			if ($status) {
	            $this->session->set_flashdata('flashSuccess', 'Successfully Confirmed.');
	        }else{
	            $this->session->set_flashdata('flashError', 'Failed to confirm .');
	        }
			redirect('payment_controller/online_settlement_report');
		}

		public function get_transaction_status() {
			$opm_id = $this->input->post('opm_id');
			$payment_gateway = $this->settings->getSetting('payment_gateway');
			switch ($payment_gateway) {
			case 'payu':
				echo json_encode($this->payment->get_transaction_status($opm_id));
				break;
			case 'jodo':
				echo json_encode($this->payment->get_transaction_status_jodo($opm_id));
				break;
			default:
				echo json_encode($this->payment->get_transaction_status($opm_id));				
				break;
			}

			
		}

		public function get_transaction_status_parent(){
			$source_id = $this->input->post('source_id');
			echo json_encode($this->payment->get_transaction_status_from_tx_id($source_id));
		}

		public function redrive_transaction() {
			$response = json_decode($this->input->post('response'));
			$data = $this->payment->redrive_transaction($response->data[0]);
			$data['main_content'] = 'online_payment/payment_done';
			$this->load->view('inc/template', $data);	  
		}

		
		public function payment_done_s2s_fees(){

			// trigger_error("Payment done s2s Fees");
			// trigger_error("API response from TraknPay: ");
			var_dump('TESTING..111' .$_POST['order_id']);
			// trigger_error('order_id : '.$_POST['order_id']);
			
			if (empty($_POST)) {
				trigger_error("S2S API Post is empty");
				return;
			}
			die();
			// update Online payment master table
			
			// $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt(10);

			// echo "<pre>"; print_r($data['fee_trans']); die();
			
			$result = $this->payment->payment_callback_school_handler($_POST);

			if ($result['transaction_status'] == 'FAILED'){
				trigger_error("Payment failed");
				return;
			}

			$this->load->model('feesv2/fees_collection_model');

			$is_receipt_generated = $this->fees_collection_model->is_receipt_generated($_POST['source_id']);
			// trigger_error("Receipt generated: " . $is_receipt_generated?'Yes':'No');

			if (!$is_receipt_generated) {

				// update transaction and student schedule table
				$this->fees_collection_model->update_trans_student_all_table($_POST['source_id']);

				// Generate pdf for fee receipts 
				$this->fees_collection_model->create_pdf_template_for_fee_receipts($_POST['source_id'], $_POST['transaction_id'], date('d-m-Y', strtotime($_POST['payment_datetime'])), date('H:i', strtotime($_POST['payment_datetime'])));
			}
			return;
		}

		// public function challan_online_transactions(){

		// 	if (!$this->ion_auth->logged_in()) {
		// 		redirect('auth/login', 'refresh');
		// 	}
		// 	$data['main_content'] = 'feesv2/settlement/challan_transaction';
		// 	$this->load->view('inc/template', $data);
		// }

		public function get_get_challan_transactions_details(){

			$fromdate = $_POST['fromdate_challan'];
			$toDate = $_POST['toDate_challan']; 
			 
			echo $this->payment->get_get_challan_details($fromdate, $toDate);
		}

		public function online_challan_payment_report(){
			if (!$this->ion_auth->logged_in()) {
				redirect('auth/login', 'refresh');
			}
			if (!$this->authorization->isModuleEnabled('FEESV2') || !$this->authorization->isAuthorized('FEESV2.VIEW_ONLINE_CHALLAN_REPORT')) {
				redirect('dashboard', 'redirect');
			}

			// $data['transaction_list'] = $this->payment_model->get_online_transaction_data();	
			$data['main_content'] = 'feesv2/online_transaction_report/challan';
			$this->load->view('inc/template', $data);
		}

		public function getChallanTransactionDetailsByStudent(){
			$fromdate = $_POST['fromdate'];
			$toDate = $_POST['toDate'];
			$result = $this->payment_model->getChallanTransactionDetails($fromdate, $toDate);
			echo json_encode($result);
		}
		
		public function failed_online_transaction_report(){	
			$data['main_content'] = 'feesv2/online_transaction_report/failed';		
			$this->load->view('inc/template', $data);
		}

		public function get_online_transaction_report_all_data(){
			$fromdate = $_POST['fromdate'];
			$toDate = $_POST['toDate'];
			$result = $this->payment_model->get_online_transaction_report_all_data($fromdate, $toDate);
			$opmids = array_chunk($result, 25);
			echo json_encode($opmids);
		}


		public function receipt_not_generated_online_transaction_report(){
			$onlineOpmIds = $_POST['onlineOpmIds'];
			$result = $this->payment_model->get_receipt_not_generated_online_transaction_report($onlineOpmIds);
			echo json_encode($result);
		}


	}



?>
