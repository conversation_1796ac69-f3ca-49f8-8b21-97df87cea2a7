<div class="col-md-12">
    <div class="card panel_new_style" style="border: none !important;">
        <div class="card-header panel_heading_new_style_padding text-center">
            <h3 class="card-title panel_title_new_style">
                <strong>Parent Login Report</strong>
            </h3>
        </div>
        <div class="card-body px-0">
            <div class="col-12 px-0 mb-3">
                <div class="form-group text-center" style="border-radius:16px;padding:10px 5px;border:solid 1px #ddd">
                    <h5>Admission Type</h5>
                    <div class="row d-flex" style="margin: 0px">
                        <div class="col-md-8 col-md-offset-2">
                            <select id="admission_type" name="admission_type" onchange="getData()" required class="form-control custom-select input-md">
                                <option value=""><?php echo "All" ?></option>
                                <?php foreach ($admission_type as $value => $type) { ?>
                                    <option value="<?php echo $value ?>"><?php echo $type ?></option>
                                <?php } ?>
                            </select>
                        </div>
                    </div>

                </div>
            </div>
            <div class="col-12 px-0 mb-3">
                <div class="form-group text-center" style="border-radius:16px;padding:10px 5px;border:solid 1px #ddd">
                    <h5>Search By Class</h5>
                    <div class="row d-flex" style="margin: 0px">

                        <div class="col-md-8 col-md-offset-2">
                            <?php 
								$array = array();
								$array[0] = 'Select Class';
								foreach ($classList as $key => $cl) {
									$array[$cl->classId] = $cl->className;
								}
								echo form_dropdown("classId", $array, set_value("classId",$selectedClassId), "id='classId' onchange='class_wise_search_std();' class='form-control custom-select'");
							?>
                        </div>
                    </div>

                </div>
            </div>

            <div class="col-12 px-0 mb-3">
                <div class="form-group text-center" style="border-radius:16px;padding:10px 5px;border:solid 1px #ddd">
                    <h5>Search By Class / Section</h5>
                    <div class="row">
                        <div class="col-md-8 col-md-offset-2">
                            <?php 
								$array = array();
								$array[0] = 'Select Section';
								foreach ($classSectionList as $key => $cl) {
									$array[$cl->id] = $cl->class_name . $cl->section_name;
								}
								echo form_dropdown("classSectionId", $array, '', "id='classSectionId'  onchange='classSection_wise_search_std()'  class='form-control custom-select'");
							?>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 px-0">
                <div class="d-flex justify-content-end align-items-center" style="padding: 10px 5px;">
                    <label class="mb-0 d-flex align-items-center" style="margin-right: 4%;">
                        Show only both logins not logged in
                        <input style="width: 20px; height: 20px; margin-left: 10px;" onchange="show_no_loggins()" type="checkbox" id="show-no-loggins">
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card-body mt-1 pt-0 px-0">
    <div class="col-md-12">
        <div id="printArea" class="stdudentData leaveData" style="overflow:auto">
            <div class="no-data-display">Select filter to Student list</div>
        </div>
    </div>
</div>

<div class="visible-xs">
    <a href="<?php echo site_url('SchoolAdmin_menu');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>

<script>

function getData(){
    var sectionId = $("#classSectionId").val();
    var classId = $("#classId").val();
    if (sectionId != 0) {
        classSection_wise_search_std();
    } else if (classId != 0) {
        class_wise_search_std();
    }
}
function show_no_loggins() {
    if($("#show-no-loggins").is(':checked')) {
        $(".all-logins").hide();
        $(".no-loggins").show();
    } else {
        $(".all-logins").show();
    }
}

function admission_status_change() {
    var sectionId = $("#classSectionId").val();
    if (sectionId !=0) {
        classSection_wise_search_std();
    }else{
        class_wise_search_std();
    }
}

function class_wise_search_std() {
    $(".stdudentData").html('<div class="no-data-display">Loading...</div>');
    var classId = $("#classId").val();
    var adm_status = $('#admission_status').val();
    let admission_type = $('#admission_type').val();
    $("#show-no-loggins").prop('checked', false);
    if(classId) {
        $.ajax({
            url: '<?php echo site_url('user_provisioning_controller/getStudentDetails'); ?>',
            type: 'post',
            data: {'classId':classId, 'mode':'class_id', 'admission_type': admission_type},
            success: function(data) {
                var std = JSON.parse(data);
                $(".stdudentData").html(prepare_student_table(std));
                $('#customers2').DataTable({
                    destroy: true,            // Destroy any previous instance
                    paging: false,            // No pagination
                    ordering: false,          // No column ordering
                    searching: true,         // No search box
                    lengthChange: false,      // No length dropdown
                    info: false,              // No "Showing x of y" info
                    dom: 'lBfrtip',           // Enable Buttons extension
                    buttons: [
                        {
                            extend: 'excel',
                            text: 'Excel',
                            className: 'btn btn-success',
                            filename: 'Parent Login Report'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            className: 'btn btn-primary',
                            filename: 'Parent Login Report'
                        }
                    ]
                });
            }
        });
    }
}

function classSection_wise_search_std() {
    $(".stdudentData").html('<div class="no-data-display">Loading...</div>');
    var sectionId = $("#classSectionId").val();
    var adm_status = $('#admission_status').val();
    let admission_type = $('#admission_type').val();
    $("#show-no-loggins").prop('checked', false);
    if(sectionId) {
        $.ajax({
            url: '<?php echo site_url('user_provisioning_controller/getStudentDetails'); ?>',
            type: 'post',
            data: {'sectionId':sectionId, 'mode':'section_id', 'admission_type': admission_type},
            success: function(data) {
                var std = JSON.parse(data);
                $(".stdudentData").html(prepare_student_table(std));
                $('#customers2').DataTable({
                    destroy: true,            // Destroy any previous instance
                    paging: false,            // No pagination
                    ordering: false,          // No column ordering
                    searching: true,         // No search box
                    lengthChange: false,      // No length dropdown
                    info: false,              // No "Showing x of y" info
                    dom: 'lBfrtip',           // Enable Buttons extension
                    buttons: [
                        {
                            extend: 'excel',
                            text: 'Excel',
                            className: 'btn btn-success',
                            filename: 'Parent Login Report'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            className: 'btn btn-primary',
                            filename: 'Parent Login Report'
                        }
                    ]
                });
            }
        });
    }
}

var names = [];
$(document).ready(function(){
    var stdNames = JSON.parse('<?php echo json_encode($studentNames); ?>');
    for(var i=0; i<stdNames.length; i++){
        names.push(stdNames[i].stdName);
    }
});

function prepare_student_table (std) {
    var path = '<?php echo $this->filemanager->getFilePath(""); ?>';
    var picUrl = '';
    var html = '';
    
    if(!std)
        html += "<h4>Select any filter to get student data</h4>";
    else {
        html += `<table id="customers2" class="table datatable table-bordered">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th style="white-space: nowrap;">Student Name</th>
                            <th>Adm No</th>
                            <th>Section</th>
                            <th>Relation</th>
                            <th>Username</th>
                            <th>Details</th>
                            <th>Status</th>
                            <th>Last accessed</th>
                            <th>Can Recieve Notification</th>
                        </tr>
                    </thead>
                    <tbody>`;
        var j = 0;
        for (var i in std) {
            picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/femalestu.png';
            if(std[i].gender == 'Male'){
                picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/malestu.png';
            }

            if(std[i].picture_url != '' && std[i].picture_url != null) {
                picUrl = path + std[i].picture_url;
            }

            var parents = std[i].parents;
            // var rows = Object.keys(parents).length;
            var noLoggins = '';
            if(std[i].logins == 0) {
                noLoggins = 'no-loggins';
            } 

            html += "<tr class='all-logins "+noLoggins+"'><td>" + (++j) + "</td>";
            // html += "<td></td>";
            html += "<td style='white-space: nowrap;''><img width='30' height='30' class='img-circle' src='"+picUrl+"' />&nbsp;&nbsp;" + std[i].studentName + "</td>";
            html += "<td>" + std[i].admission_no + "</td>";
            html += "<td>" + std[i].class_name + ""+ std[i].sectionName + "</td>";
            html += `<td>${std[i].relation_type}</td>`;
            html += `<td>${std[i].username}</td>`;
            html += `<td><b>${std[i].parentName}</b> (${std[i].mobile})</td>`;
            html += `<td>
                        ${
                            std[i].loggedin_atleast_once == '1' ? 
                            '<b class="text-success">Logged In</b>' : 
                            '<b class="text-warning">Activated<br>Not Logged In</b>'
                        }
                    </td>`;
            html += `<td>${std[i].last_login != null ? std[i].last_login : '-'}</td>`;
            html += `<td>
                        ${
                            (std[i].token != null || std[i].token != '' ) ? 
                            '<b class="text-success">Yes</b>' : 
                            '<b class="text-danger">No</b>'
                        }
                    </td>`;
            html += "</tr>";
        }
        html += '</tbody></table>';
    }
    return html;
}
</script>

<style>
.dt-buttons{
    margin-bottom: 5px;
    float: right;
}
#tags{
    position:relative
    padding: 10px;
}
.autocomplete-items {
    position: absolute;
    overflow-y:auto;
    border-bottom: none;
    border-top: none;
    height:300px;
    margin:0px 15px;
    z-index: 99;
    /*position the autocomplete items to be the same width as the container:*/
    top: 100%;
    left: 0;
    right: 0;
}
.autocomplete-items div {
    padding: 10px;
    cursor: pointer;
    background-color: #fff; 
    border-bottom: 1px solid #d4d4d4; 
}
.autocomplete-items div:hover {
    /*when hovering an item:*/
    background-color: #e9e9e9; 
}
.autocomplete-active {
    /*when navigating through the items using the arrow keys:*/
    background-color: DodgerBlue !important; 
    color: #ffffff; 
}
ul.panel-controls>li>a {
    border-radius: 50%;
}
#customers2_filter{
    width: 52% !important;
    float: left !important;
}
</style>