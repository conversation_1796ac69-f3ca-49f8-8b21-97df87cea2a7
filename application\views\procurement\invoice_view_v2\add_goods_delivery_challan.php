<ul class="breadcrumb">
<li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2');?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/invoice_controller_v2/index_v2');?>">Goods Delivery Challan Dashboard</a></li>
    <li>Add Goods Delivery Challan</li>
</ul>

<hr>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <h3 class="">
                    <a href="<?php echo site_url('procurement/invoice_controller_v2/index_v2');?>" class="back_anchor control-primary"><span class="fa fa-arrow-left"></span></a>
                    Add Goods Delivery Challan
                </h3>
            </div>
        </div>
        <form enctype="multipart/form-data" id="demo-form" action="<?php echo site_url('procurement/invoice_controller_v2/submitInvoicePO/'); ?>" class="form-horizontal"  data-parsley-validate method="post" >
            <div class="panel-body">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-4 control-label" for="name">PO Number <font color="red">*</font></label>  
                        <div class="col-md-8">
                        <div class="input-group">
                            <select name="PO_id" id="PO_id" class="form-control select2" required="" onchange="get_po_data()">
                                <option value="">Select PO...</option>
                                <?php 
                                foreach ($POs as $key => $value) {
                                    echo '<option value="'.$value->id.'">'.$value->request_number. ' - ' . $value->created_on. '</option>';
                                }
                                ?>
                            </select>
                            <!-- <span class="input-group-addon" onclick="get_po_data()" style=""><span>Add</span></span> -->
                        </div></div>
                    </div>


                    <div class="form-group">
                        <label class="col-md-4 control-label" for="name">Vendor <font color="red">*</font></label>  
                        <div class="col-md-8">
                        <div class="input-group">
                            <select name="vendor" id="vendor" class="form-control" required="" readonly style="pointer-events: none;">
                                <option value="">Select Vendor</option>
                                <?php 
                                foreach ($vendorData as $key => $value) {
                                    echo '<option value="'.$value->id.'">'.$value->vendor_name.'</option>';
                                }
                                ?>
                            </select>
                            <span class="input-group-addon">
                                    <span class="fa fa-caret-down"></span>
                                </span>
                        </div></div>
                    </div>
                    <div class="form-group" style="display: none;">
                        <label class="col-md-4 control-label" for="name">Invoice No  <font color="red">*</font></label>
                        <div class="col-md-8 ">
                            <div class="input-group">
                                
                                <input placeholder="Enter invoice number" id="invoice_no" name="invoice_no" type="text"  class="form-control input-md">
                                <span class="input-group-addon">
                                    <span class="fa fa-pencil"></span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-4 control-label" for="name">e-Way Bill No </label>
                        <div class="col-md-8">
                        <div class="input-group">
                                
                            <input placeholder="Enter bill number" id="bill_no" name="bill_no" type="text"  class="form-control input-md">
                            <span class="input-group-addon">
                                    <span class="fa fa-pencil"></span>
                                </span>
                        </div></div>
                    </div>
                    <div class="form-group" style="display: none;">
                        <label class="col-md-4 control-label" for="name">Order No</label>
                        <div class="col-md-8">
                        <div class="input-group">
                                
                            <input placeholder="Enter order number" id="order_no" name="order_no" type="text"  class="form-control input-md">
                            <span class="input-group-addon">
                                    <span class="fa fa-pencil"></span>
                                </span>
                        </div> </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-4 control-label" for="delivery_challan_note_number">Delivery Challan Note Number</label>
                        <div class="col-md-8">
                        <div class="input-group">
                                
                            <input placeholder="It will generated automatically" id="delivery_challan_note_number" name="delivery_challan_note_number" type="text"  class="form-control input-md" readonly>
                            <span class="input-group-addon">
                                    <span class="fa fa-pencil"></span>
                                </span>
                        </div> </div>
                    </div>
                    
                    
                    <div class="form-group">
                        <label class="col-md-4 control-label" for="name">Narration</label>
                        <div class="col-md-8">
                            <textarea rows="4" placeholder="Enter delivery note" id="delivery_note" name="delivery_note" class="form-control input-md"> </textarea>
                        </div>
                    </div>
                    
                </div>











                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-4 control-label" for="sales_year_id">Sales Year</label>
                        <div class="col-md-8">
                            <div class="input-group">
                               
                                <select readonly style="pointer-events: none;" name="sales_year_id" id="sales_year_id" class="form-control">
                                    <option value="">Select Year</option>
                                    <?php
                                        foreach($sales_year as $key => $val) {
                                            if($val->is_active == 1) $selected= 'selected';
                                            else $selected= '';
                                            echo "<option $selected value='$val->id'>$val->year_name</option>";
                                        }
                                    ?>
                                    
                                </select>
                                <span class="input-group-addon">
                                    <span class="fa fa-caret-down"></span>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-4 control-label" for="name">Delivery Challan Date</label>
                        <div class="col-md-8">
                            <div class="input-group date" id="deliveryDate">
                                
                                <input value="<?php echo date('d-m-Y'); ?>" autocomplete="off" placeholder="Enter delivery note date" id="delivery_note_date" name="delivery_note_date" type="text"  class="form-control input-md date">
                                <span class="input-group-addon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group" style="display: none;">
                        <label class="col-md-4 control-label" for="name">Invoice Date</label>
                        <div class="col-md-8">
                            <div class="input-group date" id="invoiceDate">
                                
                                <input autocomplete="off" placeholder="Enter invoice date" id="invoice_date" name="invoice_date" type="text"  class="form-control input-md date">
                                <span class="input-group-addon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" style="display: none;">
                        <label class="col-md-4 control-label" for="name">Mode of payment</label>
                        <div class="col-md-8">
                            <div class="input-group">
                                
                            <select name="payment_mode" id="payment_mode" class="form-control" >
                                <option value="">Select payment mode</option>
                                <option value="cash">Cash</option>
                                <option value="cheque">Cheque</option>
                                <option value="bank_account">Bank Account</option>
                            </select>
                            <span class="input-group-addon">
                                    <span class="fa fa-caret-down"></span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" style="display: none;">
                        <label class="col-md-4 control-label" for="name">Order Date  <font color="red">*</font></label>
                        <div class="col-md-8">
                            <div class="input-group date" id="orderDate">
                               
                                <input autocomplete="off" placeholder="Enter order date" id="order_date" name="order_date" type="text"  class="form-control input-md date" required="" value="<?php echo date('d-m-Y'); ?>">
                                <span class="input-group-addon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" style="display: none;">
                        <label class="col-md-4 control-label" for="name">Supplier Ref No</label>
                        <div class="col-md-8">
                        <div class="input-group">
                                
                            <input placeholder="Enter supplier reference number" id="supplier_ref_no" name="supplier_ref_no" type="text"  class="form-control input-md">
                            <span class="input-group-addon">
                                    <span class="fa fa-pencil"></span>
                                </span>
                        </div> </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-4 control-label" for="name">Dispatched By</label>
                        <div class="col-md-8">
                        <div class="input-group">
                                
                            <input placeholder="Enter dispatched by" id="dispatched_by" name="dispatched_by" type="text"  class="form-control input-md">
                            <span class="input-group-addon">
                                    <span class="fa fa-pencil"></span>
                                </span>
                        </div></div>
                    </div>
                    <div class="form-group" style="display: none;">
                        <label class="col-md-4 control-label" for="name">Payment instrument </label>  
                        <div class="col-md-8">
                            <div class="input-group">
                                
                            <select name="payment_ins" id="payment_ins" class="form-control">
                                <option value="">Select payment instrument</option>
                            </select>
                            <span class="input-group-addon">
                                    <span class="fa fa-caret-down"></span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-4 control-label" for="dispatch_doc_no">Dispatch Doc No </label>
                        <div class="col-md-8">
                        <div class="input-group">
                                
                            <input placeholder="Enter dispatch document number" id="dispatch_doc_no" name="dispatch_doc_no" type="text"  class="form-control input-md">
                            <span class="input-group-addon">
                                    <span class="fa fa-pencil"></span>
                                </span>
                        </div></div>
                    </div>
                    <div class="form-group" style="display: none;">
                        <label class="col-md-4 control-label" for="name">Destination</label>
                        <div class="col-md-8">
                            <textarea rows="4" placeholder="Enter destination" id="destination" name="destination" class="form-control input-md"> </textarea>
                        </div>
                    </div>
                    
                    <input type="hidden" class="form-control" id="varIds" name="varIds">
                    <input type="hidden" name="status" id="status" value="save">

                    <div class="form-group" style="display: none;">
                        <label class="col-md-4 control-label" for="">Add Items<font color="red">*</font></label>
                        <div class="col-md-8">
                            <button type="button" class="btn btn-warning" style="" id="addItem" data-toggle="modal" data-target="#view-modal">Add Items</button>
                        </div>
                    </div>

                    <div class="form-group" style="display: none;">
                        <label class="col-md-4 control-label" for="name">Total Amount </label>
                        <div class="col-md-8">
                        <div class="input-group">
                                
                            <input placeholder="Enter total amount" value="0" readonly="" id="grand_total" name="grand_total" type="text"  class="form-control input-md">
                            <span class="input-group-addon">
                                    <span class="fa fa-pencil"></span>
                                </span>
                        </div> </div>
                    </div>


                </div>

                <div class="col-md-12" style="margin-top:5px;padding: 10px; width: 100%; overflow: auto;">
                    <table class="table table-bordered table-responsie" style="min-width: 100%; overflow: auto;" id="variantTable">
                        <thead>
                            <th style="min-width: fit-content;">Item</th>
                            <th style="min-width: fit-content; display: none;">Expected Rate</th>
                            <th style="min-width: fit-content; display: none;">Expected cGST %</th>
                            <th style="min-width: fit-content; display: none;">Expected sGST %</th>
                            <th style="min-width: fit-content;">Expected Quantity</th>
                            <th style="min-width: fit-content; display: none;">Expected Total Amount</th>

                            <th style="min-width: fit-content; display: none;">Actual Rate</th>
                            <th style="min-width: fit-content; display: none;">Actual cGST %</th>
                            <th style="min-width: fit-content; display: none;">Actual sGST %</th>
                            <th style="min-width: fit-content;">Rejected Quantity</th>
                            <th style="min-width: fit-content;">Actual Accepted Quantity</th>
                            <th style="min-width: fit-content; display: none;">Actual Total Amount<br> <span class="text-danger tolerance-span"></span></th>

                            <th style="min-width: fit-content;">Selling Price</th>
                            <th style="min-width: fit-content;">Quality Remarks</th>
                            <th style="min-width: fit-content;"></th>
                        </thead>
                        <tbody id="variantAdded">
                            <tr>
                                <td colspan="14" class="text-center">Items not added. Select PO to add items...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>



                <div class="col-md-12" style="margin-top:5px;padding: 10px; width: 100%; overflow: auto;">
                    <label style="display: inline-flex;  gap: 8px; cursor: pointer;">
                        <input id="accept_consent" type="checkbox" name="consent" required style="width: 16px; height: 16px; margin-top: -0px;">
                        <span style="">
                            I confirm that the delivered goods have been thoroughly inspected and meet the required quality standards. I verify that the items are in their original condition, undamaged, and comply with the agreed specifications. By checking this box, I formally accept the delivery and acknowledge that the vendor has fulfilled their obligations.
                        </span>
                    </label>
                </div>


            </div> <!-- panel-body end -->
        </form>

        <div class="col-md-12" style="margin-bottom: 12px;">
            <center>
                <input disabled style="margin-left:5px; width: 2in;" onclick="finalSave(this)" id="saveBtn" class="btn btn-primary" type="button" value="Final Submit">
                <a style="margin-left:5px; width: 2in;" class="btn btn-secondary" href="<?php echo site_url('procurement/invoice_controller_v2'); ?>">Back</a>
            </center>
        </div>
    </div>
</div>

<div id="view-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog" style="width:75%; margin: auto;"> 
     <div class="modal-content">  
   
        <div class="modal-header"> 
            <h4 class="modal-title">Add Items</h4> 
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><span class="fa fa-times"></span></button> 
        </div> 
            
        <div class="modal-body" style="height:auto;">
           <div id="dynamic-content" class="form-horizontal">
               <div class="form-group">
                    <label class="col-md-4 control-label" for="name">Sub Categories</label>  
                    <div class="col-md-8 col-xs-12" style="">
                        <select style="width: 500px;" name="product" id="products" class="form-control select2" required="">
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-4 control-label" for="name">Items</label>  
                    <div class="col-md-8">
                        <select style="width: 500px;" name="variants[]" id="variants" multiple class="form-control select2" required="">
                        </select>
                    </div>
                </div>
           </div>
        </div> 
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            <button type="button" onclick="addVariant()" id="confirmP" class="btn btn-primary" data-dismiss="modal">Confirm</button>  
        </div> 
                        
    </div> 
  </div>
</div>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript">
    var varIds = '';
    var variantTotal = [];
    var slNo = 1;
    $(document).ready(function () {
        var academic_year= $("#academic_year").find(':selected').text();

        $(".select2").select2();
        $('#invoiceDate').datepicker({
            format: 'dd-mm-yyyy',
            "autoclose": true
        });
        $('#orderDate').datepicker({
            format: 'dd-mm-yyyy',

            "autoclose": true
        });
        $('#deliveryDate').datepicker({
            format: 'dd-mm-yyyy',
            "autoclose": true
        });

        

        
    });

    function getSum(total, num) {
      return total + num;
    }

    // function addGrandTotal(id) {
    //     variantTotal[id] = parseFloat($("#variant"+id).val());
    //     var total = variantTotal.reduce(getSum);
    //     $("#grand_total").val(total);
    // }

    function addGrandTotalNew2() {
        var index= 1;
        var grand_total= 0;
        $("input.ttl_amt_cls").each(function() {
            grand_total += Number($(".total_amount_" +index).val()) || 0;
            index ++;
        });
        $("#grand_total").val(Number(grand_total));
    }

    function addGrandTotalNew() {
        var index= 0;
        var grand_total= 0;
        $("tr.check_item_count_class").each(function() {
            var rate= Number($("input.rate-class").eq(index).val()) || 0;
            var cGSTp= Number($("input.cGST-class").eq(index).val()) || 0;
            var sGSTp= Number($("input.sGST-class").eq(index).val()) || 0;
            var quantity= $("input.quantity-class").eq(index).val() || 0;
            var sGST= ( Number(rate) * Number(sGSTp) ) / 100;
            var cGST= ( Number(rate) * Number(cGSTp) ) / 100;
            var total= (  Number(rate) + Number(cGST) + Number(sGST)  ) * Number(quantity);
            grand_total += Number(total);
            $("input.ttl_amt_cls").eq(index).val(Number(total).toFixed(2));
            index ++;
        });
        // alert(grand_total)
        $("#grand_total").val(Number(grand_total).toFixed(2));
    }

    

    function remove_the_row(global_serialNumber) {
        $("#removar_class"+global_serialNumber).remove();
        setTimeout(addGrandTotalNew, 400);
    }

    function finalSave(){

        let accept_consent= $("#accept_consent").is(":checked");
        if(!accept_consent) {
            return Swal.fire({
                icon: 'error',
                text: 'Please accept the terms and conditions before final submit.'
            });
        }
        var sales_year_id= $("#sales_year_id").val();
        if(!sales_year_id) {
            return Swal.fire({
                icon: 'error',
                text: 'Current sales year not active.'
            });
        }
        if ($('#demo-form').parsley().validate()){
            bootbox.confirm({
            title: 'Final submit invoice',
              message: 'You cannot modify after final submit. Are you sure?',
              buttons: {
                  confirm: {
                      label: 'Yes',
                      className: 'btn-success'
                  },
                  cancel: {
                      label: 'No',
                      className: 'btn-danger'
                  }
              },
              callback: function (result) {
                if(result) {
                    let checkIfItemsAdded= $(".check_item_count_class")
                    if(checkIfItemsAdded.length) {
                        $("#status").val('final_submit');
                        $('#saveBtn').val('Please Wait...').attr('disabled','disabled');
                        $('#demo-form').submit();
                    } else {
                        Swal.fire({
                            icon: 'error',
                            text: 'No items added. Please add items to proceed.'
                        });
                    }
                }
              }
            }).addClass('reduce_width');
        }
    }

    function get_po_data() {
        let PO_id = $("#PO_id").val();
        if(PO_id) {
            $.ajax({
                url: "<?php echo site_url('procurement/invoice_controller_v2/get_po_data');?>",
                data: {PO_id},
                type: 'post',
                success: function(data) {
                    data = JSON.parse(data);
                    if(Object.keys(data)?.length > 0) {
                        __construct_PO_items(data);
                    } else {
                        alert('No items found for this PO number');
                    }
                },
                error: function(err) {
                    console.log(err);
                }
            });
        } else {
            alert('Please select PO number');
        }
    }

    function __construct_PO_items(data) {
        let vendor_id = data[0].vendor_id;
        $("#vendor").val(vendor_id).trigger('change');
        let trs= '';
        let slNo = 1;
        data.forEach(element => {
            expectedQty= Number(element.item_quantity) - Number(element.delivered_quantity);
            var tolerance= Number(element.tolerance) || 0;
            $(".tolerance-span").html(`
                Tolerance = 
                <span class="d-inline-flex flex-column align-items-center mx-1" style="font-size: 0.8em; line-height: 0.5; font-weight: bolder; position: relative; top: -2px;">
                    <span>+</span>
                    <span>-</span>
                </span>
                ${tolerance}%
                `);
            var toleranceAmt= (Number(element.total_item_amt_with_gst) * tolerance) / 100;
            itemMinAmt= Number(element.total_item_amt_with_gst) - toleranceAmt;
            itemMaxAmt= Number(element.total_item_amt_with_gst) + toleranceAmt;
            var sell_price= Number(element.rate_per_unit) + (Number(element.rate_per_unit) * Number(element.cgst_per) / 100) + (Number(element.rate_per_unit) * Number(element.sgst_per) / 100);
            trs += `<tr class="check_item_count_class" id="removar_class${slNo}">
                <td>
                    ${element.item_name}
                    <input type="hidden" name="variants[]" value="${element.proc_im_items_id}">
                    <input type="hidden" name="proc_im_subcategory_id_arr[]" value="${element.proc_im_subcategory_id}">
                    <input type="hidden" name="proc_im_category_id_arr[]" value="${element.proc_im_category_id}">
                    <input type="hidden" name="procurement_requisition_items_arr[]" value="${element.procurement_requisition_items_id}">
                    <input type="hidden" name="expected_quantity_tobe_delivered[]" value="${expectedQty}">
                </td>
                <td style="display: none;">₹${Number(element.rate_per_unit).toLocaleString()}</td>
                <td style="display: none;">₹${Number(element.cgst_per).toLocaleString()}</td>
                <td style="display: none;">₹${Number(element.sgst_per).toLocaleString()}</td>
                <td>${expectedQty}</td>
                <td style="display: none;">₹${Number(element.total_item_amt_with_gst).toLocaleString()}</td>

                <td style="display: none;"><input value="${Number(element.rate_per_unit)}" step="0.1" onkeyup="addGrandTotalNew()" type="number" placeholder="Rate" class="form-control rate-class" id="rate${slNo}" name="rate[]"></td>
                <td style="display: none;"><input value="${Number(element.cgst_per)}" step="0.1" onkeyup="addGrandTotalNew()" type="number" placeholder="cGST" class="form-control cGST-class" id="cGST${slNo}" name="cGST[]"></td>
                <td style="display: none;"><input value="${Number(element.sgst_per)}" step="0.1" onkeyup="addGrandTotalNew()" type="number" placeholder="sGST" class="form-control sGST-class" id="sGST${slNo}" name="sGST[]"></td>
                <td><input max="${Number(expectedQty) - 1}" min="0" value="0" step="1" type="number" placeholder="Rejected quantity" class="form-control" name="rejected_quantity[]"></td>
                <td><input max="${Number(expectedQty)}" min="1" value="${Number(expectedQty)}" step="1" onkeyup="addGrandTotalNew()" type="number" placeholder="quantity" class="form-control quantity-class" id="quantity${slNo}" name="quantity[]"></td>
                <td style="display: none;">
                    <input  value="${Number(element.total_item_amt_with_gst)}" step="0.1" id="varient${element.proc_im_items_id}" onkeyup="addGrandTotalNew2()" onchange="addGrandTotal(${element.proc_im_items_id})" required placeholder="Total amount" type="text" class="form-control ttl_amt_cls total_amount_${slNo}" name="total_amount[]" required=""> <!-- min="${itemMinAmt}" max="${itemMaxAmt}" -->
                </td>
                 
                <td><input value="${Number(sell_price).toFixed(2)}" step="0.1" type="number" placeholder="0.00" class="form-control" id="selling_price${slNo}" name="selling_price[]"></td>
                <td><textarea placeholder="Ex: Added from PO. Quality checked and goods accepted." class="form-control" name="description[]" id="description${slNo}"></textarea></td>
                <td><span class="fa fa-times text-bold text-danger" onclick="remove_the_row('${slNo}')"></span></td>
            
                    </tr>`;
            slNo++;
        });

        $("#variantAdded").html(trs);
        $("#saveBtn").prop('disabled', false);
        addGrandTotalNew();
        $("#varIds").val(varIds);
    }

</script>




<style>
    .reduce_width {
        width: 600px;
        margin: 0 auto;
    }
</style>