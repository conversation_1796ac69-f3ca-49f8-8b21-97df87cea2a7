<?php
class User_provisioning_model extends CI_Model {
    private $current_branch;
    public function __construct() {
        // $this->load->library('Settings');
        parent::__construct();
        $this->current_branch = $this->authorization->getCurrentBranch();
    }
    public function getstudentDetails($classSectionId, $sendTo, $status, $std_name) {
        if($classSectionId == 0) {
            return array();
        }
        if($classSectionId != -1){
            list($section_id, $class_id) = explode("_", $classSectionId); 
        }

        $this->db->select("s.id as std_id,CONCAT(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as studentName,c.class_name,CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName,cs.section_name as sectionName, cs.id as csId,p.mobile_no as mobile,p.id as pid, u.active as Active, u.id as userId, sr.relation_type, u.username, up.attempts, s.dob, s.admission_no, cs.class_teacher_id, CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staffName, up.code, u.loggedin_atleast_once, p.email, a.old_user_id as oldUid")
        ->from('student_admission s')
        ->join('student_year sy', 'sy.student_admission_id=s.id');
        if($classSectionId != -1){
            $this->db->where('sy.class_section_id', $section_id);
        }
        //$this->db->where('s.class_section_id', $classSectionId);  //SKIP THIS WHILE ALL CLASSES
        $this->db->where('s.admission_status', '2');
        $this->db->where('sy.promotion_status!=', '4');
        $this->db->where('sy.promotion_status!=', '5');
        $this->db->where('sy.acad_year_id', $this->acad_year->getAcadYearId());
        // $this->db->join('academic_year ay', 's.admission_acad_year_id=ay.id');
        $this->db->join('student_relation sr','s.id=sr.std_id');
        $this->db->join('parent p','sr.relation_id=p.id');
        $this->db->join('avatar a','a.stakeholder_id=p.id');
        $this->db->join('users u','u.id=a.user_id');
        $this->db->join('user_prov_login_att up','up.parent_id=p.id','left');
        $this->db->where('a.avatar_type',2);
        
        switch ($status) {
            case 'Provisioned': 
                $this->db->where('p.id IN (SELECT parent_id from user_prov_login_att where attempts<=5)');
                $this->db->where('u.active', 0);
                break;
            case 'Not Provisioned': 
                $this->db->where('p.id NOT IN (SELECT parent_id from user_prov_login_att)');
                $this->db->where('u.active !=1');
                break;
            case 'Failed': 
                $this->db->where('p.id IN (SELECT parent_id from user_prov_login_att where attempts>=6)');
                $this->db->where('u.active !=1');
                break;
            case 'Activated': 
                $this->db->where('u.active', 1);
                break;
        }
        if($sendTo != 'Both'){
            $this->db->where('sr.relation_type',$sendTo);
        }
        if($std_name) {
            $this->db->where("(LOWER(s.first_name) like '%$std_name%' OR (LOWER(s.last_name) like '%$std_name%'))");
        }
        $this->db->join('class c','sy.class_id=c.id');
        if($this->current_branch) {
            $this->db->where('c.branch_id',$this->current_branch);
        }
        $this->db->join('class_section cs', 'sy.class_section_id=cs.id');
        $this->db->join('staff_master sm', 'sm.id=cs.class_teacher_id','left');
        $this->db->group_by('p.id');
        // $this->db->order_by('s.admission_acad_year_id, cs.id, s.first_name');
        $this->db->order_by('s.first_name, cs.id, s.admission_acad_year_id');
        return $this->db->get()->result();
        // $return = $this->db->get()->result();
        //  echo $this->db->last_query();die();
    }

    public function getstudentDetailsbyId($studentId, $sendTo, $status) {
      
        $this->db->select("s.id as std_id,CONCAT(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as studentName,c.class_name,CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName,cs.section_name as sectionName, cs.id as csId,p.mobile_no as mobile,p.id as pid, u.active as Active, u.id as userId, sr.relation_type, u.username, up.attempts, s.dob, s.admission_no, cs.class_teacher_id, CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staffName, up.code, u.loggedin_atleast_once, p.email, a.old_user_id as oldUid")
        ->from('student_admission s')
        ->where('s.id',$studentId)
        ->join('student_year sy', 'sy.student_admission_id=s.id');
       
        $this->db->where('s.admission_status', '2');
        $this->db->where('sy.promotion_status!=', '4');
        $this->db->where('sy.promotion_status!=', '5');
        $this->db->where('sy.acad_year_id', $this->acad_year->getAcadYearId());
        // $this->db->join('academic_year ay', 's.admission_acad_year_id=ay.id');
        $this->db->join('student_relation sr','s.id=sr.std_id');
        $this->db->join('parent p','sr.relation_id=p.id');
        $this->db->join('avatar a','a.stakeholder_id=p.id');
        $this->db->join('users u','u.id=a.user_id');
        $this->db->join('user_prov_login_att up','up.parent_id=p.id','left');
        $this->db->where('a.avatar_type',2);
        
        switch ($status) {
            case 'Provisioned': 
                $this->db->where('p.id IN (SELECT parent_id from user_prov_login_att where attempts<=5)');
                $this->db->where('u.active', 0);
                break;
            case 'Not Provisioned': 
                $this->db->where('p.id NOT IN (SELECT parent_id from user_prov_login_att)');
                $this->db->where('u.active !=1');
                break;
            case 'Failed': 
                $this->db->where('p.id IN (SELECT parent_id from user_prov_login_att where attempts>=6)');
                $this->db->where('u.active !=1');
                break;
            case 'Activated': 
                $this->db->where('u.active', 1);
                break;
        }
        if($sendTo != 'Both'){
            $this->db->where('sr.relation_type',$sendTo);
        }
        $this->db->join('class c','sy.class_id=c.id');
        if($this->current_branch) {
            $this->db->where('c.branch_id',$this->current_branch);
        }
        $this->db->join('class_section cs', 'sy.class_section_id=cs.id');
        $this->db->join('staff_master sm', 'sm.id=cs.class_teacher_id','left');
        $this->db->group_by('p.id');
        // $this->db->order_by('s.admission_acad_year_id, cs.id, s.first_name');
        $this->db->order_by('s.first_name, cs.id, s.admission_acad_year_id');
        return $this->db->get()->result();
        // $return = $this->db->get()->result();
        //  echo $this->db->last_query();die();
    }

    public function getClassSectionNames($yearId) {
        $show_placeholder_sections = $this->settings->getSetting('show_placeholder_sections');
        if(!$show_placeholder_sections) {
            $getphsections = FALSE;
        }
        $this->db_readonly->select('cs.section_name,cs.class_id,cs.id,c.class_name, c.is_placeholder');
        $this->db_readonly->from('class_section as cs');
        $this->db_readonly->join('class c', "c.id = cs.class_id");
        $this->db_readonly->where('c.is_placeholder!=',1);
        $this->db_readonly->where('c.acad_year_id',$yearId);
        $this->db_readonly->order_by('cs.display_order');
        // if (!$getphsections) {
        //     $this->db->where('cs.is_placeholder !=', 1);
        // }
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        $result = $this->db_readonly->get()->result();
 
        return $result;
    }

    public function getPreviewData(){
        $pids = $_POST['pids'];
        $this->db->select("s.id as std_id,CONCAT(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as studentName,CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName,p.mobile_no as mobile,p.id as pid,sr.relation_type, p.email, u.username, u.id as user_id");
        $this->db->from('parent p');
        $this->db->join('student_relation sr','p.id=sr.relation_id');
        $this->db->join('student_admission s','s.id=sr.std_id');
        $this->db->join('avatar a','a.stakeholder_id=p.id');
        $this->db->join('users u','u.id=a.user_id');
        $this->db->where('s.admission_status', '2');
        $this->db->where('a.avatar_type', '2');
        $this->db->where_in('p.id', $pids);
        return $this->db->get()->result();
    }

    public function getUserData($id){
        $this->db->select("a.stakeholder_id as pId,p.mobile_no, u.username, u.id as userId, sr.relation_type, u.active, CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName");
        $this->db->from('parent p');
        $this->db->join('student_relation sr', 'p.id=sr.relation_id');
        $this->db->join('avatar a', 'p.id=a.stakeholder_id');
        $this->db->join('users u', 'a.user_id=u.id');
        $this->db->where('p.id', $id);
        $this->db->where('a.avatar_type', 2);
        return $this->db->get()->row();
    }

      public function getstdId($id){
        $this->db->select("p.id,s.id as stdId, CONCAT(ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) AS stdName, date_format(s.dob,'%d-%m-%Y') as dob, admission_no,cs.section_name AS section,cs.class_name AS class,CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as class_teacher");
        $this->db->from('parent p');
        $this->db->where('p.id',$id);
        $this->db->join('student_admission s', 'p.student_id=s.id','left');
        $this->db->join('student_year sy', 'sy.student_admission_id=s.id');
        // $this->db->join('class c','sy.class_id=c.id','left');
        $this->db->join('class_section cs','sy.class_section_id=cs.id');
        $this->db->join('staff_master sm','cs.class_teacher_id=sm.id','left');
        $this->db->where('s.admission_status', '2');
        $this->db->where('sy.acad_year_id', $this->acad_year->getAcadYearId());
        return $this->db->get()->row();
        //echo "<pre>"; print_r($a); die();
    }

    public function updateStatus($pid){
        $uid = $this->db->select('u.id')
                    ->from('parent p')
                    ->join('avatar a','p.id=a.stakeholder_id','left')
                   ->where('avatar_type',2)
                   ->join('users u','a.user_id=u.id','left')
                   ->where('p.id',$pid)
                   ->get()->row();
        $data = array(
                'active' => 1
                );
        $this->db->where('id', $uid->id);

        return $this->db->update('users', $data);
        

    }

    public function getClasses(){
        $this->db->select('class_name');
        $this->db->from('class');
        $this->db->where('is_placeholder',0);
        $this->db->where('acad_year_id',$this->acad_year->getAcadYearId());
        return $this->db->get()->result();
    }

    public function addAttempt($parentId, $code){
        $this->db->where('code',$code);
        $this->db->where('parent_id',$parentId);
        $this->db->set('attempts', '`attempts`+ 1', FALSE);
        $this->db->update('user_prov_login_att');

        $this->db->select('attempts');
        $this->db->from('user_prov_login_att');
        $this->db->where('code',$code);
        return $this->db->get()->row();
    }

    public function addNewAttempt($parentId, $code, $type, $cmType){
        if($type == 'send') {
            $data = array(
                'parent_id' => $parentId,
                'code' => $code,
                'attempts' => 1,
                'communication_type' => $cmType
            );
            return $this->db->insert('user_prov_login_att', $data);
        } else {
            $data = array(
                'code' => $code,
                'attempts' => 1
            );
            $this->db->where('parent_id', $parentId);
            return $this->db->update('user_prov_login_att', $data);
        }
    }

    public function getAttempts($parentId, $code){
        $n = $this->db->select('code')->from('user_prov_login_att')->where('code',$code)->where('parent_id',$parentId)->count_all_results();
        if($n==0){
            return 0;
        }else{
                $this->db->select('attempts');
               $this->db->from('user_prov_login_att');
               $this->db->where('code',$code);
               $this->db->where('parent_id',$parentId);
               $a = $this->db->get()->row();
               return $a->attempts;
        }
    }

    public function deleteAttempts($code){
        $this->db->where('code',$code);
        $this->db->delete('user_prov_login_att');
        return 1;
    }

    public function deactivateUser($userId){
        $this->db->where('id', $userId);
        return $this->db->update('users', array('active' => 0, 'loggedin_atleast_once' => 0));
    }

    public function getExistingCodes(){
        $result = $this->db->query('select distinct(code) from user_prov_login_att')->result_array();
        $ret = array();
        foreach ($result as $key => $value) {
            array_push($ret, $value['code']);
        }
        return $ret;
    }

    public function getParentIdByCode($code){
        $id = $this->db->query('select parent_id from user_prov_login_att where code="'.$code.'"')->row();
        if(empty($id))
            return 0;
        return $id->parent_id;
    }

    public function getCommunicationType($code) {
        $data = $this->db->query('select communication_type from user_prov_login_att where code="'.$code.'"')->row();
        return $data->communication_type;
    }

    public function manualActivate($userid,$rPassword){
        $data = array(
                    'active'=> 1,
                    'password' => $rPassword
                     );
        $result = $this->ion_auth->update($userid, $data);

        
        if ($result) {
           return $this->db->select('u.username,p.mobile_no,p.first_name,s.first_name as stuName,s.id as student_id')
            ->from('users u')
            ->join('avatar a ','u.id=a.user_id')
            ->join('parent p ','a.stakeholder_id=p.id')
            ->join('student_admission s', 'p.student_id=s.id')
            ->where('a.avatar_type','2')
            ->where('u.id',$userid)
            ->get()->row();
            
        }else{
            return 0;
        }

    }

    public function activateUser($userid,$rPassword){
        $data = array(
                    'active'=> 1,
                    'password' => $rPassword
                );
        return $this->ion_auth->update($userid, $data);
    }

    public function getActivationPreviewData($userIds){
        return $this->db->select('u.username,p.mobile_no,p.email,p.first_name,s.first_name as stuName,s.id as student_id')
            ->from('users u')
            ->join('avatar a ','u.id=a.user_id')
            ->join('parent p ','a.stakeholder_id=p.id')
            ->join('student_admission s', 'p.student_id=s.id')
            ->where('a.avatar_type','2')
            ->where_in('u.id',$userIds)
            ->get()->result();
    }

    public function getActivationData($userIds){
        return $this->db->select("u.id as userId,u.username,p.mobile_no,p.email,CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as first_name,CONCAT(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as stuName,s.id as student_id,sr.relation_type, p.id as parentId")
            ->from('users u')
            ->join('avatar a ','u.id=a.user_id')
            ->join('parent p ','a.stakeholder_id=p.id')
            ->join('student_relation sr','sr.relation_id=p.id')
            ->join('student_admission s', 'p.student_id=s.id')
            ->where('a.avatar_type','2')
            ->where_in('u.id',$userIds)
            ->get()->result();
    }

    public function getEmailsByParentId($pIds) {
        if(empty($pIds)){
            return [];
        }
        return $this->db->select("u.id as userId, u.username, p.mobile_no, p.email, CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName, p.id as parentId, a.avatar_type")
            ->from('users u')
            ->join('avatar a ','u.id=a.user_id')
            ->join('parent p ','a.stakeholder_id=p.id')
            ->where('a.avatar_type','2')
            ->where_in('p.id',$pIds)
            ->get()->result();
    }

    public function manualPasswordReset($userid,$rPassword){
        $data = array(
                    'active'=> 1,
                    'password' => $rPassword
                     );
        $result = $this->ion_auth->update($userid, $data);

        if ($result) {
           return $this->db->select('u.username,p.mobile_no,p.first_name,s.first_name as stuName, s.id as student_id')
            ->from('users u')
            ->join('avatar a ','u.id=a.user_id')
            ->join('parent p ','a.stakeholder_id=p.id')
            ->join('student_admission s', 'p.student_id=s.id')
            ->where('a.avatar_type','2')
            ->where('u.id',$userid)
            ->get()->row();
            
        }else{
            return array();
        }

    }

    public function resetPassword($user_id,$randomString) {
        $data = array(
            'password' => $randomString
        );
        return $this->ion_auth->update($user_id, $data);
    }

    public function getEmailIdsById($ids) {
        $this->db->select("id, email, CONCAT(ifnull(first_name,''),' ',ifnull(last_name,'')) as name");
        $this->db->from('parent');
        $this->db->where_in('id', $ids);
        return $this->db->get()->result();
    }

    public function getParentData($id) {
        $this->db->select("id, email, CONCAT(ifnull(first_name,''),' ',ifnull(last_name,'')) as name");
        $this->db->from('parent');
        $this->db->where('id', $id);
        return $this->db->get()->row();
    }

    public function getAllClasses(){
        $this->db->select('id, class_name');
        $this->db->from('class');
        $this->db->where('is_placeholder',0);
        $this->db->where('acad_year_id',$this->acad_year->getAcadYearId());
        return $this->db->get()->result();
    }

    public function getProvisionReport() {
        $result = $this->db->select("c.class_name, u.active as Active, up.attempts, u.loggedin_atleast_once,a.old_user_id as oldUid, a.user_id as currentUid, u.token")
        ->from('user_prov_login_att up')
        ->join('parent p','p.id=up.parent_id', 'right')
        ->join('student_relation sr','sr.relation_id=p.id')
        ->join('student_admission s', 's.id=p.student_id')
        ->join('student_year sy', 'sy.student_admission_id=s.id')
        ->join('class c','sy.class_id=c.id')
        ->join('avatar a','a.stakeholder_id=p.id')
        ->join('users u','u.id=a.user_id')
        ->where_not_in('sr.relation_type',["Guardian","Driver","Driver_1","Driver_2"])
        ->where('a.avatar_type',2)
        ->where('s.admission_status',2)
        ->where('c.is_placeholder!=',1)
        ->where('sy.acad_year_id',$this->acad_year->getAcadYearId())
        ->where('sy.promotion_status!=', '4')
        ->where('sy.promotion_status!=', '5')
        ->order_by('c.display_order')
        ->get()->result();

        $classReport = array();
        $totalReport = array(
            'total' => 0,
            'excluded' => 0,
            'not_provisioned' => 0,
            'provisioned' => 0,
            'activated' => 0,
            'failed_activation' => 0,
            'logged_id' => 0,
            'tokens' =>0
        );
        foreach ($result as $key => $student) {
            $class = $student->class_name;
            if(!array_key_exists($class, $classReport)) {
                $classReport[$class] = array(
                    'total' => 0,
                    'not_provisioned' => 0,
                    'provisioned' => 0,
                    'activated' => 0,
                    'failed_activation' => 0,
                    'logged_id' => 0,
                    'excluded' => 0,
                    'tokens' => 0
                );
            }

            $classReport[$class]['total']++;
            $totalReport['total']++;

            // if($student->oldUid != '' && $student->oldUid = $student->currentUid) {
            //     $totalReport['excluded']++;
            //     $classReport[$class]['excluded']++;
            //     if($student->loggedin_atleast_once == 1) {
            //         $classReport[$class]['logged_id']++;
            //         $totalReport['logged_id']++;
            //     } else if($student->Active == 1 && $student->loggedin_atleast_once == 0){
            //         $classReport[$class]['activated']++;
            //         $totalReport['activated']++;
            //     }
            //     continue;
            // }
            if($student->token) {
                $classReport[$class]['tokens']++;
                $totalReport['tokens']++;
            }
            
            if($student->attempts == '') {
                if($student->loggedin_atleast_once == 1) {
                    $classReport[$class]['logged_id']++;
                    $totalReport['logged_id']++;
                } else if($student->Active == 1){
                    $classReport[$class]['activated']++;
                    $totalReport['activated']++;
                } else if($student->oldUid != '' && $student->oldUid = $student->currentUid) {
                    $classReport[$class]['provisioned']++;
                    $totalReport['provisioned']++;
                } else {
                    $classReport[$class]['not_provisioned']++;
                    $totalReport['not_provisioned']++;
                }
            } else {
                if($student->loggedin_atleast_once == 1) {
                    $classReport[$class]['logged_id']++;
                    $totalReport['logged_id']++;
                } else if($student->Active == 1){
                    $classReport[$class]['activated']++;
                    $totalReport['activated']++;
                } else if($student->attempts >= 6) {
                    $classReport[$class]['failed_activation']++;
                    $totalReport['failed_activation']++;
                } else if($student->attempts <= 5 && $student->attempts >= 1) {
                    $classReport[$class]['provisioned']++;
                    $totalReport['provisioned']++;
                } 
            }
        }
        // echo "<pre>"; print_r($totalReport); die();
        return array('clsReport' => $classReport, 'totalReport' => $totalReport);
    }

    public function deleteAttemptsById($pIds) {
        $this->db->where_in('parent_id', $pIds);
        return $this->db->delete('user_prov_login_att');
    }

    public function addNewAttempts($data, $type) {
        if($type == "insert") {
            return $this->db->insert_batch('user_prov_login_att', $data);
        } else {
            return $this->db->update_batch('user_prov_login_att', $data, 'parent_id');
        }
    }

    public function getCode($parentId) {
        return $this->db->select('code')->where('parent_id', $parentId)->get('user_prov_login_att')->row()->code;
    }

    public function activateUsers($userIds) {
        return $this->db->where_in('id', $userIds)->update('users', array('active' => 1));
    }

    public function refreshAttempts($codes) {
        $pIds = array();
        foreach ($codes as $pid => $code) {
            $pIds[] = $pid;
        }
        return $this->db->where_in('parent_id', $pIds)->update('user_prov_login_att', array('attempts'=>1));
    }

    public function getstudentNames(){
        $this->db->select("ss.id as stdYearId, sd.first_name AS stdName, cs.section_name, c.class_name ");
        $this->db->from('student_admission sd');
        $this->db->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db->where('ss.acad_year_id',$this->yearId);
        $this->db->where('ss.promotion_status!=','JOINED');
        $this->db->where('sd.admission_status',2);
        $this->db->join("class_section cs", "ss.class_section_id=cs.id",'left');
        $this->db->join("class c", "ss.class_id=c.id",'left');
        if($this->current_branch) {
            $this->db->where('c.branch_id',$this->current_branch);
        }
        return $this->db->get()->result();

    }

    public function getstdDataByClass($classId, $admission_type){
        $this->db_readonly->select("s.id as std_id,CONCAT(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as studentName,c.class_name,CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName,cs.section_name as sectionName, cs.id as csId,p.mobile_no as mobile,p.id as pid, u.active as Active, u.id as userId, sr.relation_type, u.username, up.attempts, s.dob, s.admission_no, u.loggedin_atleast_once, p.email, a.old_user_id as oldUid,  u.token, DATE_FORMAT(u.last_accessed_on,'%d/%m/%Y @ %h:%i %p') AS last_login, sy.picture_url")
        ->from('student_admission s')
        ->join('student_year sy', 'sy.student_admission_id=s.id')
        ->where('s.admission_status', '2')
        ->where('sy.acad_year_id', $this->acad_year->getAcadYearId())
        // ->join('academic_year ay', 's.admission_acad_year_id=ay.id')
        ->join('student_relation sr','s.id=sr.std_id')
        ->join('parent p','sr.relation_id=p.id')
        ->join('avatar a','a.stakeholder_id=p.id')
        ->join('users u','u.id=a.user_id')
        ->join('user_prov_login_att up','up.parent_id=p.id','left')
        ->where_not_in('sr.relation_type',["Guardian","Driver","Driver_1","Driver_2"])
        ->where('a.avatar_type',2);
        if($classId != -1){
            $this->db_readonly->where("sy.class_id", $classId);
        }
        if ($admission_type) {
            $this->db_readonly->where('sy.admission_type',$admission_type);
        }
        $this->db_readonly->where("s.admission_status", 2)
        ->where('sy.promotion_status!=', '4')
        ->where('sy.promotion_status!=', '5')
        ->join('class c','sy.class_id=c.id');
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        $this->db_readonly->join('class_section cs', 'sy.class_section_id=cs.id');
        // ->join('staff_master sm', 'sm.id=cs.class_teacher_id','left')
        $this->db_readonly->group_by('p.id');
        $this->db_readonly->order_by('cs.id, s.first_name');
        $data = $this->db_readonly->get()->result();
        return $data;
    }

    public function getstdDataByClassSection($sectionId, $admission_type){
        $this->db_readonly->select("s.id as std_id,CONCAT(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as studentName,c.class_name,CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName,cs.section_name as sectionName, cs.id as csId,p.mobile_no as mobile,p.id as pid, u.active as Active, u.id as userId, sr.relation_type, u.username, up.attempts, s.dob, s.admission_no, u.loggedin_atleast_once, p.email, a.old_user_id as oldUid, u.token, DATE_FORMAT(u.last_accessed_on,'%d/%m/%Y @ %h:%i %p') AS last_login, sy.picture_url")
        ->from('student_admission s')
        ->join('student_year sy', 'sy.student_admission_id=s.id')
        ->where('s.admission_status', '2')
        ->where('sy.acad_year_id', $this->acad_year->getAcadYearId())
        // ->join('academic_year ay', 's.admission_acad_year_id=ay.id')
        ->join('student_relation sr','s.id=sr.std_id')
        ->join('parent p','sr.relation_id=p.id')
        ->join('avatar a','a.stakeholder_id=p.id')
        ->join('users u','u.id=a.user_id')
        ->join('user_prov_login_att up','up.parent_id=p.id','left')
        ->where('a.avatar_type',2)
        ->where("sy.class_section_id", $sectionId)
        ->where("s.admission_status", 2)
        ->where_not_in('sr.relation_type',["Guardian","Driver","Driver_1","Driver_2"])
        ->where('sy.promotion_status!=', '4')
        ->where('sy.promotion_status!=', '5') 
        ->join('class c','sy.class_id=c.id');
        if ($admission_type) {
            $this->db_readonly->where('sy.admission_type',$admission_type);
        }
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }

        $this->db_readonly->join('class_section cs', 'sy.class_section_id=cs.id');
        // ->join('staff_master sm', 'sm.id=cs.class_teacher_id','left')
        $this->db_readonly->group_by('p.id');
        $this->db_readonly->order_by('cs.id, s.first_name');
        $data =  $this->db_readonly->get()->result();
        return $data;
    }

    public function getStudentByPhoneNo($phoneNo, $admission_type){
        $std_data = $this->db_readonly->select('student_id')->where('mobile_no', $phoneNo)->get('parent')->result();
        if(empty($std_data)) {
            return array();
        }
        $std_ids = array();
        foreach ($std_data as $key => $std) {
            array_push($std_ids, $std->student_id);
        }
        
        $data = $this->_getStdDataByIds($std_ids, $admission_type);
        return $data;
    }

    public function getStudentByEmail($email, $admission_type){
        $std_data = $this->db_readonly->select('student_id')->where('email', $email)->get('parent')->result();
        if(empty($std_data)) {
            return array();
        }
        $std_ids = array();
        foreach ($std_data as $key => $std) {
            array_push($std_ids, $std->student_id);
        }
        
        $data = $this->_getStdDataByIds($std_ids, $admission_type);
        return $data;
    }

    private function _getStdDataByIds($std_ids, $admission_type) {
        $this->db_readonly->select("s.id as std_id,CONCAT(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as studentName,c.class_name,CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName,cs.section_name as sectionName, cs.id as csId,p.mobile_no as mobile,p.id as pid, u.active as Active, u.id as userId, sr.relation_type, u.username, up.attempts, s.dob, s.admission_no, u.loggedin_atleast_once, p.email, a.old_user_id as oldUid, u.token, DATE_FORMAT(u.last_accessed_on,'%d/%m/%Y @ %h:%i %p') AS last_login, sy.picture_url")
        ->from('student_admission s')
        ->join('student_year sy', 'sy.student_admission_id=s.id')
        ->where('s.admission_status', '2')
        ->where('sy.acad_year_id', $this->acad_year->getAcadYearId())
        // ->join('academic_year ay', 's.admission_acad_year_id=ay.id')
        ->join('student_relation sr','s.id=sr.std_id')
        ->join('parent p','sr.relation_id=p.id')
        ->join('avatar a','a.stakeholder_id=p.id')
        ->join('users u','u.id=a.user_id')
        ->join('user_prov_login_att up','up.parent_id=p.id','left')
        ->where_not_in('sr.relation_type',["Guardian","Driver","Driver_1","Driver_2"])
        ->where('a.avatar_type',2)
        ->where_in("s.id", $std_ids)
        // ->where("(p.email='$email' OR u.email='$email')")
        ->join('class c','sy.class_id=c.id');
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        if ($admission_type) {
            $this->db_readonly->where('sy.admission_type',$admission_type);
        }
        $this->db_readonly->join('class_section cs', 'sy.class_section_id=cs.id');
        // ->join('staff_master sm', 'sm.id=cs.class_teacher_id','left')
        $this->db_readonly->group_by('p.id');
        $this->db_readonly->order_by('cs.id, s.first_name');
        return $this->db_readonly->get()->result();
    }

    public function getstdDataByName($name, $admission_type) {
        $this->db_readonly->select("s.id as std_id,CONCAT(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as studentName,c.class_name,CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName,cs.section_name as sectionName, cs.id as csId,p.mobile_no as mobile,p.id as pid, u.active as Active, u.id as userId, sr.relation_type, u.username, up.attempts, s.dob, s.admission_no, u.loggedin_atleast_once, p.email, a.old_user_id as oldUid, u.token, DATE_FORMAT(u.last_accessed_on,'%d/%m/%Y @ %h:%i %p') AS last_login, sy.picture_url")
        ->from('student_admission s')
        ->join('student_year sy', 'sy.student_admission_id=s.id')
        ->where('s.admission_status', '2')
        ->where('sy.promotion_status!=', '4')
        ->where('sy.promotion_status!=', '5') 
        ->where('sy.acad_year_id', $this->acad_year->getAcadYearId())
        // ->join('academic_year ay', 's.admission_acad_year_id=ay.id')
        ->join('student_relation sr','s.id=sr.std_id')
        ->join('parent p','sr.relation_id=p.id')
        ->join('avatar a','a.stakeholder_id=p.id')
        ->join('users u','u.id=a.user_id')
        ->join('user_prov_login_att up','up.parent_id=p.id','left')
        ->where_not_in('sr.relation_type',["Guardian","Driver","Driver_1","Driver_2"])
        ->where('a.avatar_type',2)
        ->like('s.first_name', $name,'both')
        ->join('class c','sy.class_id=c.id');
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        if ($admission_type) {
            $this->db_readonly->where('sy.admission_type',$admission_type);
        }
        $this->db_readonly->join('class_section cs', 'sy.class_section_id=cs.id');
        // ->join('staff_master sm', 'sm.id=cs.class_teacher_id','left')
        $this->db_readonly->group_by('p.id');
        $this->db_readonly->order_by('cs.id, s.first_name');
        $data = $this->db_readonly->get()->result();
        return $data;
    }

    public function getStudentByAdNo($adNo, $admission_type){
        $this->db_readonly->select("s.id as std_id,CONCAT(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as studentName,c.class_name,CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName,cs.section_name as sectionName, cs.id as csId,p.mobile_no as mobile,p.id as pid, u.active as Active, u.id as userId, sr.relation_type, u.username, up.attempts, s.dob, s.admission_no, u.loggedin_atleast_once, p.email, a.old_user_id as oldUid,  u.token, DATE_FORMAT(u.last_accessed_on,'%d/%m/%Y @ %h:%i %p') AS last_login, sy.picture_url")
        ->from('student_admission s')
        ->join('student_year sy', 'sy.student_admission_id=s.id')
        ->where('s.admission_status', '2')
        ->where('sy.promotion_status!=', '4')
        ->where('sy.promotion_status!=', '5') 
        ->where('sy.acad_year_id', $this->acad_year->getAcadYearId())
        // ->join('academic_year ay', 's.admission_acad_year_id=ay.id')
        ->join('student_relation sr','s.id=sr.std_id')
        ->join('parent p','sr.relation_id=p.id')
        ->join('avatar a','a.stakeholder_id=p.id')
        ->join('users u','u.id=a.user_id')
        ->join('user_prov_login_att up','up.parent_id=p.id','left')
        ->where('a.avatar_type',2)
        ->where_not_in('sr.relation_type',["Guardian","Driver","Driver_1","Driver_2"])
        ->where("s.admission_no", $adNo)
        ->join('class c','sy.class_id=c.id');
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        if ($admission_type) {
            $this->db_readonly->where('sy.admission_type',$admission_type);
        }
        $this->db_readonly->join('class_section cs', 'sy.class_section_id=cs.id');
        // ->join('staff_master sm', 'sm.id=cs.class_teacher_id','left')
        $this->db_readonly->group_by('p.id');
        $this->db_readonly->order_by('cs.id, s.first_name');
        $data = $this->db_readonly->get()->result();
        return $data;
    }

    public function get_student_details_by_id($studentId) {
        return $this->db->select("sd.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as stdName, c.class_name as clsName,  sd.admission_no, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection, c.id as cId, cs.id as csId")
        ->from('student_year sy')
        ->join('student_admission sd','sy.student_admission_id=sd.id')
        ->join("class_section cs", "sy.class_section_id=cs.id",'left')
        ->join("class c", "sy.class_id=c.id",'left')
        ->where('sd.id',$studentId)
        ->where('sy.acad_year_id', $this->acad_year->getAcadYearId())
        ->get()->row();
    }

    public function get_student_details_for_provision($student_ids) {
        $result = $this->db_readonly->select("s.id as std_id,CONCAT(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as studentName,c.class_name,CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName,cs.section_name as sectionName, cs.id as csId,p.mobile_no as mobile,p.id as pid, u.active as Active, u.id as userId, sr.relation_type, u.username, up.attempts, s.dob, s.admission_no, cs.class_teacher_id, CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staffName, up.code, u.loggedin_atleast_once, p.email, a.old_user_id as oldUid, u.token")
        ->from('student_admission s')
        ->where('s.id',$student_ids)
        ->join('student_year sy', 'sy.student_admission_id=s.id')
        ->where('sy.acad_year_id', $this->acad_year->getAcadYearId())
        ->join('student_relation sr','s.id=sr.std_id')
        ->join('parent p','sr.relation_id=p.id')
        ->join('avatar a','a.stakeholder_id=p.id')
        ->join('users u','u.id=a.user_id')
        ->join('user_prov_login_att up','up.parent_id=p.id','left')
        ->where('a.avatar_type',2)
        ->join('class c','sy.class_id=c.id')
        ->join('class_section cs', 'sy.class_section_id=cs.id')
        ->join('staff_master sm', 'sm.id=cs.class_teacher_id','left')
        ->group_by('p.id')
        ->get()->result();

        foreach($result as $key => $val){
            $val->siblings = array();
            if(!empty($val->oldUid) && $val->userId != $val->oldUid){
                $val->siblings = $this->get_sibling_names($val->oldUid,$val->studentName); 
            }
        }
        return $result;
    }

    public function get_user_provision_email_template() {
        return $this->db->select('*')
        ->from('email_template')
        ->where('name','user_provision')
        ->get()->row();
    }

    public function get_staff_provision_email_template() {
        return $this->db->select('*')
        ->from('email_template')
        ->where('name','staff_provision')
        ->get()->row();
    }
}