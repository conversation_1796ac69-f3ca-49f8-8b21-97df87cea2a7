<div class="profile-section">
    <div class="section-header">
        <h4><i class="fa fa-user me-2"></i>Student Profile</h4>
        <?php if ($studentData->profile_status == 'Unlock') { ?>
        <a href="<?php echo site_url('parent_controller/edit_profile_parent_mobile/'.$callFrom); ?>" class="edit-profile-btn">
            <i class="fa fa-pencil"></i>
        </a>
        <?php } ?>
    </div>
    <div class="section-content">
        <!-- Student Header with Photo and Name -->
        <div class="student-header">
            <?php if ($this->settings->isProfile_profile_enabled('STUDENT_PHOTO')) : ?>
            <div class="student-photo">
                <?php
                $picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/femalestu.png';
                $gender = 'Female';
                if($studentData->gender == 'M'){
                  $picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/malestu.png';
                  $gender = 'Male';
                }?>
                <img src="<?php echo (empty($studentData->picture_url)) ? $picUrl : $this->filemanager->getFilePath($studentData->picture_url); ?>" alt="Student Photo" class="student-avatar">
            </div>
            <?php endif ?>

            <?php if ($this->settings->isProfile_profile_enabled('STUDENT_NAME')) : ?>
            <div class="student-info">
                <h3 class="student-name"><?php echo ucfirst($studentData->stdName); ?></h3>
            </div>
            <?php endif ?>
        </div>

        <!-- Student Details -->
        <div class="profile-details">

            <?php if($this->settings->isProfile_profile_enabled('ADMISSION_NO') && !$isNewStudent){ ?>
            <div class="detail-item">
                <span class="detail-label">Admission No</span>
                <span class="detail-value"><?php echo $studentData->admissionNo; ?></span>
            </div>
            <?php } ?>

            <?php if($this->settings->isProfile_profile_enabled('ENROLLMENT_NUMBER')){ ?>
            <div class="detail-item">
                <span class="detail-label">Enrollment Number</span>
                <span class="detail-value"><?php echo $studentData->enrollment_number; ?></span>
            </div>
            <?php } ?>

            <?php if($this->settings->isProfile_profile_enabled('ALPHA_ROLL_NUMBER')){ ?>
            <div class="detail-item">
                <span class="detail-label">Alpha Roll Number</span>
                <span class="detail-value"><?php echo $studentData->alpha_rollnum; ?></span>
            </div>
            <?php } ?>

            <?php if($this->settings->isProfile_profile_enabled('CLASS_SECTION') && !$isNewStudent){ ?>
            <div class="detail-item">
                <span class="detail-label">Class / Section</span>
                <span class="detail-value">
                    <?php
                    echo !empty($studentData->className) && !empty($studentData->sectionName)
                        ? $studentData->className . '/' . $studentData->sectionName
                        : 'Not available';
                    ?>
                </span>
            </div>
            <?php } ?>

            <?php if($this->settings->getSetting('is_semester_scheme')){ ?>
            <div class="detail-item">
                <span class="detail-label">Semester</span>
                <span class="detail-value"><?php echo $studentData->semester; ?></span>
            </div>
            <?php } ?>

            <?php if ($this->settings->isProfile_profile_enabled('STUDENT_EMAIL')) : ?>
            <div class="detail-item">
                <span class="detail-label">Email ID</span>
                <span class="detail-value"><?php echo $studentData->student_email == '' ? 'Not available' : $studentData->student_email; ?></span>
            </div>
            <?php endif ?>

            <?php if ($this->settings->isProfile_profile_enabled('INITIAL_PASSWORD')) : ?>
            <div class="detail-item">
                <span class="detail-label">Password</span>
                <span class="detail-value"><?php echo $studentData->student_email_password == '' ? 'Not available' : $studentData->student_email_password; ?></span>
            </div>
            <?php endif ?>

            <?php if ($this->settings->isProfile_profile_enabled('STUDENT_DOB')) : ?>
            <div class="detail-item">
                <span class="detail-label">Date of Birth</span>
                <span class="detail-value"><?php echo ($studentData->dob) ? date('d-M-Y', strtotime($studentData->dob)) : 'Not available'; ?></span>
            </div>
            <?php endif ?>
            <?php if ($this->settings->isProfile_profile_enabled('STUDENT_MOTHER_TONGUE')) : ?>
            <div class="detail-item">
                <span class="detail-label">Mother Tongue</span>
                <span class="detail-value"><?php echo ($studentData->mother_tongue == '') ? 'Not available' : $studentData->mother_tongue; ?></span>
            </div>
            <?php endif ?>

            <?php if ($this->settings->isProfile_profile_enabled('STUDENT_GENDER')) : ?>
            <div class="detail-item">
                <span class="detail-label">Gender</span>
                <span class="detail-value"><?php echo $gender; ?></span>
            </div>
            <?php endif ?>

            <?php if ($this->settings->isProfile_profile_enabled('STUDENT_BLOOD_GROUP')) : ?>
            <div class="detail-item">
                <span class="detail-label">Blood Group</span>
                <span class="detail-value">
                    <?php echo (!empty($studentData) && $studentData->blood_group != '') ? $studentData->blood_group : 'Not available'; ?>
                </span>
            </div>
            <?php endif ?>

            <?php if ($this->settings->isProfile_profile_enabled('STUDENT_STOP')) : ?>
            <div class="detail-item">
                <span class="detail-label">Stop</span>
                <span class="detail-value">
                    <?php
                    echo (!empty($stops[$studentData->stop]))
                        ? $stops[$studentData->stop]
                        : 'Not available';
                    ?>
                </span>
            </div>
            <?php endif ?>

            <?php if ($this->settings->isProfile_profile_enabled('STUDENT_PICKUP_MODE')) : ?>
            <div class="detail-item">
                <span class="detail-label">Pickup Mode</span>
                <span class="detail-value"><?php echo ($studentData->pickup_mode == '') ? 'Not available' : $studentData->pickup_mode; ?></span>
            </div>
            <?php endif ?>

            <?php if ($this->settings->isProfile_profile_enabled('STUDENT_NATIONALITY')) : ?>
            <div class="detail-item">
                <span class="detail-label">Nationality</span>
                <span class="detail-value">
                    <?php
                    if (isset($studentData->nationality)) {
                        echo ($studentData->nationality == '') ? 'Not available' : $studentData->nationality;
                    } else {
                        echo 'Not available';
                    }
                    ?>
                </span>
            </div>
            <?php endif ?>

            <?php if ($this->settings->isProfile_profile_enabled('CATEGORY')) : ?>
            <div class="detail-item">
                <span class="detail-label">Category</span>
                <span class="detail-value"><?php echo ($studentData->category == '' || $studentData->category == '0') ? 'Not available' : $studentData->category; ?></span>
            </div>
            <?php endif ?>

            <?php if ($this->settings->isProfile_profile_enabled('STUDENT_CASTE')) : ?>
            <div class="detail-item">
                <span class="detail-label">Caste</span>
                <span class="detail-value"><?php echo ($studentData->caste == '' || $studentData->caste == '0') ? 'Not available' : $studentData->caste; ?></span>
            </div>
            <?php endif ?>

            <?php if ($this->settings->isProfile_profile_enabled('STUDENT_RELIGION')) : ?>
            <div class="detail-item">
                <span class="detail-label">Religion</span>
                <span class="detail-value"><?php echo ($studentData->religion == '' || $studentData->religion == '0') ? 'Not available' : $studentData->religion; ?></span>
            </div>
            <?php endif ?>

            <?php if ($this->settings->isProfile_profile_enabled('STUDENT_MOBILE_NUMBER')) : ?>
            <div class="detail-item">
                <span class="detail-label">Student Mobile Number</span>
                <span class="detail-value"><?php echo ($studentData->student_mobile_no == '') ? 'Not available' : $studentData->student_mobile_no; ?></span>
            </div>
            <?php endif ?>

            <?php if ($this->settings->isProfile_profile_enabled('PREFFERED_CONTACT_NUMBER')) : ?>
            <div class="detail-item">
                <span class="detail-label">Preferred Contact Number</span>
                <span class="detail-value"><?php echo ($studentData->preferred_contact_no == '') ? 'Not available' : $studentData->preferred_contact_no; ?></span>
            </div>
            <?php endif ?>

            <?php if ($this->settings->isProfile_profile_enabled('STUDENT_AADHAR')) : ?>
            <div class="detail-item">
                <span class="detail-label">Aadhar Number</span>
                <span class="detail-value"><?php echo ($studentData->aadhar_no == '') ? 'Not available' : $studentData->aadhar_no; ?></span>
            </div>
            <?php endif ?>

            <?php if ($this->settings->isProfile_profile_enabled('NAME_AS_PER_AADHAR')) : ?>
            <div class="detail-item">
                <span class="detail-label">Name As Per Aadhar</span>
                <span class="detail-value"><?php echo ($studentData->name_as_per_aadhar == '') ? 'Not available' : $studentData->name_as_per_aadhar; ?></span>
            </div>
            <?php endif ?>

            <?php if ($this->settings->isProfile_profile_enabled('STUDENT_ADDRESS') && !$isNewStudent) : ?>
            <?php if (!empty($studentAddress)) {
                foreach ($studentAddress as $val => $address) { ?>
                <div class="detail-item">
                    <span class="detail-label"><?php echo $val; ?></span>
                    <span class="detail-value">
                        <?php
                        if (empty($address)) {
                            echo 'Not available';
                        } else {
                            foreach ($address as $key => $s_ad) {
                                if($s_ad->Address_line1 == '' && $s_ad->Address_line2 == '' && $s_ad->area == '' && $s_ad->district == '' && $s_ad->state == '' && $s_ad->country == '' && $s_ad->pin_code == '') {
                                    echo 'Not available';
                                } else {
                                    $address_parts = array_filter([
                                        $s_ad->Address_line1,
                                        $s_ad->Address_line2,
                                        $s_ad->area,
                                        $s_ad->district,
                                        $s_ad->state,
                                        $s_ad->country,
                                        $s_ad->pin_code
                                    ]);
                                    echo implode(', ', $address_parts);
                                }
                            }
                        }
                        ?>
                    </span>
                </div>
                <?php } } ?>
            <?php endif ?>

            <?php if ($this->settings->isProfile_profile_enabled('STUDENT_HOUSE')) : ?>
            <div class="detail-item">
                <span class="detail-label">House</span>
                <span class="detail-value">
                    <?php
                    if ($studentData->student_house != 0 && $studentData->student_house != null && $studentData->student_house != '') {
                        echo ucwords($studentData->student_house);
                    } else {
                        echo 'Not available';
                    }
                    ?>
                </span>
            </div>
            <?php endif ?>

            <?php if ($this->settings->isProfile_profile_enabled('COMBINATION')) : ?>
            <div class="detail-item">
                <span class="detail-label">Combination</span>
                <span class="detail-value">
                    <?php
                    if ($studentData->combination_name != 0 && $studentData->combination_name != null && $studentData->combination_name != '') {
                        echo ucwords($studentData->combination_name);
                    } else {
                        echo 'Not available';
                    }
                    ?>
                </span>
            </div>
            <?php endif ?>
        </div>
    </div>
</div>

<!-- Modern Student Profile Styles -->
