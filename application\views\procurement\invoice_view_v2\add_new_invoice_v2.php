<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2'); ?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/invoice_controller_v2/invoice_management_dashboard'); ?>">Invoice Management Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/invoice_controller_v2/manage_all_invoices'); ?>">Invoice Management</a></li>
    <li>Add Invoice</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div style="width: 100%;" class="d-flex justify-content-between">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('procurement/invoice_controller_v2/manage_all_invoices') ?>">
                        <span class="fa fa-arrow-left"></span>
                        </a> 
                        Add Invoice
                    </h3>

                </div>
            </div>
        </div>
        <div class="panel-body">






        <div class="col-md-12">
                            <div class="container">
                                <div class="stepper">
                                        <div class="step-line"></div>
                                        <div class="step" data-step="1" >
                                                <div class="step-circle circle-1" style="background: green;">o</div>
                                                <div>Details</div>
                                        </div>
                                        <div class="step" data-step="2" >
                                                <div class="step-circle circle-2" >○</div>
                                                <div>Payment Details</div>
                                               
                                        </div>
                                        <div class="step" data-step="3" >
                                                <div class="step-circle circle-3">○</div>
                                                 <div>Attachments</div>
                                                
                                        </div>
                                        <div class="step" data-step="4" >
                                                <div class="step-circle circle-4">○</div>
                                                <div>Approval Policy</div>
                                        </div>
                                </div>

                                <div class="card p-4 col-md-12">
                                        <div id="step-1" class="step-content">
                                                <!-- Details starts here -->
                                                <div id="div-scrollable-1" class="details-container container gap-div" style="height: 500px;">
                                                        <h4>Details</h4>
                                                        <form id="step-1-form">
                                                        <input type="hidden" name="invoice_master_id" class="invoice_master_id" value="0">
                                                        <input type="hidden" name="insert_update_type" id="insert_update_type_basic" value="Add">
                                                                <div class="form-row">
                                                                        <div class="form-group col-md-6">
                                                                                <label for="invoice_number">Invoice No.
                                                                                </label>
                                                                                <input type="text" class="form-control"
                                                                                        id="invoice_number"
                                                                                        name="invoice_number"
                                                                                        readonly
                                                                                        placeholder="Auto Generated"
                                                                                        value=""
                                                                                        >
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="invoice_date">Invoice Date
                                                                                        <font color="red">*</font>
                                                                                </label>
                                                                                <input type="text" class="form-control"
                                                                                        placeholder="DD-MM-YYYY"
                                                                                        name="invoice_date"
                                                                                        id="invoice_date"
                                                                                        minlength="10"
                                                                                        maxlength="10"
                                                                                        required>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="invoice_type">Invoice Type<font
                                                                                                color="red">*</font>
                                                                                </label>
                                                                                <select class="form-control"
                                                                                        name="invoice_type"
                                                                                        id="invoice_type"
                                                                                        required>
                                                                                        <option value='Regular'>Regular</option>
                                                                                        <option value='Credit Note'>Credit Note</option>
                                                                                        <option value='Debit Note'>Debit Note</option>
                                                                                </select>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="due_date">Due Date <font color="red">*</font></label>
                                                                                <input type="text" class="form-control"
                                                                                        placeholder="DD-MM-YYYY"
                                                                                        name="due_date"
                                                                                        id="due_date"
                                                                                        minlength="10"
                                                                                        styll="z-index: 90;"
                                                                                        maxlength="10"
                                                                                        required>
                                                                        </div>
                                                                        
                                                                        <div class="form-group col-md-6">
                                                                                <label for="purchase_order_id">Purchase Order <font color="red">*</font></label>
                                                                                <select name="purchase_order_id" id="purchase_order_id"
                                                                                        class="form-control"
                                                                                        onchange="onchange_PO()"
                                                                                        required>
                                                                                        <option value="">Select..
                                                                                        </option>
                                                                                        <?php
                                                                                            if(!empty($POs)) {
                                                                                                foreach($POs as $key => $val) {
                                                                                                    echo "<option value='$val->id'>$val->request_number ($val->po_name)</option>";
                                                                                                }
                                                                                            }
                                                                                        ?>
                                                                                </select>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="vendor_id">Vendor Name</label>
                                                                                <select style="pointer-events: none; background: #e9ecef;" name="vendor_id" id="vendor_id" class="form-control">
                                                                                        <option selected value="">Select PO to display Vendor Name</option>
                                                                                </select>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="vendor_instruments_id">Vendor Instrument</label>
                                                                                <select name="vendor_instruments_id" id="vendor_instruments_id"
                                                                                        class="form-control"
                                                                                        >
                                                                                        <option value="">Select..
                                                                                        </option>
                                                                                </select>
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label
                                                                                        for="invoice_status">Invoice Status <font color="red">*</font></label>
                                                                                        <input type="text" class="form-control"
                                                                                        name="invoice_status"
                                                                                        id="invoice_status"
                                                                                        value="Draft"
                                                                                        required
                                                                                        readonly>
                                                                        </div>
                                                                        <div class="form-group col-md-12">
                                                                                <label for="invoice_remarks">Narration
                                                                                </label>
                                                                                <textarea rows="7" name="invoice_remarks" id="invoice_remarks" class="form-control" placeholder="Enter invoice name and description"></textarea>
                                                                        </div>
                                                                        
                                                                    </div>
                                                                <!-- </div> -->
                                                        </form>
                                                </div>
                                                <div class="col-md-12">
                                                        <button type="button" onclick="save_basic_details(this, 'step-1', 1)" class="btn btn-dark pull-right">Next</button>
                                                </div>
                                        </div>
                                        <div id="step-2" class="step-content hidden">
                                                <!-- Delivery lists here -->
                                                <div id="div-scrollable-2" class="product-list-container container gap-div"  style="overflow: auto; height: 500px; min-width: 100%;">
                                                <!--
                                                        <h4>Payment Details <span id="delivery-type"></span><span style="cursor: pointer; margin-left: 15px;" class="fa fa-info-circle" onclick="info_about_calculations()"></span> <span class="text-warning" id="warning-message"></span></h4> 
                                                -->
                                                        <form id="step-2-form">
                                                                <input type="hidden" name="invoice_master_id" class="invoice_master_id" value="0">
                                                                <input type="hidden" name="insert_update_type" id="insert_update_type_items" value="Add">
                                                                <input type="hidden" name="selected_purchase_order_id" class="selected_purchase_order_id" value="0">
                                                                <input type="hidden" name="selected_vendor_id" class="selected_vendor_id" value="0">
                                                                <input type="hidden" name="selected_reference_type" class="selected_reference_type" value="0">
                                                                <div class="form-row" id="deliveries-div" style="max-height: 400px; overflow: auto;">

                                                                        <div style="width: 100%;" id="po-details-div"></div>
                                                                        <div style="width: 100%;" id="delivery-details-div"></div>
                                                                        <div style="width: 100%;" id="payment-details-div"></div>

                                                                </div>
                                                        </form>
                                                </div>

                                                <div class="col-md-12">
                                                        <button type="button" onclick="save_delivery_items(this, 'step-2', 2)" class="btn btn-dark pull-right">Next</button>
                                                        <button id="delivery-challan-prev-btn" style="margin-right: 10px;" type="button" onclick="reach_at_prev_tab(this, 'step-1')" class="btn btn-outline-secondary pull-right">Previous</button>
                                                </div>
                                        </div>
                                        

                                        <div id="step-3" class="step-content hidden">
                                                <!-- Additional Attachements Section -->
                                                <div id="div-scrollable-3" class="milestones-container container gap-div" style="overflow: auto; height: 340px;">
                                                        <div class="">
                                                                <h5 class="font-weight-bold">Additional Attachements</h5>
                                                                <div class="empty-state col-md-6" id="attachments-list-empty" style="">
                                                                        <div class="col-md-12" style="height: 30px;"></div>
                                                                        <div>
                                                                                <div id="attachments-list">
                                                                                        <h5>No Attachments Found</h5>
                                                                                        <p class="text-muted">Please add Attachments to see</p>
                                                                                </div>
                                                                                <font color="" class="text-warning">Please use the right-side form to add attachments</font>
                                                                        </div>
                                                                </div>
                                                                <div id="additional_attachements_div" class="col-md-6" style="display: none; ">
                                                                        <form id="step-6-form">
                                                                        <input type="hidden" name="invoice_master_id" class="invoice_master_id" value="0">
                                                                        <table class="table table-bordered">
                                                                                <thead class="thead-dark">
                                                                                        <tr>
                                                                                                <th>File Name</th>
                                                                                                <th>Size</th>
                                                                                                <th>Remarks</th>
                                                                                                <th>Action</th>
                                                                                        </tr>
                                                                                </thead>
                                                                                <tbody id="additional_attachements_tbody">
                                                                                        <tr class="tr_class_remover">
                                                                                                <td colspan="4" class="text-center">
                                                                                                No documents found
                                                                                                </td>

                                                                                        </tr>
                                                                                </tbody>
                                                                        </table>
                                                                        </form>
                                                                </div>
                                                                <div class="col-md-6">
                                                                        <div id="add_additional_attachements_div" style="padding-left: 7px; overflow: auto; border-left: 3px dashed gray;">
                                                                                <form id="docs_form">
                                                                                <input type="hidden" name="invoice_master_id" class="invoice_master_id" value="0">
                                                                                <div class="form-group col-md-12">
                                                                                        <label for="invoice_document_type">Document Type
                                                                                        </label>
                                                                                        <select class="form-control" name="invoice_document_type" id="invoice_document_type">
                                                                                                <option value="Invoice Copy">Invoice Copy</option>
                                                                                                <option value="Delivery Challan">Delivery Challan</option>
                                                                                                <option value="PO">PO</option>
                                                                                                <option value="GRN">GRN</option>
                                                                                                <option value="Quality Report">Quality Report</option>
                                                                                                <option value="Other">Other</option>
                                                                                        </select>
                                                                                </div>
                                                                                <div class="form-group col-md-12">
                                                                                        <label for="additional_description_notes">Remarks
                                                                                        </label>
                                                                                        <textarea rows="3"  class="form-control" name="additional_description_notes" id="additional_description_notes"></textarea>
                                                                                </div>
                                                                                <div class="form-group col-md-12">
                                                                                        <label for="additional_attachements">Upload File<font color="red">*</font></label>
                                                                                        <div class="file-upload-container">
                                                                                                <div class="file-upload-box" id="dropZone">
                                                                                                        <i class="fas fa-cloud-upload-alt"></i> <!-- Font Awesome icon, include FA if needed -->
                                                                                                        <div>Drag & drop your file here or <span class="browse-link">browse file</span></div>
                                                                                                        <div class="file-name" id="fileName">No file selected</div>
                                                                                                        <input type="file" id="additional_attachements" name="additional_attachements" required accept=".pdf">
                                                                                                </div>
                                                                                        </div>
                                                                                </div>
                                                                                </form>
                                                                                <div class="col-md-12" style="height: 5px;"></div>
                                                                                <div class="col-md-12">
                                                                                        <button style="" id="add_doc" type="Button" class="btn btn-dark col-md-12" onclick="add_additional_notes(this, 'close')">Add Document</button>
                                                                                </div>
                                                                        </div>
                                                                </div>
                                                        </div>
                                                </div>
                                                        <div class="col-md-12"><button type="button" onclick="save_approver_details(this, 'step-3', 3)" class="btn btn-dark pull-right">Next</button>
                                                        <button style="margin-right: 10px;" type="button" onclick="reach_at_prev_tab(this, 'step-2')" class="btn btn-outline-secondary pull-right">Previous</button>
                                                </div>
                                        </div>



                                        <div id="step-4" class="step-content hidden">
                                                <!-- Approvers here -->
                                                <div id="div-scrollable-3" class="milestones-container container gap-div" style="overflow: auto; height: 500px;">
                                                        <h4>Approver's Details</h4>
                                                        <div class="col-md-12" id="approvers_div">

                                                        </div>
                                                </div>
                                                <div class="col-md-12">

                                                        <button type="button" onclick="save_and_close(this)" class="btn btn-dark pull-right">Submit</button>
                                                        <button style="margin-right: 10px;" type="button" onclick="reach_at_prev_tab(this, 'step-3')" class="btn btn-outline-secondary pull-right">Previous</button>

                                                        
                                                </div>
                                        </div>



                                </div>
                                <div class="d-flex justify-content-between mt-3 col-md-12">
                                        <!-- <button id="prevBtn" class="btn btn-outline-secondary"
                                                disabled>Previous</button>
                                        <button id="nextBtn" class="btn btn-dark">Next</button> -->
                                </div>
                        </div>
        </div>







        
        </div>
    </div>
</div>


 <?php
        $this->load->view('procurement/invoice_view_v2/__scripts_add_new_invoice_v2.php');
 ?>






<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
        .center-div {
                display: flex;
                justify-content: center;
                align-items: center;
        }

        .gap-div {
                margin: auto auto 6rem;
                background-color: #fff;
                padding: 2rem;
        }

        .gap-div-top {
                margin-top: 2rem;
        }

        /* UI for stepped component */
        .stepper {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 20px;
                position: relative;
        }

        .step {
                text-align: center;
                position: relative;
                z-index: 1;
        }

        .step-circle {
                width: 30px;
                height: 30px;
                border-radius: 50%;
                background: gray;
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 5px;
                position: relative;
                z-index: 2;
        }

        .active-step {
                background: black;
        }

        .step-line {
                position: absolute;
                top: 15px;
                left: 0;
                width: 100%;
                height: 4px;
                background: gray;
                z-index: 1;
        }

        .hidden {
                display: none;
        }

        div#div-scrollable-6::-webkit-scrollbar {
        width: 12px; /* Adjust as needed */
        }

        /* Style the scrollbar track */
        div#div-scrollable-6::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        }

        /* Customize the scrollbar thumb appearance */
        div#div-scrollable-6::-webkit-scrollbar-thumb {
        background: #eee; /* Adjust the color as desired */
        }

        div#div-scrollable-6 {
        scrollbar-width: thin;
        }




        div#div-scrollable-5::-webkit-scrollbar {
        width: 12px; /* Adjust as needed */
        }

        /* Style the scrollbar track */
        div#div-scrollable-5::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        }

        /* Customize the scrollbar thumb appearance */
        div#div-scrollable-5::-webkit-scrollbar-thumb {
        background: #eee; /* Adjust the color as desired */
        }

        div#div-scrollable-5 {
        scrollbar-width: thin;
        }



        div#div-scrollable-4::-webkit-scrollbar {
        width: 12px; /* Adjust as needed */
        }

        /* Style the scrollbar track */
        div#div-scrollable-4::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        }

        /* Customize the scrollbar thumb appearance */
        div#div-scrollable-4::-webkit-scrollbar-thumb {
        background: #eee; /* Adjust the color as desired */
        }

        div#div-scrollable-4 {
        scrollbar-width: thin;
        }



        div#div-scrollable-3::-webkit-scrollbar {
        width: 12px; /* Adjust as needed */
        }

        /* Style the scrollbar track */
        div#div-scrollable-3::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        }

        /* Customize the scrollbar thumb appearance */
        div#div-scrollable-3::-webkit-scrollbar-thumb {
        background: #eee; /* Adjust the color as desired */
        }

        div#div-scrollable-3 {
        scrollbar-width: thin;
        }




        div#div-scrollable-1::-webkit-scrollbar {
        width: 12px; /* Adjust as needed */
        }

        /* Style the scrollbar track */
        div#div-scrollable-1::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        }

        /* Customize the scrollbar thumb appearance */
        div#div-scrollable-1::-webkit-scrollbar-thumb {
        background: #eee; /* Adjust the color as desired */
        }

        div#div-scrollable-1 {
        scrollbar-width: thin;
        }

        .file-upload-container {
        width: 100%;
        margin: 10px 0;
    }
    
    .file-upload-box {
        border: 2px dashed #ccc;
        border-radius: 5px;
        height: 100px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 20px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
        position: relative;
    }
    
    .file-upload-box:hover {
        border-color: #999;
        background-color: #f9f9f9;
    }
    
    .file-upload-box i {
        font-size: 24px;
        color: #666;
        margin-bottom: 10px;
    }
    
    .file-upload-box .browse-link {
        color: #0066cc;
        text-decoration: underline;
        cursor: pointer;
    }
    
    .file-upload-box input[type="file"] {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        opacity: 0;
        cursor: pointer;
    }
    
    .file-upload-box .file-name {
        margin-top: 10px;
        font-size: 14px;
        color: #333;
    }





div#gdc-table::-webkit-scrollbar {
  width: 12px; /* Adjust as needed */
}

/* Style the scrollbar track */
div#gdc-table::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

/* Customize the scrollbar thumb appearance */
div#gdc-table::-webkit-scrollbar-thumb {
  background: #eee; /* Adjust the color as desired */
}

div#gdc-table {
  scrollbar-width: thin;
}





div#total-table::-webkit-scrollbar {
  width: 12px; /* Adjust as needed */
}

/* Style the scrollbar track */
div#total-table::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

/* Customize the scrollbar thumb appearance */
div#total-table::-webkit-scrollbar-thumb {
  background: #eee; /* Adjust the color as desired */
}

div#total-table {
  scrollbar-width: thin;
}





div#div-scrollable-2::-webkit-scrollbar {
  width: 12px; /* Adjust as needed */
}

/* Style the scrollbar track */
div#div-scrollable-2::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

/* Customize the scrollbar thumb appearance */
div#div-scrollable-2::-webkit-scrollbar-thumb {
  background: #eee; /* Adjust the color as desired */
}

div#div-scrollable-2 {
  scrollbar-width: thin;
}




div#table_div_tag::-webkit-scrollbar {
  width: 12px; /* Adjust as needed */
}

/* Style the scrollbar track */
div#table_div_tag::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

/* Customize the scrollbar thumb appearance */
div#table_div_tag::-webkit-scrollbar-thumb {
  background: #eee; /* Adjust the color as desired */
}

div#table_div_tag {
  scrollbar-width: thin;
}
</style>