<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2');?>">Procurement</a></li>
    <!-- <li><a href="<?php // echo site_url('procurement/inventory_controller_v2/item_master_widgets');?>">Item Master Dashboard</a></li> -->
    <li>Item Master</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <h3>
                    <a href="<?php echo site_url('procurement/requisition_controller_v2');?>" class="control-primary"><span class="fa fa-arrow-left"></span></a>
                    Item Master
                </h3>
            </div>
        </div>
    
        <div class="col-md-12" style="">
                <div class="col-md-3" style="">
                    <div id="categories_span" style="height: 500px; overflow: auto;" class="table-responsive"></div>
                </div>

                <div class="col-md-9" style="display: none;" id="tab_div">
                    <div>
                        <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button onclick="categoryTab()" style="border: none;" class="nav-link" id="categories_details_span_button"  type="button" role="tab" aria-selected="false">Category Details</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button onclick="subCategoryTab()" style="border: none;" class="nav-link active" id="sub_categories_span_button"  type="button" role="tab" aria-selected="true">Sub Category</button>
                            </li>
                            
                            <!-- Approvers policy commented b'coz now we have implemented staff dept level approvers and we should use them for PO modules -->
                            <!-- <li class="nav-item" role="presentation">
                                <button onclick="approvalsTab()" style="border: none;" class="nav-link" id="approval_categories_span_button"  type="button" role="tab" aria-selected="true">Approver Policy</button>
                            </li> -->
                        </ul>
                    </div>
                    <span id="sub_categories_span" style="height: 455px;" class="table-responsive"></span>
                    <span id="categories_details_span" style="display: none;">Select a category</span>
                    <span id="approval_details_span" style="display: none;">Select a category</span>
                </div>
        </div>

    </div>
</div>

<?php
    $avatar_id= $this->authorization->getAvatarId();
?>


<!-- Category Add Modal -->
<div id="add_category_modal" class="modal fade" tabindex="-1" role="dialog" style="width:50%;margin:auto;top:1%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-dialog modal-content" style="left: auto; ">
        <div class="modal-header">
            <h4 class="modal-title">Add Category</h4>
            <button type="button" class="close" data-dismiss="modal"> <font color="red" class="fa fa-times"></font></button>
        </div>
        <form>
        <div id="category-input" class="modal-body form-horizontal">
        <div class="form-group">
                <label class="control-label col-md-3" for="category_name">Category Name <font color="red">*</font></label>
                <div class="col-md-9">
                    <div class="input-group">
                        <span class="input-group-addon">
                            <span class="fa fa-pencil"></span>
                        </span>
                        <input type="text" name="category_name" class="form-control" id="category_name" placeholder="Enter category of the item" onkeyup="category_validity(this)" onpaste="category_validity(this)" onchange="category_validity(this)" />
                    </div>
                    <div class="help-block">Enter category name.</div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-3" for="category_type">Category Type <font color="red">*</font> <span class="fa fa-info btn-secondary btn-sm" style="cursor: pointer;" onclick="category_type_info();"></span></label>
                <div class="col-md-9">
                    <div class="input-group">
                        <span class="input-group-addon">
                            <span class="fa fa-caret-down"></span>
                        </span>
                        <select name="category_type" id="category_type" class="form-control">
                            <option value="Consumables">Consumables</option>
                            <option value="Regular Item">Regular Item</option>
                            <option value="Assets">Assets</option>
                            <option value="Services">Services</option>
                        </select>
                    </div>
                    <div class="help-block">Select category type.</div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-3" for="approvar_1">Category Administrator</label>
                <div class="col-md-9">
                    <div class="input-group">
                        <span class="input-group-addon">
                            <span class="fa fa-user"></span>
                        </span>
                        <select name="category_administrator" id="category_administrator" class="form-control">
                            <option value="">All Inventory Users</option>
                            <?php foreach($staffList as $satff){ ?>
                                <option value="<?php echo $satff->id ?>"><?php echo $satff->staffName ?></option>
                            <?php } ?>
                        </select>
                    </div>
                    <div class="help-block">If this option is selected, only administrators will be authorized to perform issue/return and allocate/collect operations. Otherwise, any authorized inventory user will have access to these functions.</div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-3 control-label" for="is_sellable">Is Sellable?</label>  
                <div class="col-md-9">
                    <label class="check"><input style="height: 20px; width: 20px; top: 5px;" type="checkbox" class="category_icheckbox" name="is_sellable" id="is_sellable" value="" /> 
                    <span style="margin-top: -5px;" class="help-block">Check this box if products under this category are sellable.</span></label>
                </div>
            </div>
            <div class="form-group">
                <label for="name" class="col-md-3 control-label">Category Description</label>
                <div class="col-md-9 col-xs-12">
                    <div class="input-group">
                        <textarea placeholder="Enter description" rows="5" type="text" class="form-control" id="category_description" name="category_name" maxlength="1000"></textarea>
                    </div>
                    <div class="help-block">Enter is not allowed (maximum 1000 charactors only)</div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <div> <input type="reset" value="Clear Form" class="btn btn-secondary"></div>
            <button id="item_master_cat_btn" type="button" id="" onclick="add_category()" class="btn btn-primary col-sm-3">Add Category</button>
        </div>
        </form>
    </div>
</div>

<!-- Sub category add model -->
<div class="modal fade" id="add_sub_category_modal" tabindex="-1" role="dialog" style="width:80%;margin:auto;top:1%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
        <div class="modal-header" style="border-bottom: 2px solid #ccc;">
            <h4 class="modal-title" id="modalHeader"><span id="category_name_in_modal_header"></span></h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
      
        <form id="my_form_sub_category">
        <input type="hidden" id="edit_or_add">
        <input type="hidden" id="sub_category_id_for_update">
            <div class="modal-body">
                <div class="col-md-12">
                    <div class="col-md-6">
                        <input type="hidden" id="category_id_modal" value="">
                        <div class="form-group">
                            <label class="col-md-3" style="text-align: right;" for="product_name">Sub-category <font color="red">*</font></label>  
                            <div class="col-md-9">
                                <div class="input-group">
                                <span class="input-group-addon">
                                    <span class="fa fa-pencil"></span>
                                </span>

                                <input placeholder="Enter Sub-category Name" id="product_name" name="product_name" type="text"  class="form-control input-md" required="" onkeyup="sub_category_validity(this)" onpaste="sub_category_validity(this)" onchange="sub_category_validity(this)" />
                                </div>
                                <div class="help-block">Enter sub-category name (Shoes, Shirts, Table etc...)</div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-3" style="text-align: right;" for="product_name">Budget Sub category <font color="red">*</font></label>  
                            <div class="col-md-9">
                                <div class="input-group">
                                <span class="input-group-addon">
                                    <span class="fa fa-caret-down"></span>
                                </span>
                                <select class="form-control" name="expenseSubCatId" id="expenseSubCatId">
                                    <?php if(!empty($expenseSubCategories)){
                                        foreach($expenseSubCategories as $key => $subCategory){
                                            echo "<option value='$subCategory->id'>$subCategory->category_name > $subCategory->name</option>";
                                        }
                                    }else{
                                        echo "<option value='-1'>No category to show!</option>";
                                    } ?>
                                </select>
                                </div>
                                <div class="help-block">Select sub-category</div>
                            </div>
                        </div>

                        <div class="form-group" style="display: none;">
                            <label class="col-md-3 control-label" style="text-align: right;" for="is_stockable">Is Stockable?</label>  
                            <div class="col-md-9">
                                <label class="check"><input type="checkbox" class="sub_category_icheckbox" name="is_stockable" id="is_stockable" value="" /> 
                                <span style="margin-top: -5px;" class="help-block">Check this box if product is stockable.</span></label>
                            </div>
                            
                        </div>

                        
                    </div>

                    <div class="col-md-6" style="">
                        <div class="form-group">
                        <label style="text-align: right;" class="col-md-3 control-label" for="brand_name">Description</label>
                            <div class="col-md-9">
                                <textarea placeholder=" Product Description" id="description" name="description" rows="3" type="checkbox" class="form-control input-md"></textarea>
                                <span class="help-block">Add description of product.</span>
                            </div>
                        </div>
                    </div>


                    <!-- attributes -->
                    <?php
                        $attributes = $this->config->item('inventory_attributes');
                        $attrArry = array_chunk($attributes, 3);
                    ?>
                    <div class="col-md-12"></div>
                    <div class="col-md-12">
                        <strong class="modal-title col-md-12">Select Attributes <font color="red">*</font></strong> &nbsp;&nbsp;&nbsp;<span id="warn" class="text-danger" style="display: none;">(Select atleast one attribute)</span>
                    </div>
                    <div class="col-md-12">
                        <div class="col-md-12" style="color: red;" id="attribute_validity_information"></div>
                        <?php foreach ($attrArry as $key => $value) {
                            echo '<div class="col-md-3">';
                            foreach ($value as $key => $val) {
                                echo '<input id="'.$val.'" type="checkbox"  name="attributes[]" class="checkbox_attr" value="'.$val.'" /> '.'<label style="position: relative; top: -5px;" class="check" for="'.$val.'">'.ucwords($val).'</label><br>';
                            }
                            echo '</div>';
                        } ?>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div> <input type="reset" value="Clear Form" class="btn btn-secondary"></div>
                <button id="itemmaster_sub_cat_btn" type="button" class="btn btn-primary" onclick="add_sub_category()">Save and Close</button>
            </div>
        </form>
    </div>
  </div>

<!-- add new item model -->
<div class="modal fade" id="add_item_modal" tabindex="-1" role="dialog" style="width:92%;margin:0 auto;" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="modalHeader">Add New Item Under <span id="item_modal_header"></span></h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
       <form id="my_form_add_item">
        <div class="modal-body">
            <div class="col-md-12" id="add_item_popup" style="overflow: auto; height: 500px;">
                
                <input type="hidden" name="product_id" id="product_id" value="">
                <input type="hidden" name="varient_id" id="varient_id" value="">
                
                        <div class="col-md-6" style="margin-bottom: 10px;"> <div class="card cd_border">
                            <div style="" class="card-header panel_heading_new_style_staff_border">
                                <div class="row" style="margin: 0px">
                                    <h3>
                                    Basics
                                    </h3>
                                </div>
                            </div>
                            <div class="form-group">
                                <label style="text-align: right;" class="col-md-3 control-label" for="variant_name">Item Name <font color="red">*</font></label>  
                                <div class="col-md-9">
                                <div class="input-group">
                                    <span class="input-group-addon">
                                        <span class="fa fa-pencil"></span>
                                    </span>
                                    <input placeholder="Enter respected field value" type="text" id="variant_name" class="form-control amountClass" name="variant_name" onkeyup="item_validity(this)" onpaste="item_validity(this)" onchange="item_validity(this)" />
                                   
                                    </div>
                                    <div class="help-block">Enter item name.</div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label style="text-align: right;" class="col-md-3 control-label" for="variant_name">SKU Code</label>  
                                <div class="col-md-9">
                                <div class="input-group">
                                    <span class="input-group-addon">
                                        <span class="fa fa-pencil"></span>
                                    </span>
                                    <input placeholder="Enter respected field value" type="text" id="sku_code" class="form-control" name="sku_code" onkeyup="validate_sku_asUnique(this)" onpaste="validate_sku_asUnique(this)" onchange="validate_sku_asUnique(this)" />
                                   
                                    </div>
                                    <font id="unique_sku_validation_block" class="help-block text-danger"></font>
                                    <div class="help-block">Type SKU code (if any)</div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label style="text-align: right;" class="col-md-3 control-label" for="unit_type">Unit Type</label>  
                                <div class="col-md-9">
                                <div class="input-group">
                                    <span class="input-group-addon">
                                        <span class="fa fa-caret-down"></span>
                                    </span>
                                    <select id="unit_type" name="unit_type" class="form-control input-md">
                                        <option value=""><?php echo "Select Unit Type" ?></option>
                                        <?php foreach ($unit_types as $key => $type) { ?>
                                                <option value="<?php echo $type; ?>"><?php echo $type; ?></option>
                                        <?php } ?>
                                    </select>
                                </div>
                                    <span class="help-block">BOX, Peaces, Packets, ROLL, SHEET</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label style="text-align: right;" class="col-md-3 control-label" for="threshold_quantity">Threshold Quantity</label>  
                                <div class="col-md-9">
                                    <div class="input-group">
            <span class="input-group-addon">
                <span class="fa fa-pencil"></span>
            </span>
                                    <input type="text" id="threshold_quantity" class="form-control amountClass" name="threshold_quantity"/>
                                     
                                    </div>
        <div class="help-block">Give item threshold quantity.</div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label style="text-align: right;" class="col-md-3 control-label" for="hsn_sac">HSN/SAC No.</label>  
                                <div class="col-md-9">
                                <div class="input-group">
            <span class="input-group-addon">
                <span class="fa fa-pencil"></span>
            </span>
                                    <input placeholder="Enter respected field value" type="text" id="hsn_sac" class="form-control " name="hsn_sac"/>
                                     
                                    </div>
        <div class="help-block">HSA/SAC Number</div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label style="text-align: right;" class="col-md-3 control-label" for="is_perishable">Is Perishable?</label>  
                                <div class="col-md-9">
                                    <select name="is_perishable" id="is_perishable" class="form-control select">
                                        <option value="NO">NO</option>
                                        <option value="YES">YES</option>
                                    </select>
                                    <span class="help-block">Select here</span>

                                </div>
                            </div>
                        </div></div>
                        <div class="col-md-6" id="" style="margin-bottom: 10px;"> <div class="card cd_border">
                            <div style="margin: 0 0 1vh 0;" class="card-header panel_heading_new_style_staff_border">
                                <div class="row" style="margin: 0px;">
                                    <h3>
                                    Attributes
                                    </h3>
                                </div>
                            </div>
                            <div id="modal_attributes_id"></div>
                        </div></div>

            </div>
        </div>
        <div class="modal-footer">
            <div> <input type="reset" value="Clear Form" class="btn btn-secondary"></div>
            <button id="addItemButton" type="button" class="btn btn-primary" onclick="add_item()">Add Item</button>
        </div>
        </form>
    </div>
  </div>
  <div id="flash_storage_div" style="display: none;"></div>

  <!-- delete items modal -->
  <div class="modal fade" id="delete_item_modal" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content" style="width: 50%;margin: auto;" >
            <div class="modal-header">
                <h4 class="modal-title">Delete item / items from <span style="font-weight: bold;" id="delete_sub_category_name"></span></h4>
                <button type="button" class="close" data-dismiss="modal"><font color="red" class="fa fa-times"></font></button>
            </div>
            <div class="modal-body">
                <div id="delete_multiple_items_box" style="overflow: auto; height: 500px;"></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-danger" onclick="delete_items()">Delete Items</button>
            </div>
        </div>
    </div>
</div>

<!-- Information about item model -->
<div class="modal fade" id="item_information_modal" tabindex="-1" role="dialog" style="width:40%; margin: 0 auto;top:0%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="modalHeader_of_item"><span id="item_name_information"></span></h4>
        <button id="inf_abt_item_btn_id" style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
        <div id="inf_item_popup" class="modal-body" style="overflow: auto; height: 300px;">
            <form> <div id="item_information_div"></div> </form>
        </div>
        <div class="modal-footer">
            <button type="button" style="display: none;" class="btn btn-success item_class" onclick="saveItemInforation()"><span class="fa fa-save"></span> Save</button>
            <button type="button" style="display: none;" class="btn btn-danger item_class" onclick="cancelEditingItemInforation()">Cancel</button>
        </div>
    </div>
  </div>
  <div id="format_flash_storer" style="display: none;"></div>

  <!-- Receipt Format -->
  <div id="receipt_html_templates"  class="modal fade" role="dialog" style="width: 90%; margin: auto;">
    <div class="modal-dialog">
        <div class="panel panel-default new-panel-style_3">
            <div class="panel-heading new-panel-heading">
                <h3 class="panel-title"><strong>HTML for Receipt</strong></h3>
            </div>
            <div class="panel-body">
             <div class="form-group">
                <label class="col-md-3 col-xs-12 control-label">Html Template</label>
                <div class="col-md-8 col-xs-12">
                    <textarea rows="20" class="form-control" name="html_template" id="html_template_id"></textarea>
                </div>
            </div>                                    
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" onclick="submit_receipt_template()">Submit Template</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
  </div>

   <!-- Edit category modal -->
   <div id="edit_category_modal" class="modal fade" tabindex="-1" role="dialog" style="width:50%;margin:auto;top:1%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-dialog modal-content" style="left: auto; ">
        <div class="modal-header">
            <h4 class="modal-title">Update Category</h4>
            <button type="button" class="close" data-dismiss="modal"><font color="red" class="fa fa-times"></font></button>
        </div> <form>
        <div id="category-input" class="modal-body form-horizontal">
            <div class="form-group">
                <label class="control-label col-md-3" for="category_name">Category Name <font color="red">*</font></label>
                <div class="col-md-9">
                    <input type="text" name="category_name" class="form-control" id="category_name_edit" placeholder="Enter category of the item" onkeyup="category_validity(this)" onpaste="category_validity(this)" onchange="category_validity(this)" />
                    <span class="help-block">Provide new category name.</span>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-3" for="category_status_edit">Category Status</label>
                <div class="col-md-9">
                    <div class="input-group">
                        <span class="input-group-addon">
                            <span class="fa fa-caret-down"></span>
                        </span>
                        <select name="category_status_edit" id="category_status_edit" class="form-control">
                            <option value="1">Active</option>
                            <option value="0">De-active</option>
                        </select>
                    </div>
                    <div class="help-block">Select category type.</div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-3" for="category_type_edit">Category Type <font color="red">*</font> <span class="fa fa-info btn-secondary btn-sm" style="cursor: pointer;" onclick="category_type_info();"></span></label>
                <div class="col-md-9">
                    <div class="input-group">
                        <span class="input-group-addon">
                            <span class="fa fa-caret-down"></span>
                        </span>
                        <select name="category_type" id="category_type_edit" class="form-control">
                            <option value="Consumables">Consumables</option>
                            <option value="Regular Item">Regular Item</option>
                            <option value="Assets">Assets</option>
                            <option value="Services">Services</option>
                        </select>
                    </div>
                    <div class="help-block">Select category type.</div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-3" for="category_administrator_edit">Category Administrator</label>
                <div class="col-md-9">
                    <div class="input-group">
                        <span class="input-group-addon">
                            <span class="fa fa-user"></span>
                        </span>
                        <select name="category_administrator" id="category_administrator_edit" class="form-control">
                            <option value="">Select...</option>
                            <?php foreach($staffList as $satff){ ?>
                                <option value="<?php echo $satff->id ?>"><?php echo $satff->staffName ?></option>
                            <?php } ?>
                        </select>
                    </div>
                    <div class="help-block">If selected, only admin can perform issue/return and allocate/collect otherwise any authorized inventory user can do this.</div>
                </div>
            </div>

            <div class="form-group">
                <label class="col-md-3 control-label" for="is_sellable">Is Sellable?</label>  
                <div class="col-md-9">
                    <label class="check"><input type="checkbox" class="category_icheckbox" name="is_sellable" id="is_sellable_edit" value="" /> </label>
                    <span class="help-block">Check this box if products under this category are sellable.</span>
                </div>
            </div>
            <div class="form-group">
                <label for="name" class="col-md-3 control-label">Category Description</label>
                <div class="col-md-9 col-xs-12">
                    <div class="input-group">
                        <textarea rows="5" type="text" class="form-control" id="category_description_edit" name="category_name" maxlength="1000"></textarea>
                    </div>
                    <div class="help-block">Enter is not allowed (maximum 1000 charactors only)</div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <div> <input type="reset" value="Clear Form" class="btn btn-secondary"></div>
            <button type="button" id="item_master_edit_cat_btn" onclick="edit_category()" class="btn btn-primary">Update Category</button>
        </div> </form>
    </div>
</div>

<!-- Approvals modal -->
<div class="modal fade" id="approvar_modal" tabindex="-1" role="dialog" style="width:60%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="approval_modalHeader">Create Approvals</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
       <form id="approval_form">
        <input type="hidden" id="category_id_for_approval">
        <input type="hidden" id="approval_type_for_approval">
        <div class="modal-body">
            <div class="col-md-12">
            <div class="form-group" id="approverAlgorithmContainer">
                <label class="control-label col-md-3" for="approval_algorithm">Approval Algorithm <font color="red">*</font></label>
                <div class="col-md-9">
                    <div class="input-group">
                        <span class="input-group-addon">
                            <span class="fa fa-caret-down"></span>
                        </span>
                        <select name="approval_algorithm" id="approval_algorithm" class="form-control" onchange="show_hide_approvar('apr_div_2', 'approvar_2', 'apr_div_3', 'approvar_3')">
                            <option value="1">Primary</option>
                            <option value="2">Secondary</option>
                            <option value="3">Ternary</option>
                            <option value="4">Approvers Based On The Budget</option>
                        </select>
                    </div>
                    <div class="help-block">Select which levels of approval is required while allocating items</div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-3" for="approvar_1">Primary Approver <font color="red">*</font></label>
                <div class="col-md-9">
                    <div class="input-group">
                        <span class="input-group-addon">
                            <span class="fa fa-caret-down"></span>
                        </span>
                        <select name="approvar_1" id="approvar_1" class="form-control" required>
                            <option value="">Select...</option>
                            <?php foreach($staffList as $satff){ ?>
                                <option value="<?php echo $satff->id ?>"><?php echo $satff->staffName ?></option>
                            <?php } ?>
                        </select>
                        <input type="number" min="0" value="0" name="approver-1-min_amount" id="approver-1-min_amount" style="display:none;">
                    </div>
                    <div class="help-block">Select</div>
                </div>
            </div>
            <div style="display: none;" id="apr_div_2" class="form-group">
                <label class="control-label col-md-3" for="approvar_2">Secondary Approver <font color="red">*</font></label>
                <div class="col-md-9">
                    <div class="input-group">
                        <span class="input-group-addon">
                            <span class="fa fa-caret-down"></span>
                        </span>
                        <select name="approvar_2" id="approvar_2" class="form-control">
                        <option value="">Select...</option>
                            <?php foreach($staffList as $satff){ ?>
                                <option value="<?php echo $satff->id ?>"><?php echo $satff->staffName ?></option>
                            <?php } ?>
                        </select>
                        <input type="number" min="0" value="0" name="approver-2-min_amount" id="approver-2-min_amount" style="display:none;">
                    </div>
                    <div class="help-block">Select</div>
                </div>
            </div>
            <div style="display: none;" id="apr_div_3" class="form-group">
                <label class="control-label col-md-3" for="approvar_3">Ternary Approver <font color="red">*</font></label>
                <div class="col-md-9">
                    <div class="input-group">
                        <span class="input-group-addon">
                            <span class="fa fa-caret-down"></span>
                        </span>
                        <select name="approvar_3" id="approvar_3" class="form-control">
                        <option value="">Select...</option>
                            <?php foreach($staffList as $satff){ ?>
                                <option value="<?php echo $satff->id ?>"><?php echo $satff->staffName ?></option>
                            <?php } ?>
                        </select>
                        <input type="number" min="0" value="0" name="approver-3-min_amount" id="approver-3-min_amount" style="display:none;">
                    </div>
                    <div class="help-block">Select</div>
                </div>
            </div>
            </div>
        </div>
        <div class="modal-footer">
            <div> <input type="reset" value="Clear Form" ></div>
            <button type="button" class="btn btn-primary" onclick="create_approvals()">Submit</button>
        </div>
        </form>
    </div>
  </div>

  <!-- Modal Item Details -->
  <div class="modal fade" id="view_all_items_modal" tabindex="-1" role="dialog" style="width:92%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="view_all_items_modal_modalHeader">Item Details</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
        <div class="modal-body">
            <div class="col-md-12" id="view_all_items_details">

            </div>
        </div>
    </div>
  </div>

<input type="hidden" id="category_status_flash" value="1">
<input type="hidden" id="sub_category_id_flash">
<input type="hidden" id="sub_category_name_flash">
<input type="hidden" id="item_id_flash">
<input type="hidden" id="item_name_flash">
   

<div class="modal fade" id="update_invoice_modal" tabindex="-1" role="dialog" style="width:70%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="modalHeader_invoice">Update Invoice</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
       <form id="myform_invoice">
        <input type="hidden" id="invItemId">
        <div class="modal-body">
            <div class="col-md-12">
                <div class="form-group">
                    <label for="invItemPrice" class="col-md-4 col-xs-12 control-label">Item Price<font color="red">*</font></label>
                    <div class="col-md-8 col-xs-12">
                        <div class="input-group">
                            <input type="number" class="form-control" id="invItemPrice" placeholder="Enter Category Name" name="invItemPrice" required="">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                        </div>
                        <div class="help-block">Write Here</div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="invQuantity" class="col-md-4 col-xs-12 control-label">Item Add Quantity<font color="red">*</font></label>
                    <div class="col-md-8 col-xs-12">
                        <div class="input-group">
                            <input type="number" class="form-control" id="invQuantity" placeholder="Enter Category Name" name="invQuantity" required="">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                        </div>
                        <div class="help-block">Write Here</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <div> <input type="reset" value="Clear Form" ></div>
            <button style="min-width: 120px;" type="button" class="btn btn-primary" onclick="update_price_qty_in_invoices()">Save</button>
        </div>
        </form>
    </div>
  </div>


  <!-- Script -->
  <?php $this->load->view('procurement/inventory_view_v2/__script_item_master.php'); ?>



<style>

input.sub_category_icheckbox {
        height: 20px;
        width: 20px;
        top: -10px;
        margin-top: -9px;
    }

    input.checkbox_attr {
        height: 20px;
        width: 20px;
        /* top: 15px; */
        margin-top: 5px;
    }

    #item_information_modal .modal-body, #add_item_modal .modal-body{
        height: 500px;
        overflow-y: auto;
    }

    input.im_delete_class {
        height: 20px;
        width: 20px;
    }

input.sub_category_icheckbox {
        height: 20px;
        width: 20px;
        top: -10px;
        margin-top: -9px;
    }

    input.checkbox_attr {
        height: 20px;
        width: 20px;
        /* top: 15px; */
        margin-top: 5px;
    }

    #item_information_modal .modal-body, #add_item_modal .modal-body{
        height: 500px;
        overflow-y: auto;
    }

    input.im_delete_class {
        height: 20px;
        width: 20px;
    }


    .new_circleShape_res {
        padding: 8px 11px;
        border-radius: 50% !important;
        color: white !important;
        font-size: 20px;
        height: 3.2rem !important;
        width: 3.2rem !important;
        text-align: center;
        vertical-align: middle;
        border: none !important;
        box-shadow: 0px 3px 7px #ccc;
        line-height: 1.7rem !important;
    }
    .new_circleShape_res1 {
        font-size: small;
        padding: 4px 7px;
        border-radius: 50% !important;
        color: white !important;
        font-size: 15px;
        height: 2.5rem !important;
        width: 2.5rem !important;
        text-align: center;
        vertical-align: middle;
        border: none !important;
        box-shadow: 0px 3px 7px #ccc;
        line-height: 1.7rem !important;
    }

   .th_category {
    vertical-align: center;
   }



   .dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}

    .swal_ka_class2 {
        width: 600px;
        margin: 0 auto;
    }

    .swal_ka_class {
        width: 900px;
    }
div#inf_item_popup::-webkit-scrollbar, div#add_item_popup::-webkit-scrollbar, div#categories_span::-webkit-scrollbar, div#delete_multiple_items_box::-webkit-scrollbar {
  width: 12px; /* Adjust as needed */
}

/* Style the scrollbar track */
div#inf_item_popup::-webkit-scrollbar-track, div#add_item_popup::-webkit-scrollbar-track, div#categories_span::-webkit-scrollbar-track, div#delete_multiple_items_box::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

/* Customize the scrollbar thumb appearance */
div#inf_item_popup::-webkit-scrollbar-thumb, div#add_item_popup::-webkit-scrollbar-thumb, div#categories_span::-webkit-scrollbar-thumb, div#delete_multiple_items_box::-webkit-scrollbar-thumb {
  background: #eee; /* Adjust the color as desired */
}

div#inf_item_popup, div#add_item_popup, div#categories_span, div#delete_multiple_items_box {
  scrollbar-width: thin;
}

.swal_ka_class_info {
    width: 700px;
    text-align: left;
}

</style>