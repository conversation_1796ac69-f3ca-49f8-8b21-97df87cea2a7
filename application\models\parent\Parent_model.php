<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Parent_model extends CI_Model
{
	public function __construct()
	{
		parent::__construct();
	}

    public function get_student_datails($student_id){
        $yearId =  $this->acad_year->getAcadYearId();
		$result =  $this->db_readonly->select("concat(sa.first_name,' ', ifnull(sa.last_name,'')) as student_name, concat(c.class_name,' ', ifnull(cs.section_name,'')) as class_section, cs.id as class_section_id, c.class_name, cs.section_name, sy.board, sy.picture_url,  IF(sy.profile_confirmed = 'No', 0, 1) as profile_confirmed, IF(sa.sibling_id IS NULL, 0, 1) as has_sibling, sy.id as stdYearId, ifnull(profile_status,'') as profile_status, profile_confirmed as profileConfirmed, date_format(profile_confirmed_date,'%d-%m-%Y') as profile_confirmed_date, date_format(profile_status_changed_date,'%d-%m-%Y') as profile_status_changed_date")
        ->from('student_admission sa')
        ->join('student_year sy','sa.id = sy.student_admission_id')
        ->join('class c','c.id = sy.class_id')
        ->join('class_section cs','cs.id = sy.class_section_id','left')
        ->where('sa.id',$student_id)
		->where('sy.acad_year_id',$yearId)
		->get()->row();
		if ($result) {
			$result->student_photo = base_url('assets/img/icons/profile.png');
			if (!empty($result->picture_url)) {
				$result->student_photo = $this->filemanager->getFilePath($result->picture_url);
			}
		}
		return $result;
      
    }

    public function getStudentIdOfLoggedInParent(){
		$parentAvatarId = $this->authorization->getAvatarId();
		if (empty($parentAvatarId)) {
			log_message('error', 'parent_model->getStudentIdOfLoggedInParent: Invalid parent avatar ID');
			return 0;
		}

		//Get Student Id of the logged in parent
		$studentRecord = $this->db_readonly->select('sr.std_id as studentId')
			->from('student_relation sr')
			->join('parent p', 'sr.relation_id=p.id')
			->join('avatar a', 'a.stakeholder_id=p.id')
			->where('a.avatar_type', '2')
			->where('a.id', $parentAvatarId)
			->get()->row();

			if(!empty($studentRecord->studentId)){
				return $studentRecord->studentId;
			}else{
				log_message('error', 'parent_model->getStudentIdOfLoggedInParent: Student ID not found');
				return 0;
			}
	}

	public function getSiblingdata($currentStudentId){
        $this->yearId = $this->acad_year->getAcadYearId();
        $siblingCount = "select a.user_id,sa.id student_id from avatar a
        join parent p on a.stakeholder_id = p.id
        join student_admission sa on p.student_id = sa.id
        where avatar_type = 2 and sa.id = ?";
        $sibCountResult = $this->db->query($siblingCount, array($currentStudentId))->result();

        $userIds = [];
        foreach ($sibCountResult as $key => $value) {
            array_push($userIds, $value->user_id);
        }

        $siblingsid = [];
        if (!empty($userIds)) {
            $siblings = "select sa.id as student_id, p.student_id from avatar a
            join parent p on p.id = a.stakeholder_id
            join student_admission sa on p.student_id = sa.id
            where avatar_type = 2 and a.user_id IN (" . implode(',', array_fill(0, count($userIds), '?')) . ")
            group by p.student_id";
            $siblingsid = $this->db->query($siblings, $userIds)->result();
        }

        $student_ids = [];
        foreach ($siblingsid as $key => $value) {
            if ($value->student_id != $currentStudentId) {
                array_push($student_ids, $value->student_id);
            }
        }
        $pAvatarResult = [];
        if (count($student_ids) > 0) {
            $pAvatarResult = $this->db_readonly->select("sa.id, CONCAT(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name, concat(c.class_name,' ', ifnull(cs.section_name,'')) as class_section, sy.picture_url")
                ->from('student_admission sa')
                ->join('student_year sy', ' sa.id=sy.student_admission_id')
                ->join('class c', 'sy.class_id=c.id')
                ->join('class_section cs', 'sy.class_section_id=cs.id','left')
                ->where_in('sa.id', $student_ids)
                ->where('sy.acad_year_id', $this->yearId)
                ->get()->result();
        }
        if ($pAvatarResult) {
            foreach ($pAvatarResult as $key => $result) {
                $result->student_photo = base_url('assets/img/icons/profile.png');
                if (!empty($result->picture_url)) {
                    $result->student_photo = $this->filemanager->getFilePath($result->picture_url);
                }
            }
        }
        return $pAvatarResult;
	}

    public function getStudentDataById($studentId, $tablColumn){
        
        // Get the latest academic year ID for the student in a single query
        $studentAcadYearId = $this->db_readonly
            ->select_max('acad_year_id')
            ->where('student_admission_id', $studentId)
            ->get('student_year')
            ->row('acad_year_id');

        if (!$studentAcadYearId) {
            return null;
        }

        // Use concise join syntax and avoid redundant where clauses
        // $studentData = $this->db_readonly
        //     ->select("sa.id as stdId, concat(ifnull(sa.first_name,''),ifnull(sa.student_middle_name,''),' ',ifnull(sa.last_name,'')) as stdName, sa.first_name,ifnull(sa.family_picture_url,'') as family_picture_url, sa.last_name, c.id as classId, cs.id as sectionId, c.class_name as className, cs.is_placeholder, cs.section_name as sectionName, sa.dob, sa.mother_tongue, sa.gender, sy.id as stdYearId, sy.picture_url , sy.student_house, sy.id as student_id, sy.roll_no,sa.admission_no  as admissionNo, u.id as student_userId, u.email as studentEmail, nationality, caste, religion, aadhar_no, sy.high_quality_picture_url, sa.blood_group, sy.stop, sy.pickup_mode, (case when sy.promotion_status = 4 or sy.promotion_status = 5 then sy.promotion_status else sa.admission_status end) as admission_status, sy.profile_confirmed, date_format(sy.profile_confirmed_date,'%d-%m-%Y') as profile_confirmed_date, sa.email as student_email, sa.email_password as student_email_password, ifnull(sem.sem_name,'NA') as semester,sy.profile_status,sy.profile_status_changed_date,sa.student_remarks,sy.combination,sa.category,name_as_per_aadhar,student_mobile_no,enrollment_number,cmc.combination_name,ifnull(sy.alpha_rollnum,'') as alpha_rollnum,ifnull(sa.preferred_contact_no,'-') as preferred_contact_no")

	$this->db_readonly->select("sa.id as stdId, sy.id as stdYearId, (CASE WHEN sy.promotion_status = 4 OR sy.promotion_status = 5  THEN sy.promotion_status  ELSE sa.admission_status END) AS admission_status, cs.id as sectionId,  $tablColumn")
            ->from('student_admission sa')
            ->join('student_year sy', 'sa.id=sy.student_admission_id AND sy.acad_year_id='.$studentAcadYearId)
            ->join('class_master_combinations cmc','cmc.id=sy.combination_id','left')
            ->join('class c', 'sy.class_id=c.id', 'left')
            ->join('class_section cs', 'cs.id=sy.class_section_id', 'left')
            ->join('semester sem', 'sy.semester=sem.id', 'left')
            ->join('avatar a','sa.id=a.stakeholder_id AND a.avatar_type=1')
            ->join('users u','a.user_id=u.id');

        // Add parent joins if needed
        if (strpos($tablColumn, 'father') !== false || strpos($tablColumn, 'FATHER') !== false) {
            $this->db_readonly->join('student_relation sr_f', 'sa.id=sr_f.std_id AND sr_f.relation_type="Father"')
                             ->join('parent pf', 'sr_f.relation_id=pf.id', 'left');
        }

        if (strpos($tablColumn, 'mother') !== false || strpos($tablColumn, 'MOTHER') !== false) {
            $this->db_readonly->join('student_relation sr_m', 'sa.id=sr_m.std_id AND sr_m.relation_type="Mother"')
                             ->join('parent pm', 'sr_m.relation_id=pm.id');
        }

        if (strpos($tablColumn, 'guardian') !== false || strpos($tablColumn, 'GUARDIAN') !== false) {
            $this->db_readonly->join('student_relation sr_g', 'sa.id=sr_g.std_id AND sr_g.relation_type="Guardian"', 'left')
                             ->join('parent pg', 'sr_g.relation_id=pg.id', 'left');
        }

        $studentData = $this->db_readonly->where('sa.id', $studentId)->get()->row();
        if(!empty($studentData->category)){
			$studentData->category = $this->settings->getSetting('category')[$studentData->category];
		}
		$studentData->family_picture_url = '';
		if(!empty($studentData->family_picture_url)){
			$studentData->family_picture_url = $studentData->family_picture_url;
		}
		
		$studentData->transportStops ='';
        if(!empty($studentData->stop)){
			$studentData->transportStops =  $this->db_readonly->select('*')->where('id',$studentData->stop)->get('feev2_stops')->result();
		}
      

        $student_address_types = $this->settings->getSetting('student_address_types', 1, 1);
        
		$sAddress = [];
		if (!empty($student_address_types)) {
			foreach ($student_address_types as $key => $address) {
				$sAddress[$address] = $this->getStudent_Address_Details($studentId, $key);
			}
		} else {
			$sAddress = array();
		}
        $father_address_types = $this->settings->getSetting('father_address_types');

		$data['fatherData'] = $this->getFatherDetails($studentId, $tablColumn);
		$fAddress = [];
		if (!empty($father_address_types)) {
			foreach ($father_address_types as $key => $address) {
				$fAddress[$address] = $this->parent_model->getFather_Address_Details($data['fatherData']->id, $key);
			}
		} else {
			$fAddress = array();
		}

		$mother_address_types = $this->settings->getSetting('mother_address_types');
		$data['motherData'] = $this->getMotherDetails($studentId, $tablColumn);
		$mAddress = [];
		if (!empty($mother_address_types)) {
			foreach ($mother_address_types as $key => $address) {
				$mAddress[$address] = $this->parent_model->getFather_Address_Details($data['motherData']->id, $key);
			}
		} else {
			$mAddress = array();
		}
          
		$show_guardian = 0;
		$guardianData = [];
		$electives = [];
		$clsTeacherSub = [];
		$class_teacher = [];
		if($this->settings->isProfile_profile_enabled('GUARDIAN_PHOTO') || $this->settings->isProfile_profile_enabled('GUARDIAN_NAME') || $this->settings->isProfile_profile_enabled('GUARDIAN_CONTACT_NO') || $this->settings->isProfile_profile_enabled('GUARDIAN_EMAIL')){
		// if ($guardian_fields && $guardian_fields->display == 1) {
			$show_guardian = 1;
			$guardianData = $this->getGuardianDetails($studentId);
            if(empty($guardianData)){
                $guardianData = [];
            }
		}

    
        $electives = $this->getElectives($studentId);
        if(empty($electives)){
            $electives = [];
        }
        $clsTeacherSub = $this->get_class_wise_teacher_subj($studentData->sectionId, $studentAcadYearId);
         if(empty($clsTeacherSub)){
            $clsTeacherSub = [];
        }
        
		$class_teacher = $this->get_classTeacher_section($studentData->sectionId);
       
         if(empty($class_teacher)){
            $class_teacher = [];
        }

     
        $studentData->studentAddress = $sAddress;
        $studentData->fatherAddress = $fAddress;
        $studentData->motherAddress = $mAddress;
        $studentData->guardianData = $guardianData;
        $studentData->show_guardian = $show_guardian;
        $studentData->electives = $electives;
        $studentData->clsTeacherSub = $clsTeacherSub;
        $studentData->class_teacher = $class_teacher;
        
        return $studentData;
    }

    public function getStudent_Address_Details($pId, $address){
		return $this->db_readonly->select("CONCAT_WS(', ', NULLIF(Address_line1, ''), NULLIF(Address_line2, ''), NULLIF(area, ''), NULLIF(district, ''), NULLIF(state, ''), NULLIF(country, ''), NULLIF(pin_code, '')) AS student_address")
			->from('address_info')
			->where('address_type', $address)
			->where('stakeholder_id', $pId)
			->where('avatar_type','1')
			->get()->result();
	}
    public function getFather_Address_Details($pId, $address)
	{
		return $this->db_readonly->select(
			"CONCAT_WS(', ', 
				NULLIF(Address_line1, ''), 
				NULLIF(Address_line2, ''), 
				NULLIF(area, ''), 
				NULLIF(district, ''), 
				NULLIF(state, ''), 
				NULLIF(country, ''), 
				NULLIF(pin_code, '')
			) AS {$address}_address"
		)
		->from('address_info')
		->where_in('address_type', $address)
		->where('stakeholder_id', $pId)
		->where('avatar_type', '2')
		->get()
		->result();
	}



    public function getFatherDetails($studentId, $tablColumn)
	{
		return $this->__getRelationDetails($studentId, 'Father', $tablColumn);
	}

	public function getMotherDetails($studentId, $tablColumn)
	{
		return $this->__getRelationDetails($studentId, 'Mother', $tablColumn);
	}

    private function __getRelationDetails($studentId, $relation, $tablColumn)
	{
		$fatherData = $this->db_readonly->select("concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as name, p.*, u.id as userId, p.email as fatherEmail")
			->from('parent p')
			->join('student_relation sr', 'p.id=sr.relation_id', 'left')
		  	->join('avatar a','p.id=a.stakeholder_id')
	        ->where('a.avatar_type','2')
	        ->join('users u','a.user_id=u.id')
			->where('sr.relation_type', $relation)
			->where('sr.std_id', $studentId)
			->get()->row();

		return $fatherData;
	}

    public function getGuardianDetails($studentId) {
		$guardian = $this->db_readonly->select("concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as name, p.*")
			->from('parent p')
			->join('student_relation sr', 'p.id=sr.relation_id', 'left')
			->where('sr.relation_type', 'Guardian')
			->where('sr.std_id', $studentId)
			->get()->row();

		return $guardian;
	}

    public function get_class_wise_teacher_subj($section_id, $studentYearId){
	 	$this->db_readonly->distinct('sss.subject_id');
	    $list = $this->db_readonly->select("s.long_name as sub_name, sss.class_section_id,  concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as teacher_name, sm.id as staff_id")
	    ->from('staff_subject_section sss')
	    ->where('sss.acad_year_id',$studentYearId)
	    ->where('sss.class_section_id', $section_id)
	    ->join('stafflist_subject_section stss', 'stss.sss_id=sss.id')
	    ->join('subjects s','sss.subject_id=s.id')
	    ->join('staff_master sm','stss.staff_id=sm.id')
	    ->order_by('sm.first_name')
	    ->get()->result();
	    // echo "<pre>"; print_r($list); die();
	    $temp = array();
	    foreach ($list as $key => $val) {
    		$temp[trim($val->teacher_name)][] = $val->sub_name;
	    }

	   return $temp;
	}

    public function get_classTeacher_section($section_id){
		return $this->db_readonly->select("concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as teacher_name")
		->from('class_section cs')
		->where('cs.id',$section_id)
		->join('staff_master sm','cs.class_teacher_id=sm.id')
		->get()->row();
	}

    public function getElectives($stdId){
		$this->db->select('aeg.entity_name, ae.friendly_name');
		$this->db->from('assessment_students_elective as');
		$this->db->join('assessment_entities_group aeg', 'as.ass_entity_gid=aeg.id');
		$this->db->join('assessment_elective_group ae', 'as.ass_elective_gid=ae.id');
		$this->db->where('as.student_id', $stdId);
		return $this->db->get()->result();
	}

}
?>