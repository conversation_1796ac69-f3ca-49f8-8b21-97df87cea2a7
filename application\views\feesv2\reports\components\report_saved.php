<?php $this->load->helper('reports_datatable');
echo render_saved_report_filters(); ?>
<script>
  function get_predefined_filters() {
    $.ajax({
      url: '<?php echo site_url('feesv2/reports/get_predefined_filters'); ?>',
      type: 'post',
      success: function(data) {
        var res_data = JSON.parse(data);
        if (res_data != '') {
          var html = '';
          html += '<option value="">Select Report</option>';
          for (var i = 0; i < res_data.length; i++) {
            html += `<option value="${res_data[i].id}">${res_data[i].title}</option>`;
          }
          $('#filter_types').html(html);
        }
      }
    });
  }

  // Collect all filters from the filter UI (match day_books1.php logic)
  function collectFilters() {
    var from_date = $('#from_date').val();
    var to_date = $('#to_date').val();
    var classId = $('#classId').val();
    var paymentModes = $('#paymentModes').val();
    var fee_type = $('#fee_type').val();
    var admission_type = $('#admission_type').val();
    // Checkboxes (match day_books1.php logic)
    var components = $('#components_include').is(':checked') ? 1 : 0;
    var components_v_h = $('#components_v_h').is(':checked') ? 0 : 1;
    var components_exclude = $('#components_exclude').is(':checked') ? 1 : 0;
    var include_delete = $('#include_delete').is(':checked') ? 1 : 0;
    var recon = $('#recon').is(':checked') ? 1 : 0;
    // Radio (use value, not id)
    var report_type = $('input[name="report_type"]:checked').val();
    return {
      from_date: from_date,
      to_date: to_date,
      classId: classId,
      paymentModes: paymentModes,
      fee_type: fee_type,
      admission_type: admission_type,
      components: components,
      components_v_h: components_v_h,
      components_exclude: components_exclude,
      include_delete: include_delete,
      recon: recon,
      report_type: report_type
    };
  }

  function saveFilter() {
    var formData = collectFilters();
    var columns_activeArry = [];
    if ($.fn.DataTable && $('#daily_dataTable').length && $('#daily_dataTable').DataTable().columns) {
      var table = $('#daily_dataTable').DataTable();
      table.columns().every(function(index) {
        if (this.visible()) {
          columns_activeArry.push(index);
        }
      });
    }
    bootbox.prompt({
      inputType: 'text',
      placeholder: 'Enter the Title name',
      title: 'Save filters',
      className: 'half-width-box',
      buttons: {
        confirm: {
          label: 'Yes',
          className: 'btn-success'
        },
        cancel: {
          label: 'No',
          className: 'btn-danger'
        }
      },
      callback: function(remarks) {
        if (remarks === null) return;
        $('.bootbox .error-message').remove();
        remarks = remarks.trim();
        if (!remarks) {
          new PNotify({
            title: 'Missing Title',
            text: 'Please enter a name to save the filter.',
            type: 'error',
            addclass: 'custom-pnotify half-width-notify',
            cornerclass: '',
            animate: {
              animate: true,
              in_class: 'fadeInRight',
              out_class: 'fadeOutRight'
            },
            styling: 'bootstrap3',
            delay: 3000
          });
          return false;
        }
        if (remarks.length < 5 || remarks.length > 50) {
          setTimeout(() => {
            $('.bootbox-input').after(`<div class="error-message" style="color: red; font-size: 0.9em; margin-top: 0.25rem;">Title must be between 5 and 50 characters.</div>`);
          }, 10);
          return false;
        }
        let duplicate = false;
        $('#filter_types option').each(function() {
          if ($(this).text().trim().toLowerCase() === remarks.toLowerCase()) {
            duplicate = true;
            return false;
          }
        });
        if (duplicate) {
          setTimeout(() => {
            $('.bootbox-input').after(`<div class="error-message" style="color: red; font-size: 0.9em; margin-top: 0.25rem;">A filter with this name already exists.</div>`);
          }, 10);
          return false;
        }
        // Check if any columns are visible (if DataTable is present)
        if ($.fn.DataTable && $('#daily_dataTable').length && $('#daily_dataTable').DataTable().columns) {
          if (columns_activeArry.length === 0) {
            new PNotify({
              title: 'Action Required',
              text: 'Please click the "Get Report" button first before saving a filter.',
              type: 'warning',
              addclass: 'custom-pnotify half-width-notify',
              delay: 3000
            });
            return false;
          }
        }
        var data = Object.assign({}, formData, {
          title: remarks,
          columns_activeArry: columns_activeArry
        });
        $.ajax({
          url: controllerUrl + reportEndpoints.save_filters,
          type: 'post',
          data: data,
          success: function(resp) {
            if (resp) {
              let lastId = 0;
              $('#filter_types option').each(function() {
                const val = parseInt($(this).val());
                if (!isNaN(val) && val > lastId) lastId = val;
              });
              const newId = lastId + 1;
              $('#filter_types').append(
                $('<option>', {
                  value: newId,
                  text: remarks
                })
              );
              $('#filter_types').val(newId).trigger('change');
              new PNotify({
                title: 'Success',
                text: 'Filter Saved Successfully',
                type: 'success',
                addclass: 'custom-pnotify half-width-notify'
              });
            } else {
              new PNotify({
                title: 'Error',
                text: 'Something went wrong',
                type: 'error',
                addclass: 'custom-pnotify half-width-notify'
              });
            }
          }
        });
      }
    });
    setTimeout(() => {
      $('.bootbox .modal-dialog').css({
        'max-width': '400px',
        'margin': '1.75rem auto'
      });
      $('.bootbox-input').on('input', function() {
        const inputVal = $(this).val().trim();
        if (inputVal.length > 50) {
          $(this).val(inputVal.slice(0, 50));
          $('.bootbox .error-message').remove();
          $(this).after(`<div class="error-message" style="color: red; font-size: 0.9em; margin-top: 0.25rem;">Title must be between 5 and 50 characters.</div>`);
        } else {
          $('.bootbox .error-message').remove();
        }
      });
    }, 10);
  }

  function updateFilter() {
    var formData = collectFilters();
    var columns_activeArry = [];
    if ($.fn.DataTable && $('#daily_dataTable').length && $('#daily_dataTable').DataTable().columns) {
      var table = $('#daily_dataTable').DataTable();
      table.columns().every(function(index) {
        if (this.visible()) {
          columns_activeArry.push(index);
        }
      });
    }
    var filter_types_id = $('#filter_types').val();
    if (!filter_types_id) {
      bootbox.alert({
        title: "No Filter Selected",
        message: "Please select a filter to update.",
        className: "half-width-box",
        buttons: {
          ok: {
            label: 'OK',
            className: 'btn-primary'
          }
        }
      });
      setTimeout(() => {
        $('.bootbox .modal-dialog').css({
          'max-width': '400px',
          'margin': '1.75rem auto'
        });
      }, 10);
      return;
    }
    bootbox.confirm({
      title: "Update Filters",
      message: 'Are you sure you want to update the filter?',
      className: "half-width-box",
      buttons: {
        confirm: {
          label: 'Yes',
          className: 'btn-success'
        },
        cancel: {
          label: 'No',
          className: 'btn-danger'
        }
      },
      callback: function(result) {
        if (result) {
          var data = Object.assign({}, formData, {
            columns_activeArry: columns_activeArry,
            filter_types_id: filter_types_id,
            title: $('#filter_types option:selected').text().trim()
          });
          $.ajax({
            url: controllerUrl + reportEndpoints.update_filters,
            type: 'post',
            data: data,
            success: function(resp) {
              $.when(get_predefined_filters()).done(function() {
                if ($('#filter_types option[value="' + filter_types_id + '"]').length === 0) {
                  $('#filter_types').append(
                    $('<option>', {
                      value: filter_types_id,
                      text: data.title || ''
                    })
                  );
                }
                $('#filter_types').val(filter_types_id);
                selectFilters();
                new PNotify({
                  title: 'Success',
                  text: 'Filter updated successfully.',
                  type: 'success',
                  addclass: 'custom-pnotify half-width-notify'
                });
              });
            },
            error: function() {
              new PNotify({
                title: 'Error',
                text: 'Something went wrong',
                type: 'error',
                addclass: 'custom-pnotify half-width-notify'
              });
            }
          });
        }
      }
    });
    setTimeout(() => {
      $('.bootbox .modal-dialog').css({
        'max-width': '400px',
        'margin': '1.75rem auto'
      });
    }, 10);
  }

  function selectFilters() {
    var filter_id = $('#filter_types').val();
    if (!filter_id) {
      getReport();
      return;
    }
    $.ajax({
      url: controllerUrl + reportEndpoints.get_filter_by_id,
      type: 'post',
      data: {
        filter_id: filter_id
      },
      success: function(data) {
        var res_data = JSON.parse(data);
        var filters = res_data['filters_selected'];
        if (filters.from_date) {
          $('#from_date').val(filters.from_date);
          $('#to_date').val(filters.to_date);
          $('#reportrange').val(moment(filters.from_date, 'DD-MM-YYYY').format('MMM D, YYYY') + ' - ' + moment(filters.to_date, 'DD-MM-YYYY').format('MMM D, YYYY'));
        }
        if (filters.classId) {
          $('#classId').val(filters.classId).trigger('change');
          $('.classId').selectpicker && $('.classId').selectpicker('refresh');
        }
        if (filters.paymentModes) {
          $('#paymentModes').val(filters.paymentModes).trigger('change');
          $('#paymentModes').selectpicker && $('#paymentModes').selectpicker('refresh');
        }
        if (filters.fee_type) {
          $('#fee_type').val(filters.fee_type).trigger('change');
          $('#fee_type').selectpicker && $('#fee_type').selectpicker('refresh');
        }
        if (filters.admission_type !== undefined) {
          $('#admission_type').val(filters.admission_type);
        }
        $('#components_include').prop('checked', filters.components == 1);
        $('#components_v_h').prop('checked', filters.components_v_h == 1);
        $('#components_exclude').prop('checked', filters.components_exclude == 1);
        $('#includeCancelled, #include_delete').prop('checked', filters.include_delete == 1);
        $('#considerRecon, #recon').prop('checked', filters.recon == 1);
        if (filters.report_type) {
          var val = filters.report_type;
          if (val == 1) $('#type-1').prop('checked', true);
          else if (val == 2) $('#type-2').prop('checked', true);
          else if (val == 3) $('#type-3').prop('checked', true);
          else if (val == 4) $('#type-4').prop('checked', true);
        }
        if (filters.columns_activeArry && $.fn.DataTable && $('#daily_dataTable').length) {
          var table = $('#daily_dataTable').DataTable();
          table.columns().every(function(index) {
            var isVisible = filters.columns_activeArry.includes(index.toString()) || filters.columns_activeArry.includes(index);
            table.column(index).visible(isVisible);
          });
        }
        getReport();
      }
    });
  }
</script>