
<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/jeditor/editable.js"></script>

<script>
    var indentApproversListFinal={
        approvar_1:0,
        approvar_2:0,
        approvar_3:0,
        financial:0,
    }

    var indentApproversList={
        approvar_1:0,
        approvar_2:0,
        approvar_3:0,
        financial:0,
    }
    
    var approverModalType="Financial";
    
    var staffs = <?php echo json_encode($staffList); ?>;

    $(document).ready(function() {
        $("#inf_abt_item_btn_id").click(function() {
            $("#item_information_modal").modal('hide');
            $("#view_all_items_modal").modal('show');
        });
    });

    async function editItemInforation(item_id, is_used, item_name) {
        // await show_information_about_item(item_name, item_id);
        if(is_used == '1' && !'<?php echo $this->authorization->isSuperAdmin(); ?>') {
            return Swal.fire({
                        icon: 'error',
                        title: "Can't Edit!",
                        html: `Item - <b>${$("#item_information_modal #item_name_information b").html()}</b> is issued by some member(s) of this school. Contact to your administrator for update / delete the item details.`,
                    });
        }
        $(".item_class").show();
        $(".item_edit_class").hide();
        $(".item_edit_class").parent('th').append(`<label for="i_item_name" class="col-md-4">Item Name </label> <input class="form-control col-md-8" type="text" name="i_item_name" id="i_item_name" value="${$("#item_information_modal #item_name_information b").html()}" />`);
        $("#item_information_modal #item_information_div #i_sku_code").html( `<input name="i_sku_code" class="form-control" type="text" value="${$("#i_sku_code").html()}" />` );
        $("#item_information_modal #item_information_div #i_classification").html( `<input name="i_classification" class="form-control" type="text" value="${$("#i_classification").html()}" />` );
        $("#item_information_modal #item_information_div #i_image").html( `<input name="i_image" class="form-control" type="file" value="${$("#i_image").html()}" />` );
        // Anish
        $("#item_information_modal #item_information_div #i_item_name_td").html( `<input class="form-control" type="text" name="i_item_name" id="i_item_name" value="${$("#item_information_modal #item_name_information b").html()}" />` );
        $("#item_information_modal #item_information_div #i_hsn").html( `<input name="i_hsn" class="form-control" type="text" value="${$("#i_hsn").html()}" />` );
        $("#item_information_modal #item_information_div #i_current_quantity").html( `<input name="i_current_quantity" class="form-control" type="text" value="${$("#i_current_quantity").html()}" />` );
        $("#item_information_modal #item_information_div #i_threashold_quantity").html( `<input name="i_threashold_quantity" class="form-control" type="text" value="${$("#i_threashold_quantity").html()}" />` );
        $("#item_information_modal #item_information_div #i_initial_quantity").html( `<input name="i_initial_quantity" class="form-control" type="text" value="${$("#i_initial_quantity").html()}" />` );
        $("#item_information_modal #item_information_div #i_total_quantity").html( `<input name="i_total_quantity" class="form-control" type="text" value="${$("#i_total_quantity").html()}" />` );
        $("#item_information_modal #item_information_div #i_cost_product").html( `<input name="i_cost_product" class="form-control" type="text" value="${$("#i_cost_product").html()}" />` );
        $("#item_information_modal #item_information_div #i_selling_price").html( `<input name="i_selling_price" class="form-control" type="text" value="${$("#i_selling_price").html()}" />` );
        $("#item_information_modal #item_information_div #i_desc").html( `<input name="i_desc" class="form-control" type="text" value="${$("#i_desc").html()}" />` );
        $("#item_information_modal #item_information_div .attribute_class").each(function() {
            $(this).html( `<input name="${$(this).siblings('th').html()}" class="form-control" type="text" value="${$(this).html()}" />` );
        });
        $("#item_information_modal #item_information_div #i_perishable").html( `<select name="i_perishable" id="selected_item_perishabl" class="form-control"><option value="1" ${ $("#i_perishable").html() == 'YES' ? 'selected' : ''}>Yes</option> <option ${ $("#i_perishable").html() == 'NO' ? 'selected' : ''} value="0">No</option></select>` );

        var selected_unit= $("#i_unit_type").html();
        $("#item_information_modal #item_information_div #i_unit_type").html( `<select name="i_unit_type" id="selected_item_i_unit_type" class="form-control">${$("#unit_type").html()}</select>` );
        $(`#item_information_modal #item_information_div #i_unit_type option[value='${selected_unit}']`).prop('selected', true);
    }

    function saveItemInforation() {
       
            var form = $('#item_information_modal form')[0];
            var formData = new FormData(form);
            formData.append('itemId', $("#item_information_modal #item_information_div #itemId").val());
            $.ajax({
                url: '<?php echo site_url('procurement/inventory_controller_v2/saveItemInforation'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    var p_data = JSON.parse(data);
                    if(p_data){
                        $(function(){
                            new PNotify({
                                title:'Success',
                                text: 'Success',
                                type:'success',
                            });
                        });
                    }else{
                        $(function(){
                            new PNotify({
                                title:'Error',
                                text: 'Something went wrong',
                                type:'error',
                            });
                        });
                    }
                    var category_id= $("#flash_storage_div #item_category_id").val();
                    var category_name= $("#flash_storage_div #item_category_name_id").val();
                    get_and_construct_sub_categories_table(category_id, category_name);
                    subCategoryTab();
                    $("#item_information_modal").modal('hide');
                    var sub_category_id= $("#sub_category_id_flash").val();
                    var sub_category_name= $("#sub_category_name_flash").val();
                    open_all_item_in_popup(category_id, sub_category_id, sub_category_name);
                }
            });
    }

    function cancelEditingItemInforation() {
        $(".item_class").hide();
        $(".item_edit_class").show();
        $("#item_information_modal").modal('hide');

        $("#view_all_items_modal").modal('show');
        // var category_id= $("#flash_storage_div #item_category_id").val();
        // var sub_category_id= $("#sub_category_id_flash").val();
        // var sub_category_name= $("#sub_category_name_flash").val();
        // open_all_item_in_popup(category_id, sub_category_id, sub_category_name);
    }

    async function edit_category() {
        var catName= $("#edit_category_modal #category_name_edit").val();
        var catDesc= $("#edit_category_modal #category_description_edit").val();

        var category_status_edit= $("#edit_category_modal select#category_status_edit").val();
        var category_type_edit= $("#edit_category_modal select#category_type_edit").val();
        var category_administrator_edit= $("#edit_category_modal select#category_administrator_edit").val();

        if(!catName) {
            return alert("Category Name is a necessary field and it can't be empty");
        }

        await $("#item_master_edit_cat_btn").prop('disabled', true).html('Please Wait..');
        await $.ajax({
            url: '<?php echo site_url('procurement/inventory_controller_v2/editCategory'); ?>',
            type: "post",
            async: true,
            data: {
                'catName': catName,
                'catDesc': catDesc,
                'catId': $("#item_category_id").val(),

                'category_status_edit': category_status_edit,
                'category_type_edit': category_type_edit,
                'category_administrator_edit': category_administrator_edit,

                'isSellable': $("#is_sellable_edit").is(':checked') ? 1 : 0
            },
            success: await function(data) {
                var p_data = JSON.parse(data);
                categoryTab();
                get_all_categories();
                $("#edit_category_modal").modal('hide');
            }
        });
        await $("#item_master_edit_cat_btn").prop('disabled', false).html('Update Category');

    }

    function submit_receipt_template() {
        var item_category_id= $("#item_category_id").val();
        var html_template_id= $("#html_template_id").val();
        if(html_template_id) {
            $.ajax({
                url: '<?php echo site_url('procurement/inventory_controller_v2/submit_receipt_template'); ?>',
                type: "post",
                data: {
                    'item_category_id': item_category_id,
                    'html_template_id': html_template_id
                },
                success: function(data) {
                    var p_data = JSON.parse(data);
                    categoryTab();
                    $("#receipt_html_templates").modal('hide');
                    
                }
            });
        } else {
            alert(`Html Template field can't be empty.`);
        }
    }

    function subCategoryTab() {
        // var item_category_id= $("#item_category_id").val();
        // var item_category_name= $("#item_category_name_id").val();
        $("#categories_details_span_button").removeClass('active');
        $("#approval_categories_span_button").removeClass('active');
        $("#sub_categories_span_button").addClass('active');

        $("#sub_categories_span").show();
        $("#categories_details_span").hide();
        $("#approval_details_span").hide();

    }

    function categoryTab() {
        $("#categories_details_span").html(`<img src="<?php echo base_url('assets/img/ajax-loader.gif');?>">`);
        var item_category_id= $("#item_category_id").val();
        var item_category_name= $("#item_category_name_id").val();

        $("#sub_categories_span_button").removeClass('active');
        $("#approval_categories_span_button").removeClass('active');
        $("#categories_details_span_button").addClass('active');

        $("#sub_categories_span").hide();
        $("#approval_details_span").hide();
        $("#categories_details_span").show();

        get_and_construct_category_tab(item_category_id, item_category_name);
    }

// 
    function approvalsTab() {
        $("#approval_details_span").html(`<img src="<?php echo base_url('assets/img/ajax-loader.gif');?>">`);
        var item_category_id= $("#item_category_id").val();
        var item_category_name= $("#item_category_name_id").val();

        $("#sub_categories_span_button").removeClass('active');
        $("#categories_details_span_button").removeClass('active');
        $("#approval_categories_span_button").addClass('active');


        $("#sub_categories_span").hide();
        $("#categories_details_span").hide();
        $("#approval_details_span").show();

        $.ajax({
            url: '<?php echo site_url('procurement/inventory_controller_v2/get_category_approvals_details'); ?>',
            type: "post",
            data: {'item_category_id': item_category_id,},
            success(data) {
                var p_data = JSON.parse(data);
                // setting amount based approvers modal pop-up data
                $("#approver-1-min_amount").val(p_data.min_approver_1_amount);
                $("#approver-2-min_amount").val(p_data.min_approver_2_amount);
                $("#approver-3-min_amount").val(p_data.min_approver_3_amount);

                let tab_contect= `
                                <div class="col-md-12" id="all_approvals_div">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th class="" colspan="2">
                                                    Staff Requisition Approvers
                                                    <button onclick="prepare_modal_for_approval('Allocation', '${item_category_id}', '${item_category_name}')" class="btn btn-warning pull-right" id="allocation_approvals_button">${+p_data.approval_algorithm <= 0 ? 'Add Approvals' : 'Edit Approvals'}</button>
                                                </th>
                                            </tr>`;
                if(+p_data.approval_algorithm < 1) {
                    tab_contect += `
                                    <tr>
                                        <td class="text-center" colspan="2">Not Created</td>
                                    </tr>
                                    `;
                } 
                if(+p_data.approval_algorithm > 0) {
                    tab_contect += `
                                    <tr>
                                        <td class="">Primary Approver</td>
                                        <td class="">${p_data.approvar_1}</td>
                                    </tr>
                                    `;
                }  
                if(+p_data.approval_algorithm > 1) {
                    tab_contect += `
                                    <tr>
                                        <td class="">Secondary Approver</td>
                                        <td class="">${p_data.approvar_2}</td>
                                    </tr>
                                    `;
                }  
                if(+p_data.approval_algorithm > 2) {
                    tab_contect += `
                                    <tr>
                                        <td class="">Ternary Approver</td>
                                        <td class="">${p_data.approvar_3}</td>
                                    </tr>
                                    `;
                }

                tab_contect += `<tr style="height: 20px;"> </tr>
                                <tr>
                                    <th class="" colspan="2">
                                        Indent Approvers
                                        <button onclick="prepare_modal_for_approval('Indent', '${item_category_id}', '${item_category_name}')" class="btn btn-warning pull-right" id="bom_approvals_button">Add Approvals</button>
                                    </th>
                                </tr>`;
                    
                if(+p_data.approval_algorithm_for_indent < 1) {
                    tab_contect += `
                                    <tr>
                                        <td class="text-center" colspan="2">Not Created</td>
                                    </tr>
                                    `;
                } 
                if(+p_data.approval_algorithm_for_indent > 0) {
                    tab_contect += `
                                    <tr>
                                        <td class="">Primary Approver</td>
                                        <td class="">${p_data.bom_approver_1}</td>
                                    </tr>
                                    `;
                }  
                if(+p_data.approval_algorithm_for_indent > 1) {
                    tab_contect += `
                                    <tr>
                                        <td class="">Secondary Approver</td>
                                        <td class="">${p_data.bom_approver_2}</td>
                                    </tr>
                                    `;
                }  
                if(+p_data.approval_algorithm_for_indent > 2) {
                    tab_contect += `
                                    <tr>
                                        <td class="">Ternary Approver</td>
                                        <td class="">${p_data.bom_approver_3}</td>
                                    </tr>
                                    `;
                }


                // Financial Approver
                tab_contect += `<tr style="height: 20px;"> </tr>
                                <tr>
                                    <th class="" colspan="2">
                                    Financial Approver
                                        <button onclick="prepare_modal_for_approval('Financial', '${item_category_id}', '${item_category_name}')" class="btn btn-warning pull-right" id="bom_approvals_button">Add Financial Approval</button>
                                    </th>
                                </tr>`;

                if(p_data.financial_approver){
                    tab_contect += `
                        <tr>
                            <td class="">Primary Approver</td>
                            <td class="" colspan="2">${p_data.financial_approver}</td>
                        </tr>
                        `;
                }else{
                    tab_contect += `
                        <tr>
                            <td class="text-center" colspan="2">Not Created</td>
                        </tr>
                        `;
                }

                tab_contect += `</thead>
                            </table>
                        </div>
                        `;
                $("#approval_details_span").html(tab_contect);
                
            }
        });
    }
    
    function handleAllocationStaffList(){
        staffs = <?php echo json_encode($staffList); ?>;
    }

    async function handleIndentStaffList(){
        const procImCatId=window.sessionStorage.getItem("procImCatId");
        try {
            await $.ajax({
                url:"<?php echo site_url('procurement/inventory_controller_v2/getStaffForIndentApproval') ?>",
                type:"POST",
                data:{procImCatId},
                success:function(res){
                    const staffList=JSON.parse(res);
                    staffs=staffList;
                }
            });
        } catch (err) {
            throw new Error(err.message)
        }
    }

    async function handleFinancialStaffList(){
        const procImCatId=window.sessionStorage.getItem("procImCatId");
        try {
            await $.ajax({
                url:"<?php echo site_url('procurement/inventory_controller_v2/getStaffForFinancialApproval') ?>",
                type:"POST",
                data:{procImCatId},
                success:function(res){
                    const staffList=JSON.parse(res);
                    staffs=staffList;
                }
            });
        } catch (err) {
            throw new Error(err.message)
        }
    }

    async function prepare_modal_for_approval(approval_type, item_category_id, item_category_name) {
        approverModalType="";
        const procImCatId=window.sessionStorage.getItem("procImCatId");
        indentApproversList=JSON.parse(JSON.stringify(indentApproversListFinal));
        // $(`#category_table_td_id_${procImCatId}`).trigger("click");

        if(approval_type==="Allocation"){
            handleAllocationStaffList();
        }else if(approval_type==="Indent"){
            await handleIndentStaffList();
        }else if(approval_type==="Financial"){
            approverModalType="financial"
            await handleFinancialStaffList();
        }

        if(approval_type==="Financial"){
            $("#approval_algorithm").val(1);
            $("#apr_div_2").hide();
            $("#apr_div_3").hide();
            $("#approverAlgorithmContainer").hide();
        }else{
            $("#apr_div_2").show();
            $("#apr_div_3").show();
            $("#approverAlgorithmContainer").show();
        }

        $("#approvar_modal #approval_modalHeader").html('Add ' +approval_type+ ' approval for <b>' +item_category_name + '</b>');
        $("#approvar_modal #category_id_for_approval").val(item_category_id);
        $("#approvar_modal #approval_type_for_approval").val(approval_type);
        
        $("#approvar_modal").modal('show');
    }

    function errorMessage(message){
        return Swal.fire({
        icon: "error",
        title: "Oops...",
        text: `${message}`,
        });
    }

    function create_approvals() {
        var item_category_id= $("#approvar_modal #category_id_for_approval").val();
        var approval_type= $("#approvar_modal #approval_type_for_approval").val();
        var approval_algorithm = $("#approval_algorithm").val();
        var approvar_1 = $("#approvar_1").val() || '';
        var approvar_2 = $("#approvar_2").val() || '';
        var approvar_3 = $("#approvar_3").val() || '';

        let minApproverOneAmout=0;
        let minApproverTwoAmout=0;
        let minApproverThreeAmout=0;

        if(approval_algorithm==4){
            minApproverOneAmout=$("#approver-1-min_amount").val() || 0;
            minApproverTwoAmout=$("#approver-2-min_amount").val() || 0;
            minApproverThreeAmout=$("#approver-3-min_amount").val() || 0;
        }

        if(approval_type==="Financial"){
            if(!approvar_1.length) return errorMessage("Approver cannot be empty!");
        }

        $.ajax({
            url: '<?php echo site_url('procurement/inventory_controller_v2/create_approvals_type_wise'); ?>',
            type: "post",
            data: {item_category_id, approval_type, approval_algorithm, approvar_1, approvar_2, approvar_3, minApproverOneAmout, minApproverTwoAmout, minApproverThreeAmout},
            success(data) {
                var p_data = JSON.parse(data);
                // resetting final approvers
                if(p_data){
                    indentApproversListFinal=JSON.parse(JSON.stringify(indentApproversList));
                }
                $("#approvar_modal").modal('hide');
                approvalsTab();
                
            }
        });
    }

    function deleteCategory(item_category_id, category_name) {
        var askOnce= confirm(`You are deleting ${category_name}. Are you sure?`);
        if(askOnce)
        $.ajax({
            url: '<?php echo site_url('procurement/inventory_controller_v2/deleteCategory'); ?>',
            type: "post",
            data: {
                'id': item_category_id
            },
            success: function(data) {
                var p_data = JSON.parse(data);
                window.location.reload();
                
            }
        });
    }

    function get_and_construct_category_tab(item_category_id, item_category_name) {
        $.ajax({
            url: '<?php echo site_url('procurement/inventory_controller_v2/get_category_all_details'); ?>',
            type: "post",
            data: {
                'item_category_id': item_category_id,
            },
            success: function(data) {
                var p_data = JSON.parse(data);
                var temp_rec= p_data[0].rec_temp == 0 ? '<font color"red">Not Added</font>' : `<button class="btn btn-secondary" onclick="view_template_format('${p_data[0].id}')"><span class="fa fa-eye"></span></button>`;
                if(p_data[0].rec_temp != 0) {
                    $("#receipt_html_templates #html_template_id").val(p_data[0].rec_temp);
                } else {
                    $("#receipt_html_templates #html_template_id").val('');
                }
                var html= ` <table class="table table-bordered table-hover">
                                <thead>
                                    <tr><th class="" style="height: 3rem; font-size: large; " colspan="3" class='editable'>${p_data[0].category_name || ''} <button ${p_data[0].status != '1' ? 'disabled' : ''} style="float: right; margin: 0 10px;" class="btn btn-warning catEditButton" data-toggle="modal" data-target="#edit_category_modal"><span class="fa fa-edit"></span></button>   <button style="float: right;" class="btn btn-danger catEditButton" onclick="deleteCategory(${p_data[0].id}, '${p_data[0].category_name}')"><span class="fa fa-trash-o"></span></button></th></tr>
                                    <tr>
                                        <th>Description</th>
                                        <td colspan="2">${p_data[0].category_description || '-'}</td>
                                    </tr>
                                    <tr>
                                        <th>Category Type</th>
                                        <td colspan="2">${p_data[0].category_type || ''}</td>
                                    </tr>
                                    <tr>
                                        <th>Category Admin</th>
                                        <td colspan="2">${p_data[0].admin != '-' ? p_data[0].admin : 'All Inventory Users'}</td>
                                    </tr>
                                    <tr>
                                        <th>Created On</th>
                                        <td colspan="2">${moment(p_data[0].created_on).format('DD-MM-YYYY hh:MM A') || ''}</td>
                                    </tr>
                                    <tr>
                                        <th>Created By</th>
                                        <td colspan="2">${p_data[0].creater == 0 ? 'Admin' : p_data[0].creater}</td>
                                    </tr>
                                    <tr>
                                        <th>Is Sellable?</th>
                                        <td colspan="2">${p_data[0].is_sellable == 1 ? 'Yes' : 'No'}</td>
                                    </tr>
                                    <tr>
                                        <th class="editable">Status</th>
                                        <td colspan="2">${p_data[0].status == 1 ? 'Active' : 'Inactive'}</td>
                                    </tr>
                                    <?php if($this->authorization->isSuperAdmin()) { ?>
                                    <tr>
                                        <th>Receipt Book Format (for Sales)</th>
                                        <td><span class="" id="before_click_on_receipt_editor">${p_data[0].receipt_book_id == 0 ? 'Not Added' : p_data[0].receipt_book_id}</span> <span class="col-md-10" style="display: none;" id="after_click_on_receipt_editor"><select class="form-control" id="selected_receipt_format_id"> </select></span></td>
                                        <td id="format_edit_save_button_id"><button ${p_data[0].status != '1' ? 'disabled' : ''} class="btn btn-warning" onclick="edit_receipt_format(${p_data[0].id}, ${p_data[0].receipt_book_id})"><span class="fa fa-edit"></span></button></td>
                                    </tr>
                                    <tr>
                                        <th style="vertical-align: top; width: 10vw;">Receipt HTML Template (for Sales)</th>
                                        <td>${temp_rec}</td>
                                        <td><a ${p_data[0].status != '1' ? 'disabled' : ''} class="btn btn-warning btn-md mrg" data-toggle="modal" data-target="#receipt_html_templates" data-placement="top" data-toggle="tooltip" data-original-title="html format"><i class="fa fa-code"></i></a></td>
                                    </tr>
                                    <?php } ?>
                            </thead>
                        </table>`;
                $("#categories_details_span").html(html);
                if(isSubCategoryAdded) {
                    $(".catEditButton").hide();
                } else {
                    $(".catEditButton").show();
                }
                $("#html_template_id").html(`${p_data[0].rec_temp.toString() != '0' ? p_data[0].rec_temp.toString() : ''}`);


                var format_id= Number(p_data[0].receipt_book_id);
                $("#format_flash_storer option").each(function() {
                    if( $(this).val() == format_id ) {
                        $("#before_click_on_receipt_editor").html( $(this).text() );
                        return false;
                    }
                });


                // Input sets for edit category
                $("#edit_category_modal h4").html(`Update ${p_data[0].category_name}`);
                $("#edit_category_modal #category_name_edit").val(p_data[0].category_name);
                $("#edit_category_modal #category_description_edit").val(p_data[0].category_description);
                $(`#edit_category_modal select#category_type_edit option[value="${p_data[0].category_type}"]`).attr('selected', 'selected');
                $(`#edit_category_modal select#category_administrator_edit option[value="${p_data[0].category_administrator_id}"]`).attr('selected', 'selected');
                $(`#edit_category_modal select#category_status_edit option[value="${p_data[0].status}"]`).attr('selected', 'selected');
                if(p_data[0].is_sellable == 1) {
                    $("#is_sellable_edit").prop('checked', true);
                } else {
                    $("#is_sellable_edit").prop('checked', false);
                }
                
            }
        });
    }

    async function view_template_format(category_id) {
        await $.ajax({
        url: '<?php echo site_url('procurement/inventory_controller_v2/get_template_format_by_category_id'); ?>',
        type: "post",
        data: {category_id},
        success(data) {
            var p_data = JSON.parse(data);
            Swal.fire({
                        html: p_data.receipt_template,
                        width: 1000,
                    });
            
        }
    });
    }

    function edit_receipt_format(category_id, receipt_book_id) {
        $("#before_click_on_receipt_editor").hide();
        $("#after_click_on_receipt_editor").show();
        $("#selected_receipt_format_id").html( $("#format_flash_storer").html() );
        $(`#selected_receipt_format_id`).find(`option[value="${receipt_book_id}"]`).attr("selected", "selected");
        $("#format_edit_save_button_id").html(`<button class="btn btn-success" onclick="save_receipt_format(${category_id})"><span class="fa fa-save"></span> Save</button>  <button class="btn btn-danger" onclick="cancel_editing_receipt_format()">Cancel</button>`);
    }

    function save_receipt_format(category_id) {
        var receipt_book_id= Number($("#selected_receipt_format_id").val());
        $.ajax({
            url: '<?php echo site_url('procurement/inventory_controller_v2/saveReceiptFormat'); ?>',
            type: "post",
            data: {
                'id': receipt_book_id,
                'category_id': category_id
            },
            success: function(data) {
                var p_data = JSON.parse(data);
                categoryTab();
                
            }
        });
    }

    function cancel_editing_receipt_format() {
        categoryTab();
    }

    $(document).ready(function() {
        $("#categories_span").html(`<img src="<?php echo base_url('assets/img/ajax-loader.gif');?>">`);
        get_all_categories();
        getAllPossibleReceiptFormat();
        
    });

    function getAllPossibleReceiptFormat() {
        $.ajax({
        url: '<?php echo site_url('procurement/inventory_controller_v2/getAllPossibleReceiptFormat'); ?>',
        type: "post",
        success: function(data) {
            var p_data = JSON.parse(data);
            var html= ``;
            for(var v of p_data) {
                html += `<option value="${v.id}">${v.receipt}</option>`;
            }

            $("#format_flash_storer").html(html);

        }
    });
    }

    function edit_sub_category(proc_im_category_id, sub_category_id, subcategory_name, subcategory_item_type, is_stockable, subcategory_description, attributes, current) {
// after edit I have to reset to it like following
            $("#add_sub_category_modal #my_form_sub_category").trigger('reset');


        $("#category_name_in_modal_header").html(`Update <b>${subcategory_name}</b>`);
        $("#product_name").val(`${subcategory_name}`);
        $("#sub_category_item_type").val(`${subcategory_item_type}`);
        if( is_stockable == 1 ) {
            $("#is_stockable")[0].checked= true;
        }
//Need this        $(`#unit_type option:contains('${unit_type}')`).attr('selected', 'selected');
        $("#description").val(`${subcategory_description}`);
        var attr= attributes.split(',');
        for( var i= 0; i< attr.length; i++) {
            var a= document.getElementById(attr[i]);
            $(`#${attr[i].toLowerCase()}`).prop('checked', true);
        }
        $("#edit_or_add").val('edit');
        $("#sub_category_id_for_update").val(sub_category_id);
        $("#category_id_modal").val(proc_im_category_id);

        $("#add_sub_category_modal").modal('show');
    }

    function category_validity(current) {
        var category_name = $("#category_name").val().toString();
        if(category_name == 'null' || category_name == null || category_name == '' || category_name == undefined) {
            $("#category_name").css('background', 'hotpink');
            $("#category_name").parent().children('font, br').remove();
            $("#category_name").parent().append(`<font class="col-md-12" color="red">This value is required</font>`);
            return false;
        } else if( category_name.includes('"') || category_name.includes("'") ) {
            $("#category_name").css('background', 'hotpink');
            $("#category_name").parent().children('font, br').remove();
            $("#category_name").parent().append(`<font class="col-md-12" color="red">Inverted commaas are not allowed</font>`);
            return false;
        } else {
            $("#category_name").css('background', 'none');
            $("#category_name").parent().children('font, br').remove();
            return true;
        }
    }

    function sub_category_validity(current) {
        var sub_cat_name= $("#product_name").val().toString();
        if(sub_cat_name == 'null' || sub_cat_name == null || sub_cat_name == '' || sub_cat_name == undefined) {
            $("#product_name").css('background', 'hotpink');
            $("#product_name").parent().children('font').remove();
            $("#product_name").parent().append(`<font class="col-md-12" color="red">This value is required</font>`);
            return false;
        } else if( sub_cat_name.includes('"') || sub_cat_name.includes("'") ) {
            $("#product_name").css('background', 'hotpink');
            $("#product_name").parent().children('font, br').remove();
            $("#product_name").parent().append(`<font class="col-md-12" color="red">Inverted commaas are not allowed</font>`);
            return false;
        } else {
            $("#product_name").css('background', 'none');
            $("#product_name").parent().children('font').remove();
            return true;
        }
    }

    function attribute_validity() {
        var is_checked= false;
        $(".checkbox_attr").each(function() {
            if($(this).is(':checked')) {
                is_checked= true;
            }
        });
        if(is_checked === true) {
            $("#attribute_validity_information").html('');
        } else {
            $("#attribute_validity_information").html('Select at least one attribute.');
        }
        return is_checked;
    }

    function item_validity(current) {
        var item= $("#variant_name").val().toString();
        if(item && item != 'null' && !item.includes('"') && !item.includes("'")) {
            $("#variant_name").siblings('font').remove();
            $("#variant_name").css('background', 'none');
            return true;
        } else if( item.includes('"') || item.includes("'") ) {
            $("#variant_name").css('background', 'hotpink');
            $("#variant_name").parent().children('font, br').remove();
            $("#variant_name").parent().append(`<font class="col-md-12" color="red">Inverted commaas are not allowed</font>`);
            return false;
        } else {
            $("#variant_name").siblings('font').remove();
            $("#variant_name").parent().append(`<font class="col-md-12" color="red">This value is required</font>`);
            $("#variant_name").css('background', 'hotpink');
            return false;
        }
    }

    // adding new item category
    async function add_category() {
        var category_name = $("#category_name").val();
        // var approval_algorithm = $("#approval_algorithm").val();
        // var approvar_1 = $("#approvar_1").val() || '';
        // var approvar_2 = $("#approvar_2").val() || '';
        var category_administrator = $("#category_administrator").val() || '';
        
        var category_type = $("#category_type").val();
		var is_sellable = ($("#is_sellable").is(':checked'))?1:0;
        var category_description= $("#category_description").val();
        if(category_validity('') === true) {
            await $("#item_master_cat_btn").prop('disabled', true).html('Please Wait..');

            await $.ajax({
                url:'<?php echo site_url('procurement/inventory_controller_v2/add_category') ?>',
                type:'post',
                data: {'category_name': category_name, 'is_sellable': is_sellable, 'category_description': category_description, category_type, category_administrator},
                async: true,
                success : await function(data){
                    $("#add_category_modal").modal('hide');
                    $("#add_category_modal form").trigger('reset');
                    if(data) {
                        get_all_categories();
                        Swal.fire({
                        title: "Success",
                        text: "Category Added Successfully",
                        icon: "success",
                        showCancelButton: false,
                        showCancelButton: false,
                        showDeniedButton: false,
                        timer: 3000
                        }).then((result) => {
                            $("#category_table_td_id_" + data.trim()).click();
                        });
                    } else {
                        Swal.fire({
                        title: "Error",
                        text: "Something went wrong",
                        icon: "error",
                        // timer: 1300
                        });
                    }
                }
            });

            await $("#item_master_cat_btn").prop('disabled', false).html('Add Category');
        }
	}

    // adding new item
    function add_item() {
        var product_id= $("#product_id").val();
        var form = $('#my_form_add_item')[0];
        var formData = new FormData(form);
        var category_id= $("#flash_storage_div #item_category_id").val();
        formData.append('product_id',product_id);
        formData.append('category_id',category_id);
        var cat_id= $("#item_category_id").val();
        var category_name= $("#item_category_name_id").val();

        var unit_type= $("#unit_type").val();
        if( !unit_type ) {
            unit_type= 'Not Provided';
        }

        formData.append('unit_type',unit_type);

        if(item_validity(''))
        $.ajax({
	        url:'<?php echo site_url('procurement/inventory_controller_v2/add_item') ?>',
	        type:'post',
            data: formData,
            processData: false,
            contentType: false,
            cache : false,
	        success : function(data){
                $("#add_item_modal").modal('hide');
                $("#add_item_modal form").trigger('reset');
                if(data == 1) {
                    get_and_construct_sub_categories_table(cat_id, category_name);
	        		$(function(){
			          new PNotify({
			              title: 'Success',
			              text: 'Added new item',
			              type: 'success',
			          });
			        });
	        	} else {
	        		$(function(){
			          new PNotify({
			              title: 'Error!',
			              text: 'Something went wrong...',
			              type: 'error',
			          });
			        });
	        	}
	        }
	    });
    }

    function prepare_modal_before_add_sub_category() {
        $("#add_sub_category_modal #my_form_sub_category").trigger('reset');
    }

    // adding new item sub category
    async function add_sub_category() {
        var call_for_edit= 'no';
        var sub_category_id_for_update= 0;
        const expenseSubCatId=$("#expenseSubCatId").val();
        if( $("#sub_category_id_for_update").val() != '') {
            sub_category_id_for_update= $("#sub_category_id_for_update").val();
        }
        if( $("#edit_or_add").val() == 'edit' ) {
            call_for_edit= 'yes';
        }

        var category_id= $("#category_id_modal").val();
        var product_name= $("#product_name").val();
        var is_stockable= 0;
        if( $('#is_stockable').is(":checked") ) {
            is_stockable= 1;
        }
// Need this        // var unit_type= $("#unit_type").val();
        // if( !unit_type ) {
        //     unit_type= 'Not Provided';
        // } 'unit_type': unit_type,
        var description= $("#description").val();
        var category_id= $("#category_id_modal").val();
        var category_name= $("#item_category_name_id").val();
        var sub_category_item_type= $("#sub_category_item_type").val();
        var attributes= [];
        $(".checkbox_attr").each(function() {
            if(this.checked)
                attributes.push($(this).val());
        });

        if(sub_category_validity('') && attribute_validity()) {
            await $("#itemmaster_sub_cat_btn").prop('disabled', true).html('Please Wait...');
        await $.ajax({
            url: '<?php echo site_url('procurement/inventory_controller_v2/add_sub_category'); ?>',
            type: "post",
            data: {
                'category_id': category_id,
                'product_name': product_name,
                'is_stockable': is_stockable,
// Need this                'unit_type': unit_type,
                'description': description,
                'category_id': category_id,
                'attributes': attributes,
                'sub_category_item_type': sub_category_item_type,
                'call_for_edit': call_for_edit,
                'sub_category_id_for_update': sub_category_id_for_update,
                'expenseSubCatId':expenseSubCatId
            },
            success: await function(data) {
                get_and_construct_sub_categories_table(category_id, category_name);
                $("#add_sub_category_modal").modal('hide');
                $("#add_sub_category_modal #my_form_sub_category").trigger('reset');
                var data= JSON.parse(data);
                if(data == '-1') {
                    Swal.fire({
                    title: "error",
                    text: "Sub-category already exist",
                    icon: "error"
                    });
                } else if(data) {
                    Swal.fire({
                    title: "Success",
                    text: "Sub-category added successfully",
                    icon: "success",
                    timer: 3000
                    });
                } else {
                    Swal.fire({
                    title: "Error",
                    text: "Something was wrong",
                    icon: "error"
                    });
                }
            }
        });
        await $("#itemmaster_sub_cat_btn").prop('disabled', false).html('Save and Close');
    }
    }

    function store_category_status(category_id, category_name, cateory_status) {
        $("#category_status_flash").val(cateory_status);
        get_and_construct_sub_categories_table(category_id, category_name);
    }

    function get_all_categories() {

        $.ajax({
            url: '<?php echo site_url('procurement/inventory_controller_v2/get_all_categories'); ?>',
            type: "post",
            success: function(data) {
                var p_data = JSON.parse(data);
                var categories= `<table style="" class="table table-bordered no-footer" aria-describedby="dataTable_info" role="grid">
                                    <thead><tr role="row">
                                        <th class="th_category" style="display: flex; justify-content: space-between; align-items: center;"><span class="cat_text">Categories</span><span title="Add new category" class="pull-right" style="float: right;"><a href="#" class="control-primary btn btn-primary" data-toggle="modal" data-target="#add_category_modal" style="">Add Category</a></span></th>
                                        </tr>
                                    </thead>
                                    <tbody >`; 
                for(var v of p_data) {
                    var is_sellable= 'Yes';
                    if(v.is_sellable == 0) {
                        is_sellable= 'No';
                    }
                    categories += `<tr role="row" class="">
                                        <td style="" class="">
                                            <a href="#" class="list-group-item category_table_td_class" id="category_table_td_id_${v.id}" onclick="store_category_status(${v.id}, '${v.category_name}', '${v.status}')">
                                            <span style="font-weight: bold; ${v.status != '1' ? 'color: lightgray; font-style: italic; opacity: 0.5;' : ''}"><h6><strong>${v.category_name}</strong></h6></span>
                                            <small style="${v.status != '1' ? 'font-style: italic; opacity: 0.6;' : ''}"><b><strong>Is sellable? </strong></b> ${is_sellable}</small><br>
                                            <small style="${v.status != '1' ? 'font-style: italic; opacity: 0.6;' : ''}"><b><strong>Admin </strong></b> ${v.admin || 'Not assigned'}</small><br>
                                        </td>
                                    </tr>`;
                }
                categories += `</tbody></table>`;

                $("#categories_span").html(categories);

                // Get the DataTable instance
                var table = $('.dataTable').DataTable();

                // Check if DataTable exists and destroy it
                if ($.fn.DataTable.isDataTable('.dataTable')) {
                    table.destroy();
                }

                $('.dataTable').DataTable( {
                    dom: 'Blfrtip',
                    buttons: [{
                        extend: 'excelHtml5',
                        exportOptions: {
                            columns: ':visible'
                        }
                    }],
                });
                // Decoration
                $("#DataTables_Table_0_length").hide().remove();
                $(".dt-buttons span").text(" Export").css('border', 'none').addClass('btn btn-info');
                $(".dt-buttons").css('float', 'right').css('border', 'none');
                $(".buttons-html5").css('border', 'none').css('background', 'white');

                    
            }
        });

        

    }

    let isSubCategoryAdded= false;
    function get_and_construct_sub_categories_table(category_id, category_name) {
        window.sessionStorage.setItem("procImCatId",category_id);

        $("#sub_categories_span").html(`<img src="<?php echo base_url('assets/img/ajax-loader.gif');?>">`);
        subCategoryTab();
        $("#tab_div").show();
            $(".category_table_td_class").css('background', 'none');
            $("#category_table_td_id_"+ category_id).css('background', 'skyblue');
            // storing category
            $("#flash_storage_div").html(`<input id="item_category_id" name="item_category_id" value="${category_id}" /> <input id="item_category_name_id" name="" value="${category_name}" />`);

        $.ajax({
            url: '<?php echo site_url('procurement/inventory_controller_v2/get_categoryWise_subCategories'); ?>',
            type: "post",
            data: {'category_id': category_id},
            success: function(data) {
                var p_data = JSON.parse(data).result;
                const approversList = JSON.parse(data).approversList;

                indentApproversListFinal={
                    approvar_1:approversList?.bom_approver_1 ? approversList.bom_approver_1 : 0,
                    approvar_2:approversList?.bom_approver_2 ? approversList.bom_approver_2 : 0,
                    approvar_3:approversList?.bom_approver_3 ? approversList.bom_approver_3 : 0,
                    financial:approversList?.financial_approver ? approversList.financial_approver : 0,
                }

                indentApproversList=JSON.parse(JSON.stringify(indentApproversListFinal));

                let category_status= $("#category_status_flash").val();
                if(p_data.length == 0) {
                    $("#sub_categories_span").html(`<table id="sub_category_table" class="table table-bordered   no-footer" aria-describedby="dataTable_info" role="grid">
                                                        <thead>
                                                            <tr>
                                                                <th colspan="7" class="th_category" style="display: flex; justify-content: space-between; align-items: center;">Sub Categories <button ${category_status != '1' ? 'disabled' : ''} onclick="store_category_id_in_model_popup(${category_id}, '${category_name}')" style="" title="add new sub-category" class="pull-right btn btn-primary" data-toggle="modal" data-target="#add_sub_category_modal" onclick="prepare_modal_before_add_sub_category()"><span class=""> Add Sub-category</span></button></th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td colspan="5" class="text-center">No Sub-categories added</td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                            `);
                    isSubCategoryAdded= false;
                    return;
                }
                isSubCategoryAdded= true;

                var sub_categories= `<table id="sub_category_table" class="table table-bordered   no-footer" aria-describedby="dataTable_info" role="grid">
                                        <thead>
                                            <tr>
                                                <th class="th_category"  colspan="7"><span style="position: relative; top: 6px;" >Sub Categories</span> <button ${category_status != '1' ? 'disabled' : ''} onclick="store_category_id_in_model_popup(${category_id}, '${category_name}')" style="" title="add new sub-category" class="pull-right btn btn-primary" data-toggle="modal" data-target="#add_sub_category_modal"><span class=""> Add Sub-category</span></button></th>
                                            </tr>
                                            <tr>
                                                <th>#</th>
                                                <th>Actions</th>
                                                <th>Sub Category</th>
                                                <th>Visibility in Ekart</th>
                                                <th>Stockable</th>
                                                <th>Items</th>
                                                <th>Photo</th>
                                            </tr>
                                        </thead>
                                        <tbody>`;

                var sr= 1;
                for(var v in p_data) {
                    var stockable= 'No';
                    var c_by= p_data[v].c_by;
                    var l_m_by= p_data[v].l_m_by;
                    if(p_data[v].is_stockable == 1) {
                        stockable= 'Yes';
                    }

                    if(p_data[v].created_by == 0) {
                        c_by= 'Admin';
                    }
                    if(p_data[v].last_modified_by == 0) {
                        l_m_by= 'Admin';
                    }




let category_status= $("#category_status_flash").val();
// 
    var display= '';
    var display_2= `disabled`;
    var style_2= ``;
    let item_inf= '';
    if(p_data[v].items.length) {
        display= `display: none;`;
        display_2= '';
        style_2= '';
    }
    else {
        item_inf += `<font color="red">Items not added</font><br>`;
    }
    var parent_visable_button_text=p_data[v].is_item_visible_for_parent == '1' ? "Make Invisible in Ekart" :"Make Visible in Ekart" ;
// 
                    sub_categories += `<tr>
                                        <td>${sr++}</td>


    <td>
        <div class='dropdown float-right'>
            <div style='cursor: pointer;' class="btn btn-dark" data-toggle='dropdown' style=''>Actions</div>
            <div class='dropdown-menu'>
                <button style="${display}" class="dropdown-item" onclick="delete_sub_category(${p_data[v].proc_im_category_id}, ${p_data[v].id}, '${p_data[v].subcategory_name}', this)">
                    Delete Subcategory
                </button>
                <button ${category_status != '1' ? 'disabled' : ''} style="${display}" class="dropdown-item" onclick="edit_sub_category(${p_data[v].proc_im_category_id}, ${p_data[v].id}, '${p_data[v].subcategory_name}', '${p_data[v].subcategory_item_type}', ${p_data[v].is_stockable}, '${p_data[v].subcategory_description}', '${p_data[v].attributes}', this)">
                    Edit Subcategory
                </button>
                <!-- Show to parent button (smaller size) -->
                <button ${category_status != '1' ? 'disabled' : ''} id="visible_to_parents_button_${p_data[v].id}" onclick="make_sub_category_visible_to_parents(${p_data[v].id}, '${p_data[v].subcategory_name}','${p_data[v].is_item_visible_for_parent}',${p_data[v].proc_im_category_id}, '${p_data[v].category_name}')" class="dropdown-item" style="">${parent_visable_button_text}</button>
                <button ${category_status != '1' ? 'disabled' : ''} onclick="delete_items_modal(${p_data[v].proc_im_category_id}, '${p_data[v].category_name}', ${p_data[v].id}, '${p_data[v].subcategory_name}')" style="border: none; ${style_2}" ${display_2} title="delete items" class="dropdown-item">Delete Items</button>
                <button ${category_status != '1' ? 'disabled' : ''} onclick="store_sub_category_informations_in_model(${p_data[v].category_id}, '${p_data[v].category_name}', ${p_data[v].id}, '${p_data[v].subcategory_name}', '${p_data[v].created_by}', '${p_data[v].last_modified_by}', '${p_data[v].product_code}', '${p_data[v].attributes}', '${p_data[v].variants || ''}', '${p_data[v].is_sellable}')" style="border: none;" title="add new item" class="dropdown-item" data-toggle="modal" data-target="#add_item_modal">Add Items</button>
                <button class="dropdown-item" id="see_item_details_${p_data[v].id}" style="border: none; ${style_2}" ${display_2} class="" onclick="open_all_item_in_popup(${p_data[v].proc_im_category_id}, ${p_data[v].id}, '${p_data[v].subcategory_name}', this)">View Items</button>
            </div>
        </div>
    </td>
                                        <td><strong>${p_data[v].subcategory_name || ''}</strong><br><small>${p_data[v].subcategory_description || 'Not Described'}</small></td>`;
                    sub_categories += ` <td>${p_data[v].is_item_visible_for_parent == '1' ? 'Visible' : 'Invisible'}</td>
                                        <td>${stockable}</td>
                                        <td style="margin: 10px;">
                                        ${item_inf}
                                            <b>No. of Items = ${p_data[v].items.length}</b>
                                        </td>`;

                                        let picture= p_data[v].picture_url;
                                        if(picture == '-') {
                                            picture= `https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/default_image.jpg`;
                                        }
                    
                     sub_categories += `<td style="fit-content;">
                                            
                                            <div id="dvPreview" style="position: relative; display: inline-block; width: 100px; height: 100px;">
                                                <img id="previewing_${p_data[v].id}" name="photograph" style="width: 100px; height: 100px;" src="${picture}" alt="Preview" /><br>
                                                <span id="percentage_student_completed_${p_data[v].id}" 
                                                    style="font-size: 13px; display: none; position: absolute; 
                                                            top: 50%; left: 50%; transform: translate(-50%, -50%);
                                                            color: white; background: rgba(0, 0, 0, 0.5); padding: 5px; border-radius: 5px;">
                                                    0%
                                                </span>
                                            </div>
                                            <div>
                                            <div style="" class="input-group">
                                                <!-- File upload input -->
                                                <input class="form-control" onchange="fileUploadSubcategoryImage(${p_data[v].id})" id="fileupload_${p_data[v].id}" name="student_photo" type="file" accept="image/*" style="width: auto; margin-right: 10px;"/>
                                                <span onclick="get_and_construct_sub_categories_table('${category_id}', '${category_name}')" class="input-group-addon"><span class="fa fa-refresh"></span></span>
                                                
                                            </div>
                                            <span id="fileuploadError_${p_data[v].id}" style="color:red;"></span> 
                                            </div> 
                                        </td>

                                    </tr>`;
                }
                sub_categories += `</tbody>
                                </table>`;

                $("#sub_categories_span").html(sub_categories);

                $('#sub_category_table').DataTable( {
                    "language": {
                            "search": "",
                            "searchPlaceholder": "Enter Search..."
                        },
                        "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
                        "pageLength": 10,
                        dom: 'lBfrtip',
                        buttons: [
                            {
                                extend: 'excelHtml5',
                                text: 'Excel',
                                filename: 'categories',
                                className: 'btn btn-info'
                            },
                            {
                                extend: 'print',
                                text: 'Print',
                                filename: 'categories',
                                className: 'btn btn-info'
                            }
                        ]
                    } );
            
            }
        });
    }

    function make_sub_category_visible_to_parents(sub_category_id,sub_category_name,is_item_visible_for_parent,category_id,category_name){
        var button_status=is_item_visible_for_parent ==1 ? 0 : 1;
        $.ajax({
            url: '<?php echo site_url('procurement/inventory_controller_v2/make_sub_category_visible_to_parents'); ?>',
            type: "post",
            data: {sub_category_id,button_status},
            success(data) {
                var p_data = JSON.parse(data);
                if(p_data){
                Swal.fire({
                        title: "Do you want to make "+sub_category_name+" visible to parents",
                        showCancelButton: true,
                        confirmButtonText: "Yes",
                        }).then((result) => {
                        if (result.isConfirmed) {
                            Swal.fire("Sub Category is now visible for parents!", "", "success");
                            get_and_construct_sub_categories_table(category_id, category_name);
                        }
                    });
                }
            }
        });
    }

    function open_all_item_in_popup(category_id, sub_category_id, sub_category_name) {
        let category_status= $("#category_status_flash").val();
        let is_super_admin= '<?php echo $this->authorization->isSuperAdmin(); ?>';
        $("#sub_category_id_flash").val(sub_category_id);
        $("#sub_category_name_flash").val(sub_category_name);
        $.ajax({
            url: '<?php echo site_url('procurement/inventory_controller_v2/open_all_item_in_popup'); ?>',
            type: "post",
            data: {sub_category_id},
            success(data) {
                var p_data = JSON.parse(data);
                var lists= `<div style="height: 400px; overflow: auto;">
                                <table class="table table-bordered" id="student_details_table_2">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>Actions</th>
                                            <th>Item Name</th>
                                            <th>Is Active</th>
                                            <th>SKU Code</th>
                                            <th>Unit Type</th>
                                        </tr>
                                    </thead>
                                    <tbody>`;
                    var i=1;
                    for(var index in p_data) {
                        var item_name= (p_data[index].item_name).replace("'", "$");
                        lists += `
                                <tr>
                                    <td style="text-align: left;">${i++}</td>
                                    <td style="text-align: left;">

                                        <div class='dropdown'>
                                            <div style='cursor: pointer;' data-toggle='dropdown' style='' class="btn btn-dark">Actions</div>
                                            <div class='dropdown-menu'>
                                                <button ${category_status != '1' ? 'disabled' : ''} type="button" style="" class="dropdown-item" onclick="activate_deactivate_item(${p_data[index].id}, '${p_data[index].status == '1' ? '0' : '1'}', '${item_name}', 'second', ${category_id}, ${sub_category_id}, '${sub_category_name}')">${p_data[index].status == '1' ? 'De-activate' : 'Activate'}</button>
                                                <button type="button" style="" class="dropdown-item" onclick="show_information_about_item('${item_name}', '${p_data[index].id}')">View Item Details</button>
                                                <button ${category_status != '1' ? 'disabled' : ''} type="button" style="" class="dropdown-item" onclick="edit_item_information(${p_data[index].id}, '${p_data[index].is_used}', '${item_name}')">Edit Item Details</button>
                                                <!--        
                                                    <button type="button" style="display: ${is_super_admin == 1 ? 'auto' : 'none'};" class="dropdown-item" onclick="update_price_and_sales_transaction(${p_data[index].id}, '${p_data[index].is_used}', '${item_name}', this)">Update Price & Sales Tx</button>          
                                                    <button data-toggle="modal" data-target="#update_invoice_modal" type="button" style="display: ${is_super_admin == 1 ? 'auto' : 'none'};" class="dropdown-item" onclick="open_modal_invoice(${p_data[index].id}, '${p_data[index].is_used}', '${item_name}', this)">Add new Price & Quantity in Invoice</button>        
                                                -->
                                            </div>
                                        </div>
                                    </td>
                                    <td style="text-align: left;"><!--<a href="#" id="i_edit_${p_data[index].id}" onclick="show_information_about_item('${p_data[index].item_name}', ${p_data[index].id})"></a> --> ${p_data[index].item_name}</td>
                                    <td style="text-align: left;">${p_data[index].status == '1' ? 'Yes' : 'No'}</td>
                                    <td style="text-align: left;">${p_data[index].sku_code || ''}</td>
                                    <td style="text-align: left;">${p_data[index].unit_type || ''}</td>
                                </tr>
                                `;
                    }
                   
                    lists += `</tbody>
                        </table></div>`;
                    $("#view_all_items_modal #view_all_items_details").html(lists);
                    $("#view_all_items_modal #view_all_items_modal_modalHeader").html(`Item in Sub Category - ${sub_category_name}`);
                    $("#view_all_items_modal").modal('show');

                        setTimeout(() => {
                            $('#student_details_table_2').DataTable( {
                            "language": {
                                    "search": "",
                                    "searchPlaceholder": "Enter Search..."
                                },
                                "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
                                "pageLength": 10,
                                dom: 'lBfrtip',
                                "ordering": false,
                                buttons: [
                                    {
                                        extend: 'excelHtml5',
                                        text: 'Excel',
                                        filename: 'items',
                                        className: 'btn btn-info',
                                        exportOptions: {
                                            columns: [0, 1, 2, 3, 4, 5] // Specify the column indices you want to export
                                        }
                                    },
                                    {
                                        extend: 'print',
                                        text: 'Print',
                                        filename: 'items',
                                        className: 'btn btn-info',
                                        exportOptions: {
                                            columns: [0, 1, 2, 3, 4, 5] // Specify the column indices you want to export
                                        }
                                    }
                                ]
                            } );
                        }, 100);

            }
        });
    }

    async function edit_item_information(item_id, is_used, item_name) {
        item_name= item_name.replace("$", "'");
        await show_information_about_item(item_name, item_id);
        $("#view_all_items_modal").modal('hide');
        setTimeout(() => {
            editItemInforation(item_id, is_used);
        }, 500);
    }

    function show_information_about_item(item_name, item_id) {
        item_name= item_name.replace("$", "'");
        $("#view_all_items_modal").modal('hide');
        $(".swal2-confirm").click();
        $("#item_name_information").html(`Information of item - <b>${item_name}</b> `);
        cancelEditingItemInforation();

        $.ajax({ 
            url: '<?php echo site_url('procurement/inventory_controller_v2/get_information_about_item'); ?>',
            type: "post",
            data: {
                'item_id': item_id
            },

            success: function(data) {
                var p_data = JSON.parse(data);
                // $("#is_used_item").val(p_data[0].is_used);
                var information= `<table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <!--
                                                    <th class="" colspan="2"> 
                                                        <button type="button" style="float: right; display: none;" class="btn btn-warning item_edit_class" onclick="editItemInforation(${item_id}, '${p_data[0].is_used}')"><span class="fa fa-edit"></span></button>
                                                    </th>
                                                -->
                                            </tr>

                                            <tr>
                                                <th>Item Name</th>
                                                <td id="i_item_name_td">${item_name}</td>
                                            </tr>

                                            <tr>
                                                <th>Is active?</th>
                                                <td id="">${p_data[0].status == '1' ? 'Yes' : 'No'} </td>
                                            </tr>

                                            <tr>
                                                <th>SKU Code</th>
                                                <td id="i_sku_code">${p_data[0].sku_code || 'Not Added'}</td>
                                            </tr>
                                            <tr>
                                                <th>Threshold Quantity</th>
                                                <td  id="i_threashold_quantity">${p_data[0].threshold_quantity || 'Not Given'}</td>
                                            </tr>

                                            <tr>
                                                <th>Unit Type</th>
                                                <td id="i_unit_type">${p_data[0].unit_type || 'Not Added'}</td>
                                            </tr>
                                            <tr>
                                                <th>Is Perishable?</th>
                                                <td id="i_perishable">${p_data[0].is_perishable || 'Not Given'}</td>
                                            </tr>
                                            <tr>
                                                <th>Classification</th>
                                                <td id="i_classification">${p_data[0].classification || 'Not Classified'}</td>
                                            </tr>
                                            <tr>
                                                <th>HSN SAC</th>
                                                <td  id="i_hsn">${p_data[0].hsn_sac || 'Not Found'}</td>
                                            </tr>
                                            <tr>
                                                <th>Image</th>
                                                <td  id="i_image">${p_data[0].image || 'Not Found'}</td>
                                            </tr>
                                            <?php if(! $this->settings->getSetting("inventory_shoppingcart_donot_show_price")){  ?>
                                                <tr>
                                                    <th>Provisional Selling Price</th>
                                                    <td id="i_selling_price">${p_data[0].selling_price || 'Not Given'}</td>
                                                </tr>
                                            <?php } ?>
<!--
                                            <tr>
                                                <th>Current Quantity</th>
                                                <td  id="i_current_quantity">${p_data[0].current_quantity || 'Not Given'}</td>
                                            </tr>
                                            <tr>
                                                <th>Initial Quantity</th>
                                                <td  id="i_initial_quantity">${p_data[0].initial_quantity || 'Not Given'}</td>
                                            </tr>
                                            
                                            <tr>
                                                <th>Total Quantity</th>
                                                <td id="i_total_quantity">${p_data[0].total_quantity || 'Not Given'}</td>
                                            </tr>
                                           
                                            <tr>
                                                <th>Cost Product</th>
                                                <td  id="i_cost_product">${p_data[0].cost_prodcut || 'Not Given'} </td>
                                            </tr>
                                                                                       
                                            <tr>
                                                <th>About Item</th>
                                                <td id="i_desc">${p_data[0].item_description || 'Not Described'}</td>
                                            </tr>
-->
                                            <tr>
                                                <th class="text-center" colspan="2"><b>Item Attributes</b></th>
                                            </tr>`;
                                    
                for( var index in p_data[0].attributes ) {
                    information += `<tr>
                                        <th>${toSentenceCase(index)}</th>
                                        <td class="attribute_class i_${index}">${p_data[0].attributes[index]}</td>
                                    </tr>`;
                }
                    
                information += `    </thead>
                                </table>
                                <input type="hidden" id="itemId" value="${item_id}" />`;

                $("#item_information_div").html(information);
                if('<?php echo $this->authorization->isSuperAdmin(); ?>') {
                    $("#item_name_information").html(`Information of item - <b>${item_name}</b> (Super Admin)`);
                    // $("#view_all_items_modal_modalHeader").html(`Information of item (Super Admin) - <b>${item_name}</b>`);
                    // $("#modalHeader_of_item").html(`Information of item (Super Admin) - <b>${item_name}</b>`);
                }
                $("#item_information_modal").modal('show');
                
            }
        });

    }

    function toSentenceCase(str) {
        if (!str || typeof str !== 'string') return str;
        
        return str
        .toLowerCase()  // First convert entire string to lowercase
        .split(' ')     // Split into words
        .map(word => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize first letter of each word
        .join(' ');     // Join back into a single string
    }

    function change_initial_quantity_of_item(item_id, initial_quantity, item_name, initial_cost_selling) {
        if('<?php echo $this->authorization->isSuperAdmin(); ?>') {
            $("#item_information_modal").modal('hide');
            if(initial_cost_selling == 'initial') {
                var title= 'Initial Value';
                var column= 'initial_quantity';
            } else if(initial_cost_selling == 'cost') {
                var title= 'Cost Product';
                var column= 'cost_prodcut';
            } else {
                var title= 'Provisional Selling Price';
                var column= 'selling_price';
            }
            bootbox.prompt({
                title: `Update ${title}`,
                value: initial_quantity,
                inputType: "text",
                callback: function(result) {
                    if(result) {
                        $.ajax({
                            url: '<?php echo site_url('procurement/inventory_controller_v2/change_initial_quantity_of_item'); ?>',
                            type: "post",
                            data: {item_id, column, 'changed_initial_quantity' : result},
                            success(data) {
                                var p_data = JSON.parse(data);
                                Swal.fire({
                                    title: "Success",
                                    text: "Changed successfully",
                                    icon: "success",
                                    timer: 3000
                                });
                                
                                setTimeout(() => {
                                    show_information_about_item(item_name, item_id);
                                }, 1800);
                                
                            }
                        });
                    }
                }
            }).addClass('swal_ka_class2');


            
          
        }
    }

    function activate_deactivate_item(item_id, status, item_name, first_or_second, category_id, sub_category_id, sub_category_name) {
        item_name= item_name.replace("$", "'");
        var confirm_first= confirm(`You are ${status == '1' ? 'Activating' : 'Deactivating'} item - ${item_name}. Are you sure?`);
        if(confirm_first) {
            $.ajax({
                url: '<?php echo site_url('procurement/inventory_controller_v2/activate_deactivate_item'); ?>',
                type: "post",
                data: {item_id, status},
                success(data) {
                    var p_data = JSON.parse(data);
                    $("#item_information_modal").modal('hide');
                    setTimeout(() => {
                        if(first_or_second == 'first') {
                            show_information_about_item(item_name, item_id);
                        } else {
                            open_all_item_in_popup(category_id, sub_category_id, sub_category_name, '');
                        }
                    }, 500);
                    
                }
            });
        }
    }

    function delete_items_modal(category_id, category_name, sub_category_id, sub_category_name) {
        $.ajax({
            url: '<?php echo site_url('procurement/inventory_controller_v2/get_items_from_sub_category'); ?>',
            type: "post",
            data: {
                'sub_category_id': sub_category_id
            },

            success: function(data) {
                var p_data = JSON.parse(data);
                if(p_data.length) {
                    var html= `<input class="im_delete_class" type="checkbox" id="select_all_item" name="item_all" value="all" onclick="select_all_items(this)"> <label style="margin-left: 6px;  position: relative; top: -7px;" for="item_all">Select All</label><br />`;
                    html += `<div class="" id="" style="">`;
                    for(var v of p_data) {
                        if(v.is_deletable == '-1')
                        html += `<div style=""><input style="" type="checkbox" class="im_delete_class" name="selected_items[]" id="item_${v.id}" value="${v.id}" /><label style="margin-left: 6px; position: relative; top: -7px;" for="selected_items[]" class="">${v.item_name}</label></div>`;
                    }
                    html += `</div>`;          
                    $("#delete_multiple_items_box").html(html);
                    $("#delete_sub_category_name").html(`${category_name.toUpperCase()} > ${sub_category_name.toUpperCase()}`); 
                    $("#delete_item_modal").modal('show');
                } else {
                    alert(`Items not found. Add items first.`);
                }
            }
        });

    }

    function delete_items() {
        var item_ids_arr= [];
        var category_name= $("#item_category_name_id").val();
        var category_id= $("#item_category_id").val();
        $("#delete_multiple_items_box input").each(function() {
            if(this.checked && $(this).val() != 'all') {
                item_ids_arr.push( $(this).val() );
            }
        });

        $.ajax({
            url: '<?php echo site_url('procurement/inventory_controller_v2/delete_items'); ?>',
            type: "post",
            data: {
                'item_ids_arr': item_ids_arr
            },

            success: function(data) {
                $("#delete_item_modal").modal('hide');
                if(data == 1) {
                    var cat_id= $("#flash_storage_div #item_category_id").val();
                    get_and_construct_sub_categories_table(category_id, category_name);
	        		$(function(){
			          new PNotify({
			              title: 'Success',
			              text: 'Deletion Successful',
			              type: 'success',
			          });
			        });
	        	} else {
	        		$(function(){
			          new PNotify({
			              title: 'Oh No!',
			              text: 'Something went wrong...',
			              type: 'error',
			          });
			        });
	        	}
               
            }
        });

    }

    function select_all_items(current) {
        if(current.checked) {
            // Iterate each checkbox
            $(':checkbox').each(function() {
                this.checked = true;                        
            });
        } else {
            $(':checkbox').each(function() {
                this.checked = false;                       
            });
        }
    }

    function delete_sub_category(category_id, sub_category_id, product_name, current) {
        var ask_once= confirm(`Are you sure, you want to delete ${product_name}?`);
        var category_name= $("#item_category_name_id").val();
        if(ask_once) {
            // deletion row animation
            setTimeout(function(current) {
                set_animation(current, category_id, category_name)
            }.bind(this, current), 10);


            $.ajax({
                url: '<?php echo site_url('procurement/inventory_controller_v2/delete_sub_category'); ?>',
                type: "post",
                data: {
                    'sub_category_id': sub_category_id
                },

                success: function(data) {
                    var p_data = JSON.parse(data);
                    if(data == 1) {
                        $(function(){
                        new PNotify({
                            title: 'Success',
                            text: 'Deletion Successful',
                            type: 'success',
                        });
                        });
                    } else {
                        $(function(){
                        new PNotify({
                            title: 'Oh No!',
                            text: 'Something went wrong...',
                            type: 'error',
                        });
                        });
                    }
                    
                }
            });
        }
        

    }

    function set_animation(current, category_id, category_name) {
        
        $(current).parent().parent().fadeTo(2000, 0, function() {
            get_and_construct_sub_categories_table(category_id, category_name);
        });
    }

    function store_sub_category_informations_in_model(category_id, category_name, sub_category_id, sub_category_name, created_by, last_modified_by, item_code, attributes, varients, is_sellable) {
        // prepare modal before show
        var attrs= ``;
        var attributes= attributes.split(',')
        if(attributes[0] != 'null') {
            for(var v of attributes) {
                attrs +=`<div class="form-group">
                            <label style="text-align: right;" class="col-md-3 control-label" for="">${'' + v.charAt(0).toUpperCase() + v.substring(1)}</label>  
                            <div class="col-md-9">
                            <div class="input-group">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                                <input placeholder="Add item's ${v}" type="text" id="attribute_${v}" class="form-control amountClass" name="attributes[${v}]" />
                                 
                                </div>
                                <div class="help-block">Add item's ${v}</div>
                            </div>
                        </div>`;
            }
        } else {
            attrs += `No fields for attributes`;
        }

        $("#modal_attributes_id").html(attrs);
        var category_name= category_name.toUpperCase();
        var sub_category_name= sub_category_name.toUpperCase();
        $("#item_modal_header").html(`${category_name} > ${sub_category_name}`);
        $("#product_id").val(sub_category_id);
        
        if(varients.length)
            $("#varient_id").val(varients[0].id);

        if( is_sellable == '1') {
            $("#selling_price").attr('disabled', false);
        } else {
            $("#selling_price").attr('disabled', true);
        }
    }

    function store_category_id_in_model_popup(category_id, category_name) {
        $("#edit_or_add").val('add');
        $("#category_id_modal").val(category_id);
        $("#category_name_in_modal_header").html(`Add Sub Category Under <b>${category_name}</b>`);
    }

    function show_hide_approvar(div_2_id, apr_input_2_id, div_3_id, apr_input_3_id) {
        $("#approver-1-min_amount").hide();
        $("#approver-2-min_amount").hide();
        $("#approver-3-min_amount").hide();

        if($("#approval_algorithm").val() == '2') {
            $(`#${div_2_id}`).show();
            $(`#${apr_input_2_id}`).val('');
            $(`#${div_3_id}`).hide();
            $(`#${apr_input_3_id}`).val('');

            setApprovers("approvar_1","");
            setApprovers("approvar_2","");

        } else if($("#approval_algorithm").val() == '3') {
            $(`#${div_2_id}`).show();
            $(`#${apr_input_2_id}`).val('');
            $(`#${div_3_id}`).show();
            $(`#${apr_input_3_id}`).val('');

            setApprovers("approvar_1","");
            setApprovers("approvar_2","");
            setApprovers("approvar_3","");
        } else if($("#approval_algorithm").val() == '4') {
            // Approvers based on Budget Amount
            $(`#${div_2_id}`).show();
            $(`#${apr_input_2_id}`).val('');
            $(`#${div_3_id}`).show();
            $(`#${apr_input_3_id}`).val('');

            $("#approver-1-min_amount").show();
            $("#approver-2-min_amount").show();
            $("#approver-3-min_amount").show();

            setApprovers("approvar_1","");
            setApprovers("approvar_2","");
            setApprovers("approvar_3","");
        } else {
            $(`#${div_2_id}`).hide();
            $(`#${apr_input_2_id}`).val('');
            $(`#${div_3_id}`).hide();
            $(`#${apr_input_3_id}`).val('');

            setApprovers("approvar_1","");
        }
    }

    async function update_price_and_sales_transaction(item_id, is_used, item_name, current) {
        item_name= item_name.replace("$", "'");
        var price= prompt(`Enter ${item_name}'s Price`);
        if(+price > 0) {
            $(current).prop('disabled', true).html(`Updating... Please Wait...`);
            await $.ajax({
                url: '<?php echo site_url('procurement/inventory_controller_v2/update_price_and_sales_transaction'); ?>',
                type: "post",
                data: {item_id, is_used, price},
                async: true,
                success(data) {
                    $(current).prop('disabled', false).html(`Update Price & Sales Tx`);
                    var p_data = JSON.parse(data);
                    if(p_data) {
                        Swal.fire({
                            'icon': 'success',
                            'title': 'Successful',
                            'text': 'Updating price and respected transaction is successfully completed.',
                            timer: 3000
                        });
                    } else {
                        Swal.fire({
                            'icon': 'eror',
                            'title': 'Error',
                            'text': 'Something went wrong.'
                        });
                    }
                    console.log(p_data);
                    
                }
            });
        }
        
    }

    function open_modal_invoice(item_id, is_used, item_name) {
        $("#view_all_items_modal").modal('hide');
        $("#invItemId").val(item_id);
        $("#invItemPrice").val('');
        $("#invQuantity").val('');
        $("#modalHeader_invoice").html(`Update ${item_name}`);
        $("#update_invoice_modal").modal('show');
    }

    function update_price_qty_in_invoices() {
        
        var invItemPrice= $("#invItemPrice").val();
        var invQuantity= $("#invQuantity").val();
        var item_id= $("#invItemId").val();
        if(invItemPrice && invQuantity)
        $.ajax({
            url: '<?php echo site_url('procurement/inventory_controller_v2/update_price_qty_in_invoices'); ?>',
            type: "post",
            data: {item_id, invItemPrice, invQuantity},
            success(data) {
                var p_data = JSON.parse(data);
                if(p_data)
                    Swal.fire("Changes are saved", "", "success")
                    .then(() => {
                   $("#update_invoice_modal").modal('hide');
                   $("#view_all_items_modal").modal('show');
                });
                else
                    Swal.fire("Something went wrong", "", "error")
                .then(() => {
                   $("#update_invoice_modal").modal('hide');
                   $("#view_all_items_modal").modal('show');
                });
            }
        });
        else {
            Swal.fire("Inputs are not filled properly", "", "error")
            .then(() => {
                   $("#update_invoice_modal").modal('hide');
                   $("#view_all_items_modal").modal('show');
                });
        }
    }

    function validate_sku_asUnique(current) {
        var sku= $(current).val();
        if(sku) {
            $.ajax({
                url: '<?php echo site_url('procurement/inventory_controller_v2/validate_sku_asUnique'); ?>',
                type: "post",
                data: {sku},
                async: true,
                success(data) {
                    var p_data= JSON.parse(data);
                    if(p_data == '1') {
                        $("#unique_sku_validation_block").html('SKU code already exists');
                        $("#addItemButton").prop('disabled', true);
                    } else {
                        $("#unique_sku_validation_block").html('');
                        $("#addItemButton").prop('disabled', false);
                    }
                }
            });
        } else {
            $("#unique_sku_validation_block").html('');
            $("#addItemButton").prop('disabled', false);
        }
    }

    function fileUploadSubcategoryImage(subcatId){
        var fileInput = $('#fileupload_'+subcatId)[0];
        var file = fileInput.files[0];
        if (file && validateStudentPhoto(file, 'fileupload')) {
            completed_promises = 0;
            current_percentage = 0;
            total_promises = 1;
            in_progress_promises = total_promises;

            saveFileToStorage(file, subcatId); 
            $('#previewing_'+ subcatId).css('opacity', '0.3');
            $("#fileuploadError").html("");

            readURL(fileInput,subcatId);
        } else {
            fileInput.value = null;
        }
    }

  
function validateStudentPhoto(file,errorId){
    if (file.size > 10000000 || file.fileSize > 10000000)
    {
       $("#"+errorId+"Error").html("Allowed file size exceeded. (Max. 10 MB)")
       return false;
    }
    if(file.type != 'image/jpeg' && file.type != 'image/jpg' && file.type != 'image/png') {
        $("#"+errorId+"Error").html("Allowed file types are jpeg, jpg and png");
        return false;
    }
    return true;
}

function readURL(input,subcatId) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = function (e) {
            $('#previewing_'+subcatId).attr('src', e.target.result);
        }

        reader.readAsDataURL(input.files[0]);
    }
}

 function saveFileToStorage(file, subCatId) {
    
    $('#percentage_student_completed_'+subCatId).show();
    $('#fileupload').attr('disabled','disabled');
    $("#btnSubmit").prop('disabled',true);
    $.ajax({
        url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {'filename':file.name, 'file_type':file.type, 'folder':'procurement'},
        success: function(response) {
            // console.log('Response: ',response)
            single_file_progress(0,subCatId);
            response = JSON.parse(response);
            var path = response.path;
            var signedUrl = response.signedUrl;
            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type, 
                    "x-amz-acl":"public-read" 
                },
                processData: false,
                data: file,
                xhr: function () {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function (e) {
                        // For uploads
                        if (e.lengthComputable) {
                            single_file_progress(e.loaded / e.total *100|0,subCatId);
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    upload_image_path_subcategory(path, subCatId);
                    $('#percentage_student_completed_'+subCatId).hide();
                    $('#fileupload').removeAttr('disabled');
                    $('#previewing_').css('opacity','1');
                },
                error: function(err) {
                    // console.log(err);
                    reject(err);
                }
            });
        },
        error: function (err) {
            reject(err);
        }
    });
}

function upload_image_path_subcategory(path, subCatId){
    $.ajax({
        url: '<?php echo site_url("procurement/inventory_controller_v2/upload_category_image_path"); ?>',
        type: 'post',
        data: {'path':path, 'subCatId':subCatId},
        success: function(response) {
            console.log(response);
        },
        error: function (err) {
            reject(err);
        }
    });

}

function single_file_progress(percentage,subCatId) {
  if(percentage == 100) {
      in_progress_promises--;
      if(in_progress_promises == 0) {
          current_percentage = percentage;
      }
  } else {
      if(current_percentage<percentage) {
          current_percentage = percentage;
      }
  }
  $("#percentage_student_completed_"+subCatId).html(`${current_percentage} %`);
  return false;
}

$("#approvar_modal").on("show.bs.modal",(e)=>{
    $("#approval_algorithm").trigger("change");
});

function setApprovers(level,staffId){
    if(approverModalType.trim()==="financial"){
        const staffsToAvoid={};

        if(Number(staffId)){
            indentApproversList["financial"]=staffId;
        }

        let tempStaffId=indentApproversList["financial"];
        
        staffsToAvoid[indentApproversList[`approvar_${1}`]]=1;
        staffsToAvoid[indentApproversList[`approvar_${2}`]]=1;
        staffsToAvoid[indentApproversList[`approvar_${3}`]]=1;

        let options=``;
        for(let {id,staffName} of staffs){
            if(id in staffsToAvoid) continue;

            options+=`<option ${tempStaffId==id && "selected"} value="${id}">${staffName}</option>`;
        }

        $(`#approvar_${1}`).html(options);

    }else{
        if(Number(staffId)){
            indentApproversList[level]=staffId;
        }

        for(let i=1;i<=3;i++){
            const staffsToAvoid={};

            if(i==1){
                staffsToAvoid[indentApproversList[`approvar_${2}`]]=1;
                staffsToAvoid[indentApproversList[`approvar_${3}`]]=1;
            }else if(i==2){
                staffsToAvoid[indentApproversList[`approvar_${1}`]]=1;
                staffsToAvoid[indentApproversList[`approvar_${3}`]]=1;
            }else if(i==3){
                staffsToAvoid[indentApproversList[`approvar_${1}`]]=1;
                staffsToAvoid[indentApproversList[`approvar_${2}`]]=1;
            }

            let options=``;
            for(let {id,staffName} of staffs){
                if(id in staffsToAvoid) continue;

                options+=`<option ${indentApproversList[`approvar_${i}`]==id && "selected"} value="${id}">${staffName}</option>`;
            }

            $(`#approvar_${i}`).html(options);
        }
    }
}

$("#approvar_1").change(e=>{
    const staffId=$("#approvar_1").val();
    setApprovers("approvar_1",staffId);
});

$("#approvar_2").change(e=>{
    const staffId=$("#approvar_2").val();
    setApprovers("approvar_2",staffId);
})

$("#approvar_3").change(e=>{
    const staffId=$("#approvar_3").val();
    setApprovers("approvar_3",staffId);
})

function category_type_info() {
    Swal.fire({
        title: 'Category Type Information',
        html: `<p style="text-align: left;"><b>Consumables</b> - Items that are used up quickly and require frequent replenishment. <i>Examples: Stationery (pens, paper), printer ink, cleaning supplies, food items.</i></p>
            <p style="text-align: left;"><b>Regular Items</b> - General-purpose goods that don't fall under assets or consumables. <i>Examples: Office furniture, gameplay equipment.</i></p>
            <p style="text-align: left;"><b>Assets</b> - High-value items tracked for depreciation, maintenance. <i>Examples: Laptops, machinery, company vehicles.</i></p>
            <p style="text-align: left;"><b>Services</b> - Non-tangible work provided by vendors or contractors. <i>Examples: Repair services, consulting fees, software subscriptions or utility bills.</i></p>`,
        icon: 'info',
        confirmButtonText: 'OK',
        customClass: 'swal_ka_class_info'
    });

}
</script>