<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<style>
    /* Form styling for better spacing and responsiveness */
    .form-group {
        margin-bottom: 10px; /* Further reduced from 15px */
    }
    
    .form-control {
        height: auto;
        padding: 8px; /* Reduced from 10px */
        border-radius: 4px;
        border: 1px solid #ddd;
    }
    
    .form-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    select.form-control {
        padding: 7px 8px; /* Reduced from 9px 10px */
        height: 34px; /* Further reduced from 38px */
    }
    
    textarea.form-control {
        min-height: 70px; /* Further reduced from 80px */
    }
    
    /* Restore glyphicon color */
    .glyphicon {
        color: #000; /* Black color for glyphicons */
    }
    
    .input-group-addon {
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        border-left: none;
        padding: 6px 10px; /* Reduced from 8px 12px */
        border-radius: 0 4px 4px 0;
        color: #000; /* Ensure text/icon color is black */
    }
    
    label {
        font-weight: 600;
        margin-bottom: 5px; /* Further reduced from 8px */
        display: block;
    }
    
    .card {
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .card-header {
        padding: 15px 20px;
    }
    
    .card-body {
        /* padding: 30px; Increased padding */
    }
    
    .btn {
        padding: 8px 16px; /* Reduced from 10px 20px */
        font-weight: 600;
        border-radius: 4px;
    }
    
    .btn-primary {
        background-color: #007bff;
        border-color: #007bff;
    }
    
    .btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;
    }
    
    .alert {
        padding: 12px 15px;
        border-radius: 4px;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .card-body {
            padding: 15px;
        }
        
        .form-group {
            margin-bottom: 25px; /* Still increased but slightly less on mobile */
        }
        
        .row {
            margin-left: -10px;
            margin-right: -10px;
        }
        
        .col-md-6, .col-md-12 {
            padding-left: 10px;
            padding-right: 10px;
        }
        
        /* Mobile-specific help text styling */
        .help-block {
            font-size: 12px;
            margin-top: 8px;
            line-height: 1.4;
        }
        
        .help-block i {
            margin-right: 4px;
        }
        
        /* Adjust photo upload container for mobile */
        .photo-upload-container {
            flex-direction: column;
            gap: 15px;
        }
        
        .upload-details {
            width: 100%;
        }
    }
    
    /* Reduce space between rows */
    .row {
        margin-bottom: 5px; /* Further reduced from 10px */
    }
    
    /* Reduce space before the submit button */
    .mt-4 {
        margin-top: 1rem !important; /* Further reduced from 1.5rem */
    }
    
    /* Reduce space after the last form group */
    .form-group:last-child {
        margin-bottom: 15px; /* Further reduced from 20px */
    }
    
    /* Reduce padding in card body */
    .card-body {
        padding: 15px; /* Further reduced from 20px */
    }
    
    /* Reduce space between label and input */
    label {
        margin-bottom: 5px; /* Further reduced from 8px */
    }
    
    /* Adjust row inline spacing */
    .row[style="margin-bottom: 25px;"] {
        margin-bottom: 10px !important; /* Further reduced from 15px */
    }
    
    /* File input styling */
    input[type="file"].form-control {
        padding: 6px;
    }
    
    /* Improve parent info display */
    #parent_info_div {
        margin-top: 15px;
        padding: 15px;
        border-radius: 8px;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
    }

    .parent-info-container {
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .parent-photo {
        width: 100px;
        height: 100px;
        border-radius: 8px;
        object-fit: cover;
        border: 2px solid #dee2e6;
    }

    .parent-details {
        flex: 1;
    }

    .parent-name {
        font-size: 16px;
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }

    .parent-contact {
        font-size: 14px;
        color: #6c757d;
        margin-top: 5px;
    }

    .parent-label {
        font-size: 14px;
        color: #6c757d;
    }

    /* Photo loading styles */
    .photo-container {
        position: relative;
        width: 100px;
        height: 100px;
    }

    .photo-loader {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
    }

    .photo-loader .spinner {
        width: 30px;
        height: 30px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .parent-photo {
        width: 100px;
        height: 100px;
        border-radius: 8px;
        object-fit: cover;
        border: 2px solid #dee2e6;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .parent-photo.loaded {
        opacity: 1;
    }

    /* Photo upload styles */
    .photo-upload-container {
        margin: 10px 0;
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .photo-preview-container {
        position: relative;
        width: 100px;
        height: 100px;
        border-radius: 8px;
        overflow: hidden;
        display: none;
        background: #f8f9fa;
        border: 2px solid #dee2e6;
    }

    .photo-preview {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .upload-progress {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 5px;
        text-align: center;
        display: none;
    }

    .upload-progress-bar {
        height: 4px;
        background: #4CAF50;
        width: 0%;
        transition: width 0.3s ease;
    }

    .remove-photo {
        position: absolute;
        top: 5px;
        right: 5px;
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 25px;
        height: 25px;
        cursor: pointer;
        display: none;
        font-size: 16px;
        line-height: 1;
        padding: 0;
    }

    .remove-photo:hover {
        background: rgba(255, 255, 255, 1);
    }

    .upload-details {
        flex: 1;
    }

    .custom-file-upload {
        display: inline-block;
        padding: 8px 16px;
        background: #007bff;
        color: white;
        border-radius: 4px;
        cursor: pointer;
        transition: background 0.3s ease;
        font-size: 14px;
    }

    .custom-file-upload:hover {
        background: #0056b3;
    }

    .custom-file-upload i {
        margin-right: 5px;
    }

    #pickup_photo {
        display: none;
    }
</style>

<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2');?>">Attendance</a></li>
    <li><a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2/emergency_exit');?>">Emergency Exit</a></li>
    <li>Create Emergency Exit</li>
</ul>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card cd_border">
                <div class="card-header panel_heading_new_style_staff_border">
                    <div class="row" style="margin: 0px">
                        <h3 class="card-title panel_title_new_style_staff">
                            <a class="back_anchor" href="<?php echo site_url('attendance_day_v2/Attendance_day_v2/emergency_exit') ?>">
                                <span class="fa fa-arrow-left"></span>
                            </a> 
                            Create Emergency Exit
                        </h3>
                    </div>
                </div>
                <div class="card-body">
                    <form action="<?php echo site_url('attendance_day_v2/Attendance_day_v2/save_emergency_exit'); ?>" method="post" enctype="multipart/form-data" id="emergency_exit_form" novalidate>
                        <div class="row justify-content-center">
                            <div class="col-md-10">
                                <div class="row" style="margin-bottom: 25px;">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Exit Date & Time <font color="red">*</font></label>
                                            <div class="input-group">
                                                <input type="text" id="exit_datetime" name="exit_datetime" class="form-control" value="<?php echo date('d-m-Y h:i A'); ?>" required>
                                                <span class="input-group-addon"><span class="glyphicon glyphicon-calendar"></span></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <!-- Placeholder for balance if needed -->
                                    </div>
                                </div>
                                
                                <div class="row" style="margin-bottom: 25px;">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Section <font color="red">*</font></label>
                                            <select id="class_section_id" name="class_section_id" class="form-control" required>
                                                <option value="">Select Section</option>
                                                <?php foreach ($class_section as $section): ?>
                                                <option value="<?= $section->sectionID ?>"><?= $section->class_name.$section->section_name ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Student <font color="red">*</font></label>
                                            <select id="student_id" name="student_id" class="form-control" required disabled>
                                                <option value="">Select Student</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row" style="margin-bottom: 25px;">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Pick-up By <font color="red">*</font></label>
                                            <select id="pickup_by" name="pickup_by" class="form-control" required>
                                                <option value="">Select</option>
                                                <option value="Father">Father</option>
                                                <option value="Mother">Mother</option>
                                                <option value="Other">Other</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6" id="other_name_div" style="display:none;">
                                        <div class="form-group">
                                            <label>Other Name <font color="red">*</font></label>
                                            <input type="text" placeholder="Enter other name" id="pickup_other_name" name="pickup_other_name" class="form-control" maxlength="50">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row" id="photo_div" style="display:none;">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label>Pick-up Person Photo <font color="red">*</font></label>
                                            <div class="photo-upload-container">
                                                <div class="photo-preview-container" id="photo_preview_container">
                                                    <img id="photo_preview" class="photo-preview">
                                                    <div class="upload-progress" id="upload_progress">
                                                        <div class="upload-progress-bar" id="upload_progress_bar"></div>
                                                        <span id="upload_percentage">0%</span>
                                                    </div>
                                                    <button type="button" class="remove-photo" onclick="removePhoto()">×</button>
                                                </div>
                                                <div class="upload-details">
                                                    <label for="pickup_photo" class="custom-file-upload">
                                                        <i class="fa fa-camera"></i> Choose Photo
                                                    </label>
                                                    <input type="file" id="pickup_photo" name="pickup_photo" accept="image/*" onchange="handlePhotoUpload(this)">
                                                    <input type="hidden" id="uploaded_photo_url" name="uploaded_photo_url">
                                                    <small class="help-block">
                                                        <i class="fa fa-info-circle"></i> Maximum file size: 5MB. Supported formats: JPG, PNG, GIF
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label>Remarks <font color="red">*</font></label>
                                            <textarea placeholder="Enter remarks" id="remarks" name="remarks" class="form-control" rows="3" required></textarea>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row mt-4">
                                    <div class="col-md-12 text-center">
                                        <button type="submit" class="btn btn-primary">Submit</button>
                                        <a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2/emergency_exit'); ?>" class="btn btn-danger">Cancel</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
$(document).ready(function() {    
    // Set today's date in the correct format
    var today = new Date();
    var dd = String(today.getDate()).padStart(2, '0');
    var mm = String(today.getMonth() + 1).padStart(2, '0');
    var yyyy = today.getFullYear();
    var hh = String(today.getHours()).padStart(2, '0');
    var min = String(today.getMinutes()).padStart(2, '0');
    var ampm = today.getHours() >= 12 ? 'PM' : 'AM';
    var hours12 = today.getHours() % 12 || 12;
    var formattedTime = String(hours12).padStart(2, '0') + ':' + min + ' ' + ampm;
    var formattedToday = dd + '-' + mm + '-' + yyyy + ' ' + formattedTime;

    $('#exit_datetime').val(formattedToday);
    
    // Check which datepicker library is available and use it
    if ($.fn.datetimepicker) {
        $('#exit_datetime').datetimepicker({
            format: 'DD-MM-YYYY hh:mm A',
            defaultDate: today,
            maxDate: today,
            minDate: moment().subtract(1, 'year'),
            useCurrent: true,
            sideBySide: true,
            toolbarPlacement: 'top',
            keepOpen: false,
            showClose: true,
            icons: {
                time: 'glyphicon glyphicon-time',
                date: 'glyphicon glyphicon-calendar',
                up: 'glyphicon glyphicon-chevron-up',
                down: 'glyphicon glyphicon-chevron-down',
                previous: 'glyphicon glyphicon-chevron-left',
                next: 'glyphicon glyphicon-chevron-right',
                today: 'glyphicon glyphicon-screenshot',
                clear: 'glyphicon glyphicon-trash',
                close: 'glyphicon glyphicon-remove'
            },
            widgetPositioning: {
                horizontal: 'auto',
                vertical: 'bottom'
            }
        }).on('dp.show', function() {
            // Add custom close button to the top-right
            setTimeout(function() {
                // Remove the default close button from toolbar if it exists
                $('.bootstrap-datetimepicker-widget .picker-switch td span.glyphicon-remove').parent().remove();
                
                // Add custom close button if it doesn't exist already
                if ($('.bootstrap-datetimepicker-widget .custom-close-btn').length === 0) {
                    var closeBtn = $('<a>', {
                        href: 'javascript:void(0)',
                        class: 'custom-close-btn',
                        style: 'position: absolute; top: 5px; right: 10px; z-index: 1000; color: #333;',
                        html: '<span class="glyphicon glyphicon-remove"></span>'
                    });
                    
                    closeBtn.on('click', function() {
                        $('#exit_datetime').data('DateTimePicker').hide();
                    });
                    
                    $('.bootstrap-datetimepicker-widget').append(closeBtn);
                }
                
                // Remove extra vertical spacing at the top
                $('.bootstrap-datetimepicker-widget .datepicker-days thead tr:first-child').css('display', 'none');
                $('.bootstrap-datetimepicker-widget .datepicker-days thead tr.picker-switch').css('padding-top', '0');
                $('.bootstrap-datetimepicker-widget .datepicker-days thead').css('padding-top', '0');
                $('.bootstrap-datetimepicker-widget .datepicker-days').css('padding-top', '0');
                $('.bootstrap-datetimepicker-widget .picker-switch').css('margin-top', '0');
            }, 0);
        }).on('dp.error', function(e) {
            console.error('Datetimepicker error:', e);
        }).on('dp.change', function(e) {
            // Reset section, student and pickup-by when date changes
            $('#class_section_id').val('');
            $('#student_id').html('<option value="">Select Student</option>').prop('disabled', true);
            $('#pickup_by').val('');
            $('#other_name_div').hide();
            $('#photo_div').hide();
            if ($('#parent_info_div').length) {
                $('#parent_info_div').remove();
            }
        });
        
        // Add manual outside click handler
        $(document).on('mousedown', function(e) {
            // If the target of the click isn't the container nor a descendant of the container
            if (!$(e.target).closest('.bootstrap-datetimepicker-widget').length && 
                !$(e.target).closest('#exit_datetime').length && 
                !$(e.target).closest('.input-group-addon').length) {
                
                var picker = $('#exit_datetime').data('DateTimePicker');
                if (picker && picker.hide) {
                    picker.hide();
                }
            }
        });
        
    } else {
        console.error('Datetimepicker library not found');
    }
    
    // When section is selected, load present students
    $('#class_section_id').change(function() {
        var class_section_id = $(this).val();
        if (class_section_id) {
            loadPresentStudents(class_section_id);
            // Reset student and pickup-by when section changes
            $('#student_id').html('<option value="">Select Student</option>').prop('disabled', true);
            $('#pickup_by').val('');
            $('#other_name_div').hide();
            $('#photo_div').hide();
            if ($('#parent_info_div').length) {
                $('#parent_info_div').remove();
            }
        } else {
            $('#student_id').html('<option value="">Select Student</option>').prop('disabled', true);
        }
    });
    
    // Reset pickup-by when student changes
    $('#student_id').change(function() {
        $('#pickup_by').val('');
        $('#other_name_div').hide();
        $('#photo_div').hide();
        if ($('#parent_info_div').length) {
            $('#parent_info_div').remove();
        }
    });
    
    // Show/hide other name field based on pickup by selection
    $('#pickup_by').change(function() {
        if ($('#student_id').val() == '') {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Please select a student first',
                timer: 3000,
                showConfirmButton: false
            });
            $(this).val('');
            return;
        }
        
        if ($(this).val() == 'Other') {
            $('#other_name_div').show();
            $('#photo_div').show();
            $('#pickup_other_name').prop('required', true);
            $('#pickup_photo').prop('required', true);
            // Remove parent info div when Other is selected
            if ($('#parent_info_div').length) {
                $('#parent_info_div').remove();
            }
        } else {
            $('#other_name_div').hide();
            $('#photo_div').hide();
            $('#pickup_other_name').prop('required', false);
            $('#pickup_photo').prop('required', false);
            // Remove character count display if it exists
            $('.char-count').remove();
            
            // Get parent info when Father or Mother is selected
            if ($(this).val() == 'Father' || $(this).val() == 'Mother') {
                var student_id = $('#student_id').val();
                var pickup_by = $(this).val();
                
                if (student_id) {
                    $.ajax({
                        url: "<?php echo site_url('attendance_day_v2/Attendance_day_v2/get_parent_info'); ?>",
                        type: "POST",
                        data: {
                            student_id: student_id,
                            pickup_by: pickup_by
                        },
                        dataType: "json",
                        success: function(data) {
                            if (data.success) {
                                // Always remove existing div to ensure label is updated
                                if ($('#parent_info_div').length) {
                                    $('#parent_info_div').remove();
                                }
                                
                                // Create new div with updated content including photo
                                var parentInfoHtml = `
                                    <div id="parent_info_div" class="alert alert-info">
                                        <div class="parent-info-container">
                                            <div class="photo-container">
                                                <div class="photo-loader">
                                                    <div class="spinner"></div>
                                                </div>
                                                <img src="${data.parent_photo || (pickup_by === 'Mother' ? 
                                                    'https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/sample_girl_image.png' : 
                                                    'https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/Admission process/student_image.jpg')}" 
                                                     alt="${pickup_by} Photo" 
                                                     class="parent-photo"
                                                     onload="this.classList.add('loaded'); this.parentElement.querySelector('.photo-loader').style.display='none';"
                                                     onerror="this.onerror=null; this.src='${pickup_by === 'Mother' ? 
                                                        'https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/sample_girl_image.png' : 
                                                        'https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/Admission process/student_image.jpg'}';">
                                            </div>
                                            <div class="parent-details">
                                                <div class="parent-label">${pickup_by} Information</div>
                                                <div class="parent-name">${data.parent_name}</div>
                                                <div class="parent-contact">${data.parent_mobile || 'Contact number not available'}</div>
                                            </div>
                                        </div>
                                    </div>`;
                                
                                $(parentInfoHtml).insertAfter('#pickup_by');
                            } else {
                                if ($('#parent_info_div').length) {
                                    $('#parent_info_div').remove();
                                }
                            }
                        }
                    });
                } else {
                    alert('Please select a student first');
                    $(this).val('');
                }
            } else {
                if ($('#parent_info_div').length) {
                    $('#parent_info_div').remove();
                }
            }
        }
    });
    
    // Add character count functionality for Other Name field
    $('#pickup_other_name').on('input', function() {
        var maxLength = 50;
        var currentLength = $(this).val().length;
        var remaining = maxLength - currentLength;
        
        // Remove existing character count display
        $('.char-count').remove();
        
        // Add character count display
        var charCountHtml = '<small class="char-count text-muted" style="display: block; margin-top: 5px; font-size: 12px;">' +
                           '<i class="fa fa-info-circle"></i> ' + remaining + ' characters remaining</small>';
        
        $(this).parent().append(charCountHtml);
        
        // Change color based on remaining characters
        if (remaining <= 10) {
            $('.char-count').removeClass('text-muted').addClass('text-warning');
        } else if (remaining <= 0) {
            $('.char-count').removeClass('text-muted text-warning').addClass('text-danger');
        } else {
            $('.char-count').removeClass('text-warning text-danger').addClass('text-muted');
        }
    });
    
    // Form validation
    $('#emergency_exit_form').submit(function(e) {
        // Prevent default form submission
        e.preventDefault();
        
        // Validate exit datetime
        if ($('#exit_datetime').val() == '') {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Please select exit date and time',
                timer: 3000,
                showConfirmButton: false
            });
            return false;
        }
        
        // Validate section
        if ($('#class_section_id').val() == '') {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Please select a section',
                timer: 3000,
                showConfirmButton: false
            });
            return false;
        }
        
        // Validate student
        if ($('#student_id').val() == '') {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Please select a student',
                timer: 3000,
                showConfirmButton: false
            });
            return false;
        }
        
        // Validate pickup by
        if ($('#pickup_by').val() == '') {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Please select who is picking up the student',
                timer: 3000,
                showConfirmButton: false
            });
            return false;
        }
        
        // Validate other name if Other is selected
        if ($('#pickup_by').val() == 'Other' && $('#pickup_other_name').val() == '') {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Please enter the name of the person picking up the student',
                timer: 3000,
                showConfirmButton: false
            });
            return false;
        }
        
        // Validate other name length if Other is selected
        if ($('#pickup_by').val() == 'Other' && $('#pickup_other_name').val().length > 50) {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Other name cannot exceed 50 characters',
                timer: 3000,
                showConfirmButton: false
            });
            return false;
        }
        
        // Validate photo if Other is selected
        if ($('#pickup_by').val() == 'Other') {
            const photoUrl = $('#uploaded_photo_url').val();
            console.log('Checking photo URL:', photoUrl); // Debug log
            
            if (!photoUrl) {
                e.preventDefault();
                Swal.fire({
                    icon: 'error',
                    title: 'Photo Required',
                    text: 'Please upload a photo of the person picking up the student',
                    timer: 3000,
                    showConfirmButton: false
                });
                return false;
            }
        }
        
        // Validate remarks
        if ($('#remarks').val() == '') {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Please enter remarks for this emergency exit',
                timer: 3000,
                showConfirmButton: false
            });
            return false;
        }
        
        // Add a hidden field with the datetime value
        var exitDateTime = $('#exit_datetime').val();
        
        
        // If all validations pass, submit the form
        this.submit();
    });
    
    // Function to load present students
    function loadPresentStudents(class_section_id) {
        // Extract just the date part from the datetime value
        var exitDate = $('#exit_datetime').val().split(' ')[0];
        
        $.ajax({
            url: "<?php echo site_url('attendance_day_v2/Attendance_day_v2/get_present_students'); ?>",
            type: "POST",
            data: {
                class_section_id: class_section_id,
                date: moment(exitDate, 'DD-MM-YYYY').format('YYYY-MM-DD')
            },
            dataType: "json",
            success: function(data) {
                var options = '<option value="">Select Student</option>';
                if (data.length > 0) {
                    $.each(data, function(index, student) {
                        options += '<option value="' + student.student_admission_id + '">' + student.student_name + '</option>';
                    });
                    $('#student_id').html(options).prop('disabled', false);
                } else {
                    $('#student_id').html('<option value="">No present students found</option>').prop('disabled', true);
                }
            },
            error: function(xhr, status, error) {
                console.error("Error loading students:", error);
                $('#student_id').html('<option value="">Error loading students</option>').prop('disabled', true);
            }
        });
    }

    // Handle photo upload
    function handlePhotoUpload(input) {
        
        if (input.files && input.files[0]) {
            const file = input.files[0];
            
            // Validate file type
            if (!file.type.match('image.*')) {
                Swal.fire({
                    icon: 'error',
                    title: 'Invalid File',
                    text: 'Please select an image file',
                    timer: 3000,
                    showConfirmButton: false
                });
                input.value = '';
                return;
            }
            
            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                Swal.fire({
                    icon: 'error',
                    title: 'File Too Large',
                    text: 'Please select an image smaller than 5MB',
                    timer: 3000,
                    showConfirmButton: false
                });
                input.value = '';
                return;
            }
            
            // Show preview immediately
            const reader = new FileReader();
            
            reader.onloadstart = function() {
                console.log('Starting to read file');
            };
            
            reader.onload = function(e) {
                const previewContainer = $('#photo_preview_container');
                const previewImage = $('#photo_preview');
                
                // Set the image source
                previewImage.attr('src', e.target.result);
                
                // Show the container and remove button
                previewContainer.show();
                $('.remove-photo').show();
                $('.custom-file-upload').html('<i class="fa fa-camera"></i> Change Photo');
            };
            
            reader.onerror = function(error) {
                console.error('Error reading file:', error);
            };
            
            // Start reading the file
            reader.readAsDataURL(file);
            
            // Upload to Wasabi
            uploadToWasabi(file);
        }
    }

    // Upload to Wasabi
    function uploadToWasabi(file) {
        return new Promise(function(resolve, reject) {
            try {
                $.ajax({
                    url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
                    type: 'post',
                    data: {'filename':file.name, 'file_type':file.type, 'folder':'emergency_exit'},
                    success: function(response) {
                        // Show progress bar
                        $('#upload_progress').show();
                        
                        response = JSON.parse(response);
                        var path = response.path;
                        var signedUrl = response.signedUrl;

                        $.ajax({
                            url: signedUrl,
                            type: 'PUT',
                            headers: {
                                "Content-Type": file.type, 
                                "x-amz-acl":"public-read" 
                            },
                            processData: false,
                            data: file,
                            xhr: function () {
                                var xhr = $.ajaxSettings.xhr();
                                xhr.upload.onprogress = function (e) {
                                    if (e.lengthComputable) {
                                        const percent = Math.round((e.loaded / e.total) * 100);
                                        $('#upload_progress_bar').css('width', percent + '%');
                                        $('#upload_percentage').text(percent + '%');
                                    }
                                };
                                return xhr;
                            },
                            success: function(response) {
                                $('#uploaded_photo_url').val(path);
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Upload Complete',
                                    text: 'Photo uploaded successfully',
                                    timer: 2000,
                                    showConfirmButton: false
                                });
                                resolve({path:path, name:file.name, type:file.type});
                            },
                            error: function(err) {
                                console.error('Upload error:', err);
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Upload Failed',
                                    text: 'Failed to upload photo. Please try again.',
                                    timer: 3000,
                                    showConfirmButton: false
                                });
                                reject(err);
                            },
                            complete: function() {
                                // Hide progress bar after a short delay
                                setTimeout(() => {
                                    $('#upload_progress').hide();
                                }, 1000);
                            }
                        });
                    },
                    error: function (err) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Upload Failed',
                            text: 'Failed to get upload URL. Please try again.',
                            timer: 3000,
                            showConfirmButton: false
                        });
                        reject(err);
                    }
                });
            } catch(err) {
                console.error('Error:', err);
                Swal.fire({
                    icon: 'error',
                    title: 'Upload Failed',
                    text: 'An unexpected error occurred. Please try again.',
                    timer: 3000,
                    showConfirmButton: false
                });
                reject(err);
            }
        });
    }

    // Remove photo
    function removePhoto() {
        $('#pickup_photo').val('');
        $('#photo_preview').attr('src', '');
        $('#photo_preview_container').hide();
        $('#uploaded_photo_url').val('');
        $('.remove-photo').hide();
        $('.custom-file-upload').html('<i class="fa fa-camera"></i> Choose Photo');
    }

    // Make functions globally available
    window.handlePhotoUpload = handlePhotoUpload;
    window.removePhoto = removePhoto;
});
</script>










