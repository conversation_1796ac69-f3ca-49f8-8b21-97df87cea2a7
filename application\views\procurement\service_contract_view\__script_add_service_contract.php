<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>


<script>
    $(document).ready(function() {
        $('#contract_date, #start_date, #end_date').datetimepicker({
            format: 'DD-MM-YYYY',
            useCurrent: false
        });
    });

    function save_basic_details(current, step_selector, step_number) {
        var $form = $('#'+step_selector+'-form');
        if ($form.parsley().validate()) {
            var isDatesValid= validateDateRange();
            if(isDatesValid) {
                
            $(current).prop('disabled', true).html('Please Wait...');
            var form = $('#'+step_selector+'-form')[0];
            var formData = new FormData(form);
            formData.append('step_number',step_selector);
            // formData.append('step_selector',step_selector);

            $.ajax({
                url: '<?php echo site_url('procurement/service_contract_controller/save_service_contract_steps'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    let p_data= JSON.parse(data);
                    if(Object.keys(p_data)?.length) {
                        if(p_data.status == '0') {
                            Swal.fire({
                                icon: 'error',
                                title: 'Something went wrong'
                            });
                        } else {
                            $(".service_contract_master_id").val(p_data.service_contract_master_id);
                            $(".basic_details_add_edit").val('Update');
                            $("#department").prop('readonly', true);
                            $("#contract_number").val(p_data.contract_number);
                            $("#step-1").addClass('hidden');
                            $("#step-2").removeClass('hidden');

                            $("div.circle-" +step_number).html(`<span class="fa fa-check"></span>`).css('background', 'green');
                            $("div.circle-" + (Number(step_number) + 1)).html(`o`).css('background', 'green');
                        }

                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Something went wrong'
                        });
                    }
                    $(current).prop('disabled', false).html('Save & Next');
                }
            });
        }
        }
    }

    function validateDateRange() {
        let start_date = $("#start_date").val();
        let end_date = $("#end_date").val();

        // Check if both dates are empty
        if (!start_date && !end_date) {
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: 'Both start date and end date are required!',
            });
            return false;
        }

        // Check if start date is empty
        if (!start_date) {
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: 'Start date is required!',
            });
            return false;
        }

        // Check if end date is empty
        if (!end_date) {
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: 'End date is required!',
            });
            return false;
        }

        var startDd = start_date.split('-');
        var endDd = end_date.split('-');
        startD= Number(startDd[2] + startDd[1] + startDd[0]);
        endD= Number(endDd[2] + endDd[1] + endDd[0]);
        if(startD >= endD) {
            Swal.fire({
                icon: 'error',
                title: 'Invalid Date Range',
                text: 'Start date must be before end date!',
            });
            return false;

        }
        // If all validations pass
        return true;
    }

     function onchange_vendor() {
        let vendor_id= $("#vendor_id").val();
        if(vendor_id) {
            $.ajax({
                url: '<?php echo site_url('procurement/service_contract_controller/get_vendor_details') ?>',
                type: 'post',
                data: {vendor_id},
                success: function(data) {
                    let p_data= JSON.parse(data);
                    let vendor= p_data.vendor;
                    let vendorCats= p_data.vendorCats;

                    if(Object.keys(vendor)?.length) {
                        $("#vendor_code").val(vendor.vendor_code);
                        $("#vendor_contact_person").val(`${vendor.contact_first_name} ${vendor.contact_last_name}`);
                        $("#vendor_email").val(vendor.vendor_email);
                        $("#vendor_phone").val(vendor.contact_number);
                        $("#vendor_gst_vat_no").val(vendor.gst_no);
                        $("#vendor_address").val(vendor.vendor_address && vendor.vendor_address != '' ? vendor.vendor_address : '-');
                    }

                    if(Object.keys(vendorCats)?.length) {
                        var options= `<option value="">Select...</option>`;
                        for(var v of vendorCats) {
                            // var style_and_functio= '';
                            // if(v.haveFinancialApprover == '0') {
                            //     style_and_functio= ` disabled style="color: lightgray; font-weight: bold; font-style: italic;" `;
                            // }
                            options += `<option value="${v.id}">${v.category_name}</option>`;
                        }
                        $("#proc_im_category_id").html(options);
                    } else {
                        $("#proc_im_category_id").html(`<option value="">Select...</option>`);
                    }
                },
                error: function(malware) {
                    console.log(malware);
                }
            });
        } else {
            $("#vendor_code").val('-');
            $("#vendor_contact_person").val('-');
            $("#vendor_email").val('-');
            $("#vendor_phone").val('-');
            $("#vendor_gst_vat_no").val('-');
            $("#vendor_address").val('-');
        }
     }

     function showReasonToNotSelect(category_name) {
        Swal.fire({
            icon: 'error',
            title: 'Oops...',
            text: `You cannot select ${category_name} as the vendor does not have financial approver`,
        });
     }

    function save_vendor_details(current, step_selector, step_number) {
        if($("#vendor_add_edit").val() == 'Update') {
               $("#step-2").addClass('hidden');
                $("#step-3").removeClass('hidden');
                $("div.circle-" +step_number).html(`<span class="fa fa-check"></span>`).css('background', 'green');
                $("div.circle-" + (Number(step_number) + 1)).html(`o`).css('background', 'green');
            return false;
        }

        var $form = $('#'+step_selector+'-form');
        if ($form.parsley().validate()) {
            $(current).prop('disabled', true).html('Please Wait...');
            var form = $('#'+step_selector+'-form')[0];
            var formData = new FormData(form);
            formData.append('step_number',step_selector);
            // formData.append('step_selector',step_selector);

            $.ajax({
                url: '<?php echo site_url('procurement/service_contract_controller/save_vendor_details'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    let p_data= JSON.parse(data);
                    if(Object.keys(p_data)?.length) {
                        if(p_data.status == '0') {
                            Swal.fire({
                                icon: 'error',
                                title: 'Something went wrong'
                            });
                        } else {
                            if(p_data.is_item_available == '1') {
                                __construct_service_items(p_data.vendorItems);
                                // __construct_service_items(p_data.expenseCAtegory);
                                // $("#add_contract_item_modal").modal('show');
                                $("select#vendor_id").prop('readonly', true).css({
                                    'pointer-events': 'none'
                                });
                                $("select#proc_im_category_id").prop('readonly', true).css({
                                    'pointer-events': 'none'
                                });
                            } else {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Successfully saved. No vendor-item-services found. If you want to proceed further, please go to the previous page by clicking Previous button and proceed with changing the vendor where at-least one item-services is available.',
                                    showConfirmButton: true,
                                    showCancelButton: true,
                                    confirmButtonText: 'Back to Change Vendor',
                                    cancelButtonText: 'Cancel'
                                }).then((result) => {
                                    if(result.isConfirmed) {
                                        $("#step-3").addClass('hidden');
                                        $("#step-2").removeClass('hidden');

                                        $("div.circle-" +step_number).html(`<span class="fa fa-check"></span>`).css('background', 'green');
                                        $("div.circle-" + (Number(step_number) + 1)).html(`o`).css('background', 'green');

                                    } else {
                                        // window.location.reload();
                                    }
                                });
                            }
                            $(".service_contract_master_id").val(p_data.service_contract_master_id);
                            $(".vendor_add_edit").val('Update');
                            $("#step-2").addClass('hidden');
                            $("#step-3").removeClass('hidden');

                            $("div.circle-" +step_number).html(`<span class="fa fa-check"></span>`).css('background', 'green');
                            $("div.circle-" + (Number(step_number) + 1)).html(`o`).css('background', 'green');

                        }

                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Something went wrong'
                        });
                    }
                    $(current).prop('disabled', false).html('Save & Next');
                }
            });
        }
    }

    function __construct_service_items(vendorItems) {
        let options= `<option value="">Select...</option>`;
        for(var v of vendorItems) { // <span class="fa fa-double-angle-right"></span>
            options += `<option value="${v.proc_im_items_id}">${v.category_name} - ${v.subcategory_name} - ${v.item_name}</option>`;
        }
        $("#item_id").html(options);
    }

    function show_add_modal() {
        $("#add_contract_item_modal").modal('show');
    }

    function go_previous_tab(current_tab_selector, previous_tab_selector) {
        $(".step-content").addClass('hidden');
        $("#" +previous_tab_selector).removeClass('hidden');
    }

    function add_contract_items(current, close_addClose) {
        let item_id= $("#item_id").val();

        $("#empty-state-3").hide();
        $("#service_items_div").show();
        

        // var $form = $('form#add_contract_item_div_form');
        // if ($form.parsley().validate()){
           
        let item_name= $("#item_id").find(':selected').text();
        let service_frequency= $("#service_frequency").val();
        let service_frequency_unit_cost= $("#service_frequency_unit_cost").val();
        let service_total_cost= $("#service_total_cost").val();
        let service_description= $("#service_description").val();

        let payment_mode= $("#payment_mode").val();
        let payment_mode_name= $("#payment_mode").find(':selected').text();
        let payment_frequency= $("#payment_frequency").val();
        let payment_frequency_name= $("#payment_frequency").find(':selected').text();
        let expense_category_id= $("#expense_category_id").val();
        let expense_category_id_name= $("#expense_category_id").find(':selected').text();
        let expense_sub_category_id= $("#expense_sub_category_id").val();
        let expense_sub_category_id_name= $("#expense_sub_category_id").find(':selected').text();
        let payment_rules_and_policy= $("#payment_rules_and_policy").val();

        if(!item_id || !service_frequency || !service_frequency_unit_cost || !expense_category_id || !expense_sub_category_id) {
            return Swal.fire({
                icon: 'error',
                title: 'Fill all the necessary fields'
            });
        }

        $("tr.tr_class_remover_item").remove();
        $("tbody#service_items_tbody").append(
            `<tr id="service_item_tr_id_${item_id}" class="">
                    <td>
                        ${item_name}
                        <input class="already-added-items" value="${item_id}" type="hidden" name="proc_im_items_ids[]">
                        <input class="already-added-items" value="${service_frequency}" type="hidden" name="service_frequency[]">
                        <input class="already-added-items" value="${service_frequency_unit_cost}" type="hidden" name="service_frequency_unit_cost[]">
                        <input class="already-added-items" value="${service_total_cost}" type="hidden" name="service_total_cost[]">
                        <input class="already-added-items" value="${service_description}" type="hidden" name="service_description[]">

                        <input class="already-added-items" value="${payment_mode}" type="hidden" name="payment_modes[]">
                        <input class="already-added-items" value="${payment_frequency}" type="hidden" name="payment_frequencys[]">
                        <input class="already-added-items" value="${expense_category_id}" type="hidden" name="expense_category_ids[]">
                        <input class="already-added-items" value="${expense_sub_category_id}" type="hidden" name="expense_sub_category_ids[]">
                        <input class="already-added-items" value="${payment_rules_and_policy}" type="hidden" name="payment_rules_and_policys[]">

                    </td>
                    <td>${service_frequency}</td>
                    <td>${service_frequency_unit_cost}</td>
                    <td>${service_total_cost}</td>
                    <td>${service_description}</td>

                    <td>${payment_mode_name}</td>
                    <td>${payment_frequency_name}</td>
                    <td>${expense_category_id_name}</td>
                    <td>${expense_sub_category_id_name}</td>
                    <td>${payment_rules_and_policy}</td>

                    <!-- 
                    <td>
                    
                    <span style="cursor: pointer;" class="fa fa-times text-danger text-bold already-added-items" onclick="remove_service_item('${item_id}')"></span>

                    </td>
                    -->
            </tr>
            `
        );
        if(close_addClose == 'close') {
            $("#add_contract_item_modal").modal('hide');
        }
        $("#item_id option[value='']").prop('selected', true);
        $("#service_frequency option[value='Monthly']").prop('selected', true);
        $("#service_frequency_unit_cost").val('0');
        $("#service_total_cost").val('0');
        $("#service_description").val('');
        // } else {
        //     Swal.fire({
        //         icon: 'error',
        //         title: 'Fill all the necessary fields'
        //     });
        // }
        $("form#add_contract_item_div_form").trigger('reset');
    }

    function remove_service_item(item_id) {
        $("#service_item_tr_id_" +item_id).remove();
        var trCounts= $("tbody#service_items_tbody").html();
        if(trCounts.trim() == '') {
            $("tbody#service_items_tbody").html(`<tr class="tr_class_remover_item">
                                                    <td colspan="11" class="text-center">Items not added</td>
                                                </tr>`);
        }
    }

    function save_service_items(current, step_selector, step_number) {

        

        var $form = $('#'+step_selector+'-form');
        if ($form.parsley().validate()) {
            $(current).prop('disabled', true).html('Please Wait...');
            var form = $('#'+step_selector+'-form')[0];
            var formData = new FormData(form);

            formData.append('step_number',step_selector);
            // formData.append('step_selector',step_selector);

            $.ajax({
                url: '<?php echo site_url('procurement/service_contract_controller/submit_service_contract_line_items'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    let p_data= JSON.parse(data);
                    if(Object.keys(p_data)?.length) {
                        if(p_data.status == '0') {
                            if($("input#isItemsAdded").val() == 'No') {
                                alert(4)
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Please add at-least one item-service to proceed further'
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Something went wrong'
                                });
                            }
                        } else {
                            // __construct_payment_inputs(p_data.serviceItems, p_data.budgets, p_data.expenseCategory);
                            __construct_approvers_details(p_data.approvers);

                            $("input#isItemsAdded").val('Yes');

                            $("input.already-added-items").remove();
                            $("span.already-added-items").after('Saved').remove();
                            
                            $(".service_contract_master_id").val(p_data.service_contract_master_id);
                            $("#step-3").addClass('hidden');
                            $("#step-5").removeClass('hidden');

                            $("div.circle-" +step_number).html(`<span class="fa fa-check"></span>`).css('background', 'green');
                            $("div.circle-" + (Number(step_number) + 2)).html(`o`).css('background', 'lightgray');

                        }

                    } else { 
                        if($("input#isItemsAdded").val() == 'No') {
                            Swal.fire({
                                icon: 'error',
                                title: 'Please add at-least one item-service to proceed further'
                            });
                        } else {
                            $("input.already-added-items").remove();
                            $("span.already-added-items").after('Saved').remove();
                            
                            $(".service_contract_master_id").val(p_data.service_contract_master_id);
                            $("#step-3").addClass('hidden');
                            $("#step-5").removeClass('hidden');

                            $("div.circle-" +step_number).html(`<span class="fa fa-check"></span>`).css('background', 'green');
                            $("div.circle-" + (Number(step_number) + 2)).html(`o`).css('background', 'lightgray');
                        }
                    }
                    $(current).prop('disabled', false).html('Save & Next');
                }
            });
        }
    }

    function __construct_payment_inputs(serviceItems, budgets, expenseCategory) {
        var budgetOptions= '<option value="">Select Budget....</option>';
        if(Object.keys(budgets)?.length) {
            for(var v of budgets) {
                budgetOptions += `<option value="${v.id}">${v.year}</option>`;
            }
        }

        var expenseCategories= '<option value="">Select Expense Category....</option>';
        if(Object.keys(expenseCategory)?.length) {
            for(var v of expenseCategory) {
                expenseCategories += `<option value="${v.id}">${v.category_name}</option>`;
            }
        }

        var html= '';
        var num= 1;
        for(var v of serviceItems) {
            html += `
            <div class="col-md-12">
                <div class="service-details" style="display: flex; justify-content: space-around; flex-wrap: wrap; padding: 10px 0; background: lightgray; margin-left: 12px; margin-right: 12px">
                    <span><b>Service: </b>${v.item_name}</span>
                    <span><b>Unit Cost: </b>₹${Number(v.frequency_unit_cost).toLocaleString()}/${v.service_frequency}</span>
                    <span><b>Total Cost: </b>₹${Number(v.total_cost).toLocaleString()}</span>
                    <input type="hidden" name="service_contract_line_item_id[]" value="${v.service_contract_line_item_id}">
                </div>
                <div class="payment-details-input">
                    <div class="form-group col-md-6">
                        <label for="payment_mode" class="">Payment Mode<font color="red">*</font>
                        </label>
                        <select name="payment_mode[]" id="payment_mode" class="form-control">
                                    <option value="Bank Transfer">Bank Transfer</option>
                                    <option value="Credit Card">Credit Card</option>
                                    <option value="Cheque">Cheque</option>
                        </select>
                    </div>
                    <div class="form-group col-md-6">
                        <label for="payment_frequency" class="">Payment Frequency<font color="red">*</font>
                        </label>
                        <select name="payment_frequency[]" id="payment_frequency_${num}" class="form-control">
                                    <option value="Monthly">Monthly</option>
                                    <option value="Quarterly">Quarterly</option>
                                    <option value="Yearly">Yearly</option>
                        </select>
                    </div>
                    <div class="form-group col-md-6">
                            <label for="frequency_payment">Unit Frequency Payment<font
                                            color="red">*</font>
                            </label>
                            <input type="number" class="form-control"
                                    id="frequency_payment_${num}"
                                    name="frequency_payment[]"
                                    value="0"
                                    onkeyup="calculate_total_payment(this, '${num}')"
                                    onchange="calculate_total_payment(this, '${num}')"
                                    >
                    </div>
                    <div class="form-group col-md-6">
                            <label for="total_payment">Total Payment<font
                                            color="red">*</font>
                            </label>
                            <input type="number" class="form-control"
                                    id="total_payment_${num ++}"
                                    name="total_payment[]"
                                    value="0"
                                    >
                    </div>
                    <div class="form-group col-md-6">
                        <label for="budget_year" class="">Budget Will Use<font color="red">*</font>
                        </label>
                        <select name="budget_year[]" id="budget_year" class="form-control">
                            ${budgetOptions}
                        </select>
                    </div>
                    <div class="form-group col-md-6">
                        <label for="expense_category_id" class="">Expense Category<font color="red">*</font>
                        </label>
                        <select name="expense_category_id[]" id="expense_category_id" class="form-control">
                            ${expenseCategories}
                        </select>
                    </div>
                    <div class="form-group col-md-12">
                        <label for="payment_rules_and_policy">Payment Rules<font
                                        color="red">*</font>
                        </label>
                        <textarea rows="4" class="form-control" name="payment_rules_and_policy[]" id="payment_rules_and_policy"></textarea>
                    </div>
                </div>
            </div>
            <div class="col-md-12" style="height: 15px;"></div>
            `;
        }
        $("#payment_details_div").html(html);
    }

    function save_payment_details(current, step_selector, step_number) {

        var $form = $('#'+step_selector+'-form');
        if ($form.parsley().validate()) {
            $(current).prop('disabled', true).html('Please Wait...');
            var form = $('#'+step_selector+'-form')[0];
            var formData = new FormData(form);
            formData.append('step_number',step_selector);
            // formData.append('step_selector',step_selector);

            $.ajax({
                url: '<?php echo site_url('procurement/service_contract_controller/save_payment_details'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    let p_data= JSON.parse(data);
                    if(Object.keys(p_data)?.length) {
                        if(p_data.status == '0') {
                            Swal.fire({
                                icon: 'error',
                                title: 'Something went wrong'
                            });
                        } else {
                            __construct_approvers_details(p_data.approvers);
                            
                            $(".service_contract_master_id").val(p_data.service_contract_master_id);
                            $("#step-4").addClass('hidden');
                            $("#step-5").removeClass('hidden');

                            $("div.circle-" +step_number).html(`<span class="fa fa-check"></span>`).css('background', 'green');
                            $("div.circle-" + (Number(step_number) + 1)).html(`o`).css('background', 'green');

                        }

                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Something went wrong'
                        });
                    }
                    $(current).prop('disabled', false).html('Save & Next');
                }
            });
        }
    }

    function __construct_approvers_details(approvers) {
        if(!Object.keys(approvers)?.length) {
            $("#approvers_div").html(`Approvers not added`);
            return;
        }
        var html= `<table class="table table-bordered">
                        <thead class="thead-dark">
                            <tr>
                                <th>Approver Type</th>
                                <th>Approver</th>
                                <th>Designation</th>
                                <th>Department</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>`;
        if(Object.keys(approvers)?.length) {
            for(var v of approvers) {
                html += `<tr>
                            <td>${v.approver_type}</td>
                            <td>${v.staff}</td>
                            <td>${v.department}</td>
                            <td>${v.designation}</td>
                            <td>Pending</td>
                        </tr>`;
            }
        }
        html += ` </tbody>
                </table>`;
        $("#approvers_div").html(html);
    }

    function save_approver_details(current, step_selector, step_number) {
        // $(".service_contract_master_id").val(p_data.service_contract_master_id);
        $("#step-5").addClass('hidden');
        $("#step-6").removeClass('hidden');

        $("div.circle-" +step_number).html(`<span class="fa fa-check"></span>`).css('background', 'green');
        $("div.circle-" + (Number(step_number) + 1)).html(`o`).css('background', 'green');

    }

    function show_additional_attachement_modal() {
        $("#add_additional_attachements_modal").modal('show');
    }

    async function add_additional_notes(current, close_addClose) {
        
        let additional_description_notes= $("#additional_description_notes").val();
        let service_contract_master_id= $(".service_contract_master_id").val();
        
        const fileInput = document.getElementById('additional_attachements');
        const file = fileInput.files[0];
        const fileInfoDiv = document.getElementById('fileInfo');

        // Check if the file is a PDF
        if (file.type !== 'application/pdf') {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Only PDF type files can be uploaded'
            });
            // Clear the file input
            fileInput.value = '';
            fileInput.value = '';
            fileName.textContent = 'No file selected';
            $("form#docs_form").trigger('reset');
            return false;
        }



        $("#attachments-list-empty").hide();
        $("#additional_attachements_div").show();
        $("tr.tr_class_remover").remove();
        $("tbody#additional_attachements_tbody").append(
            `<tr class="tr_class_remover" class="text-center">
                                    <td colspan="3" style="height: 60px; vertical-align: middle; width: 100%; border-right: none;">
                                        <div class="col-md-12" style="width: 100%;"></div>
                                            <div id="show_progress_color" style="height: 20px; background: lightgreen; width: 1%;"><div>
                                        </div>
                                    </td>

                                    <td style="width: 50px; border-left: none;">
                                        <div class="pull-right" id="show_progress_percentage" style="text-align: right; font-weight: bold; color: black; font-weight: bold; font-size: 24px;"><div>
                                    </td>
                            </tr>`
        );




        let fileName1= '';
        let fileSizeFormatted1= '';
        let size= 0;
        let splittedFileType= [];
        
        if (file) {
            const fileName = file.name;
            const fileSizeBytes = file.size;
            const fileExtentionType = file.type;
            size= fileSizeBytes;
            splittedFileType= fileExtentionType.split('/');
            let fileSizeFormatted;
            if (fileSizeBytes < 1024) {
                fileSizeFormatted = fileSizeBytes + ' bytes';
            } else if (fileSizeBytes < 1024 * 1024) {
                fileSizeFormatted = (fileSizeBytes / 1024).toFixed(2) + ' KB';
            } else {
                fileSizeFormatted = (fileSizeBytes / (1024 * 1024)).toFixed(2) + ' MB';
            }
            fileName1= fileName;
            fileSizeFormatted1= fileSizeFormatted;
        } else {
            return Swal.fire({
                icon: 'error',
                title: 'Fill all the necessary fields'
            });
        }

        const maxFileSize = 10 * 1024 * 1024; // 10MB
        if (size > maxFileSize) {
            Swal.fire({
                icon: 'error',
                title: 'File Too Large',
                text: 'File size should not exceed 10MB.',
                confirmButtonColor: '#3085d6',
                confirmButtonText: 'OK'
            });
            return false;
        }

        

        var form = $('#docs_form')[0];
        var formData = new FormData(form);
        formData.append('fileName', fileName1);
        formData.append('fileSizeBytes', size);
        formData.append('fileExtentionType', splittedFileType[1]);

        let relative_path_details= await upload_file_to_wasabi();
        let path= relative_path_details.path;

        formData.append('path', path);
        $.ajax({
            url: '<?php  echo site_url('procurement/service_contract_controller/add_document'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            cache : false,

            xhr: function() {
                var xhr = new window.XMLHttpRequest();
                var lastPercent = 0; // Track the last logged percentage
                var interval; // For simulated progress

                // $("#show_attachements_progress_modal").modal('show');
                xhr.upload.addEventListener("progress", function(evt) {
                    if (evt.lengthComputable) {
                        var currentPercent = Math.round((evt.loaded / evt.total) * 100);
                        
                        // Clear any previous interval to avoid duplicates
                        if (interval) clearInterval(interval);
                        
                        // Simulate smooth progress between real events
                        interval = setInterval(function() {
                            if (lastPercent < currentPercent) {
                                lastPercent++;
                                $("div#show_progress_percentage, div#show_progress_color_modal").html(lastPercent + '%');
                                $("div#show_progress_color, div#show_progress_percentage_modal").css('width', lastPercent +'%');
                            } else {
                                clearInterval(interval);
                            }
                        }, 50); // Adjust speed (ms) for smoother/faster increments
                    }
                }, false);

                // Ensure 100% is logged when upload completes
                xhr.addEventListener("load", function() {
                    clearInterval(interval); // Stop simulation
                    // $("#show_attachements_progress_modal").modal('hide');
                    if (lastPercent < 100) {
                        $("div#show_progress_percentage, div#show_progress_color_modal").html(100 + '%');
                        $("div#show_progress_color, div#show_progress_percentage_modal").css('width', 100 +'%');
                    }
                });

                return xhr;
            },

            success: function(data) {
                let p_data= JSON.parse(data);
                if(Object.keys(p_data)?.length) {
                    if(p_data.status == '1') {
                        var download_url= '<?php echo site_url('procurement/service_contract_controller/download_contract_attachement/'); ?>' + p_data.service_contract_attachements_id;

                        $("tbody#additional_attachements_tbody").append(
                            `<tr id="document_tr_id_${p_data.service_contract_attachements_id}">
                                    <td>
                                        ${fileName1}
                                    </td>
                                    <td>${fileSizeFormatted1}</td>
                                    <td>${additional_description_notes.trim() == '' ? '-' : additional_description_notes}</td>
                                    <td>
                                    <span style="cursor: pointer;" class="fa fa-times text-danger text-bold" onclick="remove_document('${p_data.service_contract_attachements_id}')"></span>
                                    <!-- 
                                        <a style="cursor: pointer;" class="fa fa-download text-warning text-bold pull-right" href="${download_url}"></a>
                                    -->
                                    <a style="cursor: pointer;" class="fa fa-download text-warning text-bold pull-right" href="${p_data.absolute_path}"></a>
                                    </td>
                            </tr>
                            `
                        );
                        $("tr.tr_class_remover").remove();
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Something went wrong'
                    });
                }
            }
        });


        if(close_addClose == 'close') {
            $("#add_additional_attachements_modal").modal('hide');
        }
        fileInput.value = '';
        fileName.textContent = 'No file selected';
        $("form#docs_form").trigger('reset');
    }

    function upload_file_to_wasabi() {
        return new Promise(function(resolve, reject) {
            try {

                const fileInput = document.getElementById('additional_attachements');
                const file = fileInput.files[0]; // Get the first selected file
                
                if (!file) {
                    return Swal.fire({
                        icon: 'error',
                        title: 'No file selected',
                        text: 'Please select a file to upload.'
                    });
                }

                // Get file details
                const fileName = file.name;
                const fileType = file.type;
                const fileSize = file.size; // in bytes

// const maxFileSize = 10 * 1024 * 1024; // 10MB

                $.ajax({
                    url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
                    type: 'post',
                    data: {'filename':file.name, 'file_type':file.type, 'folder':'service_conttract_documents'},
                    success: function(response) {
                        // Show progress bar
                        $('#upload_progress').show();
                        
                        response = JSON.parse(response);
                        var path = response.path;
                        var signedUrl = response.signedUrl;

                        $.ajax({
                            url: signedUrl,
                            type: 'PUT',
                            headers: {
                                "Content-Type": file.type, 
                                "x-amz-acl":"public-read" 
                            },
                            processData: false,
                            data: file,
                            xhr: function () {
                                var xhr = $.ajaxSettings.xhr();
                                xhr.upload.onprogress = function (e) {
                                    if (e.lengthComputable) {
                                        const percent = Math.round((e.loaded / e.total) * 100);
                                        $('#upload_progress_bar').css('width', percent + '%');
                                        $('#upload_percentage').text(percent + '%');
                                    }
                                };
                                return xhr;
                            },
                            success: function(response) {
                                $('#uploaded_photo_url').val(path);
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Upload Complete',
                                    text: 'Photo uploaded successfully',
                                    timer: 2000,
                                    showConfirmButton: false
                                });
                                resolve({path:path, name:file.name, type:file.type});
                            },
                            error: function(err) {
                                console.error('Upload error:', err);
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Upload Failed',
                                    text: 'Failed to upload photo. Please try again.',
                                    timer: 3000,
                                    showConfirmButton: false
                                });
                                reject(err);
                            },
                            complete: function() {
                                // Hide progress bar after a short delay
                                setTimeout(() => {
                                    $('#upload_progress').hide();
                                }, 1000);
                            }
                        });
                    },
                    error: function (err) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Upload Failed',
                            text: 'Failed to get upload URL. Please try again.',
                            timer: 3000,
                            showConfirmButton: false
                        });
                        reject(err);
                    }
                });
            } catch(err) {
                console.error('Error:', err);
                Swal.fire({
                    icon: 'error',
                    title: 'Upload Failed',
                    text: 'An unexpected error occurred. Please try again.',
                    timer: 3000,
                    showConfirmButton: false
                });
                reject(err);
            }
        });
    }

    function remove_document(service_contract_attachements_id) {
        $.ajax({
            url: '<?php echo site_url('procurement/service_contract_controller/remove_document'); ?>',
            type: 'post',
            data: {service_contract_attachements_id},
            success: function(data) { 
                $("#document_tr_id_"+service_contract_attachements_id).remove();
                var trCounts= $("tbody#additional_attachements_tbody").html();
                if(trCounts.trim() == '') {
                    $("tbody#additional_attachements_tbody").html(`<tr class="tr_class_remover">
                                                                            <td colspan="4" class="text-center">
                                                                            No documents found
                                                                            </td>
                                                                    </tr>`);
                }
            }
        });
       
    }

    function reach_at_prev_tab(current, previous_tab_selector) {
        $(".step-content").addClass('hidden');
        $("#" +previous_tab_selector).removeClass('hidden');
    }

    function save_and_close(current, step_selector) {
        let service_contract_master_id= $(".service_contract_master_id").val();

        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this! But you can edit it later.",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, save it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $(current).prop('disabled', true).html('Please Wait...');
                submit_service_contract(service_contract_master_id);
            } else {
                $(current).prop('disabled', false).html('Save & Close');
                Swal.fire({
                    icon: 'warning',
                    title: 'Cancelled',
                    text: 'Your service contract is not saved!'
                });
            }
        });
    }

    function submit_service_contract(service_contract_master_id) {
        $.ajax({
            url: '<?php echo site_url('procurement/service_contract_controller/submit_service_contract'); ?>',
            type: 'post',
            data: {service_contract_master_id},
            success: function(data) {
                let p_data= JSON.parse(data);
                if(Object.keys(p_data)?.length) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Successfully created service contract'
                    }).then((result) => {
                        window.location.href= '<?php echo site_url('procurement/service_contract_controller/service_contracts'); ?>';;
                    });

                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Something went wrong'
                    });
                }
            }
        });
    }

    function calculate_total_cost(current) {
        let service_frequency= $("#service_frequency").val();
        if(service_frequency == 'Ad-hoc') {
            $("#service_total_cost").prop('readonly', false);
            $("#service_total_cost").val(0);
            return ;
            // Swal.fire({
            //     icon: 'error',
            //     title: 'Enter the total cost manually, because Ad-hoc is selected as frequency'
            // });
        }
        setTimeout(() => {
            let service_frequency_unit_cost= $("#service_frequency_unit_cost").val();
            let start_date= $("#start_date").val();
            let end_date= $("#end_date").val();

            console.log(service_frequency, service_frequency_unit_cost, start_date, end_date);

            try {
                const total = calculateTotalCost(
                    service_frequency, // service_frequency
                    service_frequency_unit_cost,        // frequency_unit_cost (INR)
                    start_date, // start_date
                    end_date // end_date
                );
                $("#service_total_cost").val(total);
            } catch (error) {
                $("#service_total_cost").val(0);
                // Swal.fire({
                //     icon: 'error',
                //     title: 'Something went wrong with total calculation, please enter it manually'
                // });
                $("#service_total_cost").prop('readonly', false);

            }
        }, 1000);
    }

    function calculate_total_payment(current, numb) {
        setTimeout(() => {
            let payment_frequency= $("#payment_frequency_" +numb).val();
            let frequency_payment= $("#frequency_payment_" +numb).val();
            let start_date= $("#start_date").val();
            let end_date= $("#end_date").val();

            try {
                const total = calculateTotalCost(
                    payment_frequency, // service_frequency
                    frequency_payment,        // frequency_unit_cost (INR)
                    start_date, // start_date
                    end_date // end_date
                );
                $("#total_payment_" +numb).val(total);
            } catch (error) {
                $("#total_payment_" +numb).val(0);
                // Swal.fire({
                //     icon: 'error',
                //     title: 'Something went wrong with total payment calculation, please enter it manually'
                // });
                $("#total_payment_" +numb).prop('readonly', false);

            }
        }, 1000);
    }

    function onchangeExpenseCategory() {
        let expense_category_id= $("#expense_category_id").val();
        if(expense_category_id) {
            $.ajax({
                url: '<?php echo site_url('procurement/service_contract_controller/getExpenseSubCategory') ?>',
                type: 'post',
                data: {expense_category_id},
                success: function(data) {
                    let p_data= JSON.parse(data);
                    let options= '<option value="">Select Subcategory....</option>';
                    if(Object.keys(p_data)?.length) {
                        for(var v of p_data) {
                            options += `<option value="${v.id}">${v.sub_category}</option>`;
                        }
                    }
                    $("#expense_sub_category_id").html(options);
                },
                error: function(malware) {
                    console.log(malware);
                }
            });
        }
    }


</script>


<!-- Scripts for total calculation -->
 <script>
    function calculateTotalCost(service_frequency, frequency_unit_cost, start_date, end_date) {
        // Convert date strings to Date objects
        const startDate = new Date(parseDateString(start_date));
        const endDate = new Date(parseDateString(end_date));
        
        // Validate dates
        if (isNaN(startDate.getTime())) throw new Error("Invalid start date");
        if (isNaN(endDate.getTime())) throw new Error("Invalid end date");
        if (startDate > endDate) throw new Error("Start date must be before end date");
        
        // Calculate total months between dates
        const totalMonths = monthDiff(startDate, endDate);
        
        let totalCost = 0;
        
        switch(service_frequency) {
            case 'Monthly':
                // For monthly, charge for each month in the range
                totalCost = totalMonths * frequency_unit_cost;
                break;
                
            case 'Quarterly':
                // For quarterly, charge for each full or partial quarter
                const quarters = Math.ceil(totalMonths / 3);
                totalCost = quarters * frequency_unit_cost;
                break;
                
            case 'Yearly':
                // For yearly, charge for each full or partial year
                const years = Math.ceil(totalMonths / 12);
                totalCost = years * frequency_unit_cost;
                break;
                
            default:
                throw new Error("Invalid service frequency");
        }
        
        return parseFloat(totalCost.toFixed(2)); // Return cost rounded to 2 decimal places
    }

    // Helper function to calculate months between two dates
    function monthDiff(startDate, endDate) {
        let months;
        months = (endDate.getFullYear() - startDate.getFullYear()) * 12;
        months -= startDate.getMonth();
        months += endDate.getMonth();
        
        // Add one month if there are additional days beyond full months
        if (endDate.getDate() > startDate.getDate()) {
            months += 1;
        }
        
        return months <= 0 ? 1 : months; // At least 1 month
    }

    // Helper function to parse dd-mm-yyyy date format
    function parseDateString(dateString) {
        const parts = dateString.split('-');
        if (parts.length !== 3) throw new Error("Date must be in dd-mm-yyyy format");
        
        const day = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10) - 1; // Months are 0-based in JS
        const year = parseInt(parts[2], 10);
        
        return new Date(year, month, day);
    }

    function reach_at_next_tab(current, current_number, next_number) {
        $("#step-" +current_number).addClass('hidden');
        $("#step-" +next_number).removeClass('hidden');
    }
 </script>



<!-- Document Drag & drop -->
<script>
    // Get elements
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('additional_attachements');
    const fileName = document.getElementById('fileName');

    // Handle file selection via browse
    fileInput.addEventListener('change', function(e) {
        if (this.files.length) {
            fileName.textContent = this.files[0].name;
        }
    });

    // Handle drag and drop
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });

    // dropZone.addEventListener('dragleave', function(e) {
    //     e.preventDefault();
    //     this.classList.remove('dragover');
    // });

    // dropZone.addEventListener('drop', function(e) {
    //     e.preventDefault();
    //     this.classList.remove('dragover');
        
    //     if (e.dataTransfer.files.length) {
    //         fileInput.files = e.dataTransfer.files;
    //         fileName.textContent = e.dataTransfer.files[0].name;
    //     }
    // });

    // // Click on the box to trigger file input
    // dropZone.addEventListener('click', function() {
    //     fileInput.click();
    // });
</script>


        
   

