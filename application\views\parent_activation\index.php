<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('SchoolAdmin_menu');?>">User Management</a></li>
    <li>Provision Parent Credentials</li>
</ul>

<div class="col-md-12 col_new_padding ">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <h3 class="card-title panel_title_new_style_staff">
                    <a class="back_anchor" href="<?php echo site_url('SchoolAdmin_menu') ?>" class="control-primary">
                        <span class="fa fa-arrow-left"></span>
                    </a>
                    Provision Parent Credentials
                </h3>
            </div>
        </div>
        <div class="card-body pt-1">
            <form id="selectStd" enctype="multipart/form-data" id="user_credentials_students" class="form-horizontal">
                <div class="form-group col-md-2">
                    <label for="class" class="control-label">Class </label>
                    <div>
                        <select id="classSectionId" name="classSectionId" required class="form-control input-md">
                            <?php
                        $selected = '';
                        if($classSectionId == -1)
                            $selected = ' selected';
                            echo '<option value="0">Select Class</option>';
                            echo '<option value="-1" '.$selected.'>All classes</option>';  //-1 for all classes
                        foreach ($classSectionList as $cl) {
                            $option = '<option value="' . $cl->id.'_'.$cl->class_id . '"';
                            if($cl->id.'_'.$cl->class_id==$classSectionId){
                                $option.='selected';
                            }
                            $option .= '>' . $cl->class_name . $cl->section_name.'</option>';
                            echo $option;
                        }
                        ?>
                        </select>
                    </div>
                </div>
                <div class="form-group col-md-3">
                    <label for="class" class="control-label">Student Name</label>
                    <input type="text" class="form-control" name="student_name" id="student_name"
                        value="<?php echo $student_name ?>" placeholder="Student Name">
                </div>
                <div class="form-group col-md-3 pt-2">
                    <input type="button" value="Get" onclick="get_credentials_student_data()" style="width:150px;"
                        class="btn btn-primary mt-4" />
                </div>
            </form>
        </div>
        <div class="card-body pt-0">
            <div class="col-12 text-center loading-icon" style="display: none;">
                <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
            </div>
            <div class="text-center">
                <div style="display: none;" class="progress" id="progress">
                    <div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated"
                        role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%">
                    </div>
                </div>
            </div>
            <div class="stdudentData tableFixHead">
                <div class="no-data-display">Select filter to get Student list</div>
            </div>
        </div>
    </div>
</div>

<style>
.tableFixHead {
    overflow-y: auto;
    /* make the table scrollable if height is more than 200 px  */
    height: 35rem;
    /* gives an initial height of 200px to the table */
}

.tableFixHead thead th {
    position: sticky;
    /* make the table heads sticky */
    top: -2px;
    /* table head will be placed from the top of the table and sticks to it */
}

table {
    border-collapse: collapse;
    /* make the table borders collapse to each other */
    width: 100%;
}

th,
td {
    padding: 8px 16px;
    border: 1px solid #ccc;
}

th {
    background: #eee;
}
</style>


<style type="text/css">
.btn-danger {
    border-radius: 0.2rem;
}

.medium {
    width: 40%;
    margin: auto;
}
</style>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript">
var total_students = 0;
var completed = 0;

function get_credentials_student_data() {
    $('.stdudentData').html('');
    total_students = 0;
    completed = 0;
    var classSectionId = $('#classSectionId').val();
    if (classSectionId == 0) {
        alert('Select Class');
        return false;
    }
    var student_name = $('#student_name').val();
    $.ajax({
        url: '<?php echo site_url('parent_activation/get_credentials_student_ids_list_by_filter'); ?>',
        type: 'post',
        data: {
            'classSectionId': classSectionId,
            'student_name': student_name
        },
        success: function(data) {
            $('.loading-icon').hide();
            var data = JSON.parse(data);
            if (data.length <= 0) {
                $('.stdudentData').html('<div class="no-data-display">No Data Found</div>');
                return false;
            }
            var students = data;
            studentIds = students;
            total_students = parseInt(100 * (studentIds.length - 2)) + parseInt(studentIds[studentIds
                .length - 1].length);
            var progress = document.getElementById('progress-ind');
            progress.style.width = (completed / total_students) * 100 + '%';
            $("#progress").show();
            report_index(0);
        }
    });
}

function report_index(index) {
    if (index < studentIds.length) {
        get_fee_report(index);
    } else {
        $("#progress").hide();
    }
}

function get_fee_report(index) {
    // console.log(index);
    var student_ids = studentIds[index];
    $.ajax({
        url: '<?php echo site_url('parent_activation/get_credentials_student_data'); ?>',
        type: 'post',
        data: {
            'student_ids': student_ids
        },
        success: function(data) {
            var jsonData = JSON.parse(data);
            var stdData = jsonData;
            if (index == 0) {
                constructFeeHeader(stdData);
            }
            completed += Object.keys(stdData).length;
            var progress = document.getElementById('progress-ind');
            progress.style.width = (completed / total_students) * 100 + '%';

            prepare_student_table(stdData, index);

        }
    });
}

function constructFeeHeader(stdData) {
    var html = `<table class="table table-bordered" id="student_provision">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Student Name</th>
                    <th>Relation</th>
                    <th>Parent Name</th>
                    <th>Mobile Number</th>
                    <th>Email</th>
                    <th style="width:10%">
                        <select class="form-control" onchange="send_method_type(this.value)" name="send_method_type">
                            <option value="sms">SMS</option>
                            <option value="email">Email</option>
                        </select>
                    </th>
                    <th>Status</th>
                    <th>
                        <input type="button" onclick="send_provision_credentials()" id="sendButton" disabled="true"  class="btn btn-primary" value="Send">
                        <br><br>Send <input type="checkbox" name="selectAll" value="send" onclick="check_all(this, value)" id="sendAll" class="check">
                    </th>
                    <th>
                        <input type="button" onclick="re_send_provision_credentials()" id="re-sendButton" disabled="true"  class="btn btn-primary" value="Re-Send">
                        <br><br>Re-send <input type="checkbox" name="selectAll" value="re_send" onclick="check_all(this, value)" id="re_sendAll" class="check">
                    </th>
                    <th>
                        <input type="button" onclick="deactivate_provision_credentials()" id="deactivateButton" disabled="true"  class="btn btn-primary" value="Deactivate">
                        <br><br>Deactivate <input type="checkbox" name="selectAll" value="deactivate" onclick="check_all(this, value)" id="deactivateAll" class="check">
                    </th>
                    <th>Reset password</th>
                </tr>
                
            </thead>
            </tbody>
        </table>`;

    $('.stdudentData').html(html);
}

var new_index = 0;

function prepare_student_table(stdData, index) {
    var srNo = index * new_index;
    var m = 0;
    var html = '';

    html += '<tbody>';
    for (var k in stdData) {
        var activeDisabled = 0;
        if (stdData[k].Active == 1) {
            activeDisabled = 1;
        }

        html += '<tr>';
        html += '<td>' + (m + 1 + srNo) + '</td>';
        html += '<td>' + stdData[k].studentName + '</td>';
        html += '<td>' + stdData[k].relation_type + '</td>';
        html += '<td>' + stdData[k].parentName + '</td>';
        html += '<td>' + stdData[k].mobile + '</td>';
        html += '<td id="parent-email' + stdData[k].pid + '" >' + stdData[k].email + '</td>';

        // Display select box for choosing SMS or Email
        // if (stdData[k].oldUid != null && stdData[k].userId != stdData[k].oldUid) {
        //     html += '<td>Sibling Connected</td>';
        // } else {
        //     html += '<td><select class="form-control method_type_dropdown" name="select_send_options" id="method_type_indiv_'+stdData[k].pid+'"><option value="sms">SMS</option><option value="email">Email</option></select></td>';
        // }

        if (stdData[k].oldUid != null && stdData[k].userId != stdData[k].oldUid) {
            html += '<td>';
            for (var j = 0; j < stdData[k].siblings.length; j++) {
                html += `<p>${stdData[k].siblings[j].stdName}  (${stdData[k].siblings[j].aSection})</p>`;
            }
            html += '</td>'
        } else {
            html +=
                '<td><select class="form-control method_type_dropdown" name="select_send_options" id="method_type_indiv_' +
                stdData[k].pid + '"><option value="sms">SMS</option><option value="email">Email</option></select></td>';
        }

        //Display select box for choosing SMS or Email
        if (stdData[k].oldUid != null && stdData[k].userId != stdData[k].oldUid) {
            loggedStatus = 'Sibling Connected';
        } else {
            if (stdData[k].loggedin_atleast_once == 1) {
                loggedStatus = '<button type="button" class="btn btn-info btn-xs">Logged-in</button>';
            } else if (stdData[k].Active == 1) {
                loggedStatus = '<button type="button" class="btn btn-warning btn-xs">Activated</button> ';
            } else {
                loggedStatus = '<button type="button" class="btn btn-danger btn-xs">Not activated</button>';
            }
        }
        html += '<td>' + loggedStatus + '</td>';

        //Display Send button
        if (stdData[k].oldUid != null && stdData[k].userId != stdData[k].oldUid) {
            html += '<td>Sibling connected</td>';
        } else {
            if (activeDisabled) {
                html += (stdData[k].loggedin_atleast_once == 1) ? '<td><font color="green">Logged-in</font></td>' :
                    '<td><font color="orange">Activated</font></td>';
            } else {
                html +=
                    `<td><input type="checkbox" onclick="check_smsIndividual()" name="send_credentials" value="${stdData[k].pid}" class="sendCheck"></td>`;
            }
        }

        //Display Resend button
        if (stdData[k].oldUid != null && stdData[k].userId != stdData[k].oldUid) {
            html += '<td>Sibling connected</td>';
        } else {
            if (activeDisabled) {
                if (stdData[k].loggedin_atleast_once == 1) {
                    html += '<td><font color="green">Logged-in</font></td>';
                } else {
                    html +=
                        `<td><input type="checkbox" onclick="check_reSMSIndividual()" name="re_send_credentials" value="${stdData[k].pid}" class="re_sendCheck"></td>`;
                }
            } else {
                html += '<td>Not Activated</td>';
            }
        }

        //Display Deactivate button
        if (stdData[k].oldUid != null && stdData[k].userId != stdData[k].oldUid) {
            html += `<td>Sibling connected</td>`;
        } else if(activeDisabled){
            html +=
                `<td><input type="checkbox" onclick="check_deactivateIndividual()" name="deactivate_credentials" value="${stdData[k].userId}" class="deactivate_sendCheck"></td>`;
        } else {
            html += '<td>Not Activated</td>';
        }

        //Display Reset Password button
        if (stdData[k].oldUid != null && stdData[k].userId != stdData[k].oldUid) {
            html += `<td>Sibling connected</td>`;
        } else {
            if (activeDisabled) {
                html +=
                    `<td><input type="button" onclick="reset_password('${stdData[k].userId}','${stdData[k].username}')" id="reset_password" value="Reset to default" class="btn btn-primary"/></td>`;
            } else {
                html += '<td>Not Activated</td>';
            }
        }
        html += '</tr>';

        m++;
    }
    $('#student_provision').append(html);
    index++;
    new_index = m;
    report_index(index);
}

function reset_password(userId, username) {
    Swal.fire({
        title: 'Reset to Default Password?',
        html: `<b>User name:</b> ${username}<br><b>Password:</b> welcome123`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes',
        cancelButtonText: 'No',
        confirmButtonColor: '#28a745', // Green
        cancelButtonColor: '#dc3545',  // Red
        customClass: {
            popup: 'medium'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo site_url('parent_activation/reset_default_password_user_id'); ?>',
                type: 'post',
                data: {
                    userId: userId
                },
                success: function(data) {
                    if (data) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Password Reset',
                            text: 'Password reset successfully.',
                            confirmButtonColor: '#28a745'
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Reset Failed',
                            text: 'Failed to reset password.',
                            confirmButtonColor: '#dc3545'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'Server Error',
                        text: 'Something went wrong while resetting.',
                        confirmButtonColor: '#dc3545'
                    });
                }
            });
        }
    });
}

function check_all(check, value) {
    if (value == 'send') {
        isCheckedBySend(check)
    }
    if (value == 're_send') {
        isCheckedByReSend(check)
    }

    if (value == 'deactivate') {
        isCheckedByDeactivate(check)
    }
}

function isCheckedBySend(check) {
    if ($(check).is(':checked')) {
        $('.sendCheck').prop('checked', true);
        $('#sendButton').prop('disabled', false);
        $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop({
            "disabled": "true",
            "checked": ""
        });
    } else {
        $('.sendCheck').prop('checked', false);
        $('#sendButton').prop('disabled', true);
        $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled', false);
    }
}

function isCheckedByReSend(check) {
    if ($(check).is(':checked')) {
        $('.re_sendCheck').prop('checked', true);
        $('#re-sendButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll').prop({
            "disabled": "true",
            "checked": ""
        });
    } else {
        $('.re_sendCheck').prop('checked', false);
        $('#re-sendButton').prop('disabled', true);
        $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled', false);
    }
}

function isCheckedByDeactivate(check) {
    if ($(check).is(':checked')) {
        $('.deactivate_sendCheck').prop('checked', true);
        $('#deactivateButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll').prop({
            "disabled": "true",
            "checked": ""
        });
    } else {
        $('.deactivate_sendCheck').prop('checked', false);
        $('#deactivateButton').prop('disabled', true);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll').prop('disabled', false);
    }
}

function send_method_type(value) {
    if (value == 'sms') {
        $('.method_type_dropdown').find("option[value='sms']").attr('selected', 'selected');
        $('.method_type_dropdown').find("option[value='email']").removeAttr('selected', 'selected');
    } else {
        $('.method_type_dropdown').find("option[value='email']").attr('selected', 'selected');
        $('.method_type_dropdown').find("option[value='sms']").removeAttr('selected', 'selected');
    }
}

function check_smsIndividual() {
    if ($("input[class='sendCheck']:checked").length > 0) {
        $('#sendButton').prop('disabled', false);
        $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop({
            "disabled": "true",
            "checked": ""
        });
    } else {
        $('.sendCheck').prop('checked', false);
        $('#sendButton').prop('disabled', true);
        $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled', false);
    }
}

function check_reSMSIndividual() {
    if ($("input[class='re_sendCheck']:checked").length > 0) {
        $('#re-sendButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll').prop({
            "disabled": "true",
            "checked": ""
        });
    } else {
        $('.re_sendCheck').prop('checked', false);
        $('#re-sendButton').prop('disabled', true);
        $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled', false);
    }
}

function check_deactivateIndividual() {
    if ($("input[class='deactivate_sendCheck']:checked").length > 0) {
        $('#deactivateButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll').prop({
            "disabled": "true",
            "checked": ""
        });
    } else {
        $('.deactivate_sendCheck').prop('checked', false);
        $('#deactivateButton').prop('disabled', true);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll').prop('disabled', false);
    }
}

function send_provision_credentials() {
    var pids = [];
    var smsType = [];
    $('.sendCheck:checked').each(function() {
        var methodType = $('#method_type_indiv_' + $(this).val()).val();
        if (methodType == 'sms') {
            pids.push($(this).val() + '_' + $('#method_type_indiv_' + $(this).val()).val());
        }
        if (methodType == 'email' && $('#parent-email' + $(this).val()).html() != '') {
            pids.push($(this).val() + '_' + $('#method_type_indiv_' + $(this).val()).val());
        }
    });
    if (pids.length <= 0) {
        return false;
    }
    $('#summary').modal('show');
    sendProvisionLink(pids, 'activation');
    $("#process").val('activation');
    $("#smsType").val('send');
}

function re_send_provision_credentials() {
    var pids = [];
    var smsType = [];
    $('.re_sendCheck:checked').each(function() {
        var methodType = $('#method_type_indiv_' + $(this).val()).val();
        if (methodType == 'sms') {
            pids.push($(this).val() + '_' + $('#method_type_indiv_' + $(this).val()).val());
        }
        if (methodType == 'email' && $('#parent-email' + $(this).val()).html() != '') {
            pids.push($(this).val() + '_' + $('#method_type_indiv_' + $(this).val()).val());
        }
    });
    if (pids.length <= 0) {
        return false;
    }
    $('#summary').modal('show');
    sendProvisionLink(pids, 'activation');
    $("#process").val('activation');
    $("#smsType").val('send');
}

function sendProvisionLink(pids, provision = 'activation', alreadyProv = 0) {
    $('#modal-loader').show();
    if (pids.length > 0) {
        $('#dynamic-content').html(''); // blank before load.
        $("#warnMsg").html('');
        $.ajax({
                url: '<?php echo site_url('parent_activation/getPreview_credentials'); ?>',
                type: 'POST',
                data: {
                    'pids': pids,
                    'process': provision
                },
                dataType: 'html'
            })
            .done(function(data) {

                var data = $.parseJSON(data);
                var previewData = data.preview;
                // console.log(previewData);
                var credits_available = data.credits_available;
                //$('#studentDetails').text('Student Details: ' + previewData);
                var html = '';
                $('#dynamic-content').html('Loading data...');
                if (previewData.length == 0) {
                    html += '<h4>No data</h4>';
                } else {
                    html +=
                        '<thead><tr><th>#</th><th style="width:30%;">Name</th><th>Number</th><th>Message</th></tr></thead><tbody>';
                    for (var i = 0; i < previewData.length; i++) {
                        var escapedMessage = previewData[i].message.replace(/"/g, '&quot;');
                        html += '<tr>';
                        html += '<td>' + (i + 1) + '</td>';
                        html += '<td>' + previewData[i].name + '</td>';
                        html += '<td>' + previewData[i].message_by + '</td>';
                        html += '<td>' + previewData[i].message + '</td>';
                        html += '</tr>';
                        $("#send_provision_credentials").append(`
                            <input type="hidden" name="codes[${previewData[i].pid}]" value="${previewData[i].code}">
                            <input type="hidden" name="messages[${previewData[i].std_id}_${previewData[i].pid}_${previewData[i].relation_type}_${previewData[i].user_id}_${previewData[i].send_type}]" value="${escapedMessage}">
                        `);
                    }
                    html += '</tbody></table>';
                }
                $('#dynamic-content').html(html); // load here
                // $('#dynamic-content').html(previewData); // load here
                if (alreadyProv > 0) {
                    $("#warnMsg").html(alreadyProv + ' already provisioned numbers are not taken.');
                }
                $('#modal-loader').hide(); // hide loader  
                if (credits_available == 1) {
                    //enable confirm button only if credits available
                    $("#confirmBtn").attr('disabled', false);
                    $("#credit-err").hide();
                } else {
                    $("#confirmBtn").attr('disabled', true);
                    $("#credit-err").show();
                }
            })
            .fail(function() {
                $('#dynamic-content').html(
                    '<i class="glyphicon glyphicon-info-sign"></i> Something went wrong, Please try again...');
                $('#modal-loader').hide();
            });
    } else {
        $("#warnMsg").html('');
        if (alreadyProv > 0) {
            $("#warnMsg").html(alreadyProv + ' already provisioned member(s) is/are not shown here.');
        }
        $("#confirmBtn").hide();
        $('#dynamic-content').html('<i class="glyphicon glyphicon-info-sign"></i> Please select students...');
    }
}

function deactivate_provision_credentials() {
    var userIds = [];
    $('.deactivate_sendCheck:checked').each(function() {
        userIds.push($(this).val());
    });
    if (userIds.length <= 0) {
        Swal.fire({
            icon: 'info',
            title: 'No Users Selected',
            text: 'Please select at least one user to deactivate.'
        });
        return false;
    }

    Swal.fire({
        title: 'Deactivating Users',
        html: `You are deactivating <b>${userIds.length}</b> user(s). Are you sure?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes',
        cancelButtonText: 'No',
        confirmButtonColor: '#28a745', // Green
        cancelButtonColor: '#dc3545',  // Red
        customClass: {
            popup: 'medium'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo site_url('parent_activation/deactivate_provision_credentials_by_user_id'); ?>',
                type: 'POST',
                data: {
                    'userIds': userIds
                }
            })
            .done(function(data) {
                if (data) {
                    get_credentials_student_data();
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Something went wrong while deactivating users.'
                    });
                }
            })
            .fail(function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to send request to the server.'
                });
            });
        }
    });
}
</script>

<form enctype="multipart/form-data" method="post" id="send_provision_credentials" action="" class="form-horizontal">
    <div id="summary" class="modal fade" role="dialog">
        <div class="modal-dialog" style="width:80%;margin: auto;">

            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Check the numbers and confirm</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div id="numberBody" class="modal-body table-responsive" style="overflow-y:auto;height:500px;">
                    <div>
                        <h4 id="warnMsg"></h4>
                    </div>
                    <div id="modal-loader" style="display: none; text-align: center;">
                        <!-- ajax loader -->
                        <img src="<?php echo base_url('assets/img/ajax-loader.gif');?>" style="width:400px; height:400px;">
                    </div>
                    <table class="table" id="dynamic-content" width="100%">

                    </table>
                </div>
                <div class="modal-footer">
                    <span id="credit-err" class="text-danger">Not enough credits to send sms</span>
                    <button type="button" id="cancelModal" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" id="confirmBtn" onclick="form_submit()" class="btn btn-secondary mt-0">Confirm</button>
                </div>
            </div>
        </div>
    </div>
</form>

<script type="text/javascript">
function form_submit() {
    var action = "<?php echo site_url('parent_activation/send_parent_provision_credentials') ?>";
    $("#confirmBtn").val('Please Wait...');
    $("#confirmBtn").attr('disabled', true);
    $('#send_provision_credentials').attr('action', action);
    $('#send_provision_credentials').submit();
}
</script>