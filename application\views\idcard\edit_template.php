<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('idcards/Idcards_controller')?>">ID Cards</a></li>
    <li><a href="<?php echo site_url('idcards/Idcards_controller/template_list')?>">Manage Templates</a></li>
    <li>Edit ID Card Template</li>
</ul>
<hr>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('idcards/Idcards_controller/template_list'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a>
            Edit ID Card Template
          </h3>
        </div>
      </div>
    </div>
    <div class="card-body">
        <?= form_open('idcards/Idcards_controller/update_template', ['id' => 'templateForm']) ?>
            <input type="hidden" name="id" value="<?= $template->id ?>">
            <div class="row mb-3">
                <div class="col-md-12">
                    <label for="template_name" class="form-label">Template Name</label>
                    <input type="text" class="form-control" id="template_name" name="template_name"
                           value="<?= htmlspecialchars($template->name) ?>" required>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <label for="id_card_for" class="form-label">ID Card for</label>
                    <select class="form-control" id="id_card_for" name="id_card_for">
                        <option value="Student" <?= $template->id_card_for == 'Student' ? 'selected' : '' ?>>Student</option>
                        <option value="Staff" <?= $template->id_card_for == 'Staff' ? 'selected' : '' ?>>Staff</option>
                        <option value="Parent" <?= $template->id_card_for == 'Parent' ? 'selected' : '' ?>>Parent</option>
                    </select>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <label for="unit_price" class="form-label">Unit Price</label>
                    <input type="number" step="0.01" class="form-control" id="unit_price" name="unit_price"
                           value="<?= $template->unit_price ?>" required>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <label for="template_size" class="form-label">Card Size</label>
                    <select class="form-control" id="template_size" name="template_size">
                        <option value="portrait" <?= $template->size == 'portrait' ? 'selected' : '' ?>>
                            Standard Portrait (54mm x 86mm)
                        </option>
                        <option value="landscape" <?= $template->size == 'landscape' ? 'selected' : '' ?>>
                            Standard Landscape (86mm x 54mm)
                        </option>
                        <option value="custom" <?= $template->size == 'custom' ? 'selected' : '' ?>>
                            Custom Size
                        </option>
                    </select>
                </div>
            </div>

            <div id="custom_size_row" class="row mb-3" style="display: <?= $template->size == 'custom' ? 'flex' : 'none' ?>;">
                <div class="col-md-6">
                    <label for="custom_width" class="form-label">Width (mm)</label>
                    <input type="number" class="form-control" id="custom_width" name="custom_width"
                           min="20" max="200" value="<?= $template->width ?? '' ?>">
                </div>
                <div class="col-md-6">
                    <label for="custom_height" class="form-label">Height (mm)</label>
                    <input type="number" class="form-control" id="custom_height" name="custom_height"
                           min="20" max="200" value="<?= $template->height ?? '' ?>">
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <label for="grid_density" class="form-label">Grid Density</label>
                    <select class="form-control" id="grid_density">
                        <option value="none">No Grid</option>
                        <option value="basic" selected>Basic Grid (25%, 50%, 75%)</option>
                        <option value="1x1">1x1 Grid (1mm spacing)</option>
                        <option value="2x2">2x2 Grid (2mm spacing)</option>
                        <option value="5x5">5x5 Grid (5mm spacing)</option>
                    </select>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Front Side Design</h5>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-secondary add-element" data-side="front" data-type="text">
                                    <i class="fa fa-font"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary add-element" data-side="front" data-type="image">
                                    <i class="fa fa-image"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary add-element" data-side="front" data-type="shape">
                                    <i class="fa fa-shapes"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary add-element" data-side="front" data-type="field">
                                    <i class="fa fa-database"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="frontPreview" class="card-preview mb-3">
                                <div id="frontContent" class="card-content" data-side="front">
                                    <!-- Design elements will be added here -->
                                </div>
                            </div>

                            <div class="accordion" id="frontElementsAccordion">
                                <!-- Elements will be added here -->
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Back Side Design</h5>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-secondary add-element" data-side="back" data-type="text">
                                    <i class="fa fa-font"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary add-element" data-side="back" data-type="image">
                                    <i class="fa fa-image"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary add-element" data-side="back" data-type="shape">
                                    <i class="fa fa-shapes"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary add-element" data-side="back" data-type="field">
                                    <i class="fa fa-database"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="backPreview" class="card-preview mb-3">
                                <div id="backContent" class="card-content" data-side="back">
                                    <!-- Design elements will be added here -->
                                </div>
                            </div>

                            <div class="accordion" id="backElementsAccordion">
                                <!-- Elements will be added here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Hidden fields to store JSON design data -->
            <input type="hidden" id="front_design" name="front_design" value='<?= htmlspecialchars($template->front_design ?: '[]', ENT_QUOTES, 'UTF-8') ?>'>
            <input type="hidden" id="back_design" name="back_design" value='<?= htmlspecialchars($template->back_design ?: '[]', ENT_QUOTES, 'UTF-8') ?>'>

            <div class="row">
                <div class="col-12">
                    <button type="button" id="previewTemplateBtn" class="btn btn-info mr-2">
                        <i class="fa fa-eye"></i> Preview Template
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fa fa-save"></i> Update Template
                    </button>
                    <a href="<?= site_url('idcards/Idcards_controller/template_list') ?>" class="btn btn-secondary">
                        <i class="fa fa-times"></i> Cancel
                    </a>
                </div>
            </div>
        <?= form_close() ?>
    </div>
  </div>
</div>

<!-- Position and size indicators -->
<div id="positionIndicator" class="position-indicator">X: <span id="posX">0</span>, Y: <span id="posY">0</span></div>
<div id="sizeIndicator" class="size-indicator">W: <span id="sizeW">0</span>, H: <span id="sizeH">0</span></div>

<!-- Selection mode indicator -->
<div id="selectionModeIndicator" class="selection-mode-indicator">Group Selection Mode</div>

<!-- Element Templates -->
<div id="elementTemplates" style="display: none;">
    <!-- Text Element -->
    <div class="text-element element" data-type="text">
        <div class="element-content">Sample Text</div>
    </div>

    <!-- Image Element -->
    <div class="image-element element" data-type="image">
        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzMkMzMC42Mjc0IDMyIDM2IDI2LjYyNzQgMzYgMjBDMzYgMTMuMzcyNiAzMC42Mjc0IDggMjQgOEMxNy4zNzI2IDggMTIgMTMuMzcyNiAxMiAyMEMxMiAyNi42Mjc0IDE3LjM3MjYgMzIgMjQgMzJaIiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0xMiA2OEMxMiA1Mi41MzYxIDI0LjUzNjEgNDAgNDAgNDBDNTUuNDYzOSA0MCA2OCA1Mi41MzYxIDY4IDY4VjcySDEyVjY4WiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K" class="element-content">
    </div>

    <!-- Shape Element -->
    <div class="shape-element element" data-type="shape">
        <div class="element-content shape-rectangle"></div>
    </div>

    <!-- Field Element -->
    <div class="field-element element" data-type="field">
        <div class="element-content">[[FIELD]]</div>
    </div>
</div>

<!-- Element Properties Modal -->
<div class="modal fade" id="elementPropertiesModal" tabindex="-1" role="dialog" aria-labelledby="elementPropertiesModalTitle" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="elementPropertiesModalTitle">Element Properties</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Alignment Toolbar -->
                <div class="alignment-toolbar mb-3 p-2 bg-light rounded">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="d-block mb-2">Horizontal Alignment</label>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-secondary align-element" data-align="left"><i class="fa fa-align-left"></i></button>
                                <button type="button" class="btn btn-outline-secondary align-element" data-align="center-h"><i class="fa fa-align-center"></i></button>
                                <button type="button" class="btn btn-outline-secondary align-element" data-align="right"><i class="fa fa-align-right"></i></button>
                                <button type="button" class="btn btn-outline-secondary align-element" data-align="distribute-h"><i class="fa fa-arrows-alt-h"></i></button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="d-block mb-2">Vertical Alignment</label>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-secondary align-element" data-align="top"><i class="fa fa-arrow-up"></i></button>
                                <button type="button" class="btn btn-outline-secondary align-element" data-align="center-v"><i class="fa fa-arrows-alt-v"></i></button>
                                <button type="button" class="btn btn-outline-secondary align-element" data-align="bottom"><i class="fa fa-arrow-down"></i></button>
                                <button type="button" class="btn btn-outline-secondary align-element" data-align="distribute-v"><i class="fa fa-grip-lines"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="text-properties" class="element-property-panel">
                    <div class="form-group">
                        <label for="text-content">Text Content</label>
                        <input type="text" class="form-control" id="text-content" oninput="updateTextWidth(this.value)">
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="text-background">Background Color</label>
                            <div class="input-group">
                                <input type="color" class="form-control" id="text-background" value="#ffffff">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" id="text-background-transparent">
                                        <i class="fa fa-eye-slash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="text-width">Width</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="text-width" min="20" max="500">
                                <div class="input-group-append">
                                    <span class="input-group-text">px</span>
                                </div>
                            </div>
                            <small class="form-text text-muted">Auto-adjusts based on text length</small>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="text-font">Font</label>
                            <select class="form-control" id="text-font">
                                <option value="Arial, sans-serif">Arial</option>
                                <option value="'Times New Roman', serif">Times New Roman</option>
                                <option value="'Courier New', monospace">Courier New</option>
                                <option value="Georgia, serif">Georgia</option>
                                <option value="Verdana, sans-serif">Verdana</option>
                                <option value="'Open Sans', sans-serif">Open Sans</option>
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="text-size">Size</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="text-size" min="6" max="72" value="14">
                                <div class="input-group-append">
                                    <span class="input-group-text">px</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="text-color">Color</label>
                            <input type="color" class="form-control" id="text-color" value="#000000">
                        </div>
                        <div class="form-group col-md-6">
                            <label>Style</label>
                            <div class="btn-group btn-group-toggle w-100" data-toggle="buttons">
                                <label class="btn btn-outline-secondary">
                                    <input type="checkbox" id="text-bold"> <i class="fa fa-bold"></i>
                                </label>
                                <label class="btn btn-outline-secondary">
                                    <input type="checkbox" id="text-italic"> <i class="fa fa-italic"></i>
                                </label>
                                <label class="btn btn-outline-secondary">
                                    <input type="checkbox" id="text-underline"> <i class="fa fa-underline"></i>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Alignment</label>
                        <div class="btn-group btn-group-toggle w-100" data-toggle="buttons">
                            <label class="btn btn-outline-secondary active">
                                <input type="radio" name="text-align" id="text-align-left" value="left" checked> <i class="fa fa-align-left"></i>
                            </label>
                            <label class="btn btn-outline-secondary">
                                <input type="radio" name="text-align" id="text-align-center" value="center"> <i class="fa fa-align-center"></i>
                            </label>
                            <label class="btn btn-outline-secondary">
                                <input type="radio" name="text-align" id="text-align-right" value="right"> <i class="fa fa-align-right"></i>
                            </label>
                        </div>
                    </div>
                </div>

                <div id="image-properties" class="element-property-panel">
                    <div class="form-group">
                        <label for="image-upload">Upload Image</label>
                        <input type="file" class="form-control-file" id="image-upload" accept="image/*">
                        <small class="form-text text-muted">Images will be automatically converted to PNG format</small>
                    </div>
                    <div class="mt-3 mb-3">
                        <img id="image-preview" src="" class="img-fluid border" style="max-height: 200px;">
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="image-fit">Image Fit</label>
                            <select class="form-control" id="image-fit">
                                <option value="cover">Cover (Fill Area)</option>
                                <option value="contain">Contain (Show All)</option>
                                <option value="fill">Stretch to Fill</option>
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="image-rotation">Rotation</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="image-rotation" min="0" max="360" value="0" step="90">
                                <div class="input-group-append">
                                    <span class="input-group-text">°</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="image-border-radius">Border Radius</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="image-border-radius" min="0" max="50" value="0">
                                <div class="input-group-append">
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="image-border-width">Border Width</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="image-border-width" min="0" max="10" value="0">
                                <div class="input-group-append">
                                    <span class="input-group-text">px</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="image-border-color">Border Color</label>
                        <input type="color" class="form-control" id="image-border-color" value="#000000">
                    </div>
                    <div class="form-check mb-3">
                        <input type="checkbox" class="form-check-input" id="image-auto-rotate">
                        <label class="form-check-label" for="image-auto-rotate">Auto-rotate based on card orientation</label>
                        <small class="form-text text-muted">Automatically rotates portrait images in landscape cards and vice versa</small>
                    </div>
                </div>

                <div id="shape-properties" class="element-property-panel">
                    <div class="form-group">
                        <label for="shape-type">Shape Type</label>
                        <select class="form-control" id="shape-type">
                            <option value="rectangle">Rectangle</option>
                            <option value="circle">Circle</option>
                            <option value="line">Line</option>
                        </select>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="shape-fill-color">Fill Color</label>
                            <input type="color" class="form-control" id="shape-fill-color" value="#ffffff">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="shape-border-color">Border Color</label>
                            <input type="color" class="form-control" id="shape-border-color" value="#000000">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="shape-border-width">Border Width</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="shape-border-width" min="0" max="10" value="1">
                            <div class="input-group-append">
                                <span class="input-group-text">px</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="field-properties" class="element-property-panel">
                    <div class="form-group">
                        <label for="field-name">Field Name</label>
                        <select class="form-control" id="field-name">
                            <optgroup label="Common Fields">
                                <option value="[[NAME]]">Name</option>
                                <option value="[[ID]]">ID</option>
                                <option value="[[PHOTO]]">Photo</option>
                                <option value="[[BLOOD_GROUP]]">Blood Group</option>
                                <option value="[[DATE_OF_BIRTH]]">Date of Birth</option>
                                <option value="[[CONTACT]]">Contact</option>
                            </optgroup>
                            <optgroup label="Staff Fields">
                                <option value="[[DEPARTMENT]]">Department</option>
                                <option value="[[DESIGNATION]]">Designation</option>
                                <option value="[[EMPLOYEE_CODE]]">Employee Code</option>
                                <option value="[[JOIN_DATE]]">Join Date</option>
                                <option value="[[STAFF_ADDRESS]]">Staff Address</option>
                                <option value="[[STAFF_EMAIL]]">Staff Email</option>
                            </optgroup>
                            <optgroup label="Student Fields">
                                <option value="[[ADMISSION_NO]]">Admission No</option>
                                <option value="[[GRADE]]">Grade</option>
                                <option value="[[GRADE_SECTION]]">Grade Section</option>
                                <option value="[[ROLL_NO]]">Roll No</option>
                                <option value="[[STUDENT_ADDRESS]]">Student Address</option>
                                <option value="[[STUDENT_EMAIL]]">Student Email</option>
                            </optgroup>
                            <optgroup label="Parent Fields">
                                <option value="[[PARENT_NAME]]">Parent Name</option>
                                <option value="[[FATHER_NAME]]">Father Name</option>
                                <option value="[[MOTHER_NAME]]">Mother Name</option>
                                <option value="[[FATHER_CONTACT]]">Father Contact</option>
                                <option value="[[MOTHER_CONTACT]]">Mother Contact</option>
                                <option value="[[FATHER_ADDRESS]]">Father Address</option>
                                <option value="[[MOTHER_ADDRESS]]">Mother Address</option>
                                <option value="[[FATHER_PHOTO]]">Father Photo</option>
                                <option value="[[MOTHER_PHOTO]]">Mother Photo</option>
                            </optgroup>
                        </select>
                    </div>
                    <div class="form-check mb-3">
                        <input type="checkbox" class="form-check-input" id="field-auto-size">
                        <label class="form-check-label" for="field-auto-size">Auto-adjust text size for long content</label>
                        <small class="form-text text-muted">Automatically reduces font size to fit long text within the field</small>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="field-font">Font</label>
                            <select class="form-control" id="field-font">
                                <option value="'Roboto', sans-serif">Roboto</option>
                                <option value="'Open Sans', sans-serif">Open Sans</option>
                                <option value="'Lato', sans-serif">Lato</option>
                                <option value="'Montserrat', sans-serif">Montserrat</option>
                                <option value="'Poppins', sans-serif">Poppins</option>
                                <option value="'Source Sans Pro', sans-serif">Source Sans Pro</option>
                                <option value="'Raleway', sans-serif">Raleway</option>
                                <option value="'Ubuntu', sans-serif">Ubuntu</option>
                                <option value="'Playfair Display', serif">Playfair Display</option>
                                <option value="'Merriweather', serif">Merriweather</option>
                                <option value="'Noto Sans', sans-serif">Noto Sans</option>
                                <option value="'Nunito', sans-serif">Nunito</option>
                                <option value="'Inter', sans-serif">Inter</option>
                                <option value="'Work Sans', sans-serif">Work Sans</option>
                                <option value="'Mulish', sans-serif">Mulish</option>
                                <option value="Arial, sans-serif">Arial</option>
                                <option value="'Times New Roman', serif">Times New Roman</option>
                                <option value="'Courier New', monospace">Courier New</option>
                                <option value="Georgia, serif">Georgia</option>
                                <option value="Verdana, sans-serif">Verdana</option>
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="field-custom-font">Custom Font</label>
                            <input type="file" class="form-control-file" id="field-custom-font" accept=".ttf,.otf,.woff,.woff2">
                            <small class="form-text text-muted">Upload custom font (TTF, OTF, WOFF, WOFF2)</small>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="field-size">Size</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="field-size" min="6" max="72" value="14">
                                <div class="input-group-append">
                                    <span class="input-group-text">px</span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="field-width">Width</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="field-width" min="1" max="100" value="100">
                                <div class="input-group-append">
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="field-color">Text Color</label>
                            <input type="color" class="form-control" id="field-color" value="#000000">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="field-background">Background Color</label>
                            <input type="color" class="form-control" id="field-background" value="#ffffff">
                            <div class="custom-control custom-checkbox mt-2">
                                <input type="checkbox" class="custom-control-input" id="field-background-transparent">
                                <label class="custom-control-label" for="field-background-transparent">Transparent</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="field-stroke-color">Stroke Color</label>
                            <input type="color" class="form-control" id="field-stroke-color" value="#000000">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="field-stroke-width">Stroke Width</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="field-stroke-width" min="0" max="10" value="0" step="0.1">
                                <div class="input-group-append">
                                    <span class="input-group-text">px</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="field-border-color">Border Color</label>
                            <input type="color" class="form-control" id="field-border-color" value="#000000">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="field-border-width">Border Width</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="field-border-width" min="0" max="10" value="0">
                                <div class="input-group-append">
                                    <span class="input-group-text">px</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Text Style</label>
                        <div class="btn-group btn-group-toggle w-100" data-toggle="buttons">
                            <label class="btn btn-outline-secondary">
                                <input type="checkbox" id="field-bold"> <i class="fa fa-bold"></i>
                            </label>
                            <label class="btn btn-outline-secondary">
                                <input type="checkbox" id="field-italic"> <i class="fa fa-italic"></i>
                            </label>
                            <label class="btn btn-outline-secondary">
                                <input type="checkbox" id="field-underline"> <i class="fa fa-underline"></i>
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Text Alignment</label>
                        <div class="btn-group btn-group-toggle w-100" data-toggle="buttons">
                            <label class="btn btn-outline-secondary active">
                                <input type="radio" name="field-align" value="left" checked> <i class="fa fa-align-left"></i>
                            </label>
                            <label class="btn btn-outline-secondary">
                                <input type="radio" name="field-align" value="center"> <i class="fa fa-align-center"></i>
                            </label>
                            <label class="btn btn-outline-secondary">
                                <input type="radio" name="field-align" value="right"> <i class="fa fa-align-right"></i>
                            </label>
                            <label class="btn btn-outline-secondary">
                                <input type="radio" name="field-align" value="justify"> <i class="fa fa-align-justify"></i>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Common properties for all elements -->
                <hr>
                <div class="element-common-properties">
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="element-position-x">X Position</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="element-position-x" min="0" value="0">
                                <div class="input-group-append">
                                    <span class="input-group-text">px</span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="element-position-y">Y Position</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="element-position-y" min="0" value="0">
                                <div class="input-group-append">
                                    <span class="input-group-text">px</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="element-width">Width</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="element-width" min="10" value="100">
                                <div class="input-group-append">
                                    <span class="input-group-text">px</span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="element-height">Height</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="element-height" min="10" value="30">
                                <div class="input-group-append">
                                    <span class="input-group-text">px</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="element-z-index">Layer Order</label>
                        <input type="number" class="form-control" id="element-z-index" min="0" value="0">
                        <small class="form-text text-muted">Higher values appear on top.</small>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger mr-auto delete-element-btn">Delete Element</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary save-element-btn">Apply</button>
            </div>
        </div>
    </div>
</div>

<!-- Preview Template Modal -->
<div class="modal fade" id="previewTemplateModal" tabindex="-1" role="dialog" aria-labelledby="previewTemplateModalTitle" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewTemplateModalTitle">ID Card Preview</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-center mb-3">Front Side</h6>
                        <div id="frontPreviewRender" class="card-preview-render mx-auto"></div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-center mb-3">Back Side</h6>
                        <div id="backPreviewRender" class="card-preview-render mx-auto"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="form-group mr-auto">
                    <label for="previewDataType">Preview Data Type:</label>
                    <select id="previewDataType" class="form-control-sm">
                        <option value="staff">Staff Sample</option>
                        <option value="student">Student Sample</option>
                        <option value="parent">Parent Sample</option>
                    </select>
                </div>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Add custom CSS for template designer -->
<style>
    .card-preview {
        border: 1px solid #ccc;
        position: relative;
        overflow: hidden;
    }

    .card-content {
        position: relative;
        background: #fff;
    }

    /* Card sizes */
    .card-size-portrait {
        width: 215px;
        height: 335px;
        margin: 0 auto;
        border: 1px solid #000;
    }

    .card-size-landscape {
        width: 335px;
        height: 215px;
        margin: 0 auto;
        border: 1px solid #000;
    }

    /* Preview render sizes */
    .card-preview-render {
        position: relative;
        background: #fff;
        border: 1px solid #000;
        overflow: hidden;
    }

    .card-preview-render.card-size-portrait {
        width: 54mm;
        height: 86mm;
    }

    .card-preview-render.card-size-landscape {
        width: 86mm;
        height: 54mm;
    }

    .element {
        position: absolute;
        cursor: move;
        border: 1px dashed transparent;
    }

    .element.selected {
        border: 1px dashed #007bff;
    }

    .element.preview-element {
        cursor: default;
        border: none;
    }

    .element-content {
        width: 100%;
        height: 100%;
    }

    .text-element .element-content {
        padding: 2px;
    }

    /* Grid lines for alignment - Improved visibility and performance */
    .grid-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1000;
    }

    .grid-line {
        position: absolute;
        background-color: rgba(0, 123, 255, 0.35); /* More visible */
        pointer-events: none;
        z-index: 1000;
        display: block; /* Always visible */
    }

    .grid-line-h {
        width: 100%;
        height: 2px; /* Increased from 1px to 2px */
    }

    .grid-line-v {
        height: 100%;
        width: 2px; /* Increased from 1px to 2px */
    }

    /* Highlight grid lines when element is being dragged */
    .card-content.dragging .grid-line {
        background-color: rgba(0, 123, 255, 0.7); /* More visible when dragging */
        box-shadow: 0 0 3px rgba(0, 123, 255, 0.9);
    }

    /* Additional grid lines for better alignment */
    .grid-line-quarter-h, .grid-line-quarter-v {
        background-color: rgba(0, 123, 255, 0.3); /* More visible */
    }

    /* Dense grid lines */
    .grid-line-dense {
        background-color: rgba(0, 123, 255, 0.25) !important;
    }

    .shape-rectangle {
        background-color: #ffffff;
        border: 1px solid #000000;
    }

    .shape-circle {
        background-color: #ffffff;
        border: 1px solid #000000;
        border-radius: 50%;
    }

    .shape-line {
        background-color: transparent;
        border-top: 1px solid #000000;
        height: 0 !important;
    }

    .field-element .element-content {
        color: #3273dc;
        padding: 2px;
    }

    /* Text overflow handling for fields */
    .field-element.text-overflow-ellipsis .element-content {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .field-element.text-overflow-wrap .element-content {
        white-space: normal;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    .element-property-panel {
        display: none;
    }

    /* Alignment toolbar */
    .alignment-toolbar {
        border: 1px solid #dee2e6;
    }

    /* Group selection indicator */
    .group-selection-box {
        position: absolute;
        border: 2px dashed #007bff;
        background-color: rgba(0, 123, 255, 0.15);
        pointer-events: none;
        z-index: 999;
    }

    /* Multiple selection styling */
    .element.multi-selected {
        border: 2px dashed #28a745 !important;
        box-shadow: 0 0 5px rgba(40, 167, 69, 0.5);
    }

    /* Group selection toolbar */
    .group-selection-toolbar {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        padding: 10px 15px;
        z-index: 1050;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 10px;
    }

    .toolbar-section {
        display: flex;
        align-items: center;
        margin-right: 15px;
    }

    .toolbar-title {
        font-weight: bold;
        margin-right: 10px;
    }

    .toolbar-label {
        margin-right: 5px;
        font-size: 0.9rem;
    }

    .selection-count {
        margin-left: 5px;
    }

    /* Selection mode indicator */
    .selection-mode-indicator {
        position: fixed;
        top: 10px;
        right: 10px;
        background-color: rgba(40, 167, 69, 0.8);
        color: white;
        padding: 5px 10px;
        border-radius: 3px;
        font-size: 12px;
        z-index: 1050;
        display: none;
    }

    /* Position and size indicators */
    .position-indicator, .size-indicator {
        position: absolute;
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 12px;
        pointer-events: none;
        z-index: 1100;
        display: none;
    }

    /* Snap indicators */
    .snap-indicator {
        position: absolute;
        background-color: rgba(255, 0, 0, 0.5);
        pointer-events: none;
        z-index: 1050;
        display: none;
    }

    .snap-indicator-h {
        height: 1px;
        width: 100%;
    }

    .snap-indicator-v {
        width: 1px;
        height: 100%;
    }
</style>

<script>
$(document).ready(function() {
    // Initialize variables
    let elements = {
        front: [],
        back: []
    };
    let selectedElement = null;
    let selectedElements = [];
    let selectedSide = 'front';
    let elementCounter = 0;
    let isShiftPressed = false;
    let isCtrlPressed = false;
    let selectionBox = null;
    let selectionStart = { x: 0, y: 0 };

    // Initialize the template editor with existing data
    const frontDesign = JSON.parse($('#front_design').val() || '[]');
    const backDesign = JSON.parse($('#back_design').val() || '[]');

    // Load existing elements
    elements.front = frontDesign;
    elements.back = backDesign;

    // Set initial card size
    updateCardSize();

    // Show/hide custom size fields based on template size selection
    $('#template_size').change(function() {
        if ($(this).val() === 'custom') {
            $('#custom_size_row').show();
        } else {
            $('#custom_size_row').hide();
        }
        updateCardSize();
    });

    // Update card size based on selection
    function updateCardSize() {
        const size = $('#template_size').val();

        // Remove all size classes
        $('#frontContent, #backContent').removeClass('card-size-portrait card-size-landscape card-size-custom');

        if (size === 'portrait') {
            $('#frontContent, #backContent').addClass('card-size-portrait');
        } else if (size === 'landscape') {
            $('#frontContent, #backContent').addClass('card-size-landscape');
        } else if (size === 'custom') {
            const width = $('#custom_width').val() + 'mm';
            const height = $('#custom_height').val() + 'mm';
            $('#frontContent, #backContent').css({
                'width': width,
                'height': height
            }).addClass('card-size-custom');
        }
    }

    // Custom size inputs change handler
    $('#custom_width, #custom_height').change(function() {
        if ($('#template_size').val() === 'custom') {
            updateCardSize();
        }
    });

    // Render existing elements on page load
    renderElements('front', elements.front);
    renderElements('back', elements.back);

    // Set up grid
    updateGrid($('#grid_density').val());

    // Grid density change handler
    $('#grid_density').change(function() {
        updateGrid($(this).val());
    });

    // Function to render elements
    function renderElements(side, elementsArray) {
        const $container = $(`#${side}Content`);
        $container.empty();

        elementsArray.forEach((elementData, index) => {
            const $element = createElementFromData(elementData, side, index);
            $container.append($element);
            makeElementDraggable($element, side);
        });
    }

    // Function to create element from data
    function createElementFromData(data, side, index) {
        const elementId = `element-${side}-${index}`;
        let $element;

        switch(data.type) {
            case 'text':
                $element = $('#elementTemplates').find('.text-element').clone();
                $element.find('.element-content').text(data.content || 'Sample Text');
                break;
            case 'image':
                $element = $('#elementTemplates').find('.image-element').clone();
                if (data.src) {
                    $element.find('.element-content').attr('src', data.src);
                }
                break;
            case 'shape':
                $element = $('#elementTemplates').find('.shape-element').clone();
                $element.find('.element-content').removeClass('shape-rectangle shape-circle shape-line')
                    .addClass(`shape-${data.shapeType || 'rectangle'}`);
                break;
            case 'field':
                $element = $('#elementTemplates').find('.field-element').clone();
                $element.find('.element-content').text(data.fieldName || '[[FIELD]]');
                break;
        }

        $element.attr('id', elementId);
        $element.css({
            left: data.x + 'px',
            top: data.y + 'px',
            width: data.width + 'px',
            height: data.height + 'px',
            zIndex: data.zIndex || 0
        });

        // Apply styles
        if (data.styles) {
            applyElementStyles($element, data.styles);
        }

        return $element;
    }

    // Function to apply element styles
    function applyElementStyles($element, styles) {
        const $content = $element.find('.element-content');

        if (styles.fontSize) $content.css('font-size', styles.fontSize + 'px');
        if (styles.fontFamily) $content.css('font-family', styles.fontFamily);
        if (styles.color) $content.css('color', styles.color);
        if (styles.backgroundColor) $content.css('background-color', styles.backgroundColor);
        if (styles.textAlign) $content.css('text-align', styles.textAlign);
        if (styles.fontWeight) $content.css('font-weight', styles.fontWeight);
        if (styles.fontStyle) $content.css('font-style', styles.fontStyle);
        if (styles.textDecoration) $content.css('text-decoration', styles.textDecoration);
        if (styles.borderRadius) $content.css('border-radius', styles.borderRadius + '%');
        if (styles.borderWidth) $content.css('border-width', styles.borderWidth + 'px');
        if (styles.borderColor) $content.css('border-color', styles.borderColor);
    }

    // Function to update grid
    function updateGrid(density) {
        $('.grid-container').remove();

        if (density === 'none') return;

        ['front', 'back'].forEach(side => {
            const $container = $(`#${side}Content`);
            const $gridContainer = $('<div class="grid-container"></div>');

            const containerWidth = $container.width();
            const containerHeight = $container.height();

            if (density === 'basic') {
                // Basic grid at 25%, 50%, 75%
                [25, 50, 75].forEach(percent => {
                    const hPos = (containerHeight * percent) / 100;
                    const vPos = (containerWidth * percent) / 100;

                    $gridContainer.append(`<div class="grid-line grid-line-h" style="top: ${hPos}px;"></div>`);
                    $gridContainer.append(`<div class="grid-line grid-line-v" style="left: ${vPos}px;"></div>`);
                });
            } else {
                // Dense grids
                let spacing = 10; // Default
                if (density === '1x1') spacing = 4;
                else if (density === '2x2') spacing = 8;
                else if (density === '5x5') spacing = 20;

                for (let i = spacing; i < containerHeight; i += spacing) {
                    $gridContainer.append(`<div class="grid-line grid-line-h grid-line-dense" style="top: ${i}px;"></div>`);
                }
                for (let i = spacing; i < containerWidth; i += spacing) {
                    $gridContainer.append(`<div class="grid-line grid-line-v grid-line-dense" style="left: ${i}px;"></div>`);
                }
            }

            $container.append($gridContainer);
        });
    }

    // Add element button click handler
    $('.add-element').click(function() {
        const side = $(this).data('side');
        const type = $(this).data('type');

        // Clone template element
        const $template = $('#elementTemplates').find(`.${type}-element`).clone();
        const elementId = `element-${side}-${elementCounter++}`;

        $template.attr('id', elementId);
        $template.css({
            left: '10px',
            top: '10px',
            width: type === 'text' || type === 'field' ? '100px' : '80px',
            height: type === 'text' || type === 'field' ? '30px' : '80px',
            zIndex: elements[side].length
        });

        // Add element to the card
        $(`#${side}Content`).append($template);

        // Make element draggable
        makeElementDraggable($template, side);

        // Add to elements array
        const elementData = {
            id: elementId,
            type: type,
            x: 10,
            y: 10,
            width: type === 'text' || type === 'field' ? 100 : 80,
            height: type === 'text' || type === 'field' ? 30 : 80,
            zIndex: elements[side].length,
            content: type === 'text' ? 'Sample Text' : (type === 'field' ? '[[FIELD]]' : ''),
            styles: {}
        };

        elements[side].push(elementData);
        updateHiddenFields();
    });

    // Function to make element draggable
    function makeElementDraggable($element, side) {
        $element.draggable({
            containment: `#${side}Content`,
            start: function(event, ui) {
                // Add dragging class to highlight grid lines
                $(`#${side}Content`).addClass('dragging');
                showPositionIndicator($(this), side);
            },
            drag: function(event, ui) {
                updatePositionIndicator(ui.position.left, ui.position.top);

                // Snap to grid if shift key is pressed
                if (event.shiftKey) {
                    const density = $('#grid_density').val();
                    let snapSize = 10;

                    if (density === '1x1') snapSize = 4;
                    else if (density === '2x2') snapSize = 8;
                    else if (density === '5x5') snapSize = 20;
                    else if (density === 'basic') snapSize = 25;

                    ui.position.left = Math.round(ui.position.left / snapSize) * snapSize;
                    ui.position.top = Math.round(ui.position.top / snapSize) * snapSize;
                }
            },
            stop: function(event, ui) {
                $(`#${side}Content`).removeClass('dragging');
                hidePositionIndicator();

                // Update element data
                updateElementPosition($(this), side, ui.position.left, ui.position.top);
            }
        });

        // Make element resizable
        $element.resizable({
            containment: `#${side}Content`,
            handles: 'n, e, s, w, ne, nw, se, sw',
            start: function(event, ui) {
                showSizeIndicator($(this));
            },
            resize: function(event, ui) {
                updateSizeIndicator(ui.size.width, ui.size.height);
            },
            stop: function(event, ui) {
                hideSizeIndicator();

                // Update element data
                updateElementSize($(this), side, ui.size.width, ui.size.height);
            }
        });

        // Element click handler
        $element.click(function(e) {
            e.stopPropagation();

            if (isCtrlPressed || isShiftPressed) {
                // Multi-selection
                toggleElementSelection($(this));
            } else {
                // Single selection
                selectElement($(this), side);
            }
        });

        // Double click to edit
        $element.dblclick(function(e) {
            e.stopPropagation();
            openElementProperties($(this), side);
        });
    }

    // Function to select element
    function selectElement($element, side) {
        // Clear previous selections
        $('.element').removeClass('selected multi-selected');
        selectedElements = [];

        // Select current element
        $element.addClass('selected');
        selectedElement = $element;
        selectedSide = side;
        selectedElements = [$element];
    }

    // Function to toggle element selection (for multi-select)
    function toggleElementSelection($element) {
        if ($element.hasClass('multi-selected')) {
            $element.removeClass('multi-selected');
            selectedElements = selectedElements.filter(el => el[0] !== $element[0]);
        } else {
            $element.addClass('multi-selected');
            selectedElements.push($element);
        }

        if (selectedElements.length === 0) {
            selectedElement = null;
        } else {
            selectedElement = selectedElements[selectedElements.length - 1];
        }
    }

    // Function to open element properties modal
    function openElementProperties($element, side) {
        selectedElement = $element;
        selectedSide = side;

        const elementType = $element.data('type');

        // Hide all property panels
        $('.element-property-panel').hide();

        // Show relevant property panel
        $(`#${elementType}-properties`).show();

        // Load current element properties
        loadElementProperties($element, elementType);

        // Show modal
        $('#elementPropertiesModal').modal('show');
    }

    // Function to load element properties into modal
    function loadElementProperties($element, type) {
        const $content = $element.find('.element-content');
        const position = $element.position();

        // Common properties
        $('#element-position-x').val(Math.round(position.left));
        $('#element-position-y').val(Math.round(position.top));
        $('#element-width').val($element.width());
        $('#element-height').val($element.height());
        $('#element-z-index').val($element.css('z-index') || 0);

        // Type-specific properties
        switch(type) {
            case 'text':
                $('#text-content').val($content.text());
                $('#text-font').val($content.css('font-family'));
                $('#text-size').val(parseInt($content.css('font-size')));
                $('#text-color').val(rgbToHex($content.css('color')));
                $('#text-background').val(rgbToHex($content.css('background-color')));
                $('#text-width').val($element.width());
                $('#text-bold').prop('checked', $content.css('font-weight') === 'bold' || parseInt($content.css('font-weight')) >= 700);
                $('#text-italic').prop('checked', $content.css('font-style') === 'italic');
                $('#text-underline').prop('checked', $content.css('text-decoration').includes('underline'));
                $(`input[name="text-align"][value="${$content.css('text-align')}"]`).prop('checked', true);
                break;

            case 'field':
                $('#field-name').val($content.text());
                $('#field-font').val($content.css('font-family'));
                $('#field-size').val(parseInt($content.css('font-size')));
                $('#field-color').val(rgbToHex($content.css('color')));
                $('#field-background').val(rgbToHex($content.css('background-color')));
                $('#field-bold').prop('checked', $content.css('font-weight') === 'bold' || parseInt($content.css('font-weight')) >= 700);
                $('#field-italic').prop('checked', $content.css('font-style') === 'italic');
                $('#field-underline').prop('checked', $content.css('text-decoration').includes('underline'));
                $(`input[name="field-align"][value="${$content.css('text-align')}"]`).prop('checked', true);
                break;
        }
    }

    // Save element properties
    $('.save-element-btn').click(function() {
        if (!selectedElement) return;

        const elementType = selectedElement.data('type');
        const $content = selectedElement.find('.element-content');

        // Update common properties
        const newX = parseInt($('#element-position-x').val());
        const newY = parseInt($('#element-position-y').val());
        const newWidth = parseInt($('#element-width').val());
        const newHeight = parseInt($('#element-height').val());
        const newZIndex = parseInt($('#element-z-index').val());

        selectedElement.css({
            left: newX + 'px',
            top: newY + 'px',
            width: newWidth + 'px',
            height: newHeight + 'px',
            zIndex: newZIndex
        });

        // Update type-specific properties
        switch(elementType) {
            case 'text':
                $content.text($('#text-content').val());
                $content.css({
                    'font-family': $('#text-font').val(),
                    'font-size': $('#text-size').val() + 'px',
                    'color': $('#text-color').val(),
                    'background-color': $('#text-background').val(),
                    'text-align': $('input[name="text-align"]:checked').val(),
                    'font-weight': $('#text-bold').is(':checked') ? 'bold' : 'normal',
                    'font-style': $('#text-italic').is(':checked') ? 'italic' : 'normal',
                    'text-decoration': $('#text-underline').is(':checked') ? 'underline' : 'none'
                });
                break;

            case 'field':
                $content.text($('#field-name').val());
                $content.css({
                    'font-family': $('#field-font').val(),
                    'font-size': $('#field-size').val() + 'px',
                    'color': $('#field-color').val(),
                    'background-color': $('#field-background').val(),
                    'text-align': $('input[name="field-align"]:checked').val(),
                    'font-weight': $('#field-bold').is(':checked') ? 'bold' : 'normal',
                    'font-style': $('#field-italic').is(':checked') ? 'italic' : 'normal',
                    'text-decoration': $('#field-underline').is(':checked') ? 'underline' : 'none'
                });
                break;
        }

        // Update elements array
        updateElementInArray(selectedElement, selectedSide);
        updateHiddenFields();

        $('#elementPropertiesModal').modal('hide');
    });

    // Delete element
    $('.delete-element-btn').click(function() {
        if (!selectedElement) return;

        // Remove from DOM
        selectedElement.remove();

        // Remove from elements array
        removeElementFromArray(selectedElement, selectedSide);
        updateHiddenFields();

        selectedElement = null;
        $('#elementPropertiesModal').modal('hide');
    });

    // Function to update element position in array
    function updateElementPosition($element, side, x, y) {
        const elementId = $element.attr('id');
        const elementIndex = elements[side].findIndex(el => el.id === elementId);

        if (elementIndex !== -1) {
            elements[side][elementIndex].x = x;
            elements[side][elementIndex].y = y;
            updateHiddenFields();
        }
    }

    // Function to update element size in array
    function updateElementSize($element, side, width, height) {
        const elementId = $element.attr('id');
        const elementIndex = elements[side].findIndex(el => el.id === elementId);

        if (elementIndex !== -1) {
            elements[side][elementIndex].width = width;
            elements[side][elementIndex].height = height;
            updateHiddenFields();
        }
    }

    // Function to update element in array
    function updateElementInArray($element, side) {
        const elementId = $element.attr('id');
        const elementIndex = elements[side].findIndex(el => el.id === elementId);

        if (elementIndex !== -1) {
            const $content = $element.find('.element-content');
            const position = $element.position();

            elements[side][elementIndex] = {
                ...elements[side][elementIndex],
                x: Math.round(position.left),
                y: Math.round(position.top),
                width: $element.width(),
                height: $element.height(),
                zIndex: parseInt($element.css('z-index')) || 0,
                content: $content.text(),
                styles: {
                    fontSize: parseInt($content.css('font-size')),
                    fontFamily: $content.css('font-family'),
                    color: $content.css('color'),
                    backgroundColor: $content.css('background-color'),
                    textAlign: $content.css('text-align'),
                    fontWeight: $content.css('font-weight'),
                    fontStyle: $content.css('font-style'),
                    textDecoration: $content.css('text-decoration')
                }
            };
        }
    }

    // Function to remove element from array
    function removeElementFromArray($element, side) {
        const elementId = $element.attr('id');
        const elementIndex = elements[side].findIndex(el => el.id === elementId);

        if (elementIndex !== -1) {
            elements[side].splice(elementIndex, 1);
        }
    }

    // Function to update hidden fields
    function updateHiddenFields() {
        $('#front_design').val(JSON.stringify(elements.front));
        $('#back_design').val(JSON.stringify(elements.back));
    }

    // Utility functions
    function rgbToHex(rgb) {
        if (rgb.indexOf('#') === 0) return rgb;

        const result = rgb.match(/\d+/g);
        if (!result || result.length < 3) return '#000000';

        return '#' + result.slice(0, 3).map(x => {
            const hex = parseInt(x).toString(16);
            return hex.length === 1 ? '0' + hex : hex;
        }).join('');
    }

    function showPositionIndicator($element, side) {
        const position = $element.position();
        $('#posX').text(Math.round(position.left));
        $('#posY').text(Math.round(position.top));
        $('#positionIndicator').show().css({
            left: position.left + $element.width() + 10,
            top: position.top - 20
        });
    }

    function updatePositionIndicator(x, y) {
        $('#posX').text(Math.round(x));
        $('#posY').text(Math.round(y));
    }

    function hidePositionIndicator() {
        $('#positionIndicator').hide();
    }

    function showSizeIndicator($element) {
        $('#sizeW').text($element.width());
        $('#sizeH').text($element.height());
        $('#sizeIndicator').show();
    }

    function updateSizeIndicator(width, height) {
        $('#sizeW').text(Math.round(width));
        $('#sizeH').text(Math.round(height));
    }

    function hideSizeIndicator() {
        $('#sizeIndicator').hide();
    }

    // Keyboard event handlers
    $(document).keydown(function(e) {
        if (e.key === 'Shift') isShiftPressed = true;
        if (e.key === 'Control') isCtrlPressed = true;

        // Delete key
        if (e.key === 'Delete' && selectedElement) {
            selectedElement.remove();
            removeElementFromArray(selectedElement, selectedSide);
            updateHiddenFields();
            selectedElement = null;
        }
    });

    $(document).keyup(function(e) {
        if (e.key === 'Shift') isShiftPressed = false;
        if (e.key === 'Control') isCtrlPressed = false;
    });

    // Preview button click handler
    $('#previewTemplateBtn').click(function() {
        showTemplatePreview();
    });

    // Function to show template preview
    function showTemplatePreview() {
        const templateSize = $('#template_size').val();
        const customWidth = $('#custom_width').val();
        const customHeight = $('#custom_height').val();

        // Clear previous preview
        $('#frontPreviewRender, #backPreviewRender').empty().removeClass('card-size-portrait card-size-landscape');

        // Set preview size
        if (templateSize === 'portrait') {
            $('#frontPreviewRender, #backPreviewRender').addClass('card-size-portrait');
        } else if (templateSize === 'landscape') {
            $('#frontPreviewRender, #backPreviewRender').addClass('card-size-landscape');
        } else if (templateSize === 'custom') {
            $('#frontPreviewRender, #backPreviewRender').css({
                width: customWidth + 'mm',
                height: customHeight + 'mm'
            });
        }

        // Render elements for preview
        renderPreviewElements('front', elements.front);
        renderPreviewElements('back', elements.back);

        $('#previewTemplateModal').modal('show');
    }

    // Function to render preview elements
    function renderPreviewElements(side, elementsArray) {
        const $container = $(`#${side}PreviewRender`);

        elementsArray.forEach(elementData => {
            const $element = createPreviewElement(elementData);
            $container.append($element);
        });
    }

    // Function to create preview element
    function createPreviewElement(data) {
        let $element = $('<div class="element preview-element"></div>');
        let $content = $('<div class="element-content"></div>');

        switch(data.type) {
            case 'text':
                $content.text(data.content || 'Sample Text');
                break;
            case 'field':
                $content.text(data.content || '[[FIELD]]');
                break;
            case 'image':
                $content = $('<img class="element-content">');
                if (data.src) $content.attr('src', data.src);
                break;
            case 'shape':
                $content.addClass(`shape-${data.shapeType || 'rectangle'}`);
                break;
        }

        $element.append($content);
        $element.css({
            position: 'absolute',
            left: data.x + 'px',
            top: data.y + 'px',
            width: data.width + 'px',
            height: data.height + 'px',
            zIndex: data.zIndex || 0
        });

        // Apply styles
        if (data.styles) {
            applyElementStyles($element, data.styles);
        }

        return $element;
    }

    // Clear selection when clicking on empty area
    $('#frontContent, #backContent').click(function(e) {
        if (e.target === this) {
            $('.element').removeClass('selected multi-selected');
            selectedElement = null;
            selectedElements = [];
        }
    });

    // Auto-update text width function
    window.updateTextWidth = function(text) {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        const fontSize = $('#text-size').val() || 14;
        const fontFamily = $('#text-font').val() || 'Arial';

        context.font = `${fontSize}px ${fontFamily}`;
        const width = Math.ceil(context.measureText(text).width) + 10; // Add padding

        $('#text-width').val(Math.min(width, 500)); // Max width 500px
    };

    // Image upload handler
    $('#image-upload').change(function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#image-preview').attr('src', e.target.result).show();

                // Update selected element if it's an image
                if (selectedElement && selectedElement.data('type') === 'image') {
                    selectedElement.find('.element-content').attr('src', e.target.result);
                    updateElementInArray(selectedElement, selectedSide);
                    updateHiddenFields();
                }
            };
            reader.readAsDataURL(file);
        }
    });

    // Background transparent button handler
    $('#text-background-transparent').click(function() {
        $('#text-background').val('transparent');
        if (selectedElement && selectedElement.data('type') === 'text') {
            selectedElement.find('.element-content').css('background-color', 'transparent');
        }
    });

    // Field background transparent checkbox handler
    $('#field-background-transparent').change(function() {
        if ($(this).is(':checked')) {
            $('#field-background').val('transparent');
            if (selectedElement && selectedElement.data('type') === 'field') {
                selectedElement.find('.element-content').css('background-color', 'transparent');
            }
        }
    });

    // Initialize with existing template data if available
    if (frontDesign.length > 0 || backDesign.length > 0) {
        elements.front = frontDesign;
        elements.back = backDesign;
        renderElements('front', elements.front);
        renderElements('back', elements.back);
    }
});
</script>