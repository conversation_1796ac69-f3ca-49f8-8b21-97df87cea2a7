<?php


/**
 * Name:    OxygenV2
 * Author:  Anish
 *          <EMAIL>
 *
 * Created:  19 march 2025
 *
 * Description: Controller for Service Contract Module. Entry point for Service Contract Module
 *
 * Requirements: PHP5 or above
 *
 */

class Service_contract_model extends CI_Model {
    private $yearId;
	function __construct() {
	    parent::__construct();
        $this->yearId = $this->acad_year->getAcadYearID();
        
    }

    function getAcadYear() {
        return $this->db_readonly->where('id', $this->yearId)->get('academic_year')->row();
    }

    function currentMaxCantractId() {
        return $this->db_readonly->select("ifnull(max(id), 0) as currentRunningId")->get('procurement_service_contract_master')->row();
    }

    function getAllDepartments() {
        return $this->db_readonly->where('status', 1)->order_by('department')->get('staff_departments')->result();
    }

    function getAllActiveVendors() {
        return $this->db_readonly->where('status', 1)->order_by('vendor_name')->get('procurement_vendor_master')->result();
    }

    function getLoggedInName() {
        $id= $this->authorization->getAvatarStakeHolderId();
        $x= $this->db_readonly->select("ifnull(concat(ifnull(first_name, ''), ' ', ifnull(last_name, '')), 'Admin') as requesterName")->where('id', $id)->get('staff_master')->row();
        if(!empty($x)) {
            return $x;
        }
        $obj= new stdClass();
        $obj->requesterName= 'Admin';
        return $obj;
    }

    function save_service_contract_steps() {
        // echo '<pre>'; print_r($_POST); die();
        $input= $this->input->post();
        $step_number= $input['step_number'];

        $this->db->trans_start();
            if($input['basic_details_add_edit'] == 'Add') {
                $data= $this->__insert_step_1($input);
                $this->__add_department_approvers_to_service_contract($data['service_contract_master_id'], $input['department']);
            } else {
                $data= $this->__update_step_1($input);
            }
        $this->db->trans_complete();
        if(!$this->db->trans_status()) {
            $this->db->trans_rollback();
            $data= array(
                'status' => 0
            );
        }
        return $data;
    }

    function __add_department_approvers_to_service_contract($service_contract_master_id, $department_id) {
        $department_approvers= $this->db->select("id, ifnull(approval_algorithm, 0) as approval_algorithm, ifnull(approver_1, 0) as approver_1, ifnull(approver_2, 0) as approver_2, ifnull(approver_3, 0) as approver_3, ifnull(financial_approver, 0) as financial_approver")->where('id', $department_id)->get('staff_departments')->row();
        if(!empty($department_approvers) && $department_approvers->approval_algorithm > 0) {
            if($department_approvers->approval_algorithm >= 1) {
                $this->db->insert('procurement_service_contract_approval_flow', array('service_contract_master_id' => $service_contract_master_id, 'approver_id' => $department_approvers->approver_1, 'status' => 'Pending', 'approver_type' => 'Level 1'));
            }
            if($department_approvers->approval_algorithm >= 2) {
                $this->db->insert('procurement_service_contract_approval_flow', array('service_contract_master_id' => $service_contract_master_id, 'approver_id' => $department_approvers->approver_2, 'status' => 'Pending', 'approver_type' => 'Level 2'));
            }
            if($department_approvers->approval_algorithm >= 3) {
                $this->db->insert('procurement_service_contract_approval_flow', array('service_contract_master_id' => $service_contract_master_id, 'approver_id' => $department_approvers->approver_3, 'status' => 'Pending', 'approver_type' => 'Level 3'));
            }
            if($department_approvers->financial_approver > 0) {
                $this->db->insert('procurement_service_contract_approval_flow', array('service_contract_master_id' => $service_contract_master_id, 'approver_id' => $department_approvers->financial_approver, 'status' => 'Pending', 'approver_type' => 'Financial Approver'));
            }
        }
        return 1;
    }

    function __update_step_1($input) {
        // $currentRunningNumber= $this->db->select("ifnull(max(id), 0) as currentRunningId")->get('procurement_service_contract_master')->row();
        // $currentAcadYear = $this->db_readonly->where('id', $this->yearId)->get('academic_year')->row();
        // if(!empty($currentAcadYear)) {
        //     $currentAcadYearName = $currentAcadYear->acad_year;
        // } else {
        //     $currentAcadYearName = '';
        // }
        // $nextId = intval($currentRunningNumber->currentRunningId) + 1;
        // $tenDigitId = str_pad($nextId, 10, '0', STR_PAD_LEFT);
        // $cont_num= "CONT-" .$currentAcadYearName. "-" .$tenDigitId;
        $data_update= array(
            // 'contract_number' => intval($input['service_contract_master_id']) == 0 ? $cont_num : $input['contract_number'],
            'contract_date' => date('Y-m-d', strtotime($input['contract_date'])),
            'contract_type' => date('Y-m-d', strtotime($input['contract_type'])),
            // 'department_id' => $input['department'],
            'created_by' => $this->authorization->getAvatarStakeHolderId(),
            'contract_status' => 'Draft',
            'contract_start_date' => date('Y-m-d', strtotime($input['start_date'])),
            'contract_end_date' => date('Y-m-d', strtotime($input['end_date'])),
            'renewal_remainder' => $input['renewal_remainder'],
            'created_on' => date('Y-m-d H:i:s'),
            'modified_by' => $this->authorization->getAvatarStakeHolderId(),
            // 'currency' => isset($input['currency']) ? $input['currency'] : 'INR',
        );
        
        $this->db->where('id', $input['service_contract_master_id'])->update('procurement_service_contract_master', $data_update);
        $service_contract_master_id= $input['service_contract_master_id'];
        // History
        $history= array(
            'action_type' => intval($input['service_contract_master_id']) == 0 ? 'Contract Initiated' : 'Re-filled Details',
            'action_description' =>  intval($input['service_contract_master_id']) == 0 ? "A service contract has been successfully initiated." : 'Basic details of the service contract has been re-filled.',
            'action_by' => $this->authorization->getAvatarStakeHolderId(),
            'service_contract_master_id' => $service_contract_master_id,
        );
        $this->db->insert('procurement_service_contract_history', $history);

        return array(
            'status' => 1,
            'service_contract_master_id' => $service_contract_master_id,
            'contract_number' => $input['contract_number'] // Use the existing contract number from input
        );
    }

    function __insert_step_1($input) {
        // echo '<pre>'; print_r($input); die();
        $currentRunningNumber= $this->db->select("ifnull(max(id), 0) as currentRunningId")->get('procurement_service_contract_master')->row();
        $currentAcadYear = $this->db_readonly->where('id', $this->yearId)->get('academic_year')->row();
        if(!empty($currentAcadYear)) {
            $currentAcadYearName = $currentAcadYear->acad_year;
        } else {
            $currentAcadYearName = '';
        }
        $nextId = intval($currentRunningNumber->currentRunningId) + 1;
        $tenDigitId = str_pad($nextId, 10, '0', STR_PAD_LEFT);
        $cont_num= "CONT-" .$currentAcadYearName. "-" .$tenDigitId;
        $data_insert= array(
            'contract_number' => intval($input['service_contract_master_id']) == 0 ? $cont_num : $input['contract_number'],
            'contract_date' => date('Y-m-d', strtotime($input['contract_date'])),
            'contract_type' => date('Y-m-d', strtotime($input['contract_type'])),
            'department_id' => $input['department'],
            'created_by' => $this->authorization->getAvatarStakeHolderId(),
            'contract_status' => 'Draft',
            'contract_start_date' => date('Y-m-d', strtotime($input['start_date'])),
            'contract_end_date' => date('Y-m-d', strtotime($input['end_date'])),
            'renewal_remainder' => $input['renewal_remainder'],
            'created_on' => date('Y-m-d H:i:s'),
            'modified_by' => $this->authorization->getAvatarStakeHolderId(),
            // 'currency' => isset($input['currency']) ? $input['currency'] : 'INR',
        );
        if(intval($input['service_contract_master_id']) == 0) {
            $this->db->insert('procurement_service_contract_master', $data_insert);
            $service_contract_master_id= $this->db->insert_id();
        } else {
            $this->db->where('id', $input['service_contract_master_id'])->update('procurement_service_contract_master', $data_insert);
            $service_contract_master_id= $input['service_contract_master_id'];
        }
        // History
        $history= array(
            'action_type' => intval($input['service_contract_master_id']) == 0 ? 'Contract Initiated' : 'Re-filled Details',
            'action_description' =>  intval($input['service_contract_master_id']) == 0 ? "A service contract has been successfully initiated." : 'Basic details of the service contract has been re-filled.',
            'action_by' => $this->authorization->getAvatarStakeHolderId(),
            'service_contract_master_id' => $service_contract_master_id,
        );
        $this->db->insert('procurement_service_contract_history', $history);

        return array(
            'status' => 1,
            'service_contract_master_id' => $service_contract_master_id,
            'contract_number' => $cont_num
        );
    }

    function get_vendor_details() {
        $vendor_id= $this->input->post('vendor_id');
        $vendor= $this->db_readonly->select("pvm.id, ifnull(pvm.vendor_code, '') as vendor_code, ifnull(pvm.vendor_name, '') as vendor_name,ifnull(pvm.gst_no, '') as gst_no,ifnull(pvm.vendor_email, '') as vendor_email,ifnull(pvm.contact_first_name, '') as contact_first_name,ifnull(pvm.contact_last_name, '') as contact_last_name,ifnull(pvm.contact_number, '') as contact_number, TRIM( concat( ifnull(pvai.address_type, ''), ' ', ifnull(pvai.address_line1, ''), ' ', ifnull(pvai.address_line1, ''), ' ', ifnull(pvai.area, ''), ' ', ifnull(pvai.district, ''), ' ', ifnull(pvai.state, ''), ' ', ifnull(pvai.country, ''), ' ', ifnull(pvai.pin_code, '')) ) as vendor_address, pvai.id as pvaiId")
            ->from('procurement_vendor_master pvm')
            ->join('procurement_vendor_address_info pvai', 'pvai.vendor_id = pvm.id', 'left')
            ->where('pvm.id', $vendor_id)
            ->get()->row();
        $vendorCats= $this->db_readonly->select("pic.id, pic.category_name, if(pic.financial_approver is not null and pic.financial_approver != '' and pic.financial_approver != '0' and pic.financial_approver > 0, 1, 0) as haveFinancialApprover")
            ->from('procurement_vendor_category pvc')
            ->join('procurement_itemmaster_category pic', 'pic.id = pvc.proc_im_category_id')
            ->where('pvc.vendor_id', $vendor_id)
            ->where('pic.status', 1) // Active category only from vendor category
            ->where('pic.category_type', 'Services')
            // ->where("(pic.financial_approver is not null and pic.financial_approver != '' and pic.financial_approver != '0' and pic.financial_approver > 0)") // Bring only those categories which has financial approver
            ->get()->result();
        return array(
            'status' => 1,
            'vendor' => $vendor,
            'vendorCats' => $vendorCats
        );
    }

    function save_vendor_details() {
        $input= $this->input->post();
        $data_insert= array(
            'contract_vendor_id' => $input['vendor_id'],
            'proc_im_category_id' => $input['proc_im_category_id'],
        );
        $this->db->trans_start();
        $isAlreadyUpdated= $this->db->select("ifnull(contract_vendor_id, 0) as contract_vendor_id, ifnull(proc_im_category_id, 0) as proc_im_category_id")->where('id', $input['service_contract_master_id'])->get('procurement_service_contract_master')->row();
        if(!empty($isAlreadyUpdated) && ($isAlreadyUpdated->contract_vendor_id != $input['vendor_id'] || $isAlreadyUpdated->proc_im_category_id != $input['proc_im_category_id'])) {
            $this->db->where('id', $input['service_contract_master_id'])->update('procurement_service_contract_master', $data_insert);
// Now approvers will be added based on the department selected in the first step of sevice contract creation
            // $getApprover= $this->db->select("ifnull(bom_approver_1, 0) as bom_approver_1, ifnull(bom_approver_2, 0) as bom_approver_2, ifnull(bom_approver_3, 0) as bom_approver_3, financial_approver")->where('id', $input['proc_im_category_id'])->get('procurement_itemmaster_category')->row();
            // if(!empty($getApprover) && $getApprover->bom_approver_1 != '0') {
            //     $this->db->insert('procurement_service_contract_approval_flow', array('service_contract_master_id' => $input['service_contract_master_id'], 'approver_id' => $getApprover->bom_approver_1, 'status' => 'Pending', 'approver_type' => 'bom_approver_1'));
            // }
            // if(!empty($getApprover) && $getApprover->bom_approver_2 != '0') {
            //     $this->db->insert('procurement_service_contract_approval_flow', array('service_contract_master_id' => $input['service_contract_master_id'], 'approver_id' => $getApprover->bom_approver_2, 'status' => 'Pending', 'approver_type' => 'bom_approver_2'));
            // }
            // if(!empty($getApprover) && $getApprover->bom_approver_3 != '0') {
            //     $this->db->insert('procurement_service_contract_approval_flow', array('service_contract_master_id' => $input['service_contract_master_id'], 'approver_id' => $getApprover->bom_approver_3, 'status' => 'Pending', 'approver_type' => 'bom_approver_3'));
            // }
            // if(!empty($getApprover) && $getApprover->financial_approver) {
            //     $this->db->insert('procurement_service_contract_approval_flow', array('service_contract_master_id' => $input['service_contract_master_id'], 'approver_id' => $getApprover->financial_approver, 'status' => 'Pending', 'approver_type' => 'financial_approver'));
            // }

            // History
            $history= array(
                'action_type' => 'Vendor Details Added',
                'action_description' => "Vendor details has been successfully added to the service contract.",
                'action_by' => $this->authorization->getAvatarStakeHolderId(),
                'service_contract_master_id' => $input['service_contract_master_id'],
            );
            $this->db->insert('procurement_service_contract_history', $history);
        }
        $this->db->trans_complete();

        $vendorCats= $this->db->select("ifnull(group_concat(proc_im_category_id), '') as cat_str")->where('vendor_id', $input['vendor_id'])->group_by('vendor_id')->get('procurement_vendor_category')->row();
        
        if(empty($vendorCats) || $vendorCats->cat_str == '') {
            return array(
                'status' => 1,
                'service_contract_master_id' => $input['service_contract_master_id'],
                'is_item_available' => 0
            );
        }
        $vendorsItemCategoriesStr= $vendorCats->cat_str;
        $vendorItems= $this->db->select("pic.category_name, pis.subcategory_name, pii.item_name, pii.id as proc_im_items_id")
            ->from('procurement_itemmaster_category pic')
            ->join('procurement_itemmaster_subcategory pis', 'pis.proc_im_category_id = pic.id')
            ->join('procurement_itemmaster_items pii', 'pii.proc_im_subcategory_id = pis.id')
            ->where("pic.id in ($vendorsItemCategoriesStr)")
            ->where('pic.category_type', 'Services')
            ->where('pic.id', $input['proc_im_category_id'])
            ->order_by('pic.id, pis.id, pii.id')
            ->get()->result();

        $serviceDateRange= $this->db->select("contract_start_date, contract_end_date")->where('id', $input['service_contract_master_id'])->get('procurement_service_contract_master')->row();
        

        return array(
            'status' => 1,
            'service_contract_master_id' => $input['service_contract_master_id'],
            'is_item_available' => 1,
            'vendorItems' => $vendorItems,
            'serviceDateRange' => $serviceDateRange
        );
    }

    function getExpenseCategory() {
        return $this->db_readonly->select("ec.id, ec.category_name")
            ->join('expense_sub_category esc', 'esc.cat_id = ec.id')
            ->group_by('ec.id')
            ->get('expense_category ec')->result();
    }

    function submit_service_contract_line_items() {
        $input= $this->input->post();
        $service_contract_master_id= $input['service_contract_master_id'];
        $proc_im_items_ids= isset($input['proc_im_items_ids']) ? $input['proc_im_items_ids'] : [];
        $service_frequency= isset($input['service_frequency']) ? $input['service_frequency'] : [];
        $service_frequency_unit_cost= isset($input['service_frequency_unit_cost']) ? $input['service_frequency_unit_cost'] : [];
        $service_total_cost= isset($input['service_total_cost']) ? $input['service_total_cost'] : [];
        $service_description= isset($input['service_description']) ? $input['service_description'] : [];

        $payment_modes= isset($input['payment_modes']) ? $input['payment_modes'] : [];
        $payment_frequencys= isset($input['payment_frequencys']) ? $input['payment_frequencys'] : [];
        $expense_category_ids= isset($input['expense_category_ids']) ? $input['expense_category_ids'] : [];
        $expense_sub_category_ids= isset($input['expense_sub_category_ids']) ? $input['expense_sub_category_ids'] : [];
        $payment_rules_and_policys= isset($input['payment_rules_and_policys']) ? $input['payment_rules_and_policys'] : [];

        if(!empty($proc_im_items_ids)) {
           

        $this->db->trans_start();
        foreach($proc_im_items_ids as $key => $val) {
            $insert_items= array(
                'service_contract_master_id' => $service_contract_master_id,
                'proc_im_items_id' => $val,
                'service_description' => $service_description[$key],
                'service_frequency' => $service_frequency[$key],
                'frequency_unit_cost' => $service_frequency_unit_cost[$key],
                'total_cost' => $service_total_cost[$key],
                'created_by' => $this->authorization->getAvatarStakeHolderId(),
                'modified_by' => $this->authorization->getAvatarStakeHolderId(),
                'created_on' => date('Y-m-d H:i:s')
            );
            $this->db->insert('procurement_service_contract_line_items', $insert_items);
            $service_contract_line_item_id= $this->db->insert_id();
            $insertPamentDetails= array(
                'service_contract_line_items_id' => $service_contract_line_item_id,
                'payment_mode' => $payment_modes[$key],
                'payment_frequency' => $payment_frequencys[$key],
                'expense_subcategory_id' => $expense_sub_category_ids[$key],
                // 'frequency_amount_to_pay' => $frequency_payment[$key],
                // 'budget_year_id' => $budget_year[$key],
                'expense_category_id' => $expense_category_ids[$key],
                // 'total_amount_to_pay' => $total_payment[$key],
                'created_by' => $this->authorization->getAvatarStakeHolderId(),
                'modified_by' => $this->authorization->getAvatarStakeHolderId(),
                'created_on' => date('Y-m-d H:i:s'),
                'payment_policy' => $payment_rules_and_policys[$key]
            );
            $this->db->insert('procurement_service_contract_line_items_payment_details', $insertPamentDetails);
        }

        // echo '<pre>'; print_r($insert_items); die();

        
        // if(!empty($insert_items)) {
            

            // History
            $history= array(
                'action_type' => 'Services Details Added',
                'action_description' => "Service and Payment details has been successfully added to the service contract.",
                'action_by' => $this->authorization->getAvatarStakeHolderId(),
                'service_contract_master_id' => $service_contract_master_id,
            );
            $this->db->insert('procurement_service_contract_history', $history);

        // }

        // Updating the contract value in master table
        $ContractValue= $this->db->select("sum(ifnull(total_cost, 0)) as total")->where('service_contract_master_id', $service_contract_master_id)->get('procurement_service_contract_line_items')->row();
        $totContractValue= 0;
        if(!empty($ContractValue)) {
            $totContractValue= $ContractValue->total;
        }
        $data_insert= array(
            'contract_status' => 'Pending',
            'total_contract_value' => $totContractValue
        );
        $this->db->where('id', $service_contract_master_id)->update('procurement_service_contract_master', $data_insert);
        // End of updating the contract value in master table

        $this->db->trans_complete();
        }
        if(!$this->db->trans_status()) {
            $status= array(
                        'status' => 0,
                        'service_contract_master_id' => $service_contract_master_id
                    );
            $this->db->trans_rollback();
        }
        // $items= $this->db->select("pscli.id as service_contract_line_item_id, pscli.frequency_unit_cost, pscli.total_cost, pscli.service_frequency, pii.item_name")
        //     ->from('procurement_service_contract_line_items pscli')
        //     ->join('procurement_itemmaster_items pii', 'pii.id = pscli.proc_im_items_id')
        //     ->where('pscli.service_contract_master_id', $service_contract_master_id)
        //     ->get()->result();
        // $budgets= $this->db->select("id, year")->where('CFO_approver_status !=', 'Rejected')->get('procurement_budget_year')->result();
        // $expenseCAtegory= $this->db->select("ec.id, ec.category_name")
        //     ->join('expense_sub_category esc', 'esc.cat_id = ec.id')
        //     ->group_by('ec.id')
        //     ->get('expense_category ec')->result();

        $approvers= $this->db->select("ifnull(pscaf.approver_type, 'financial_approver') as approver_type, TRIM( CONCAT(sm.first_name, ' ', ifnull(sm.last_name, '')) ) as staff, sm.id as staff_id, pscaf.id as service_contract_approval_flow_id, ifnull(sdep.department, '-') as department, ifnull(sdeg.designation, '-') as designation")
            ->from('procurement_service_contract_approval_flow pscaf')
            ->join('staff_master sm', 'sm.id = pscaf.approver_id')
            ->join('staff_designations sdeg', 'sdeg.id = sm.designation', 'left')
            ->join('staff_departments sdep', 'sdep.id = sm.department', 'left')
            ->where('pscaf.service_contract_master_id', $service_contract_master_id)
            ->get()->result();

        $status= array(
            'status' => 1,
            'service_contract_master_id' => $service_contract_master_id,
            // 'service_contract_master_id' => $service_contract_master_id,
            'approvers' => $approvers,
            // 'serviceItems' => $items,
            // 'budgets' => $budgets,
            // 'expenseCategory' => $expenseCAtegory
        );
        return $status;
    }

    function save_payment_details() {
        $input= $this->input->post();
        $service_contract_master_id= $input['service_contract_master_id'];
        $service_contract_line_item_id= isset($input['service_contract_line_item_id']) ? $input['service_contract_line_item_id'] : [];
        $payment_mode= isset($input['payment_mode']) ? $input['payment_mode'] : [];
        $payment_frequency= isset($input['payment_frequency']) ? $input['payment_frequency'] : [];
        $frequency_payment= isset($input['frequency_payment']) ? $input['frequency_payment'] : [];
        $total_payment= isset($input['total_payment']) ? $input['total_payment'] : [];
        $budget_year= isset($input['budget_year']) ? $input['budget_year'] : [];
        $expense_category_id= isset($input['expense_category_id']) ? $input['expense_category_id'] : [];
        $payment_rules_and_policy= isset($input['payment_rules_and_policy']) ? $input['payment_rules_and_policy'] : [];

        if(empty($service_contract_line_item_id)) {
            return false;
        }

        foreach($service_contract_line_item_id as $key => $val) {
            $insertPamentDetails[]= array(
                'service_contract_line_items_id' => $val,
                'payment_mode' => $payment_mode[$key],
                'payment_frequency' => $payment_frequency[$key],
                'frequency_amount_to_pay' => $frequency_payment[$key],
                'budget_year_id' => $budget_year[$key],
                'expense_category_id' => $expense_category_id[$key],
                'total_amount_to_pay' => $total_payment[$key],
                'created_by' => $this->authorization->getAvatarStakeHolderId(),
                'modified_by' => $this->authorization->getAvatarStakeHolderId(),
                'created_on' => date('Y-m-d H:i:s'),
                'payment_policy' => $payment_rules_and_policy[$key]
            );
        }

        // echo '<pre>'; print_r($insertPamentDetails); die();

        $this->db->trans_start();
        if(!empty($insertPamentDetails)) {
            $this->db->insert_batch('procurement_service_contract_line_items_payment_details', $insertPamentDetails);
            
             // History
             $history= array(
                'action_type' => 'Payment Details Added',
                'action_description' => "Payment details has been successfully added to the service contract.",
                'action_by' => $this->authorization->getAvatarStakeHolderId(),
                'service_contract_master_id' => $service_contract_master_id,
            );
            $this->db->insert('procurement_service_contract_history', $history);

        }
        $this->db->trans_complete();
        if(!$this->db->trans_status()) {
            $status= array(
                        'status' => 0,
                        'service_contract_master_id' => $service_contract_master_id
                    );
            $this->db->trans_rollback();
        }
        $approvers= $this->db->select("TRIM( CONCAT(sm.first_name, ' ', ifnull(sm.last_name, '')) ) as staff, sm.id as staff_id, pscaf.id as service_contract_approval_flow_id, ifnull(sdep.department, '-') as department, ifnull(sdeg.designation, '-') as designation")
            ->from('procurement_service_contract_approval_flow pscaf')
            ->join('staff_master sm', 'sm.id = pscaf.approver_id')
            ->join('staff_designations sdeg', 'sdeg.id = sm.designation', 'left')
            ->join('staff_departments sdep', 'sdep.id = sm.department', 'left')
            ->where('pscaf.service_contract_master_id', $service_contract_master_id)
            ->get()->result();

        $status= array(
            'status' => 1,
            'service_contract_master_id' => $service_contract_master_id,
            'approvers' => $approvers
        );
        return $status;
    }

    function add_document() {
        $file= $_FILES;
        // echo '<pre>'; print_r($file); die();
        $service_contract_master_id= $this->input->post('service_contract_master_id');
        $additional_description_notes= $this->input->post('additional_description_notes');
        if($file) {
            // $this->load->library('filemanager');
            // $additional_attachements = $this->s3FileUpload($_FILES['additional_attachements'],'service_conttract_documents');
            
            $insert_docs= array(
                'service_contract_master_id' => $service_contract_master_id,
                'name' => $this->input->post('fileName'),
                'document_type' => $this->input->post('fileExtentionType'),
                'document_size' => $this->input->post('fileSizeBytes'),
                'remarks' => $additional_description_notes,
                'created_by' => $this->authorization->getAvatarStakeHolderId(),
                'document' => ( $this->input->post('path')) ?  $this->input->post('path') : NULL
            );
            $this->db->insert('procurement_service_contract_attachements', $insert_docs);
            $service_contract_attachements_id= $this->db->insert_id();
            
             // History
             $history= array(
                'action_type' => 'Attachment Details Added',
                'action_description' => "Attachment details has been successfully added to the service contract.",
                'action_by' => $this->authorization->getAvatarStakeHolderId(),
                'service_contract_master_id' => $service_contract_master_id,
            );
            $this->db->insert('procurement_service_contract_history', $history);

            $this->load->library('filemanager');
            $url= $this->filemanager->getFilePath($this->input->post('path'));
            $status= array(
                'status' => 1,
                'service_contract_master_id' => $service_contract_master_id,
                'service_contract_attachements_id' => $service_contract_attachements_id,
                'absolute_path' => $url
            );
        } else {
            $status= array(
                'status' => 0,
                'service_contract_master_id' => $service_contract_master_id
            );
        }
        return $status;
    }

    public function s3FileUpload($file,$folder_name='Other_Documents') {
        if($file['tmp_name'] == '' || $file['name'] == '') {
          return ['status' => 'empty', 'file_name' => ''];
        }        
        return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],$folder_name);
    }

    function remove_document() {
        $service_contract_attachements_id= $this->input->post('service_contract_attachements_id');
        return $this->db->where('id', $service_contract_attachements_id)->delete('procurement_service_contract_attachements');
    }

    function getAllServiceContracts() {
        $from_date= $this->input->post('from_date');
        $to_date= $this->input->post('to_date');

        $from_date= date('Y-m-d', strtotime($from_date));
        $to_date= date('Y-m-d', strtotime($to_date));
        $contract_type= $this->input->post('contract_type');
        $loggedin_user_id= $this->authorization->getAvatarStakeHolderId();
        $service_contract_master_id_arr= array();
        if($contract_type != 'all' && !$this->authorization->isSuperAdmin()) {
            $service_contract_master_id_obj= $this->db_readonly->select("distinct(service_contract_master_id) as service_contract_master_id")->where('approver_id', $loggedin_user_id)->get('procurement_service_contract_approval_flow')->result();
            if(!empty($service_contract_master_id_obj)) {
                foreach($service_contract_master_id_obj as $key => $val) {
                    $service_contract_master_id_arr[]= $val->service_contract_master_id;
                }
            }
        }

        $this->db_readonly->select("pscm.id as service_contract_master_id, pscm.contract_number, pscm.contract_date, pscm.contract_status, pscm.renewal_remainder, ifnull(sd.department, '-') as department, if(TRIM(concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, ''))) != '', concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')), 'Admin') as created_by_staff, ifnull(pvm.vendor_code, '-') as vendor_code, ifnull(pvm.vendor_name, '-') as vendor_name")
            ->from('procurement_service_contract_master pscm')
            ->join('staff_departments sd', 'sd.id = pscm.department_id', 'left')
            ->join('staff_master sm', 'sm.id = pscm.created_by', 'left')
            ->join('procurement_vendor_master pvm', 'pvm.id = pscm.contract_vendor_id', 'left')
            ->where("DATE_FORMAT(pscm.created_on, '%Y-%m-%d') >=", $from_date)
            ->where("DATE_FORMAT(pscm.created_on, '%Y-%m-%d') <=", $to_date);
        if(!empty($service_contract_master_id_arr) && $contract_type != 'all') {
            $this->db_readonly->where_in('pscm.id', $service_contract_master_id_arr);
        }
        $serviceContracts= $this->db_readonly->order_by('pscm.id', 'desc')
            ->get()->result();
        return $serviceContracts;
    }

    function getContractBasicDetails($service_contract_master_id) {
        return $this->db_readonly->select("pscm.id as service_contract_master_id, pscm.contract_number, date_format(pscm.contract_date, '%d-%m-%Y') as contract_date, date_format(pscm.contract_start_date, '%d-%m-%Y') as contract_start_date, date_format(pscm.contract_end_date, '%d-%m-%Y') as contract_end_date, date_format(pscm.created_on, '%d-%m-%Y %h:%i %a') as created_on, pscm.contract_type, ifnull(sd.department, '-') as department, if(TRIM(concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, ''))) != '', concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')), 'Admin') as created_by, pscm.contract_status, pscm.renewal_remainder, ifnull(pvm.vendor_name, '-') as vendor_name, ifnull(pvm.vendor_code, '-') as vendor_code")
            ->from('procurement_service_contract_master pscm')
            ->join('staff_departments sd', 'sd.id = pscm.department_id', 'left')
            ->join('staff_master sm', 'sm.id = pscm.created_by', 'left')
            ->join('procurement_vendor_master pvm', 'pvm.id = pscm.contract_vendor_id', 'left')
            ->where('pscm.id', $service_contract_master_id)
            ->get()->row();
    }

    function getContractLineItems($service_contract_master_id) {
        return $this->db_readonly->select("pscli.id as service_contract_line_items_id, pii.item_name, ifnull(pscli.service_description, '-') as service_description, pscli.service_frequency, ifnull(pscli.frequency_unit_cost, '-') as frequency_unit_cost, ifnull(pscli.total_cost, 0) as total_cost")
            ->from('procurement_service_contract_line_items pscli')
            ->join('procurement_itemmaster_items pii', 'pii.id = pscli.proc_im_items_id')
            ->where('pscli.service_contract_master_id', $service_contract_master_id)
            ->get()->result();
    }

    function getContractLineItemPaymentDetails($service_contract_master_id) {
        return $this->db_readonly->select("psclipd.id as contract_line_items_payment_details_id, pscli.id as service_contract_line_item_id, pii.item_name, ifnull(pscli.service_description, '-') as service_description, pscli.service_frequency, ifnull(pscli.frequency_unit_cost, '-') as frequency_unit_cost, ifnull(pscli.total_cost, 0) as total_cost, psclipd.payment_mode, psclipd.payment_frequency, psclipd.frequency_amount_to_pay, psclipd.total_amount_to_pay, ifnull(psclipd.budget_approval_status, 'Draft') as budget_approval_status, ifnull(ec.category_name, '-') as category_name, ifnull(esc.sub_category, '-') as sub_category, ifnull(psclipd.payment_policy, '-') as payment_policy, ifnull(pscli.service_description, '-') as service_description, ifnull(psclipd.payment_policy, '-') as payment_policy, ifnull(psclipd.budget_approval_status, 'Pending') as budget_approval_status")
            ->from('procurement_service_contract_line_items pscli')
            ->join('procurement_itemmaster_items pii', 'pii.id = pscli.proc_im_items_id')
            ->join('procurement_service_contract_line_items_payment_details psclipd', 'psclipd.service_contract_line_items_id = pscli.id')
            ->join('expense_sub_category esc', 'esc.id = psclipd.expense_subcategory_id', 'left')
            ->join('expense_category ec', 'ec.id = psclipd.expense_category_id', 'left')
            ->where('pscli.service_contract_master_id', $service_contract_master_id)
            ->get()->result();
    }

    function getContractAttachements($service_contract_master_id) {
        $docs = $this->db_readonly->select("id as contract_attachements_id, name, ifnull(document, '-') as document, if(remarks is not null && TRIM(remarks) != '', remarks, '-') as remarks")
            ->where('service_contract_master_id', $service_contract_master_id)
            ->get('procurement_service_contract_attachements')->result();

        if (!empty($docs)) {
            $this->load->library('filemanager');
            foreach ($docs as $key => $val) {
                if ($val->document != '-') {
                    $url = $this->filemanager->getFilePath($val->document);
                    $val->url = $url;
                }
            }
        }
        return $docs;
    }

    function getContractApprovers($service_contract_master_id) {
        return $this->db_readonly->select("pscaf.id as contract_approval_flow_id, pscaf.approver_id, ifnull(pscaf.status, 'Pending') as status,ifnull(pscaf.approver_type, 'financial_approver') as approver_type, ifnull(pscaf.comments, '-') as comments, ifnull(sd.department, '-') as department, ifnull(sdg.designation, '-') as designation, if(TRIM(concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, ''))) != '', concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')), 'Admin') as approver")
            ->from('procurement_service_contract_approval_flow pscaf')
            ->join('staff_master sm', 'sm.id = pscaf.approver_id', 'left')
            ->join('staff_departments sd', 'sd.id = sm.department', 'left')
            ->join('staff_designations sdg', 'sdg.id = sm.designation', 'left')
            ->where('pscaf.service_contract_master_id', $service_contract_master_id)
            ->get()->result();
    }

    function submit_service_contract() {
        $input= $this->input->post();
        $service_contract_master_id= $input['service_contract_master_id'];
        $ContractValue= $this->db->select("sum(ifnull(total_cost, 0)) as total")->where('service_contract_master_id', $service_contract_master_id)->get('procurement_service_contract_line_items')->row();
        $totContractValue= 0;
        if(!empty($ContractValue)) {
            $totContractValue= $ContractValue->total;
        }
        $data_insert= array(
            'contract_status' => 'Pending',
            'total_contract_value' => $totContractValue
        );
        $this->db->where('id', $service_contract_master_id)->update('procurement_service_contract_master', $data_insert);

        // History
        $history= array(
            'action_type' => 'Contract Created',
            'action_description' => "A service contract has been created.",
            'action_by' => $this->authorization->getAvatarStakeHolderId(),
            'service_contract_master_id' => $service_contract_master_id,
        );
        $this->db->insert('procurement_service_contract_history', $history);

        return array(
            'status' => 1,
            'service_contract_master_id' => $service_contract_master_id
        );
    }

    function getExpenseSubCategory() {
        $expense_category_id= $this->input->post('expense_category_id');
        return $this->db_readonly->select("id, sub_category")->where('cat_id', $expense_category_id)->get('expense_sub_category')->result();
    }

    function getContractHistory($service_contract_master_id) {
        return $this->db_readonly->select("psch.id as contract_history_id, action_type, action_description, date_format(action_on, '%d-%M-%Y %h:%i %p') as action_on, if(psch.action_by != 0 && psch.action_by is not null, concat(sm.first_name, ' ', ifnull(sm.last_name, '')), 'Admin') as action_by")
            ->from('procurement_service_contract_history psch')
            ->join('staff_master sm', 'sm.id = psch.action_by', 'left')
            ->where('psch.service_contract_master_id', $service_contract_master_id)
            ->order_by('psch.id', 'desc')
            ->get()->result();
    }

    function getDocumentURL($contract_attachements_id) {
        return $this->db_readonly->select("document")->where('id', $contract_attachements_id)->get('procurement_service_contract_attachements')->row();
    }

    function getDataToAddServiceLineItems() {
        $service_contract_master_id= $this->input->post('service_contract_master_id');
        $services= $this->db_readonly->select("pic.id, pic.category_name, pis.id as subcategory_id, pis.subcategory_name, pii.id as item_id, pii.item_name")
            ->from('procurement_service_contract_master pscm')
            ->join('procurement_itemmaster_category pic', 'pic.id = pscm.proc_im_category_id')
            ->join('procurement_itemmaster_subcategory pis', 'pic.id = pis.proc_im_category_id')
            ->join('procurement_itemmaster_items pii', 'pis.id = pii.proc_im_subcategory_id')
            ->where('pscm.id', $service_contract_master_id)
            ->where('pic.category_type', 'Services')
            ->where('pic.status', '1') // Active category
            ->where('pii.status', '1') // Active subcategory
            ->get()->result();
        $expenseCategory= $this->db_readonly->select("id, category_name")->get('expense_category')->result();
        return array(
            'status' => 1,
            'services' => $services,
            'expenseCategory' => $expenseCategory
        );
    }

    function submit_individual_service_contract_line_items() {
        $item_id= $this->input->post('item_id');
        $item_name= $this->input->post('item_name');
        $service_frequency= $this->input->post('service_frequency');
        $service_frequency_unit_cost= $this->input->post('service_frequency_unit_cost');
        $service_total_cost= $this->input->post('service_total_cost');
        $service_description= $this->input->post('service_description');
        $payment_mode= $this->input->post('payment_mode');
        $payment_mode_name= $this->input->post('payment_mode_name');
        $payment_frequency= $this->input->post('payment_frequency');
        $payment_frequency_name= $this->input->post('payment_frequency_name');
        $expense_category_id= $this->input->post('expense_category_id');
        $expense_category_id_name= $this->input->post('expense_category_id_name');
        $expense_sub_category_id= $this->input->post('expense_sub_category_id');
        $expense_sub_category_id_name= $this->input->post('expense_sub_category_id_name');
        $payment_rules_and_policy= $this->input->post('payment_rules_and_policy');
        $service_contract_master_id= $this->input->post('service_contract_master_id');

        $data_insert= array(
            'service_contract_master_id' => $service_contract_master_id,
            'proc_im_items_id' => $item_id,
            'service_description' => isset($service_description) ? $service_description : NULL,
            'service_frequency' => isset($service_frequency) ? $service_frequency : NULL,
            'frequency_unit_cost' => isset($service_frequency_unit_cost) ? $service_frequency_unit_cost : 0,
            'total_cost' => isset($service_total_cost) ? $service_total_cost : 0,
            'created_by' => $this->authorization->getAvatarStakeHolderId(),
            'modified_by' => $this->authorization->getAvatarStakeHolderId(),
            'created_on' => date('Y-m-d H:i:s')
        );
        $this->db->trans_start();
        $this->db->insert('procurement_service_contract_line_items', $data_insert);
        $service_contract_line_item_id= $this->db->insert_id();
        $insertPamentDetails= array(
            'service_contract_line_items_id' => $service_contract_line_item_id,
            'payment_mode' => isset($payment_mode) ? $payment_mode : NULL,
            'payment_frequency' => isset($payment_frequency) ? $payment_frequency : NULL,
            'expense_subcategory_id' => isset($expense_sub_category_id) ? $expense_sub_category_id : NULL,
            'expense_category_id' => isset($expense_category_id) ? $expense_category_id : NULL,
            'created_by' => $this->authorization->getAvatarStakeHolderId(),
            'modified_by' => $this->authorization->getAvatarStakeHolderId(),
            'created_on' => date('Y-m-d H:i:s'),
            'payment_policy' => isset($payment_rules_and_policy) ? $payment_rules_and_policy : NULL,
        );
        $this->db->insert('procurement_service_contract_line_items_payment_details', $insertPamentDetails);

      

        // Updating the contract value in master table
        $ContractValue= $this->db->select("sum(ifnull(total_cost, 0)) as total")->where('service_contract_master_id', $service_contract_master_id)->get('procurement_service_contract_line_items')->row();
        $totContractValue= 0;
        if(!empty($ContractValue)) {
            $totContractValue= $ContractValue->total;
        }
        $data_insert= array(
            // 'contract_status' => 'Pending',
            'total_contract_value' => $totContractValue
        );
        $this->db->where('id', $service_contract_master_id)->update('procurement_service_contract_master', $data_insert);
        // End of updating the contract value in master table

         // History
        $history= array(
            'action_type' => 'New Service Added',
            'action_description' => "A new service - $item_name has been added to the contract. So, total contract value is updated by ₹". number_format($totContractValue, 2). ".",
            'action_by' => $this->authorization->getAvatarStakeHolderId(),
            'service_contract_master_id' => $service_contract_master_id,
        );
        $this->db->insert('procurement_service_contract_history', $history);
        
        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    function reject_service_contract() {
        $service_contract_master_id= $this->input->post('service_contract_master_id');
        $contract_approval_flow_id= $this->input->post('contract_approval_flow_id');
        $approver_type= $this->input->post('approver_type');
        $reject_reason= $this->input->post('reject_reason');

        $this->db->trans_start();
        $this->db->where('id', $contract_approval_flow_id)->update('procurement_service_contract_approval_flow', array('status' => 'Rejected', 'comments' => $reject_reason));
        $this->db->where('id', $service_contract_master_id)->update('procurement_service_contract_master', array('contract_status' => 'Rejected'));
        $array= array(
            'approver_type' => $approver_type,
            'remarks' => $reject_reason,
            'created_by' => $this->authorization->getAvatarStakeHolderId(),
            'service_contract_master_id' => $service_contract_master_id,
            'approval_status' => 'Rejected'
        );
        $this->db->insert('procurement_service_contract_approvers_comments', $array);

         
        // History
        $history= array(
            'action_type' => 'Approval Changed',
            'action_description' => "A service contract approval status has been changed to Rejected. And left the following comment- $reject_reason.",
            'action_by' => $this->authorization->getAvatarStakeHolderId(),
            'service_contract_master_id' => $service_contract_master_id,
        );
        $this->db->insert('procurement_service_contract_history', $history);

        $this->db->trans_complete();

        return $this->db->trans_status();

    }

    function send_service_contract_for_modify() {
        $service_contract_master_id= $this->input->post('service_contract_master_id');
        $contract_approval_flow_id= $this->input->post('contract_approval_flow_id');
        $approver_type= $this->input->post('approver_type');
        $modify_reason= $this->input->post('modify_reason');

        $this->db->trans_start();
        $this->db->where('id', $contract_approval_flow_id)->update('procurement_service_contract_approval_flow', array('status' => 'Sent for Modification', 'comments' => $modify_reason));
        $this->db->where('id', $service_contract_master_id)->update('procurement_service_contract_master', array('contract_status' => 'Sent for Modification'));
        // if($approver_type == 'financial_approver') {
        // }
        $array= array(
            'approver_type' => $approver_type,
            'remarks' => $modify_reason,
            'created_by' => $this->authorization->getAvatarStakeHolderId(),
            'service_contract_master_id' => $service_contract_master_id,
            'approval_status' => 'Sent for Modification'
        );
        $this->db->insert('procurement_service_contract_approvers_comments', $array);

        
        // History
        $history= array(
            'action_type' => 'Approval Changed',
            'action_description' => "A service contract approval status has been changed to Sent for Modification. And left the following comment- $modify_reason.",
            'action_by' => $this->authorization->getAvatarStakeHolderId(),
            'service_contract_master_id' => $service_contract_master_id,
        );
        $this->db->insert('procurement_service_contract_history', $history);

        $this->db->trans_complete();

        return $this->db->trans_status();

    }

    function approve_service_contract() {
        $service_contract_master_id= $this->input->post('service_contract_master_id');
        $contract_approval_flow_id= $this->input->post('contract_approval_flow_id');
        $approver_type= $this->input->post('approver_type');
        $approve_reason= $this->input->post('approve_reason');

        // echo $approver_type; die();

        $this->db->trans_start();
        $this->db->where('id', $contract_approval_flow_id)->update('procurement_service_contract_approval_flow', array('status' => 'Approved', 'comments' => $approve_reason));
        if($approver_type == 'Financial Approver') {
            $this->db->where('id', $service_contract_master_id)->update('procurement_service_contract_master', array('contract_status' => 'Approved'));
        }
        $array= array(
            'approver_type' => $approver_type,
            'remarks' => $approve_reason,
            'created_by' => $this->authorization->getAvatarStakeHolderId(),
            'service_contract_master_id' => $service_contract_master_id,
            'approval_status' => 'Approved'
        );
        $this->db->insert('procurement_service_contract_approvers_comments', $array);

        // History
        $history= array(
            'action_type' => 'Approval Changed',
            'action_description' => "A service contract approval status has been changed to Approved by $approver_type. Comment- $approve_reason.",
            'action_by' => $this->authorization->getAvatarStakeHolderId(),
            'service_contract_master_id' => $service_contract_master_id,
        );
        $this->db->insert('procurement_service_contract_history', $history);

        $this->db->trans_complete();

        return $this->db->trans_status();

    }

    function get_all_comments() {
        $service_contract_master_id= $this->input->post('service_contract_master_id');
        $contract_number= $this->input->post('contract_number');

        $this->db_readonly->select("pscc.id as comment_id, pscc.approver_type, pscc.approval_status, ifnull(pscc.remarks, '-') as remarks, if(TRIM(concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, ''))) != '', concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')), 'Admin') as created_by, date_format(pscc.created_on, '%d-%M-%Y %h:%i %p') as created_on")
            ->from('procurement_service_contract_approvers_comments pscc')
            ->join('staff_master sm', 'sm.id = pscc.created_by', 'left')
            ->where('pscc.service_contract_master_id', $service_contract_master_id)
            ->order_by('pscc.id', 'desc');
        $comments= $this->db_readonly->get()->result();
        return $comments;
    }

    function remove_service_line_item() {
        $service_contract_line_item_id= $this->input->post('service_contract_line_item_id');
        $item_name= $this->input->post('item_name');
        $service_contract_master_id= $this->input->post('service_contract_master_id');

        $this->db->trans_start();
        $this->db->where('id', $service_contract_line_item_id)->delete('procurement_service_contract_line_items');
        $this->db->where('service_contract_line_items_id', $service_contract_line_item_id)->delete('procurement_service_contract_line_items_payment_details');

         // Updating the contract value in master table
         $ContractValue= $this->db->select("sum(ifnull(total_cost, 0)) as total")->where('service_contract_master_id', $service_contract_master_id)->get('procurement_service_contract_line_items')->row();
         $totContractValue= 0;
         if(!empty($ContractValue)) {
             $totContractValue= $ContractValue->total;
         }
         $data_insert= array(
            //  'contract_status' => 'Partially Modified',
             'total_contract_value' => $totContractValue
         );
         $this->db->where('id', $service_contract_master_id)->update('procurement_service_contract_master', $data_insert);
         // End of updating the contract value in master table

         // History
         $history= array(
            'action_type' => 'Service Removed',
            'action_description' => "A service - $item_name has been removed from the contract. So, total contract value is updated by ₹". number_format($totContractValue, 2). ".",
            'action_by' => $this->authorization->getAvatarStakeHolderId(),
            'service_contract_master_id' => $service_contract_master_id,
        );
        $this->db->insert('procurement_service_contract_history', $history);
        $this->db->trans_complete();

        return $this->db->trans_status();
    }

    function markAsModified() {
        $service_contract_master_id= $this->input->post('service_contract_master_id');
        $this->db->trans_start();
        $data_insert= array(
            'contract_status' => 'Pending',
         );
         $this->db->where('id', $service_contract_master_id)->update('procurement_service_contract_master', $data_insert);
          // History
          $history= array(
            'action_type' => 'Contract Modified',
            'action_description' => "A service contract has been modified.",
            'action_by' => $this->authorization->getAvatarStakeHolderId(),
            'service_contract_master_id' => $service_contract_master_id,
        );
        $this->db->insert('procurement_service_contract_history', $history);

        $this->db->trans_complete();

        return $this->db->trans_status();
    }

    function activateServiceContract() {
        $service_contract_master_id= $this->input->post('service_contract_master_id');

        $this->db->trans_start();
        $data_insert= array(
            'contract_status' => 'Active',
         );
         $this->db->where('id', $service_contract_master_id)->update('procurement_service_contract_master', $data_insert);
          // History
          $history= array(
            'action_type' => 'Contract Activated',
            'action_description' => "A service contract has been activated.",
            'action_by' => $this->authorization->getAvatarStakeHolderId(),
            'service_contract_master_id' => $service_contract_master_id,
        );
        $this->db->insert('procurement_service_contract_history', $history);

        $this->db->trans_complete();

        return $this->db->trans_status();
    }

    function deactivateServiceContract() {
        $service_contract_master_id= $this->input->post('service_contract_master_id');

        $this->db->trans_start();
        $data_insert= array(
            'contract_status' => 'De-active',
         );
         $this->db->where('id', $service_contract_master_id)->update('procurement_service_contract_master', $data_insert);
          // History
          $history= array(
            'action_type' => 'Contract De-activated',
            'action_description' => "A service contract has been de-activated.",
            'action_by' => $this->authorization->getAvatarStakeHolderId(),
            'service_contract_master_id' => $service_contract_master_id,
        );
        $this->db->insert('procurement_service_contract_history', $history);

        $this->db->trans_complete();

        return $this->db->trans_status();
    }

    function deleteServiceContract() {
        $service_contract_master_id= $this->input->post('service_contract_master_id');

        $this->db->trans_start();
        $data_insert= array(
            'contract_status' => 'Deleted',
         );
         $this->db->where('id', $service_contract_master_id)->update('procurement_service_contract_master', $data_insert);
          // History
          $history= array(
            'action_type' => 'Contract Deleted',
            'action_description' => "A service contract has been deleted but the deleted contract has been moved in Finished state and not deleted from the server. Admin is able to access it for later review.",
            'action_by' => $this->authorization->getAvatarStakeHolderId(),
            'service_contract_master_id' => $service_contract_master_id,
        );
        $this->db->insert('procurement_service_contract_history', $history);

        $this->db->trans_complete();

        return $this->db->trans_status();
    }

}
?>