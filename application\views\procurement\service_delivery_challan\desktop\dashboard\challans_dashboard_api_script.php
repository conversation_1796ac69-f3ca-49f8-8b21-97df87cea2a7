<?php $this->load->view("procurement/service_delivery_challan/utils.php") ?>

<script type="text/javascript">
    function getAllChallans() {
        $("#getChallansBtn").text("Please wait...").prop("disabled", true);

        $("#challanTableBody").html(generateMessageHelper("Loading..."));

        const fromDate = $("#from_date").val();

        const toDate = $("#to_date").val();

        const challanType = $("#poType").val();

        $.ajax({
            url: "<?php echo site_url('procurement/Requisition_controller_v2/getAllServiceDeliveryChallans') ?>",
            type: "POST",
            data: { fromDate, toDate, challanType },
            success: (data) => {
                data = JSON.parse(data);

                if (!data.length) {
                    $("#challanTableBody").html(generateMessageHelper("No challans found."));
                    $("#getChallansBtn").text("Get Challans").prop("disabled", false);
                    return;
                }

                let html = `
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                            <th>#</th>
                            <th>SDC Number</th>
                            <th>PO Number</th>
                            <th>Department</th>
                            <th>Vendor</th>
                            <th>Created By</th>
                            <th>Created On</th>
                            <th>Action</th>
                            </tr>
                        </thead>
               `;

                data.forEach((challan, i) => {
                    html += `
                    <tr>
                        <td>${i + 1}</td>
                        <td>${challan.sdc_number || "NA"}</td>
                        <td>${challan.po_id_number || "NA"}</td>
                        <td>${challan.department_name || "NA"}</td>
                        <td>${challan.vendor_name || "NA"}</td>
                        <td>${challan.created_by || "NA"}</td>
                        <td>${challan.created_on || "NA"}</td>
                        <td>
                            <button title="Click to view challan details" class="btn btn-dark" onclick="viewChallan(${challan.id})">View Details</button>
                        </td>
                    </tr>
                `;
                });

                $("#challanTableBody").html(html);

                $("#getChallansBtn").text("Get Challans").prop("disabled", false);
            }
        })
    }

    function viewChallan(challanId) {
        const url = "<?php echo site_url('procurement/Requisition_controller_v2/view_service_delivery_challan') ?>/" + challanId;
        window.open(url, '_blank');
    }
</script>