

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        $('#invoice_date, #due_date').datetimepicker({
            format: 'DD-MM-YYYY',
            useCurrent: true
        });
    });

    function save_basic_details(current, step_selector, step_number) {
        var $form = $('#'+step_selector+'-form');
        if ($form.parsley().validate()) {
            var isDatesValid= validateDateRange();
            if(isDatesValid) {
                
            $(current).prop('disabled', true).html('Please Wait...');
            var form = $('#'+step_selector+'-form')[0];
            var formData = new FormData(form);
            formData.append('step_number',step_selector);
            // formData.append('step_selector',step_selector);

            $.ajax({
                url: '<?php echo site_url('procurement/invoice_controller_v2/save_invoice_basic_details'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    let p_data= JSON.parse(data);
                    if(Object.keys(p_data)?.length) {
                        if(p_data.status == '0') {
                            Swal.fire({
                                icon: 'error',
                                title: 'Something went wrong'
                            });
                        } else {
                            
                            __construct_delivery_items(p_data);
                            $(".invoice_master_id").val(p_data.invoice_master_id);
                            $("#insert_update_type_basic").val('update');
                            $("#invoice_number").val(p_data.derivedInvoiceNumber);
                            $(".selected_purchase_order_id").val(p_data.purchase_order_id);
                            $(".selected_vendor_id").val(p_data.vendor_id);
                            $("#step-1").addClass('hidden');
                            $("#step-2").removeClass('hidden');

                            $("div.circle-" +step_number).html(`<span class="fa fa-check"></span>`).css('background', 'green');
                            $("div.circle-" + (Number(step_number) + 1)).html(`o`).css('background', 'green');

                            // __check_approvers_availability(p_data.is_approvers_added);
                        }

                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Something went wrong'
                        });
                    }
                    $(current).prop('disabled', false).html('Next');
                }
            });
        }
        }
    }

    function __check_approvers_availability(is_approvers_added) {
        if(is_approvers_added == '0') {
            return Swal.fire({
                icon: 'error',
                title: "Looks like there is no approvers added for this purchase order",
                showCancelButton: true,
                showConfirmButton: true,
                confirmButtonText: 'Change Purchase Order',
                cancelButtonText: 'Cancel Invoice Creation',
            }).then((result) => {
                if(result.isConfirmed) {
                    $("#delivery-challan-prev-btn").click();
                } else {
                    window.location.href= '<?php echo site_url('procurement/invoice_controller_v2/manage_all_invoices'); ?>';
                }
            });
        }
    }

    function __construct_delivery_items(ObjectData) {
        let reference_type= ObjectData.reference_type;
        let gdc= ObjectData.gdc;
        let sdc= ObjectData.sdc;
        let invoice_master_id= ObjectData.invoice_master_id;
        let vendor_id= ObjectData.vendor_id;
        let purchase_order_id= ObjectData.purchase_order_id;
        let derivedInvoiceNumber= ObjectData.derivedInvoiceNumber;

        if(!gdc.length && ! sdc.length) {
            return Swal.fire({
                icon: 'error',
                title: "Looks like all the items are already invoiced of this purchase order's delivery challan",
                showCancelButton: true,
                showConfirmButton: true,
                confirmButtonText: 'Change Purchase Order',
                cancelButtonText: 'Cancel Invoice Creation',
            }).then((result) => {
                if(result.isConfirmed) {
                    $("#delivery-challan-prev-btn").click();
                } else {
                    window.location.href= '<?php echo site_url('procurement/invoice_controller_v2/manage_all_invoices'); ?>';
                }
            });
        }
        
        let price_summary= 0;
        let cgst_summary= 0;
        let sgst_summary= 0;
        let discount_summary= 0;
        let total_after_discount_summary= 0;
        let invoiced_quantity_summary= 0;
        if(gdc.length) {
            $(".selected_reference_type").val('Goods');
            $("span#delivery-type").html('(Goods)');
            $("#deliveries-div").html(`
                                <div style="min-width: 100%;" id="gdc-table">   </div>
                                <div style="height: 35px; min-width: 100%;">   </div>
                                `);
            let gdc_table= `<table class="table table-bordered">
                                <thead class="thead-dark">
                                    <tr>
                                        <th>Item</th>
                                        <th>Bill No</th>
                                        <th>Initial</th>
                                        <th>Invoiced</th>

                                        <th>unit Price</th>
                                        <th>cGST Amount</th>
                                        <th>sGST Amount</th>
                                        <th>Unit Invoiced Price</th>
                                        <th>Quantity for Invoicing</th>

                                        <th>Total cGST Amount</th>
                                        <th>Total sGST Amount</th>
                                        <th>Total Discount Amount</th>

                                        <th>Total After Discount</th>
                                    </tr>
                                </thead>
                                <tbody>`;
            for(var v of gdc) {
                var total_after_discount= (Number(v.initial_quantity) - Number(v.invoiced_quantity)) * Number(v.price) + Number(v.cgst) + Number(v.sgst);

                price_summary += Number(v.price);
                cgst_summary += Number(v.cgst);
                sgst_summary += Number(v.sgst);
                total_after_discount_summary += (Number(v.price) + Number(v.cgst) + Number(v.sgst)) * parseInt(Number(v.initial_quantity) - Number(v.invoiced_quantity));
                invoiced_quantity_summary += parseInt(Number(v.initial_quantity) - Number(v.invoiced_quantity));

                gdc_table +=` <tr>
                                    <td>
                                    ${v.subcategory_name} - ${v.item_name}
                                    <input value="${v.delivery_challan_master_id}" type="hidden" class="form-control" id="delivery_challan_master_id${v.delivery_challan_items_id}_${v.procurement_requisition_items_id}" name="delivery_challan_master_id[]">
                                    <input value="${v.delivery_challan_items_id}" type="hidden" class="form-control" id="delivery_challan_items_id${v.delivery_challan_items_id}_${v.procurement_requisition_items_id}" name="delivery_challan_items_id[]">
                                    <input value="${v.proc_im_items_id}" type="hidden" class="form-control" id="proc_im_items_id${v.delivery_challan_items_id}_${v.procurement_requisition_items_id}" name="proc_im_items_id[]">
                                    <input value="${v.procurement_requisition_items_id}" type="hidden" class="form-control" id="procurement_requisition_items_id${v.delivery_challan_items_id}_${v.procurement_requisition_items_id}" name="procurement_requisition_items_id[]">
                                    </td>
                                    <td>${v.bill_no}</td>
                                    <td>${v.initial_quantity}</td>
                                    <td>${v.invoiced_quantity}</td>

                                    <td>${v.price}</td>
                                    <td>${v.cgst}</td>
                                    <td>${v.sgst}</td>
                                    <td><input readonly onkeyup="calculate_individual_item(this, 'price', '${v.delivery_challan_items_id}','${v.procurement_requisition_items_id}')" type="number" step="0.1" class="form-control price_for_invoicing_class" id="price_for_invoicing${v.delivery_challan_items_id}_${v.procurement_requisition_items_id}" name="price_for_invoicing[]" value="${Number(v.price)}"></td>
                                    <td><input onkeyup="calculate_individual_item(this, 'quantity', '${v.delivery_challan_items_id}','${v.procurement_requisition_items_id}')" type="number" step="1" class="form-control quantity_for_invoicing_class" id="quantity_for_invoicing${v.delivery_challan_items_id}_${v.procurement_requisition_items_id}" name="quantity_for_invoicing[]" value="${Number(v.initial_quantity) - Number(v.invoiced_quantity)}" min="0" max="${Number(v.initial_quantity) - Number(v.invoiced_quantity)}" /></td>
                                    
                                    <td><input readonly onkeyup="calculate_individual_item(this, 'cgst', '${v.delivery_challan_items_id}','${v.procurement_requisition_items_id}')" type="number" step="0.1" class="form-control cgst_for_invoicing_class" id="cgst_for_invoicing${v.delivery_challan_items_id}_${v.procurement_requisition_items_id}" name="cgst_for_invoicing[]" value="${Number(v.cgst)}"></td>
                                    <td><input readonly onkeyup="calculate_individual_item(this, 'sgst', '${v.delivery_challan_items_id}','${v.procurement_requisition_items_id}')" type="number" step="0.1" class="form-control sgst_for_invoicing_class" id="sgst_for_invoicing${v.delivery_challan_items_id}_${v.procurement_requisition_items_id}" name="sgst_for_invoicing[]" value="${Number(v.sgst)}"></td>
                                    <td><input onkeyup="calculate_individual_item(this, 'discount', '${v.delivery_challan_items_id}','${v.procurement_requisition_items_id}')" type="number" step="0.1" class="form-control discount_for_invoicing_class" id="discount_for_invoicing${v.delivery_challan_items_id}_${v.procurement_requisition_items_id}" name="discount_for_invoicing[]" value="0"></td>

                                    <td><input readonly type="number" step="0.1" class="form-control total_after_discount_class" id="total_after_discount_item_level_for_invoicing${v.delivery_challan_items_id}_${v.procurement_requisition_items_id}" name="total_after_discount_item_level_for_invoicing[]" value="${(Number(v.price) + Number(v.cgst) + Number(v.sgst)) * parseInt(Number(v.initial_quantity) - Number(v.invoiced_quantity))}"></td>
                                </tr>`;
            }
            gdc_table += `</tbody></table>
            <div id="deliveries-div-summary" style="margin-top: 20px;"></div>`;
            $("#gdc-table").html(gdc_table);
        }
        let item_type= 'Item';
        if(sdc.length) {
            item_type= 'Services';
            if(ObjectData.reference_type == 'item') {
                $("#delivery-type").html('(Services)');
                $(".selected_reference_type").val('Services');
            
                // Item based services
            $("#deliveries-div").html(`
                                    <div style="margin-top: -10px; min-width: 100%;" id="sdc-table">   </div>
                                    <div style="height: 35px; min-width: 100%;">   </div>
                                `);
            let sdc_table= `<table class="table table-bordered">
                                <thead class="thead-dark">
                                    <tr>
                                        <th>Item</th>
                                        <th>SDC No</th>
                                        <th>Initial</th>
                                        <th>Invoiced</th>

                                        <th>Unit Price</th>
                                        <th>Quantity for Invoicing</th>
                                        <th>cGST</th>
                                        <th>sGST</th>
                                        <th>Discount</th>

                                        <th>Total After Discount</th>
                                    </tr>
                                </thead>
                                <tbody>`;
            for(var v of sdc) {

                sdc_table +=` <tr>
                                    <td>
                                        ${v.subcategory_name} - ${v.item_name}
                                        <input value="${v.procurement_sdc_id}" type="hidden" class="form-control" name="procurement_sdc_id[]">
                                        <input value="${v.procurement_sdc_deliveries_id}" type="hidden" class="form-control" name="procurement_sdc_deliveries_id[]">
                                    </td>
                                    <td>${v.sdc_number}</td>
                                    <td>${v.accepted_qty}</td>
                                    <td>${v.invoiced_quantity}</td>

                                    <td><input min="0" onkeyup="calculate_individual_item(this, 'price', '${v.procurement_sdc_deliveries_id}','${v.procurement_sdc_id}')" type="number" step="0.1" class="form-control price_for_invoicing_class" id="price_for_invoicing${v.procurement_sdc_deliveries_id}_${v.procurement_sdc_id}" name="price_for_invoicing[]" value="0"></td>
                                    <td><input min="0" max="${Number(v.accepted_qty) - Number(v.invoiced_quantity)}" onkeyup="calculate_individual_item(this, 'quantity', '${v.procurement_sdc_deliveries_id}','${v.procurement_sdc_id}')" type="number" step="1" class="form-control quantity_for_invoicing_class" id="quantity_for_invoicing${v.procurement_sdc_deliveries_id}_${v.procurement_sdc_id}" name="quantity_for_invoicing[]" value="0" /></td>
                                    <td><input min="0" onkeyup="calculate_individual_item(this, 'cgst', '${v.procurement_sdc_deliveries_id}','${v.procurement_sdc_id}')" type="number" step="0.1" class="form-control cgst_for_invoicing_class" id="cgst_for_invoicing${v.procurement_sdc_deliveries_id}_${v.procurement_sdc_id}" name="cgst_for_invoicing[]" value="0"></td>
                                    <td><input min="0" onkeyup="calculate_individual_item(this, 'sgst', '${v.procurement_sdc_deliveries_id}','${v.procurement_sdc_id}')" type="number" step="0.1" class="form-control sgst_for_invoicing_class" id="sgst_for_invoicing${v.procurement_sdc_deliveries_id}_${v.procurement_sdc_id}" name="sgst_for_invoicing[]" value="0"></td>
                                    <td><input min="0" onkeyup="calculate_individual_item(this, 'discount', '${v.procurement_sdc_deliveries_id}','${v.procurement_sdc_id}')" type="number" step="0.1" class="form-control discount_for_invoicing_class" id="discount_for_invoicing${v.procurement_sdc_deliveries_id}_${v.procurement_sdc_id}" name="discount_for_invoicing[]" value="0"></td>

                                    <td><input readonly type="number" step="0.1" class="form-control total_after_discount_class" id="total_after_discount_item_level_for_invoicing${v.procurement_sdc_deliveries_id}_${v.procurement_sdc_id}" name="total_after_discount_item_level_for_invoicing[]" value="0"></td>
                                </tr>`;
            }
            sdc_table += `</tbody></table>
            <div id="deliveries-div-summary" style="margin-top: 20px;"></div>`;
            $("#sdc-table").html(sdc_table);

            } else {
                item_type= 'Milestones';
                $("#delivery-type").html('(Milestones)');
                $(".selected_reference_type").val('Milestones');

                // Item based services
                $("#deliveries-div").html(`
                                        <div style="margin-top: -10px; min-width: 100%;" id="sdc-table">   </div>
                                        <div style="height: 35px; min-width: 100%;">   </div>
                                    `);
                let sdc_table= `<table class="table table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Item</th>
                                            <th>SDC No</th>
                                            <th>Initial</th>
                                            <th>Invoiced</th>

                                            <th>Unit Price</th>
                                            <th>Quantity for Invoicing</th>
                                            <th>cGST</th>
                                            <th>sGST</th>
                                            <th>Discount</th>

                                            <th>Milestone Unit for Invoicing</th>
                                        </tr>
                                    </thead>
                                    <tbody>`;
                for(var v of sdc) {
                    sdc_table +=` <tr>
                                        <td>
                                            ${v.milestone_name} - ${v.expected_items}
                                            <input value="${v.procurement_sdc_id}" type="hidden" class="form-control" name="procurement_sdc_id[]">
                                            <input value="${v.procurement_sdc_deliveries_id}" type="hidden" class="form-control" name="procurement_sdc_deliveries_id[]">
                                        </td>
                                        <td>${v.sdc_number}</td>
                                        <td>${v.accepted_qty}</td>
                                        <td>${v.invoiced_quantity}</td>

                                        <td><input min="0" onkeyup="calculate_individual_item(this, 'price', '${v.procurement_sdc_deliveries_id}','${v.procurement_sdc_id}')" type="number" step="0.1" class="form-control price_for_invoicing_class" id="price_for_invoicing${v.procurement_sdc_deliveries_id}_${v.procurement_sdc_id}" name="price_for_invoicing[]" value="0"></td>
                                        <td><input min="0" max="1" onkeyup="calculate_individual_item(this, 'quantity', '${v.procurement_sdc_deliveries_id}','${v.procurement_sdc_id}')" type="number" step="1" class="form-control quantity_for_invoicing_class" id="quantity_for_invoicing${v.procurement_sdc_deliveries_id}_${v.procurement_sdc_id}" name="quantity_for_invoicing[]" value="0" /></td>
                                        <td><input min="0" onkeyup="calculate_individual_item(this, 'cgst', '${v.procurement_sdc_deliveries_id}','${v.procurement_sdc_id}')" type="number" step="0.1" class="form-control cgst_for_invoicing_class" id="cgst_for_invoicing${v.procurement_sdc_deliveries_id}_${v.procurement_sdc_id}" name="cgst_for_invoicing[]" value="0"></td>
                                        <td><input min="0" onkeyup="calculate_individual_item(this, 'sgst', '${v.procurement_sdc_deliveries_id}','${v.procurement_sdc_id}')" type="number" step="0.1" class="form-control sgst_for_invoicing_class" id="sgst_for_invoicing${v.procurement_sdc_deliveries_id}_${v.procurement_sdc_id}" name="sgst_for_invoicing[]" value="0"></td>
                                        <td><input min="0" onkeyup="calculate_individual_item(this, 'discount', '${v.procurement_sdc_deliveries_id}','${v.procurement_sdc_id}')" type="number" step="0.1" class="form-control discount_for_invoicing_class" id="discount_for_invoicing${v.procurement_sdc_deliveries_id}_${v.procurement_sdc_id}" name="discount_for_invoicing[]" value="0"></td>

                                        <td><input readonly type="number" step="0.1" class="form-control total_after_discount_class" id="total_after_discount_item_level_for_invoicing${v.procurement_sdc_deliveries_id}_${v.procurement_sdc_id}" name="total_after_discount_item_level_for_invoicing[]" value="0"></td>
                                    </tr>`;
                }
                sdc_table += `</tbody></table>
                <div id="deliveries-div-summary" style="margin-top: 20px;"></div>`;
                $("#sdc-table").html(sdc_table);
            }
        }
        if(sdc.length || gdc.length) {
            $("#deliveries-div-summary").html(`
                                
                                    <h4 style="min-width: 100%; margin-bottom: 10px;">Summary Calculations</h4>
                                    <div style="margin-top: -10px; min-width: 100%;" id="total-table">
                                    
                                    <table class="table table-bordered">
                                        <thead class="thead-dark">
                                            <tr>
                                                <th>Total Price</th>
                                                <th>Total Quantity</th>
                                                <th>Total cGST</th>
                                                <th>Total sGST</th>
                                                <th>Total Discount</th>
                                                <th>Sub Total</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><input readonly class="form-control" name="price_summary_td" id="price_summary_td" value="${price_summary}" /></td>
                                                <td><input readonly class="form-control" name="invoiced_quantity_summary_td" id="invoiced_quantity_summary_td" value="${invoiced_quantity_summary}" /></td>
                                                <td><input readonly class="form-control" name="cgst_summary_td" id="cgst_summary_td" value="${cgst_summary}" /></td>
                                                <td><input readonly class="form-control" name="sgst_summary_td" id="sgst_summary_td" value="${sgst_summary}" /></td>
                                                <td><input readonly class="form-control" name="discount_summary_td" id="discount_summary_td" value="${discount_summary}" /></td>
                                                <td><input readonly class="form-control" name="total_after_discount_summary_td" id="total_after_discount_summary_td" value="${total_after_discount_summary}" /></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    
                                    </div>
                                `);
        }
    }

    function info_about_calculations() {
        Swal.fire({
            icon: 'info',
            html: `Both cGST and sGST are not applied at the unit level; rather, they are calculated based on the total taxable value (unit price * quantity). Similarly, discount amounts are also computed at the aggregate level (price * quantity) rather than per unit. This ensures accurate tax assessment and discount application in accordance with standard accounting and taxation practices.`
        });
    }

    function calculate_individual_item(current, changed_field_type, challan_items_id, requisition_items_id) {

        // alert(challan_items_id)
        // alert(requisition_items_id)

        let price_for_invoicing= $("#price_for_invoicing"+challan_items_id+"_"+requisition_items_id).val();
        let cgst_for_invoicing= $("#cgst_for_invoicing"+challan_items_id+"_"+requisition_items_id).val();
        let sgst_for_invoicing= $("#sgst_for_invoicing"+challan_items_id+"_"+requisition_items_id).val();
        let discount_for_invoicing= $("#discount_for_invoicing"+challan_items_id+"_"+requisition_items_id).val();
        let quantity_for_invoicing= $("#quantity_for_invoicing"+challan_items_id+"_"+requisition_items_id).val();

        // alert(price_for_invoicing)
        // alert(quantity_for_invoicing)

        let total_after_discount= Number(price_for_invoicing) * Number(quantity_for_invoicing) + Number(cgst_for_invoicing) + Number(sgst_for_invoicing) - Number(discount_for_invoicing);
        
        $("#total_after_discount_item_level_for_invoicing"+challan_items_id+"_"+requisition_items_id).val(total_after_discount);
        
        setTimeout(calculate_total_and_subtotal, 600);
    }

    function calculate_total_and_subtotal() {
        
        let price_summary= 0;
        let cgst_summary= 0;
        let sgst_summary= 0;
        let discount_summary= 0;
        let total_after_discount_summary= 0;
        let invoiced_quantity_summary= 0;

        let index= 0;
        $("input.price_for_invoicing_class").each(function() {
            let price_for_invoicing= $("input.price_for_invoicing_class").eq(index).val();
            let cgst_for_invoicing= $("input.cgst_for_invoicing_class").eq(index).val();
            let sgst_for_invoicing= $("input.sgst_for_invoicing_class").eq(index).val();
            let discount_for_invoicing= $("input.discount_for_invoicing_class").eq(index).val();
            let quantity_for_invoicing= $("input.quantity_for_invoicing_class").eq(index).val();
            let total_after_discount_class= $("input.total_after_discount_class").eq(index).val();

            price_summary += Number(price_for_invoicing);
            cgst_summary += Number(cgst_for_invoicing);
            sgst_summary += Number(sgst_for_invoicing);
            discount_summary += Number(discount_for_invoicing);
            invoiced_quantity_summary += Number(quantity_for_invoicing);
            total_after_discount_summary += Number(total_after_discount_class);

            index++;
        });
        
        $("input#price_summary_td").val(price_summary);
        $("input#invoiced_quantity_summary_td").val(invoiced_quantity_summary);
        $("input#cgst_summary_td").val(cgst_summary);
        $("input#sgst_summary_td").val(sgst_summary);
        $("input#discount_summary_td").val(discount_summary);
        $("input#total_after_discount_summary_td").val(total_after_discount_summary);
    }

    function validateDateRange() {
        let start_date = $("#invoice_date").val();
        let end_date = $("#due_date").val();

        // Check if both dates are empty
        if (!start_date && !end_date) {
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: 'Both invoice date and due date are required!',
            });
            return false;
        }

        // Check if start date is empty
        if (!start_date) {
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: 'Invoice date is required!',
            });
            return false;
        }

        // Check if end date is empty
        if (!end_date) {
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: 'Due date is required!',
            });
            return false;
        }

        var startDd = start_date.split('-');
        var endDd = end_date.split('-');
        startD= Number(startDd[2] + startDd[1] + startDd[0]);
        endD= Number(endDd[2] + endDd[1] + endDd[0]);
        if(startD > endD) {
            Swal.fire({
                icon: 'error',
                title: 'Invalid Dates picked',
                text: 'Invoice date must be before due date!',
            });
            return false;

        }
        // If all validations pass
        return true;
    }

    function onchange_PO() {
        let purchase_order_id= $("#purchase_order_id").val();
        if(purchase_order_id) {
            $.ajax({
                url: '<?php echo site_url('procurement/invoice_controller_v2/get_details_PO_wise'); ?>',
                type: 'POST',
                data: {purchase_order_id},
                success: function(goodies) {
                    let data= JSON.parse(goodies);
                    if(Object.keys(data)?.length) {
                        var vendor= data.vendor;
                        var instruments= data.instruments;
                        if(vendor && vendor != 'null' && Object.keys(vendor)?.length) {
                            let vendor_id= vendor.vendor_id;
                            let vendor_code= vendor.vendor_code;
                            let vendor_name= vendor.vendor_name;
                            $("#vendor_id").html(`<option value="${vendor_id}">${vendor_name}${vendor_code != '-' ? '-'+vendor_code : ''}</option>`);
                        }
                        if(instruments && instruments != 'null' && Object.keys(instruments)?.length) {
                            var options= '<option value="">Select..</option>';
                            for(var v of instruments) {
                                var id= v.id;
                                var instrument_name= v.instrument_name;
                                var payment_type= v.payment_type;
                                var bank_name= v.bank_name;
                                options += `<option value="${id}">${instrument_name != '-' ? instrument_name : ''}${payment_type != '-' ? '~'+payment_type : ''} ${bank_name != '-' ? bank_name : ''}</option>`;
                            }
                            $("#vendor_instruments_id").html(options);
                        }
                    }
                },
                error: function(glitch) {
                    console.warn(glitch);
                }
            });
        }
    }

    // Step- 2
    function save_delivery_items(current, step_selector, step_number) {

        let current_status= $("#insert_update_type_items").val();
        if(current_status != 'Add') {
            $("#step-2").addClass('hidden');
            $("#step-3").removeClass('hidden');
            $("div.circle-" +step_number).html(`<span class="fa fa-check"></span>`).css('background', 'green');
            $("div.circle-" + (Number(step_number) + 1)).html(`o`).css('background', 'green');

            return false;
            
        }

        let isQuantityValid= false;
        $("input.quantity_for_invoicing_class").each(function() {
            let quantity_for_invoicing= $(this).val();
            if(Number(quantity_for_invoicing) > 0) {
                isQuantityValid= true;
                return false;
            }
        });

        if(!isQuantityValid) {
            return Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: 'Please select at least one item for invoicing!',
            });
        }

        // let confirmation= confirm("Once you save the delivery items, you won't be able to edit them. Do you want to proceed?");
        // if(!confirmation) {
        //     return false;
        // }

        var $form = $('#'+step_selector+'-form');
        if ($form.parsley().validate()) {
            $(current).prop('disabled', true).html('Please Wait...');
            var form = $('#'+step_selector+'-form')[0];
            var formData = new FormData(form);
            formData.append('step_number',step_selector);
            // formData.append('step_selector',step_selector);

            $.ajax({
                url: '<?php echo site_url('procurement/invoice_controller_v2/save_delivery_items'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    let p_data= JSON.parse(data);
                    if(Object.keys(p_data)?.length) {
                        if(p_data.status == '0') {
                            Swal.fire({
                                icon: 'error',
                                title: 'Something went wrong'
                            });
                        } else {
                            __construct_approvers_details(p_data.approvers);
                            $(".invoice_master_id").val(p_data.invoice_master_id);
                            $("#insert_update_type_items").val('Update');
                            $("#step-2").addClass('hidden');
                            $("#step-3").removeClass('hidden');

                            $("div.circle-" +step_number).html(`<span class="fa fa-check"></span>`).css('background', 'green');
                            $("div.circle-" + (Number(step_number) + 1)).html(`o`).css('background', 'green');

                            $('#'+step_selector+'-form input').each(function() {
                                $(this).attr("readonly", true);
                            });

                            setTimeout(() => {
                                $("input.invoice_master_id, input.selected_purchase_order_id, input.selected_vendor_id, input.selected_reference_type").prop('readonly', false);
                                $("input#insert_update_type_items").prop('readonly', false);
                            }, 800);

                            $("#warning-message").html('Not editable');
                            // $(current).append(`<button type="button" onclick="next_page_from_delivery_items(this, 'step-2', 2)" class="btn btn-dark pull-right">Next</button>`).remove();

                        }

                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Something went wrong'
                        });
                    }
                    $(current).prop('disabled', false).html('Next');
                }
            });
        }
    }

    function __construct_approvers_details(approvers) {
        if(!Object.keys(approvers)?.length) {
            $("#approvers_div").html(`
            <center> <img src="<?php echo base_url('assets/illustrations/no_approvers.svg'); ?>" class="img-fluid" style="width: 300px; height: 240px; object-fit: contain; margin-top: 20px;" /> <br><br> <h2>Approvers not found</h2> <p>Your invoice has not been assigned to any approver yet.</p> </center>
            `);
            return;
        }
        var html= `<table class="table table-bordered">
                        <thead class="thead-dark">
                            <tr>
                                <th>Approver Type</th>
                                <th>Approver</th>
                                <th>Department</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>`;
        if(Object.keys(approvers)?.length) {
            for(var v of approvers) {
                html += `<tr>
                            <td>${v.approval_level}</td>
                            <td>${v.staff}</td>
                            <td>${v.department}</td>
                            <td>Pending</td>
                        </tr>`;
            }
        }
        html += ` </tbody>
                </table>`;
        $("#approvers_div").html(html);
    }

    function save_approver_details(current, step_selector, step_number) {
        $("#step-3").addClass('hidden');
        $("#step-4").removeClass('hidden');

        $("div.circle-" +step_number).html(`<span class="fa fa-check"></span>`).css('background', 'green');
        $("div.circle-" + (Number(step_number) + 1)).html(`o`).css('background', 'green');

    }

    async function add_additional_notes(current, close_addClose) {
        
        let additional_description_notes= $("#additional_description_notes").val();
        let invoice_master_id= $(".invoice_master_id").val();
        
        const fileInput = document.getElementById('additional_attachements');
        const file = fileInput.files[0];
        const fileInfoDiv = document.getElementById('fileInfo');

        // Check if the file is a PDF
        if (file.type !== 'application/pdf') {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Only PDF type files can be uploaded'
            });
            // Clear the file input
            fileInput.value = '';
            fileInput.value = '';
            fileName.textContent = 'No file selected';
            $("form#docs_form").trigger('reset');
            return false;
        }



        $("#attachments-list-empty").hide();
        $("#additional_attachements_div").show();
        $("tr.tr_class_remover").remove();
        $("tbody#additional_attachements_tbody").append(
            `<tr class="tr_class_remover" class="text-center">
                                    <td colspan="3" style="height: 60px; vertical-align: middle; width: 100%; border-right: none;">
                                        <div class="col-md-12" style="width: 100%;"></div>
                                            <div id="show_progress_color" style="height: 20px; background: lightgreen; width: 1%;"><div>
                                        </div>
                                    </td>

                                    <td style="width: 50px; border-left: none;">
                                        <div class="pull-right" id="show_progress_percentage" style="text-align: right; font-weight: bold; color: black; font-weight: bold; font-size: 24px;"><div>
                                    </td>
                            </tr>`
        );

        let fileName1= '';
        let fileSizeFormatted1= '';
        let size= 0;
        let splittedFileType= [];

        const MAX_FILE_SIZE= '<?php echo $MAX_FILE_SIZE; ?>';
        const MAX_FILE_SIZE_BYTES = convertSizeToBytes(MAX_FILE_SIZE);
        
        if (file) {
            const fileName = file.name;
            const fileSizeBytes = file.size;
            const fileExtentionType = file.type;
            size= fileSizeBytes;
            splittedFileType= fileExtentionType.split('/');
            let fileSizeFormatted;
            if (fileSizeBytes < 1024) {
                fileSizeFormatted = fileSizeBytes + ' bytes';
            } else if (fileSizeBytes < 1024 * 1024) {
                fileSizeFormatted = (fileSizeBytes / 1024).toFixed(2) + ' KB';
            } else {
                fileSizeFormatted = (fileSizeBytes / (1024 * 1024)).toFixed(2) + ' MB';
            }
            fileName1= fileName;
            fileSizeFormatted1= fileSizeFormatted;
        } else {
            return Swal.fire({
                icon: 'error',
                title: 'Fill all the necessary fields'
            });
        }

        if (Number(size) > Number(MAX_FILE_SIZE_BYTES)) {
            Swal.fire({
                icon: 'error',
                title: 'File size exceeds the limit',
                text: `Maximum allowed file size is ${MAX_FILE_SIZE}.`
            });
            fileInput.value = '';
            fileName.textContent = 'No file selected';
            $("form#docs_form").trigger('reset');
            return false;
        }
        

        var form = $('#docs_form')[0];
        var formData = new FormData(form);
        formData.append('fileName', fileName1);
        formData.append('fileSizeBytes', size);
        formData.append('fileExtentionType', splittedFileType[1]);

         let relative_path_details= await upload_file_to_wasabi();
        let path= relative_path_details.path;

        formData.append('path', path);

        $.ajax({
            url: '<?php echo site_url('procurement/invoice_controller_v2/add_document'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            cache : false,

            xhr: function() {
                var xhr = new window.XMLHttpRequest();
                var lastPercent = 0; // Track the last logged percentage
                var interval; // For simulated progress

                // $("#show_attachements_progress_modal").modal('show');
                xhr.upload.addEventListener("progress", function(evt) {
                    if (evt.lengthComputable) {
                        var currentPercent = Math.round((evt.loaded / evt.total) * 100);
                        
                        // Clear any previous interval to avoid duplicates
                        if (interval) clearInterval(interval);
                        
                        // Simulate smooth progress between real events
                        interval = setInterval(function() {
                            if (lastPercent < currentPercent) {
                                lastPercent++;
                                $("div#show_progress_percentage, div#show_progress_color_modal").html(lastPercent + '%');
                                $("div#show_progress_color, div#show_progress_percentage_modal").css('width', lastPercent +'%');
                            } else {
                                clearInterval(interval);
                            }
                        }, 50); // Adjust speed (ms) for smoother/faster increments
                    }
                }, false);

                // Ensure 100% is logged when upload completes
                xhr.addEventListener("load", function() {
                    clearInterval(interval); // Stop simulation
                    // $("#show_attachements_progress_modal").modal('hide');
                    if (lastPercent < 100) {
                        $("div#show_progress_percentage, div#show_progress_color_modal").html(100 + '%');
                        $("div#show_progress_color, div#show_progress_percentage_modal").css('width', 100 +'%');
                    }
                });

                return xhr;
            },

            success: function(data) {
                let p_data= JSON.parse(data);
                if(Object.keys(p_data)?.length) {
                    if(p_data.status == '1') {
                        // var download_url= '<?php // echo site_url('procurement/invoice_controller_v2/download_invoice_attachement/'); ?>' + p_data.invoice_attachments_id;

                        $("tbody#additional_attachements_tbody").append(
                            `<tr id="document_tr_id_${p_data.invoice_attachments_id}">
                                    <td>
                                        ${fileName1}
                                    </td>
                                    <td>${fileSizeFormatted1}</td>
                                    <td>${additional_description_notes.trim() == '' ? '-' : additional_description_notes}</td>
                                    <td>
                                    <span style="cursor: pointer;" class="fa fa-times text-danger text-bold" onclick="remove_document('${p_data.invoice_attachments_id}')"></span>
                                    <a style="cursor: pointer;" class="fa fa-eye text-warning text-bold pull-right" href="${p_data.absolute_path}" target="_blank"></a>
                                    </td>
                            </tr>
                            `
                        );
                        $("tr.tr_class_remover").remove();
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Something went wrong'
                    });
                }
            }
        });

        if(close_addClose == 'close') {
            $("#add_additional_attachements_modal").modal('hide');
        }
        fileInput.value = '';
        fileName.textContent = 'No file selected';
        $("form#docs_form").trigger('reset');
    }

function convertSizeToBytes(sizeStr) {
    /**
     * Converts human-readable size strings (e.g., "5MB", "10 KB", "3bytes") into bytes.
     * Supports:
     * - KB, MB, GB (case-insensitive)
     * - Spaces or no spaces (e.g., "5MB" or "5 MB")
     * - Bytes (e.g., "5 Bytes" or "5bytes")
     * Returns integer value.
     */
    const sizeUnits = {
        'kb': 1024,
        'mb': 1024 ** 2,
        'gb': 1024 ** 3,
        'bytes': 1,
        'byte': 1,
    };

    // Extract numeric value and unit using regex
    const match = sizeStr.match(/^\s*(\d+)\s*([a-zA-Z]+)\s*$/i);
    if (!match) {
        throw new Error(`Invalid size format: "${sizeStr}"`);
    }

    const numericValue = parseInt(match[1], 10);
    const unit = match[2].toLowerCase();

    // Find the matching unit (supports 'kb', 'mb', 'gb', 'bytes', 'byte')
    const normalizedUnit = Object.keys(sizeUnits).find(key => 
        unit === key || unit === `${key}s`
    ) || unit; // Fallback to exact match if not found

    if (!sizeUnits[normalizedUnit]) {
        throw new Error(`Unknown size unit: "${unit}"`);
    }

    return numericValue * sizeUnits[normalizedUnit];
}

    function upload_file_to_wasabi() {
        return new Promise(function(resolve, reject) {
            try {

                const fileInput = document.getElementById('additional_attachements');
                const file = fileInput.files[0]; // Get the first selected file
                
                if (!file) {
                    return Swal.fire({
                        icon: 'error',
                        title: 'No file selected',
                        text: 'Please select a file to upload.'
                    });
                }

                // Get file details
                const fileName = file.name;
                const fileType = file.type;
                const fileSize = file.size; // in bytes

// const maxFileSize = 10 * 1024 * 1024; // 10MB

                $.ajax({
                    url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
                    type: 'post',
                    data: {'filename':file.name, 'file_type':file.type, 'folder':'invoice_documents'},
                    success: function(response) {
                        // Show progress bar
                        $('#upload_progress').show();
                        
                        response = JSON.parse(response);
                        var path = response.path;
                        var signedUrl = response.signedUrl;

                        $.ajax({
                            url: signedUrl,
                            type: 'PUT',
                            headers: {
                                "Content-Type": file.type, 
                                "x-amz-acl":"public-read" 
                            },
                            processData: false,
                            data: file,
                            xhr: function () {
                                var xhr = $.ajaxSettings.xhr();
                                xhr.upload.onprogress = function (e) {
                                    if (e.lengthComputable) {
                                        const percent = Math.round((e.loaded / e.total) * 100);
                                        $('#upload_progress_bar').css('width', percent + '%');
                                        $('#upload_percentage').text(percent + '%');
                                    }
                                };
                                return xhr;
                            },
                            success: function(response) {
                                $('#uploaded_photo_url').val(path);
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Upload Complete',
                                    text: 'Photo uploaded successfully',
                                    timer: 2000,
                                    showConfirmButton: false
                                });
                                resolve({path:path, name:file.name, type:file.type});
                            },
                            error: function(err) {
                                console.error('Upload error:', err);
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Upload Failed',
                                    text: 'Failed to upload photo. Please try again.',
                                    timer: 3000,
                                    showConfirmButton: false
                                });
                                reject(err);
                            },
                            complete: function() {
                                // Hide progress bar after a short delay
                                setTimeout(() => {
                                    $('#upload_progress').hide();
                                }, 1000);
                            }
                        });
                    },
                    error: function (err) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Upload Failed',
                            text: 'Failed to get upload URL. Please try again.',
                            timer: 3000,
                            showConfirmButton: false
                        });
                        reject(err);
                    }
                });
            } catch(err) {
                console.error('Error:', err);
                Swal.fire({
                    icon: 'error',
                    title: 'Upload Failed',
                    text: 'An unexpected error occurred. Please try again.',
                    timer: 3000,
                    showConfirmButton: false
                });
                reject(err);
            }
        });
    }

    function remove_document(invoice_attachment_id) {
        $.ajax({
            url: '<?php echo site_url('procurement/invoice_controller_v2/remove_document'); ?>',
            type: 'post',
            data: {invoice_attachment_id},
            success: function(data) { 
                $("#document_tr_id_"+invoice_attachment_id).remove();
                var trCounts= $("tbody#additional_attachements_tbody").html();
                if(trCounts.trim() == '') {
                    $("tbody#additional_attachements_tbody").html(`<tr class="tr_class_remover">
                                                                            <td colspan="4" class="text-center">
                                                                            No documents found
                                                                            </td>
                                                                    </tr>`);
                }
            }
        });
       
    }

    function reach_at_prev_tab(current, previous_tab_selector) {
        $(".step-content").addClass('hidden');
        $("#" +previous_tab_selector).removeClass('hidden');
    }












    function save_and_close(current, step_selector) {
        let invoice_master_id= $(".invoice_master_id").val();

        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this! But you can edit it later.",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, save it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $(current).prop('disabled', true).html('Please Wait...');
                submit_invoice(invoice_master_id);
            } else {
                $(current).prop('disabled', false).html('Save & Close');
                Swal.fire({
                    icon: 'warning',
                    title: 'Cancelled',
                    text: 'Your invoice is not saved!'
                });
            }
        });
    }

    function submit_invoice(invoice_master_id) {
        $.ajax({
            url: '<?php echo site_url('procurement/invoice_controller_v2/submit_invoice'); ?>',
            type: 'post',
            data: {invoice_master_id},
            success: function(data) {
                let p_data= JSON.parse(data);
                if(Object.keys(p_data)?.length) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Successfully created invoice'
                    }).then((result) => {
                        window.location.href= '<?php echo site_url('procurement/invoice_controller_v2/manage_all_invoices'); ?>';
                    });

                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Something went wrong'
                    });
                }
            }
        });
    }

    
</script>

<!-- Document Drag & drop -->
<script>
    // Get elements
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('additional_attachements');
    const fileName = document.getElementById('fileName');

    // Handle file selection via browse
    fileInput.addEventListener('change', function(e) {
        if (this.files.length) {
            fileName.textContent = this.files[0].name;
        }
    });

    // Handle drag and drop
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });
    
</script>