<div class="modal fade" id="upload_pdf_modal" tabindex="-1" role="dialog" aria-labelledby="upload_pdf_modalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="width: 79%;margin: auto;border-radius: .75rem;">
            <div class="modal-header" style="border-top-left-radius: .75rem; border-top-right-radius: .75rem;">
                <h4 class="modal-title" id="exampleModalLabel">Upload Quotation</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
                </button>
            </div>
            <div class="modal-body">
                <div style="margin-top:4rem;">
                    <ul class="nav nav-tabs" id="myTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button disabled class="nav-link active" id="quotation-details-tab" data-bs-toggle="tab"
                                data-bs-target="#quotation-details" type="button" role="tab"
                                aria-controls="quotation-details" aria-selected="true">
                                Quotation Details
                            </button>
                        </li>

                        <li class="nav-item" role="presentation">
                            <button disabled class="nav-link" id="quotation-items-tab" data-bs-toggle="tab"
                                data-bs-target="#quotation-items" type="button" role="tab"
                                aria-controls="quotation-items" aria-selected="true">
                                Quotation Items
                            </button>
                        </li>

                        <li class="nav-item" role="presentation">
                            <button disabled class="nav-link" id="tech-remarks-tab" data-bs-toggle="tab"
                                data-bs-target="#tech-remarks" type="button" role="tab" aria-controls="tech-remarks"
                                aria-selected="false">Tech Evaluation Remarks</button>
                        </li>

                        <li class="nav-item" role="presentation">
                            <button disabled class="nav-link" id="price-remarks-tab" data-bs-toggle="tab"
                                data-bs-target="#price" type="button" role="tab" aria-controls="price"
                                aria-selected="false">Price Evaluation
                                Remarks</button>
                        </li>

                        <li class="nav-item" role="presentation">
                            <button disabled class="nav-link" id="delivery-remarks-tab" data-bs-toggle="tab"
                                data-bs-target="#delivery" type="button" role="tab" aria-controls="delivery"
                                aria-selected="false">Delivery
                                Evaluation Remarks</button>
                        </li>

                    </ul>

                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active" id="quotation-details" role="tabpanel"
                            aria-labelledby="quotation-tab">
                            <div class="card-body" id="quotation-details-container">
                                <input type="hidden" name="indentId" id="indentId">

                                <div class="form-group">
                                    <label for="vendor_name" class="control-label">Vendor Name <font color="red">*
                                        </font></label>
                                    <div class="">
                                        <select class="form-control" required="" name="vendor_name" id="vendor_name"
                                            onchange="displayVendorNames()">
                                            <?php if (!empty($vendors)) {
                                                foreach ($vendors as $key => $vendor) {
                                                    echo '<option value="' . $vendor->id . '">' . $vendor->vendor_name . '</option>';
                                                }
                                                // echo '<option value="0">Other</option>';
                                            } ?>
                                        </select>

                                        <div class="mt-3" id="vendor_name_container" name="vendor_name_container"
                                            style="display: none;">
                                            <input type="text" required="" name="vendor_name_new" id="vendor_name_new"
                                                placeholder="Vendor name" class="form-control">
                                        </div>

                                        <p id="vendor-name-error"></p>
                                    </div>
                                    <br>
                                </div>

                                <!-- <div class="form-group">
                                    <label for="quotation_price" class="control-label">Quotation Price <font
                                            color="red">*</font></label>
                                    <div class="">
                                        <input type="number" required="" min="0" name="quotation_price"
                                            id="quotation_price" placeholder="Quotation price" class="form-control">
                                        <p id="quotation-price-error"></p>
                                    </div>
                                    <br>
                                </div> -->

                                <div class="form-group">
                                    <label for="quotation_remarks" class="control-label">General Remarks <font
                                            color="red">*</font></label>
                                    <div class="">
                                        <textarea class="form-control summernote" name="quotation_remarks"
                                            id="quotation_remarks" placeholder="General remarks"
                                            maxlength="150"></textarea>
                                        <p id="quotation-remarks-error"></p>
                                    </div>
                                    <br>
                                </div>

                                <div class="form-group">
                                    <div class="form-group">
                                        <label for="upload_file" class="control-label">
                                            Upload Quotation File <font color="red">*</font>
                                        </label>
                                        <div>
                                            <input type="file" name="upload_file" id="upload_file" class="form-control"
                                                required accept=".pdf" onchange="validateUploadFile()" />

                                            <p class="text-muted">
                                                Allowed file types: <strong>PDF</strong><br>
                                                Max size: <strong>5MB</strong>
                                            </p>
                                            <p id="file-error" style="color: red;"></p>
                                        </div>
                                        <br>
                                    </div>
                                    <br>
                                </div>

                                <div class="float-right">
                                    <!-- <button class="btn btn-primary moveTab-1" data-tabType="quotation-details" onclick="moveTab('tech-remarks-tab',1)">Next</button> -->
                                    <button class="btn btn-primary moveTab-1" data-tabType="quotation-details"
                                        onclick="moveTab('quotation-items-tab',1)">Next</button>
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="quotation-items" role="tabpanel"
                            aria-labelledby="quotation-itemss-tab">
                            <div class="card-body" id="quotation-items-container">

                                <!-- so want to show quotation price which was added in previous tab, so user can have a look and enter the amount based on this, and total items amount cannot be more than quotation price  -->

                                <!-- steps -->
                                <!-- 1. bring the current quotaion price -->
                                <!-- bring the list of items -->
                                <!-- Enable user only to twik the  quantity and approx and we update total amount automaticaly-->
                                <!-- We will validate all the final amount and amount cannot be more than quotation price -->
                                <!-- Then finally if everything goes well we will switch user to next page -->

                                <div class="form-group">
                                    <!-- <label for="quotation_price-readonly" class="control-label">Quotation Price <font
                                            color="red">*</font></label>
                                    <input type="number" required="" min="0" name="quotation_price-readonly"
                                        id="quotation_price-readonly" placeholder="Quotation price" class="form-control"
                                        value="10000" readonly> -->
                                    <!-- <h5 id="quotation_price-readonly"></h5> -->

                                    <!-- <th>CATEGORY</th> -->
                                    <!-- <th>UNITS</th> -->
                                    <div id="quotation-items-table">
                                        <table name="BOMItems" id="quotationItems" class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>#</th>
                                                    <th>ITEM NAME</th>
                                                    <th>QTY</th>
                                                    <th>APPROX UNIT PRICE</th>
                                                    <th>TOTAL AMOUNT</th>
                                                </tr>
                                            </thead>
                                            <tbody id="quotation-items-tbody">
                                                <tr class="tr_class_remover_item">
                                                    <td colspan="5" class="text-center">Items not added</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div id="quotation-items-error" class="text-danger"
                                        style="display: none; font-weight: bold;"></div>
                                </div>

                                <!-- swithing user to next tab logic -->
                                <div class="float-right">
                                    <button class="btn btn-primary"
                                        onclick="moveTab('quotation-details-tab',0)">Prev</button>
                                    <button class="btn btn-primary moveTab-2" data-tabType="quotation-details"
                                        onclick="moveTab('tech-remarks-tab',1)">Next</button>
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="tech-remarks" role="tabpanel" aria-labelledby="profile-tab">
                            <div class="card-body" id="tech-remarks-container">
                                <input type="hidden" name="indentId" id="indentId">

                                <div class="form-group">
                                    <label for="quotation_remarks" class="control-label">Tech Evaluation Remarks
                                        <font color="red">*</font>
                                    </label>
                                    <div class="">
                                        <textarea class="form-control summernote" name="tech_eval_remarks"
                                            id="tech_eval_remarks" placeholder="Tech evaluation remarks"
                                            maxlength="150"></textarea>
                                        <p id="tech-remarks-error"></p>
                                    </div>
                                    <br>
                                </div>

                                <div class="float-right">
                                    <button class="btn btn-primary"
                                        onclick="moveTab('quotation-items-tab',0)">Prev</button>
                                    <button class="btn btn-primary moveTab-2" data-tabType="quotation-details"
                                        onclick="moveTab('price-remarks-tab',1)">Next</button>
                                </div>

                            </div>
                        </div>

                        <div class="tab-pane fade" id="price" role="tabpanel" aria-labelledby="price-remarks-tab">
                            <div class="card-body" id="price-remarks-container">
                                <input type="hidden" name="indentId" id="indentId">

                                <div class="form-group">
                                    <label for="quotation_remarks" class="control-label">Price Evaluation Remarks
                                        <font color="red">*</font>
                                    </label>
                                    <div class="">
                                        <textarea class="form-control summernote" name="price_eval_remarks"
                                            id="price_eval_remarks" placeholder="Price evaluation remarks"
                                            maxlength="150"></textarea>
                                        <p id="price-remarks-error"></p>
                                    </div>
                                    <br>
                                </div>

                                <div class="float-right">
                                    <button class="btn btn-primary"
                                        onclick="moveTab('tech-remarks-tab',0)">Prev</button>
                                    <button class="btn btn-primary moveTab-3" data-tabType="quotation-details"
                                        onclick="moveTab('delivery-remarks-tab',1)">Next</button>
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="delivery" role="tabpanel" aria-labelledby="delivery-remarks-tab">
                            <div class="card-body" id="delivery-remarks-container">
                                <input type="hidden" name="indentId" id="indentId">

                                <div class="form-group">
                                    <label for="quotation_remarks" class="control-label">Delivery Evaluation Remarks
                                        <font color="red">*</font>
                                    </label>
                                    <div class="">
                                        <textarea class="form-control summernote" name="delivery_eval_remarks"
                                            id="delivery_eval_remarks" placeholder="Delivery evaluation remarks"
                                            maxlength="150"></textarea>
                                        </p>
                                        <p id="delivery-remarks-error"></p>
                                    </div>
                                    <br>
                                </div>

                                <div class="float-right">
                                    <button class="btn btn-primary"
                                        onclick="moveTab('price-remarks-tab',0)">Prev</button>
                                    <button class="btn btn-primary moveTab-4" id="submitQuotation"
                                        data-tabType="quotation-details" onclick="submitQuotation()">Submit</button>
                                </div>

                            </div>
                        </div>

                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    // Capture updated quotation items
    let updatedQuotationItems = [];

    $("#upload_pdf_modal").on("show.bs.modal", (e) => {
        const tabId = "quotation-details-tab";
        activateTab(tabId);

        const allRemarks = document.querySelectorAll(".note-editable");
        allRemarks.forEach(remarks => {
            remarks.innerHTML = '';
        })
    })

    function displayVendorNames() {
        $("#vendor_name_container").hide();
        const vendorid = +$("#vendor_name").val();
        if (!vendorid) {
            $("#vendor_name_container").show();
        }
    }

    $('#upload_pdf_modal').on('hidden.bs.modal', function (e) {
        // Resetting fields
        $("#indentId").val("");
        $("#vendor_name").val("");
        $("#quotation_price").val("");
        $("#quotation_remarks").val("");
        $("#vendor_name_new").val("");
        $("#tech_eval_remarks").val("");
        $("#price_eval_remarks").val("");
        $("#delivery_eval_remarks").val("");
        $("#upload_file").val("");


        // reset the items entered for each vendor
        $("#quotation-items-tbody").removeData("populated");
        $("#quotation-items-error").hide();

        const allRemarks = document.querySelectorAll(".note-editable");
        allRemarks.forEach(remarks => {
            remarks.innerHTML = '';
        })

        // resetting errors
        $("#delivery-remarks-error").hide();
        $("#file-error").hide();
        $("#vendor-name-error").hide();
        $("#quotation-price-error").hide();
        $("#quotation-remarks-error").hide();
        $("#tech-remarks-error").hide();
        $("#price-remarks-error").hide();
        $("#delivery-remarks-error").hide();
    })

    function moveTab(tabId, isNextTab) {
        const maxCharLength = 500;
        const allRemarks = document.querySelectorAll(".note-editable");

        // Hide all error messages
        const hideAllErrors = () => {
            $("#file-error").hide();
            $("#vendor-name-error").hide();
            $("#quotation-price-error").hide();
            $("#quotation-remarks-error").hide();
            $("#tech-remarks-error").hide();
            $("#price-remarks-error").hide();
            $("#delivery-remarks-error").hide();
            $("#quotation-items-error").hide();
        };

        // Helper to update indentItemsKart from input fields
        function syncItemInputsToKart() {
            if (typeof indentItemsKart !== "undefined" && Array.isArray(indentItemsKart)) {
                indentItemsKart.forEach(item => {
                    const qty = parseFloat($(`.qty-input[data-item-id="${item.id}"]`).val()) || 0;
                    const price = parseFloat($(`.price-input[data-item-id="${item.id}"]`).val()) || 0;
                    item.quantity = qty;
                    item.unit_price = price;
                });
            }
        }

        // Get values for validation
        const vendor_name = $("#vendor_name").val() != 0
            ? $("#vendor_name :selected").text()
            : $("#vendor_name_new").val();
        const quotationRemarks = allRemarks[0]?.innerText.trim() || "";
        const file = $("#upload_file").prop('files')[0];

        // Validate first tab (Quotation Details) before moving forward
        if (tabId === "quotation-items-tab" && Boolean(isNextTab)) {
            hideAllErrors();
            let hasError = false;

            if (!vendor_name) {
                $("#vendor-name-error").text("Please enter vendor name to proceed").css("color", "red").show();
                hasError = true;
            }
            if (quotationRemarks.length === 0) {
                $("#quotation-remarks-error").text("Please enter general remarks to proceed").css("color", "red").show();
                hasError = true;
            }
            if (quotationRemarks.length > maxCharLength) {
                $("#quotation-remarks-error").text(
                    `General remarks should be less than 500 characters. Please remove extra ${quotationRemarks.length - maxCharLength} characters`
                ).css("color", "red").show();
                hasError = true;
            }
            if (!file) {
                $("#file-error").text("Please select a file to proceed").css("color", "red").show();
                hasError = true;
            }
            if (hasError) return;

            // Populate items table if not already done
            if ($("#quotation-items-tbody").data("populated") !== true) {
                let html = ``;
                let grandTotal = 0;

                indentItemsKart.forEach((item, index) => {
                    const itemTotal = item.quantity * item.unit_price;
                    grandTotal += itemTotal;

                    html += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${item.item_name}</td>
                        <td>
                            <input type="number"
                                class="form-control qty-input"
                                value="${item.quantity}"
                                min="1"
                                step="1"
                                data-item-id="${item.id}"
                                oninput="updateQuotationItemTotals()">
                        </td>
                        <td>
                            <input type="number"
                                class="form-control price-input"
                                value="${item.unit_price}"
                                min="1"
                                step="0.01"
                                data-item-id="${item.id}"
                                oninput="updateQuotationItemTotals()">
                        </td>
                        <td class="item-total" data-item-id="${item.id}">${itemTotal.toFixed(2)}</td>
                    </tr>
                `;
                });

                html += `
                <tr>
                    <td colspan="4" class="text-right font-weight-bold">Total</td>
                    <td id="grand-total">${grandTotal.toFixed(2)}</td>
                </tr>
            `;

                $("#quotation-items-tbody").html(html);
                $("#quotation-items-tbody").data("populated", true);
            }

            hideAllErrors();
            activateTab(tabId);
            return;
        }

        // Validate Quotation Items tab before moving to Tech Remarks
        if (tabId === "tech-remarks-tab" && Boolean(isNextTab)) {
            syncItemInputsToKart();
            let itemsGrandTotal = parseFloat($("#grand-total").text()) || 0;
            hideAllErrors();

            if (itemsGrandTotal <= 0) {
                $("#quotation-items-error").text(`The total amount of all items (₹${itemsGrandTotal.toFixed(2)}) must be greater than ₹0.00.`)
                    .css("color", "red")
                    .show();
                return;
            }
            // Validate that no qty or price is 0 or less
            let zeroValueFound = false;
            indentItemsKart.forEach(inputItem => {
                const qty = parseFloat($(`.qty-input[data-item-id="${inputItem.id}"]`).val()) || 0;
                const price = parseFloat($(`.price-input[data-item-id="${inputItem.id}"]`).val()) || 0;
                if (qty === 0 || price === 0) {
                    $("#quotation-items-error")
                        .text(`Quantity and Approx Price must be at least 1 for all items.`)
                        .css("color", "red")
                        .show();
                    zeroValueFound = true;
                    return false;
                }
            });
            if (zeroValueFound) return;

            // Update updatedQuotationItems with latest values
            updatedQuotationItems = indentItemsKart.map(item => ({ ...item }));

            activateTab(tabId);
            return;
        }

        // Validate Tech Remarks tab before moving to Price Remarks
        if (tabId === "price-remarks-tab" && Boolean(isNextTab)) {
            hideAllErrors();
            const techRemarks = allRemarks[1]?.innerText.trim() || "";
            if (techRemarks.length === 0) {
                $("#tech-remarks-error").text("Please enter tech evaluation remarks to proceed").css("color", "red").show();
                return;
            }
            if (techRemarks.length > maxCharLength) {
                $("#tech-remarks-error").text(
                    `Tech evaluation remarks should be less than 500 characters. Please remove extra ${techRemarks.length - maxCharLength} characters`
                ).css("color", "red").show();
                return;
            }
            activateTab(tabId);
            return;
        }

        // Validate Price Remarks tab before moving to Delivery Remarks
        if (tabId === "delivery-remarks-tab" && Boolean(isNextTab)) {
            hideAllErrors();
            const priceRemarks = allRemarks[2]?.innerText.trim() || "";
            if (priceRemarks.length === 0) {
                $("#price-remarks-error").text("Please enter price evaluation remarks to proceed").css("color", "red").show();
                return;
            }
            if (priceRemarks.length > maxCharLength) {
                $("#price-remarks-error").text(
                    `Price evaluation remarks should be less than 500 characters. Please remove extra ${priceRemarks.length - maxCharLength} characters`
                ).css("color", "red").show();
                return;
            }
            activateTab(tabId);
            return;
        }

        // For Prev buttons or direct tab activation
        hideAllErrors();
        activateTab(tabId);
    }

    function updateQuotationItemTotals() {
        let grandTotal = 0;

        $("#quotation-items-tbody tr").each(function () {
            const qty = parseFloat($(this).find(".qty-input").val()) || 0;
            const price = parseFloat($(this).find(".price-input").val()) || 0;
            const total = qty * price;

            $(this).find(".item-total").text(total.toFixed(2));
            grandTotal += total;
        });

        $("#grand-total").text(grandTotal.toFixed(2));
    }


    function activateTab(tabId) {
        const el = document.querySelector(`#${tabId}`);
        el.removeAttribute("disabled");
        $(`#${tabId}`).trigger("click");
        el.setAttribute("disabled", true);
    }

    function getSignedUrl(file) {
        return new Promise((resolve, reject) => {
            // Step 1: Get signed URL
            $.ajax({
                url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
                type: 'POST',
                data: {
                    filename: file.name,
                    file_type: file.type,
                    folder: 'test'
                },
                success: function (response) {
                    let data = JSON.parse(response)
                    if (!data.signedUrl || !data.path) {
                        Swal.fire('Invalid Response', 'Server did not return a valid upload URL.', 'error');
                        return;
                    }

                    // Step 2: Upload file to S3
                    Swal.fire({
                        title: 'Uploading...',
                        html: 'Uploading: <b>0%</b>',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        },
                        willOpen: () => {
                            const content = Swal.getHtmlContainer().querySelector('b');

                            $.ajax({
                                url: data.signedUrl,
                                type: 'PUT',
                                headers: {
                                    "Content-Type": file.type,
                                    'x-amz-acl': 'public-read'
                                },
                                processData: false,
                                data: file,
                                xhr: function () {
                                    const xhr = new XMLHttpRequest();
                                    xhr.upload.addEventListener("progress", function (evt) {
                                        if (evt.lengthComputable) {
                                            const percentComplete = Math.round((evt.loaded / evt.total) * 100);
                                            content.textContent = `${percentComplete}%`;
                                        }
                                    }, false);
                                    return xhr;
                                },
                                success: function (_, status, xhr) {
                                    if (xhr.status == 200 || xhr.status == 201) {
                                        // Swal.fire({
                                        //     icon: 'success',
                                        //     title: 'Upload Complete!',
                                        // });
                                        resolve({ path: data.path, fileName: file.name });
                                    } else {
                                        Swal.fire('Unexpected Status', 'Upload finished with unknown status: ' + xhr.status, 'warning');
                                        reject('Unexpected status');
                                    }
                                },
                                error: function (xhr, status, err) {
                                    console.error('Upload Error:', err);
                                    Swal.fire('Upload Failed', 'There was an error uploading the file.', 'error');
                                    reject(err);
                                }
                            });
                        }
                    });
                },
                error: function () {
                    Swal.fire('Error', 'Failed to get signed URL from server.', 'error');
                    reject('Request failed');
                }
            });
        });
    }

    async function submitQuotation() {
        const maxCharLength = 500;

        const allRemarks = document.querySelectorAll(".note-editable");
        const quotation_remarks = allRemarks[0].innerHTML;
        const tech_eval_remarks = allRemarks[1].innerHTML;
        const price_eval_remarks = allRemarks[2].innerHTML;
        const delivery_eval_remarks = allRemarks[3].innerHTML;

        if (delivery_eval_remarks.trim().length > maxCharLength) {
            $("#file-error").hide();
            $("#vendor-name-error").hide();
            $("#quotation-price-error").hide();
            $("#quotation-remarks-error").hide();
            $("#tech-remarks-error").hide();
            $("#price-remarks-error").hide();
            $("#delivery-remarks-error")
                .text(`Delivery evaluation remarks should be less than 500 characters, Please remove extra ${Math.abs(500 - delivery_eval_remarks.trim().length)} characters`)
                .css("color", "red")
                .show();
            return;
        } else {
            $("#delivery-remarks-error").hide();
        }

        const submitBtn = $("#submitQuotation");
        submitBtn.prop("disabled", true).text("Submitting...");

        if (!validateUploadFile()) {
            submitBtn.prop("disabled", false).text("Submit");
            return;
        }

        const indentId = $("#indentId").val();
        const vendor_id = $("#vendor_name").val();
        // const quotation_price = $("#quotation_price").val();
        const quotation_price = +$("#grand-total").text();
        const upload_file = $("#upload_file").prop('files')[0];

        if (delivery_eval_remarks.trim().length <= 0) {
            submitBtn.prop("disabled", false).text("Submit");

            $("#file-error, #vendor-name-error, #quotation-price-error, #quotation-remarks-error, #tech-remarks-error, #price-remarks-error").hide();
            $("#delivery-remarks-error")
                .text("Please enter delivery evaluation remarks to proceed")
                .css("color", "red")
                .show();
            return;
        }

        // reset errors
        $("#file-error, #vendor-name-error, #quotation-price-error, #quotation-remarks-error, #tech-remarks-error, #price-remarks-error, #delivery-remarks-error").hide();

        const formData = new FormData();
        formData.append("indentId", indentId);
        formData.append("vendor_id", vendor_id);
        formData.append("quotation_price", quotation_price);
        formData.append("quotation_remarks", quotation_remarks);

        let storedUrl = "";
        try {
            const result = await getSignedUrl(upload_file);
            storedUrl = result.path;
        } catch (err) {
            console.error("File upload failed:", err);
            Swal.fire({ icon: 'error', title: 'Upload Error', text: `Failed to upload file: ${file.name}` });
            return;
        }

        formData.append("storedUrl", storedUrl);

        formData.append("tech_eval_remarks", tech_eval_remarks);
        formData.append("price_eval_remarks", price_eval_remarks);
        formData.append("delivery_eval_remarks", delivery_eval_remarks);
        formData.append("quotation_items", JSON.stringify(updatedQuotationItems));

        $(".moveTab-4").text("Submitting...").prop("disabled", true);

        await $.ajax({
            url: "<?php echo site_url('procurement/Requisition_controller_v2/upload_indent_qoutation'); ?>",
            type: "POST",
            data: formData,
            async: true,
            processData: false,
            contentType: false,
            cache: false,
        })
            .done(function (data) {
                updateUI(indentId);

                if (+data) {
                    Swal.fire({
                        title: "Uploaded!",
                        text: "Your file has been uploaded.",
                        icon: "success"
                    }).then(() => {
                        $("#upload_pdf_modal").trigger("click");
                        window.location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text: "Something went wrong!",
                    });
                }
            })
            .fail(function () {
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: "Something went wrong!",
                });
            })
            .always(function () {
                $(".moveTab-4").text("Submit").prop("disabled", false);
                submitBtn.prop("disabled", false).text("Submit");
            });
    }


    function validateUploadFile() {
        const allowedExtensions = ['pdf'];
        const maxSizeMB = 5;
        const fileInput = document.getElementById('upload_file');

        if (!fileInput || fileInput.files.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: 'No file selected',
                text: 'Please choose a file before proceeding.',
            });
            return false;
        }

        const file = fileInput.files[0];
        const fileName = file.name || "";
        const fileSizeMB = file.size / (1024 * 1024);
        const fileExt = fileName.split('.').pop().toLowerCase();

        if (!allowedExtensions.includes(fileExt)) {
            Swal.fire({
                icon: 'error',
                title: 'Invalid File Type',
                text: `Only the following file types are allowed: ${allowedExtensions.join(', ')}`,
            });
            fileInput.value = '';
            return false;
        }

        // validate invalid size || 0 size
        if (fileSizeMB <= 0) {
            Swal.fire({
                icon: 'error',
                title: 'Invalid File',
                text: 'The selected file is empty. Please upload a valid file.',
            });
            fileInput.value = '';
            return false;
        }

        // Validate file size doesn't exceed max
        if (fileSizeMB > maxSizeMB) {
            Swal.fire({
                icon: 'error',
                title: 'File Too Large',
                text: `Maximum allowed file size is ${maxSizeMB}MB.`,
            });
            fileInput.value = '';
            return false;
        }

        return true;
    }
</script>