<ul class="breadcrumb">
        <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
        <li><a href="<?php echo site_url('procurement/requisition_controller_v2'); ?>">Procurement</a></li>
        <li><a href="<?php echo site_url('procurement/requisition_controller_v2/purchase_order_v2'); ?>">Purchase Order
                        v2</a>
        </li>
        <li>Create New PO</li>
</ul>

<div class="col-md-12">
        <div class="card cd_border" style="background-color: #f8f8f8;">
                <div class="card-header panel_heading_new_style_staff_border">
                        <div class="panel-header"
                                style="margin: 0; background: none; border-bottom: 1px solid lightgray; height: 3.7rem;">
                                <h4>
                                        <a class="back_anchor"
                                                href="<?php echo site_url('procurement/requisition_controller_v2/purchase_order_v2') ?>">
                                                <span class="fa fa-arrow-left"></span>
                                        </a>
                                        Create New PO
                                </h4>
                        </div>
                </div>
                <div class="col-md-12">
                        <!-- Add hidden input for po_master_id -->
                        <input type="hidden" id="po_master_id" value="0">

                        <div class="container mt-5">
                                <h2>Create New Purchase Order</h2>
                                <div class="stepper">
                                        <div class="step-line"></div>
                                        <div class="step" data-step="1">
                                                <div class="step-circle active-step">○</div>
                                                <div>Details</div>
                                        </div>
                                        <div class="step" data-step="2" id="step-icon-2">
                                                <div class="step-circle">○</div>
                                                <div>Add Items</div>
                                        </div>
                                        <div class="step" data-step="3" id="step-icon-3">
                                                <div class="step-circle">○</div>
                                                <div>Add Milestones</div>
                                        </div>
                                        <div class="step" data-step="4">
                                                <div class="step-circle">○</div>
                                                <div>Additional Details</div>
                                        </div>
                                        <div class="step" data-step="5">
                                                <div class="step-circle">○</div>
                                                <div>Approvers</div>
                                        </div>
                                </div>

                                <div class="card p-4">
                                        <div id="step-1" class="step-content">
                                                <!-- Details starts here -->
                                                <div class="details-container container gap-div">
                                                        <h4>Details</h4>
                                                        <form>
                                                                <div class="form-row">
                                                                        <div class="form-group col-md-6">
                                                                                <label for="request_number">Purchase
                                                                                        Order Request No. <font
                                                                                                color="red">*</font>
                                                                                </label>
                                                                                <input type="text" class="form-control"
                                                                                        id="request_number"
                                                                                        name="request_number"
                                                                                        value="<?php echo $requestNo ?>"
                                                                                        readonly>
                                                                        </div>

                                                                        <div class="form-group col-md-6">
                                                                                <label for="purchase_order_name">Purchase
                                                                                        Order Name <font color="red">*
                                                                                        </font></label>
                                                                                <input type="text" class="form-control"
                                                                                        name="purchase_order_name"
                                                                                        id="purchase_order_name"
                                                                                        placeholder="Purchase Order Name">
                                                                        </div>

                                                                        <div class="form-group col-md-6">
                                                                                <label for="created_by_id">Created By
                                                                                        <font color="red">*</font>
                                                                                </label>
                                                                                <input type="text" class="form-control"
                                                                                        value="<?php echo $loggedinUserName; ?>"
                                                                                        readonly name="created_by_id"
                                                                                        id="created_by_id">
                                                                        </div>
                                                                        <div class="form-group col-md-6">
                                                                                <label for="requester_id">Requester
                                                                                        <font color="red">*</font>
                                                                                </label>
                                                                                <select class="form-control"
                                                                                        name="requester_id"
                                                                                        id="requester_id">
                                                                                        <option value='0'>Admin</option>
                                                                                        ;
                                                                                        <?php
                                                                                        foreach ($staffDetails as $key => $staff) {
                                                                                                $selected = $staff->id == $loggedinUserId ? "Selected" : "";
                                                                                                echo "<option " . $selected . " value='" . $staff->id . "'>" . $staff->staff_name . "</option>";
                                                                                        }
                                                                                        ?>
                                                                                </select>
                                                                        </div>

                                                                        <div class="form-group col-md-6">
                                                                                <label for="request_type">Request
                                                                                        Type <font color="red">*</font>
                                                                                </label>
                                                                                <select name="request_type"
                                                                                        id="request_type"
                                                                                        class="form-control"
                                                                                        onchange="filterVendorsByRequestType()">
                                                                                        <option value="">Select..
                                                                                        </option>

                                                                                        <?php if (!empty($poTypes)) {
                                                                                                foreach ($poTypes as $key => $val) {
                                                                                                        // Customize label if category_type is "Service Milestones"
                                                                                                        $label = ($val->category_type === 'Service Milestones') ? 'Services (Milestone Based)' : $val->category_type . ' (Item based)';
                                                                                                        echo "<option value='$val->category_type'>$label</option>";
                                                                                                }
                                                                                        } else {
                                                                                                echo "<option value=''>No request type to show</option>";
                                                                                        } ?>
                                                                                </select>
                                                                        </div>

                                                                        <div class="form-group col-md-6">
                                                                                <label for="priority">Priority</label>
                                                                                <select name="priority" id="priority"
                                                                                        class="form-control">
                                                                                        <option value="">Select..
                                                                                        </option>
                                                                                        <option value="P1">P1</option>
                                                                                        <option value="P2">P2</option>
                                                                                        <option value="P3">P3</option>
                                                                                </select>
                                                                        </div>


                                                                        <div class="form-group col-md-6">
                                                                                <label for="vendor_id">Vendor <font
                                                                                                color="red">*</font>
                                                                                </label>
                                                                                <select name="vendor_id" id="vendor_id"
                                                                                        class="form-control select2"
                                                                                        required disabled>
                                                                                        <option value="">Select Vendor
                                                                                        </option>
                                                                                        <?php foreach ($vendors as $row) { ?>
                                                                                                <option
                                                                                                        value="<?php echo $row->vendor_id ?>">
                                                                                                        <?php echo $row->vendor_name ?>
                                                                                                </option>
                                                                                        <?php } ?>
                                                                                </select>
                                                                        </div>

                                                                        <div class="form-group col-md-6">
                                                                                <label for="department_id">Department
                                                                                        <font color="red">*</font>
                                                                                </label>
                                                                                <select name="department_id"
                                                                                        onchange="check_if_approvers_assigned('department_id')"
                                                                                        id="department_id"
                                                                                        class="form-control">
                                                                                        <option value="">Select..
                                                                                        </option>
                                                                                        <?php if (!empty($department_list)) { ?>
                                                                                                <?php foreach ($department_list as $key => $val) {
                                                                                                        echo "<option data-has-approvers='$val->has_approvers' value='$val->id'>$val->department</option>";
                                                                                                } ?>
                                                                                        <?php } else {
                                                                                                echo "<option value=''>No department to show</option>";
                                                                                        } ?>
                                                                                </select>
                                                                        </div>

                                                                        <div class="form-group col-md-6">
                                                                                <label for="purchase_order_date">Purchase
                                                                                        Order Date</label>
                                                                                <input type="date" class="form-control"
                                                                                        name="purchase_order_date"
                                                                                        id="purchase_order_date">
                                                                        </div>

                                                                        <!-- <div class="form-group col-md-6">
                                                                                <label for="purchase_order_tolerance">Purchase
                                                                                        Order Tolerance</label>
                                                                                <input type="number"
                                                                                        class="form-control"
                                                                                        name="purchase_order_tolerance"
                                                                                        id="purchase_order_tolerance"
                                                                                        step="0.1" min="0" max="100"
                                                                                        value="0"
                                                                                        oninput="validateTolerance()">
                                                                        </div> -->



                                                                        <div class="form-group col-md-12">
                                                                                <label for="terms_and_conditions">Terms
                                                                                        & Conditions <font color="red">*
                                                                                        </font></label>
                                                                                <textarea class="form-control"
                                                                                        type="text"
                                                                                        name="terms_and_conditions"
                                                                                        id="terms_and_conditions"
                                                                                        placeholder="Enter terms and conditions here"
                                                                                        style="height: 150px;"></textarea>
                                                                                <div class="input-group">
                                                                                        <div class="help-block mt-2"
                                                                                                style="font-size:0.9em; text-align:left;">
                                                                                                Use <code>|</code>
                                                                                                (pipe) to separate
                                                                                                multiple Terms &
                                                                                                Conditions.<br>
                                                                                                Use <code>--&gt;</code>
                                                                                                (double dash + greater
                                                                                                than) to define
                                                                                                sub-points under a main
                                                                                                term.<br>
                                                                                                Payment Terms-->50%
                                                                                                Advance-->Remaining on
                                                                                                Delivery |
                                                                                                Delivery-->Within 7
                                                                                                days-->Subject to
                                                                                                availability |
                                                                                                Warranty-->1
                                                                                                Year-->On-site support
                                                                                        </div>
                                                                                </div>
                                                                        </div>
                                                                </div>
                                                        </form>
                                                </div>
                                        </div>
                                        <div id="step-2" class="step-content hidden">
                                                <!-- product list goes here -->
                                                <div class="product-list-container container gap-div"
                                                        style="max-width: 90%;">
                                                        <h4>Item List</h4>
                                                        <div class="empty-state center-div">
                                                                <div>
                                                                        <div id="products-list">
                                                                                <h5>No Item Found</h5>
                                                                                <p class="text-muted">Please add Item
                                                                                        to see</p>
                                                                        </div>

                                                                        <button id="addItemsBtn" type="button"
                                                                                class="btn btn-dark" data-toggle="modal"
                                                                                data-target="#addProductsModal"
                                                                                onclick="populateCategoryOptions('add')">
                                                                                <i class="fas fa-plus"></i> Add New
                                                                                Item
                                                                        </button>
                                                                </div>
                                                        </div>
                                                        <table class="table table-bordered mt-3" id="productTable"
                                                                style="display: none;">
                                                                <thead>
                                                                        <tr>
                                                                                <th>#</th>
                                                                                <th>Item Name</th>
                                                                                <th>Category</th>
                                                                                <th>Quantity</th>
                                                                                <th>Action</th>
                                                                        </tr>
                                                                </thead>
                                                                <tbody>
                                                                        <!-- Product rows will be dynamically added here -->
                                                                </tbody>
                                                        </table>
                                                </div>
                                        </div>
                                        <div id="step-3" class="step-content hidden">
                                                <!-- Payment Details Section -->
                                                <div class="container mt-4">
                                                        <div class="card p-4">
                                                                <h5 class="font-weight-bold">Payment Details</h5>

                                                                <div class="form-group">
                                                                        <label for="advanceAmount">Advance<font
                                                                                        color="red">*
                                                                                </font></label>
                                                                        <input type="text" class="form-control"
                                                                                id="advanceAmount"
                                                                                placeholder="Enter Amount" value="0">
                                                                </div>

                                                                <div class="mt-3">
                                                                        <div id="payment-details">
                                                                                <!-- payment details goes here -->
                                                                        </div>
                                                                </div>
                                                        </div>
                                                </div>

                                                <!-- milestones list goes here -->
                                                <div class="milestones-container container gap-div"
                                                        style="max-width: 97%;margin-top: 2rem;">
                                                        <h4>Milestones</h4>
                                                        <div class="empty-state center-div" id="milestones-list-empty">
                                                                <div>
                                                                        <div id="milestones-list">
                                                                                <h5>No Milestone Found</h5>
                                                                                <p class="text-muted">Please add
                                                                                        milestone to see</p>
                                                                        </div>

                                                                        <button type="button" class="btn btn-dark"
                                                                                data-toggle="modal"
                                                                                data-target="#addMilestonesModal">
                                                                                <i class="fas fa-plus"></i> Add New Milestone
                                                                        </button>
                                                                </div>
                                                        </div>
                                                </div>

                                        </div>
                                        <div id="step-4" class="step-content hidden">
                                                <!-- Additional Details Section -->
                                                <div class="container mt-4">
                                                        <h5 class="font-weight-bold mb-4">Additional Details</h5>
                                                        <div class="row">
                                                                <!-- Uploaded Files Table on the left -->
                                                                <div class="col-md-6 mb-3 order-md-1 order-2"
                                                                        id="attachments-col"
                                                                        style="border-right:2px dotted #bbb; min-height: 320px;">
                                                                        <label class="font-weight-bold">Uploaded
                                                                                Files</label>
                                                                        <table class="table table-bordered mt-3"
                                                                                id="uploadedFilesTable"
                                                                                style="display:none;">
                                                                                <thead>
                                                                                        <tr>
                                                                                                <th>#</th>
                                                                                                <th>File Name</th>
                                                                                                <th>Type</th>
                                                                                                <th>Size</th>
                                                                                                <th>Action</th>
                                                                                        </tr>
                                                                                </thead>
                                                                                <tbody>
                                                                                        <!-- Progress bar row -->
                                                                                        <tr id="uploadProgressRow"
                                                                                                style="display: none;">
                                                                                                <td colspan="5"
                                                                                                        class="text-center">
                                                                                                        <div class="progress"
                                                                                                                style="height: 20px;">
                                                                                                                <div id="uploadProgressBar"
                                                                                                                        class="progress-bar"
                                                                                                                        role="progressbar"
                                                                                                                        style="width: 0%;"
                                                                                                                        aria-valuenow="0"
                                                                                                                        aria-valuemin="0"
                                                                                                                        aria-valuemax="100">
                                                                                                                        0%
                                                                                                                </div>
                                                                                                        </div>
                                                                                                </td>
                                                                                        </tr>
                                                                                </tbody>
                                                                        </table>
                                                                        <div id="no-attachments-msg"
                                                                                class="text-center text-muted"
                                                                                style="display:block;">
                                                                                <div style="padding: 2.5rem 0;">
                                                                                        <h5>No Attachments Found</h5>
                                                                                        <p class="mb-1">Please add
                                                                                                Attachments to see</p>
                                                                                        <p class="mb-0">Please use the
                                                                                                right-side form to add
                                                                                                attachments</p>
                                                                                </div>
                                                                        </div>
                                                                </div>
                                                                <!-- Remarks and Upload Documents on the right -->
                                                                <div class="col-md-6 mb-3 order-md-2 order-1">
                                                                        <label for="remarks"
                                                                                class="font-weight-bold">Remarks</label>
                                                                        <textarea class="form-control" id="remarks"
                                                                                rows="4"
                                                                                placeholder="Enter Remarks"></textarea>
                                                                        <label for="fileUpload"
                                                                                class="font-weight-bold mt-3">Upload
                                                                                Documents<font color="red">*</font>
                                                                        </label>
                                                                        <div id="uploadArea"
                                                                                class="upload-area p-3 text-center">
                                                                                <input type="file" id="fileInput"
                                                                                        class="d-none"
                                                                                        accept="application/pdf">
                                                                                <p class="mb-2"><strong>Drag and Drop
                                                                                                File Here</strong></p>
                                                                                <p class="text-muted">OR</p>
                                                                                <button class="btn btn-primary btn-sm"
                                                                                        id="browseBtn">Browse</button>
                                                                                <div id="uploadLoader" class="mt-3"
                                                                                        style="display: none;">
                                                                                        <i
                                                                                                class="fas fa-spinner fa-spin"></i>
                                                                                        Uploading...
                                                                                </div>
                                                                                <div id="uploadBar"
                                                                                        style="height: 10px; width: 0%; background: #4caf50; border-radius: 4px;">
                                                                                </div>
                                                                        </div>
                                                                        <p class="text-muted">
                                                                                Allowed file types:
                                                                                <strong>PDF</strong><br>
                                                                                Max size: <strong>5MB</strong>
                                                                        </p>
                                                                        <p class="mt-2 text-muted small" id="fileName">
                                                                        </p>
                                                                </div>
                                                        </div>
                                                </div>
                                        </div>
                                        <div id="step-5" class="step-content hidden">
                                                <div class="container mt-4">
                                                        <div id="approvers-list">
                                                                <!-- approvers list will come here -->
                                                                <p>No approvers found for this purchase order.</p>
                                                        </div>
                                                </div>
                                        </div>
                                        <div class="col-md-12" style="display:flex;justify-content: end;gap: 1.5rem;">
                                                <button id="prevBtn" class="btn btn-outline-secondary"
                                                        disabled>Previous</button>

                                                <button id="nextBtn" class="btn btn-dark">Save &
                                                        Next</button>
                                        </div>
                                </div>

                                <!-- Move buttons to the bottom Old UI of Previous and Save & Next -->
                                <!-- <div class="d-flex justify-content-between mt-3 sticky-footer">
                                        <button id="prevBtn" class="btn btn-outline-secondary"
                                                disabled>Previous</button>
                                        <button id="nextBtn" class="btn btn-dark">Save & Next</button>
                                </div> -->
                        </div>
                </div>

        </div>
</div>
</div>

<?php $this->load->view("procurement/requisition_view_v2/purchase_order_v2/create_purchase/desktop_add_products.php") ?>
<?php $this->load->view("procurement/requisition_view_v2/purchase_order_v2/create_purchase/desktop_add_milestones.php") ?>

<style>
        .center-div {
                display: flex;
                justify-content: center;
                align-items: center;
        }

        .gap-div {
                margin: auto auto 6rem;
                background-color: #fff;
                padding: 2rem;
                border-radius: 8px;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .gap-div-top {
                margin-top: 2rem;
        }

        /* UI for stepped component */
        .stepper {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 20px;
                position: relative;
        }

        .step {
                text-align: center;
                position: relative;
                z-index: 1;
        }

        .step-circle {
                width: 30px;
                height: 30px;
                border-radius: 50%;
                background: gray;
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 5px;
                position: relative;
                z-index: 2;
        }

        .active-step {
                background: green;
                /* Change background to green for the active step */
        }

        .visited-step {
                background: green;
                /* Change background to green for visited steps */
        }

        .step-line {
                position: absolute;
                top: 15px;
                left: 0;
                width: 100%;
                height: 4px;
                background: gray;
                z-index: 1;
        }

        .hidden {
                display: none;
        }

        .upload-area {
                border: 2px dashed #ccc;
                border-radius: 5px;
                text-align: center;
                padding: 30px;
                cursor: pointer;
        }

        .upload-area:hover {
                background-color: #f8f9fa;
        }

        .sticky-footer {
                /* position: fixed; */
                bottom: 0;
                left: 0;
                right: 0;
                background-color: #fff;
                padding: 1rem 2rem;
                box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
                z-index: 1000;
        }

        .sticky-footer button {
                margin: 0 0.5rem;
        }

        .modal-dialog {
                max-width: 70%;
                margin: auto;
                display: flex;
                align-items: center;
                justify-content: center;
        }

        option[data-disabled="true"] {
                color: gray;
        }
</style>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript">
        var addedItemsBucket = [];

        var itemModalMode = "";

        var isDirectPOCreation = true;
        // handle total remaining amount to be padding
        var totalAmountToBePaid = 0;

        var isMilestoneBasedPurchaseOrder = 0;

        var isApproverExist = false;

        $("#step-icon-2").hide();
        $("#step-icon-3").hide();

        // Stepper logic starts here
        let currentStep = 1;

        const totalSteps = 5;

        // Ensure vendorList is properly initialized
        const vendorList = <?php echo json_encode($vendors); ?> || [];
        const procurementCategory = <?php echo json_encode($procurementCategory); ?> || [];

        let selectedCategoryId = null; // Variable to store the selected category ID
        let selectedSubCategoryId = null; // Variable to store the selected sub category ID

        var requestType = "";
        // Parse query string in JavaScript
        const queryParams = {};
        const queryString = window.location.search.substring(1);
        queryString.split("&").forEach(param => {
                const [key, value] = param.split("=");
                queryParams[decodeURIComponent(key)] = decodeURIComponent(value || "");
        });

        const typeOfRequest = queryParams['type'] || 'default';
        const typeOfRequestId = queryParams['id'] || 'default';
        handleTypeOfRequest(typeOfRequest, typeOfRequestId);

        function getSourceTypeAndId() {
                return isDirectPOCreation ? { sourceType: "direct", sourceTypeId: 0 } : { sourceType: typeOfRequest, sourceTypeId: typeOfRequestId };
        }

        function updateStep() {
                $('.step-content').addClass('hidden');
                $(`#step-${currentStep}`).removeClass('hidden');

                $('.step-circle').each(function (index) {
                        if (index + 1 < currentStep) {
                                $(this).addClass('visited-step').removeClass('active-step').text('✔');
                        } else if (index + 1 === currentStep) {
                                $(this).addClass('active-step').removeClass('visited-step').text('○');
                        } else {
                                $(this).removeClass('visited-step active-step').text('○');
                        }
                });

                // Ensure the first step "Details" has a green background when active
                if (currentStep === 1) {
                        $('.step-circle[data-step="1"]').addClass('active-step');
                }

                $('#prevBtn').prop('disabled', currentStep === 1);
                $('#nextBtn').text(currentStep === totalSteps ? 'Submit' : 'Save & Next');

                // Trigger handleTypeOfRequest on step update
                handleTypeOfRequest(typeOfRequest, typeOfRequestId);
        }

        $('#nextBtn').click(function () {
                if (currentStep <= totalSteps) {
                        if (currentStep === 1) {
                                if (!validateMandatoryFields()) {
                                        // Swal.fire({
                                        //         icon: 'error',
                                        //         title: 'Validation Error',
                                        //         text: 'Please fill all mandatory fields.',
                                        // });
                                        return;
                                }
                        }
                        // Check if the product list is empty when moving from step 2
                        if (currentStep === 2 && $('#products-list').find('h5').text() === 'No Item Found') {
                                Swal.fire({
                                        icon: 'error',
                                        title: 'Validation Error',
                                        text: 'Please add at least one product before proceeding.',
                                });
                                return;
                        }

                        if (currentStep === 3) {
                                // if (!isMilestoneBasedPurchaseOrder) {
                                //         // blocking this check in case of milestone
                                //         const advanceAmount = parseFloat($("#advanceAmount").val()) || 0;
                                //         const totalAmount = parseFloat($('#balanceAmount').data('total-amount')) || 0;

                                //         if (advanceAmount > totalAmount || advanceAmount < 0) {
                                //                 Swal.fire({
                                //                         icon: 'error',
                                //                         title: 'Validation Error',
                                //                         text: 'Advance amount cannot be greater than total amount or less than zero.',
                                //                 });
                                //                 return;
                                //         }

                                //         const totalAmountAdded = calculateTotalAmountAdded();
                                //         if (totalAmountAdded > Number(totalAmountToBePaid)) {
                                //                 Swal.fire({
                                //                         icon: 'error',
                                //                         title: 'Validation Error',
                                //                         text: `Payment amount cannot exceed the total amount ${formatCurrencyINR(totalAmountToBePaid)}.`,
                                //                 });
                                //                 return;
                                //         }
                                // }

                                // making milestones mandatory
                                const milestonesCount = $('#milestones-list tbody tr').length;
                                if (milestonesCount === 0) {
                                        Swal.fire({
                                                icon: 'error',
                                                title: 'Validation Error',
                                                text: 'Please add at least one milestone before proceeding.',
                                        });
                                        return;
                                }
                        }

                        // making document to upload mandatory
                        // if (currentStep === 4) {
                        //         const filesInTable = $('#uploadedFilesTable tbody tr').not('#uploadProgressRow').length;

                        //         if (filesInTable === 0) {
                        //                 Swal.fire({
                        //                         icon: 'error',
                        //                         title: 'Validation Error',
                        //                         text: 'Please upload at least one document before proceeding.',
                        //                 });
                        //                 return;
                        //         }
                        // }

                        handleStepSubmission(currentStep);

                        // Call handleTypeOfRequest for each forward step change

                        if (currentStep < totalSteps) {
                                if (+currentStep === 1 && isMilestoneBasedPurchaseOrder || currentStep === 2 && !isMilestoneBasedPurchaseOrder) {
                                        currentStep += 2;
                                } else {
                                        currentStep++;
                                }
                                updateStep();
                        }
                }
        });

        $('#prevBtn').click(function () {
                if (currentStep > 1) {
                        if (+currentStep === 3 && isMilestoneBasedPurchaseOrder || +currentStep === 4 && !isMilestoneBasedPurchaseOrder) {
                                currentStep -= 2;
                        } else {
                                currentStep--;
                        }

                        updateStep();

                        handleBackSteps(currentStep);
                }
        });

        function getCurrentPODetailsForStep1() {
                const poMasterId = $("#po_master_id").val();

                $.ajax({
                        url: '<?php echo site_url('procurement/requisition_controller_v2/getCurrentPODetailsForStep1'); ?>',
                        type: "post",
                        data: { poMasterId },
                        success: response => {
                                response = JSON.parse(response);
                                $("#request_number").prop("readonly", false).val(response.request_number).prop("readonly", true);
                        }
                })
        }

        function handleBackSteps(currentStep) {
                const poMasterId = $("#po_master_id").val();

                switch (currentStep) {
                        case 1:
                                getCurrentPODetailsForStep1();
                                // get current purchase id and bring all the data according to that
                                break;
                        case 2:
                        // break;
                        case 3:
                        // break;
                        case 4:
                        // break;
                        case 5:
                                break;
                        default:
                                console.error('Invalid step');
                }
        }

        function handleStepSubmission(step) {
                switch (step) {
                        case 1:
                                createNewPurchaseOrder();
                                break;
                        case 2:
                                // getting total payment details in next step
                                getAddedMilestoneAndTotalPaymentDetails();
                                break;
                        case 3:
                                // submit advance amount
                                submitPaymentDetails();
                                break;
                        case 4:
                                getPurchaseOrderApprovers();
                                break;
                        case 5:
                                const { sourceType, sourceTypeId } = getSourceTypeAndId();

                                if (!isApproverExist) {
                                        Swal.fire({
                                                title: 'No Approvers Found',
                                                text: 'Save as draft because no approvers were found. You can add approvers later.',
                                                icon: 'info',
                                                confirmButtonText: 'OK',
                                                allowOutsideClick: false
                                        }).then(() => {
                                                // Save as draft automatically
                                                $.ajax({
                                                        url: '<?php echo site_url('procurement/requisition_controller_v2/saveFinalSubmitStatus'); ?>',
                                                        type: 'POST',
                                                        data: { po_master_id: $("#po_master_id").val(), "status": "Draft", sourceType, sourceTypeId },
                                                        success: (response) => {
                                                                response = JSON.parse(response);
                                                                Swal.fire({
                                                                        icon: response.icon,
                                                                        title: response.title,
                                                                        text: response.text,
                                                                }).then(() => {
                                                                        if (response.icon === 'success') {
                                                                                window.location.href = "<?php echo site_url('procurement/requisition_controller_v2/purchase_order_v2'); ?>";
                                                                        }
                                                                });
                                                        },
                                                        error: () => {
                                                                Swal.fire({
                                                                        icon: 'error',
                                                                        title: 'Error',
                                                                        text: 'An error occurred while saving as draft.',
                                                                });
                                                        }
                                                });
                                        });
                                } else if (sourceType === "direct") {
                                        Swal.fire({
                                                title: 'What would you like to do?',
                                                text: 'Choose an action for this Purchase Order.',
                                                icon: 'question',
                                                showCancelButton: true,
                                                confirmButtonText: 'Send for Approval',
                                                cancelButtonText: 'Save as Draft',
                                                reverseButtons: true
                                        }).then((result) => {
                                                if (result.isConfirmed) {
                                                        // Logic for sending for approval
                                                        $.ajax({
                                                                url: '<?php echo site_url('procurement/requisition_controller_v2/saveFinalSubmitStatus'); ?>',
                                                                type: 'POST',
                                                                data: { po_master_id: $("#po_master_id").val(), "status": "Requested", sourceType, sourceTypeId },
                                                                success: (response) => {
                                                                        response = JSON.parse(response);
                                                                        Swal.fire({
                                                                                icon: response.icon,
                                                                                title: response.title,
                                                                                text: response.text,
                                                                        }).then(() => {
                                                                                if (response.icon === 'success') {
                                                                                        window.location.href = "<?php echo site_url('procurement/requisition_controller_v2/purchase_order_v2'); ?>";
                                                                                }
                                                                        });
                                                                },
                                                                error: () => {
                                                                        Swal.fire({
                                                                                icon: 'error',
                                                                                title: 'Error',
                                                                                text: 'An error occurred while sending for approval.',
                                                                        });
                                                                }
                                                        });
                                                } else if (result.dismiss === Swal.DismissReason.cancel) {
                                                        // Logic for saving as draft
                                                        $.ajax({
                                                                url: '<?php echo site_url('procurement/requisition_controller_v2/saveFinalSubmitStatus'); ?>',
                                                                type: 'POST',
                                                                data: { po_master_id: $("#po_master_id").val(), "status": "Draft", sourceType, sourceTypeId },
                                                                success: (response) => {
                                                                        response = JSON.parse(response);
                                                                        Swal.fire({
                                                                                icon: response.icon,
                                                                                title: response.title,
                                                                                text: response.text,
                                                                        }).then(() => {
                                                                                if (response.icon === 'success') {
                                                                                        window.location.href = "<?php echo site_url('procurement/requisition_controller_v2/purchase_order_v2'); ?>";
                                                                                }
                                                                        });
                                                                },
                                                                error: () => {
                                                                        Swal.fire({
                                                                                icon: 'error',
                                                                                title: 'Error',
                                                                                text: 'An error occurred while saving as draft.',
                                                                        });
                                                                }
                                                        });
                                                }
                                        });
                                } else {
                                        // creating Po from other than direct
                                        $("#prevBtn").prop("disabled", true);
                                        $("#nextBtn").prop("disabled", true).text("Submitting...");

                                        $.ajax({
                                                url: '<?php echo site_url('procurement/requisition_controller_v2/saveFinalSubmitStatus'); ?>',
                                                type: 'POST',
                                                data: { po_master_id: $("#po_master_id").val(), "status": "Approved", sourceType, sourceTypeId },
                                                success: (response) => {
                                                        response = JSON.parse(response);
                                                        Swal.fire({
                                                                icon: response.icon,
                                                                title: response.title,
                                                                text: response.text,
                                                        }).then(() => {
                                                                if (response.icon === 'success') {
                                                                        window.location.href = "<?php echo site_url('procurement/requisition_controller_v2/purchase_order_v2'); ?>";
                                                                }
                                                        });
                                                },
                                                error: () => {
                                                        Swal.fire({
                                                                icon: 'error',
                                                                title: 'Error',
                                                                text: 'An error occurred while sending for approval.',
                                                        });
                                                }
                                        });
                                }
                                // Reset button text after submission
                                $('#nextBtn').prop('disabled', false).text('Submit');
                                break;
                        default:
                                console.error('Invalid step');
                }
        }

        function validateMandatoryFields() {
                const requestNumber = $("#request_number").val();
                const purchaseOrderName = $("#purchase_order_name").val();
                const createdBy = $("#created_by_id").val();
                const requester = $("#requester_id").val();
                const requestType = $("#request_type").val();
                const vendorId = $("#vendor_id").val();
                const department = $("#department_id").val();
                const termsAndConditions = $("#terms_and_conditions").val().trim();

                if (!requestNumber) {
                        Swal.fire({
                                title: "Missing Request Number",
                                text: "Please enter the request number.",
                                icon: "warning",
                                confirmButtonText: "OK"
                        });
                        return false;
                }

                if (!purchaseOrderName) {
                        Swal.fire({
                                title: "Missing Purchase Order Name",
                                text: "Please enter the purchase order name.",
                                icon: "warning",
                                confirmButtonText: "OK"
                        });
                        return false;
                }

                if (!createdBy) {
                        Swal.fire({
                                title: "Missing Created By",
                                text: "Please select who created the purchase order.",
                                icon: "warning",
                                confirmButtonText: "OK"
                        });
                        return false;
                }

                if (!requester) {
                        Swal.fire({
                                title: "Missing Requester",
                                text: "Please select the requester.",
                                icon: "warning",
                                confirmButtonText: "OK"
                        });
                        return false;
                }

                if (!requestType) {
                        Swal.fire({
                                title: "Missing Request Type",
                                text: "Please select the request type.",
                                icon: "warning",
                                confirmButtonText: "OK"
                        });
                        return false;
                }

                if (!Number(vendorId)) {
                        Swal.fire({
                                title: "Missing Vendor",
                                text: "Please select the vendor.",
                                icon: "warning",
                                confirmButtonText: "OK"
                        });
                        return false;
                }

                if (!department) {
                        Swal.fire({
                                title: "Missing Department",
                                text: "Please select a department.",
                                icon: "warning",
                                confirmButtonText: "OK"
                        });
                        return false;
                }

                if (!termsAndConditions) {
                        Swal.fire({
                                title: "Missing Terms & Conditions",
                                text: "Please fill in the terms and conditions.",
                                icon: "warning",
                                confirmButtonText: "OK"
                        });
                        return false;
                }

                return true;
        }

        function createNewPurchaseOrder() {
                // const poMasterId = $("#po_master_id").val();

                const { sourceType, sourceTypeId } = getSourceTypeAndId();

                const PurchaseOrder = {
                        requesterId: $("#requester_id").val(),
                        purchaseOrderName: $("#purchase_order_name").val(),
                        requestType: $("#request_type").val(),
                        vendorId: $("#vendor_id").val(),
                        // requisitionStatus: $("#requisition_status").val(),
                        termsAndConditions: $("#terms_and_conditions").val(),
                        createdById: $("#created_by_id").val(),
                        priority: $("#priority").val(),
                        departmentId: $("#department_id").val(),
                        purchaseOrderDate: $("#purchase_order_date").val(),
                        poMasterId: $("#po_master_id").val(),
                        sourceType,
                        sourceTypeId,
                        purchaseOrderTolerance: 0
                };

                $.ajax({
                        url: '<?php echo site_url('procurement/requisition_controller_v2/createNewPurchaseOrderv2'); ?>',
                        type: "post",
                        data: PurchaseOrder,
                        success: response => {
                                response = JSON.parse(response);
                                if (response.icon === 'success') {
                                        $("#po_master_id").val(response.data.po_master_id);
                                        $("#request_number").val(response.data.request_number);

                                        // Call handleTypeOfRequest after po_master_id is set
                                        handleTypeOfRequest(typeOfRequest, typeOfRequestId);
                                } else {
                                        $("#po_master_id").val(0);
                                }
                        },
                        error: () => {
                                console.error('An error occurred while creating the purchase order.');
                        }
                });
        }

        function goBack() {
                window.location.href = "<?php echo site_url('procurement/requisition_controller_v2/purchase_order_v2'); ?>";
        }

        // File upload functionality for Additional Details
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileNameDisplay = document.getElementById('fileName');
        const browseBtn = document.getElementById('browseBtn');
        let selectedFiles = [];

        // Handle drag-and-drop events
        uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.style.backgroundColor = '#e9ecef';
        });

        uploadArea.addEventListener('dragleave', () => {
                uploadArea.style.backgroundColor = '#f8f9fa';
        });

        uploadArea.addEventListener('drop', async (e) => {
                e.preventDefault();
                uploadArea.style.backgroundColor = '#f8f9fa';
                const files = Array.from(e.dataTransfer.files);

                // Filter allowed file types and size
                const MAX_FILE_SIZE = 5*1024*1024;
                const allowedExtensions = ['pdf'];
                const invalidFiles = files.filter(file => {
                        const fileExtension = file.name.split('.').pop().toLowerCase();
                        return !allowedExtensions.includes(fileExtension) || file.size > MAX_FILE_SIZE;
                });

                if (invalidFiles.length > 0) {
                        Swal.fire({
                                icon: 'error',
                                title: 'Invalid File',
                                text: 'Only PDF files up to 5 MB are allowed.',
                        });
                        return;
                }

                // I need to upload these files to the server
                $("#browseBtn").hide();
                $("#uploadLoader").show();
                $("#no-attachments-msg").hide(); // Hide 'No Attachments' message as soon as upload starts

                await submitAdditionalDetails(files);

                $("#browseBtn").show();
                $("#uploadLoader").hide();
        });

        // Handle file selection via browse button
        browseBtn.addEventListener('click', () => {
                fileInput.click();
        });

        fileInput.addEventListener('change', async () => {
                const files = Array.from(fileInput.files);

                // Filter allowed file types and size
                const MAX_FILE_SIZE = 5*1024*1024;
                const allowedExtensions = ['pdf'];
                const invalidFiles = files.filter(file => {
                        const fileExtension = file.name.split('.').pop().toLowerCase();
                        return !allowedExtensions.includes(fileExtension) || file.size > MAX_FILE_SIZE;
                });

                if (invalidFiles.length > 0) {
                        Swal.fire({
                                icon: 'error',
                                title: 'Invalid File',
                                text: 'Only PDF files up to 5 MB are allowed.',
                        });
                        fileInput.value = ''; // Reset file input
                        return;
                }

                // I need to upload these files to the server
                $("#browseBtn").hide();
                $("#uploadLoader").show();
                $("#no-attachments-msg").hide(); // Hide 'No Attachments' message as soon as upload starts

                await submitAdditionalDetails(files);

                $("#browseBtn").show();
                $("#uploadLoader").hide();

                // Reset fileInput value to allow re-selection of the same file
                fileInput.value = '';
        });

        // Display selected file names
        function handleFileSelection(files) {
                selectedFiles = [...selectedFiles, ...files];
                // displaySelectedFiles();
        }

        // Show loader during file upload
        $('#fileInput').on('change', function () {
                $('#uploadLoader').show(); // Show loader
                $('#uploadedFilesTable').hide(); // Hide the table during upload
                setTimeout(() => {
                        $('#uploadLoader').hide(); // Hide loader after upload
                        $('#uploadedFilesTable').show(); // Show the table after upload
                        handleFileSelection(Array.from(this.files));
                }, 1000); // Simulate upload delay
        });

        function getSignedUrl(file) {
                return new Promise((resolve, reject) => {
                        // Step 1: Get signed URL
                        $.ajax({
                                url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
                                type: 'POST',
                                data: {
                                        filename: file.name,
                                        file_type: file.type,
                                        folder: 'test'
                                },
                                success: function (response) {
                                        let data = JSON.parse(response)
                                        if (!data.signedUrl || !data.path) {
                                                Swal.fire('Invalid Response', 'Server did not return a valid upload URL.', 'error');
                                                return;
                                        }

                                        // Step 2: Upload file to S3
                                        Swal.fire({
                                                title: 'Uploading...',
                                                html: 'Uploading: <b>0%</b>',
                                                allowOutsideClick: false,
                                                didOpen: () => {
                                                        Swal.showLoading();
                                                },
                                                willOpen: () => {
                                                        const content = Swal.getHtmlContainer().querySelector('b');

                                                        $.ajax({
                                                                url: data.signedUrl,
                                                                type: 'PUT',
                                                                headers: {
                                                                        "Content-Type": file.type,
                                                                        'x-amz-acl': 'public-read'
                                                                },
                                                                processData: false,
                                                                data: file,
                                                                xhr: function () {
                                                                        const xhr = new XMLHttpRequest();
                                                                        xhr.upload.addEventListener("progress", function (evt) {
                                                                                if (evt.lengthComputable) {
                                                                                        const percentComplete = Math.round((evt.loaded / evt.total) * 100);
                                                                                        content.textContent = `${percentComplete}%`;
                                                                                }
                                                                        }, false);
                                                                        return xhr;
                                                                },
                                                                success: function (_, status, xhr) {
                                                                        if (xhr.status == 200 || xhr.status == 201) {
                                                                                Swal.fire({
                                                                                        icon: 'success',
                                                                                        title: 'Upload Complete!',
                                                                                });
                                                                                resolve({ path: data.path, fileName: file.name });
                                                                        } else {
                                                                                Swal.fire('Unexpected Status', 'Upload finished with unknown status: ' + xhr.status, 'warning');
                                                                                reject('Unexpected status');
                                                                        }
                                                                },
                                                                error: function (xhr, status, err) {
                                                                        console.error('Upload Error:', err);
                                                                        Swal.fire('Upload Failed', 'There was an error uploading the file.', 'error');
                                                                        reject(err);
                                                                }
                                                        });
                                                }
                                        });
                                },
                                error: function () {
                                        Swal.fire('Error', 'Failed to get signed URL from server.', 'error');
                                        reject('Request failed');
                                }
                        });
                });
        }

        async function submitAdditionalDetails(selectedFiles) {
                const po_master_id = $("#po_master_id").val();
                const remarks = $('#remarks').val().trim();
                const files = selectedFiles;

                if (files.length === 0) {
                        Swal.fire({
                                icon: 'error',
                                title: 'Validation Error',
                                text: 'Please upload at least one document.',
                        });
                        return;
                }

                if (!po_master_id || isNaN(po_master_id) || po_master_id <= 0) {
                        Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'Invalid Purchase Order ID.',
                        });
                        return;
                }

                const uploadedPaths = [];
                for (const file of files) {
                        try {
                                const result = await getSignedUrl(file);
                                uploadedPaths.push({
                                        url: result.path,
                                        name: result.fileName,
                                        // type: file.type,
                                        type: file.type.split('/').pop().toUpperCase(), // e.g. "pdf"
                                        size: file.size // in bytes
                                });
                        } catch (err) {
                                console.error("File upload failed:", err);
                                Swal.fire({ icon: 'error', title: 'Upload Error', text: `Failed to upload file: ${file.name}` });
                                return;
                        }
                }

                const formData = new FormData();
                formData.append('po_master_id', po_master_id);
                formData.append('remarks', remarks); // Remarks can be empty
                uploadedPaths.forEach((file, index) => {
                        formData.append(`uploaded_files[${index}][url]`, file.url);
                        formData.append(`uploaded_files[${index}][name]`, file.name);
                        formData.append(`uploaded_files[${index}][type]`, file.type);
                        formData.append(`uploaded_files[${index}][size]`, file.size);
                });

                // Add progress bar row to the table
                // Add or reset progress bar BEFORE starting AJAX
                $('#uploadProgressRow').remove(); // in case already added
                const progressRow = `
                <tr id="uploadProgressRow">
                        <td colspan="5" class="text-center">
                        <div class="progress" style="height: 20px;">
                                <div id="uploadProgressBar" class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                        </div>
                        </td>
                </tr>`;
                $('#uploadedFilesTable tbody').append(progressRow);

                await $.ajax({
                        url: '<?php echo site_url('procurement/requisition_controller_v2/submitAdditionalDetailsInPurchaseOrderV2'); ?>',
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        xhr: function () {
                                var xhr = new window.XMLHttpRequest();
                                var lastPercent = 0; // Track the last logged percentage
                                var interval; // For simulated progress

                                xhr.upload.addEventListener("progress", function (evt) {
                                        if (evt.lengthComputable) {
                                                const currentPercent = Math.round((evt.loaded / evt.total) * 100);

                                                if (interval) clearInterval(interval);

                                                interval = setInterval(() => {
                                                        if (lastPercent < currentPercent) {
                                                                lastPercent++;
                                                                $("#uploadProgressBar")
                                                                        .css('width', lastPercent + '%')
                                                                        .attr('aria-valuenow', lastPercent)
                                                                        .text(lastPercent + '%');
                                                        } else {
                                                                clearInterval(interval);
                                                        }
                                                }, 50);
                                        }
                                });

                                xhr.addEventListener("loadend", function () {
                                        clearInterval(interval);
                                        $("#uploadProgressBar").css('width', '100%').attr('aria-valuenow', 100).text('100%');
                                });

                                return xhr;
                        },

                        success: (response) => {
                                response = JSON.parse(response);

                                if (response.icon === 'success') {
                                        displaySelectedFiles(response.data);
                                }

                                $('#remarks').val("");
                                $('#uploadProgressRow').remove(); // Remove progress bar row after upload
                        },

                        error: () => {
                                Swal.fire({
                                        icon: 'error',
                                        title: 'Error',
                                        text: 'An error occurred while submitting additional details.',
                                });
                                $('#uploadProgressRow').remove(); // Remove progress bar row on error
                        }
                });
        }

        function downloadDocument(documentPath) {
                if (!documentPath) {
                        Swal.fire('Error', 'Document path is missing.', 'error');
                        return;
                }
                // Open the document in a new tab
                window.open(documentPath, '_blank');
        }

        function displaySelectedFiles(selectedFiles) {
                const tableBody = document.querySelector('#uploadedFilesTable tbody');
                tableBody.innerHTML = '';

                if (selectedFiles.length > 0) {
                        selectedFiles.forEach((file, index) => {
                                const row = document.createElement('tr');
                                row.innerHTML = `
                        <td class="text-center">${index + 1}</td>
                        <td>${file.document_name}</td>
                        <td class="text-center">${file.document_type}</td>
                        <td class="text-center">${file.document_size === "0 Bytes" ? "N/A" : file.document_size}</td>
                        <td class="text-center">
                            <button class="btn btn-primary btn-sm" onclick="downloadDocument('${file.document_url}')">
                                <i class="fas fa-download"></i> Download
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="removeFile('${file.id}','${file.document_name}', ${file.procurement_requisition_id})">
                                <i class="fas fa-trash-alt"></i> Remove
                            </button>
                        </td>
                    `;
                                tableBody.appendChild(row);
                        });

                        document.getElementById('uploadedFilesTable').style.display = 'table';
                        document.getElementById('no-attachments-msg').style.display = 'none';
                } else {
                        document.getElementById('uploadedFilesTable').style.display = 'none';
                        document.getElementById('no-attachments-msg').style.display = 'block';
                }
        }


        function removeFile(documentId, documentName, poMasterId) {
                if (!Number(documentId)) {
                        Swal.fire({
                                title: "Error!",
                                text: "Invalid Document ID.",
                                icon: "error",
                                button: "OK",
                        });
                        return;
                }

                Swal.fire({
                        title: "Are you sure?",
                        text: `Do you want to remove this document - ${documentName}?`,
                        icon: "warning",
                        showCancelButton: true,
                        confirmButtonText: "Yes, remove it!",
                        cancelButtonText: "Cancel"
                }).then((result) => {
                        if (result.isConfirmed) {
                                // Disable all buttons and inputs during the removal process
                                $('button, input, select, textarea').prop('disabled', true);

                                $.ajax({
                                        url: '<?php echo site_url('procurement/requisition_controller_v2/removeUploadedDocsPOV2'); ?>',
                                        type: 'POST',
                                        data: { documentId, poMasterId },
                                        success: (response) => {
                                                response = JSON.parse(response);
                                                if (response.icon === 'success') {
                                                        displaySelectedFiles(response.data);
                                                } else {
                                                        Swal.fire({
                                                                icon: response.icon,
                                                                title: response.title || 'Error',
                                                                text: response.text || 'Something went wrong!',
                                                        });
                                                }
                                        },
                                        error: () => {
                                                Swal.fire({
                                                        icon: 'error',
                                                        title: 'Error',
                                                        text: 'An error occurred while submitting additional details.',
                                                });
                                        },
                                        complete: () => {
                                                // Re-enable all buttons and inputs after the process is complete
                                                $('button, input, select, textarea').prop('disabled', false);
                                        }
                                });
                        }
                });
        }

        function getPurchaseOrderApprovers() {
                const po_master_id = $("#po_master_id").val();

                // Validate po_master_id
                if (!po_master_id || isNaN(po_master_id) || po_master_id <= 0) {
                        return;
                }

                const { sourceType, sourceTypeId } = getSourceTypeAndId();

                // Fetch approvers via AJAX
                $.ajax({
                        url: '<?php echo site_url('procurement/requisition_controller_v2/getPurchaseOrderApprovers'); ?>',
                        type: 'POST',
                        data: { po_master_id, sourceType, sourceTypeId },
                        success: (response) => {
                                approvers = JSON.parse(response);

                                let approversHtml = '';

                                if (Object.entries(approvers).length) {
                                        isApproverExist = true;
                                        approversHtml += `
                                                        <table class="table table-striped table-hover">
                                                                <thead class="thead-dark">
                                                                        <tr>
                                                                                <th scope="col">#</th>
                                                                                <th scope="col">Approver</th>
                                                                                <th scope="col">Approver Type</th>
                                                                                <th scope="col">Department</th>
                                                                                <th scope="col">Designation</th>
                                                                                <th scope="col">Status</th>
                                                                        </tr>
                                                                </thead>
                                                                <tbody>
                                                `;
                                        Object.values(approvers).forEach((approver, idx) => {
                                                approversHtml += `
                                                                        <tr>
                                                                                <td>${idx + 1}</td>
                                                                                <td>${approver.name}</td>
                                                                                <td>${approver.approverType}</td>
                                                                                <td>${approver.department}</td>
                                                                                <td>${approver.designation}</td>
                                                                                <td>${approver.status}</td>
                                                                        </tr>
                                                                `;
                                        });
                                        approversHtml += '</tbody></table>';
                                } else {
                                        isApproverExist = false;
                                        approversHtml = '<p class="text-muted">No approvers found for this purchase order.</p>';
                                }
                                // Display approvers in the appropriate section
                                $("#approvers-list").html(approversHtml);


                        },
                        error: () => {
                                console.error('An error occurred while fetching approvers.');
                        }
                });
        }

        function getAddedMilestoneAndTotalPaymentDetails() {
                const po_master_id = $("#po_master_id").val();

                // Validate po_master_id
                if (!po_master_id || isNaN(po_master_id) || po_master_id <= 0) {
                        return;
                }

                // Fetch milestone details via AJAX
                $.ajax({
                        url: '<?php echo site_url('procurement/requisition_controller_v2/getAddedMilestoneAndTotalPaymentDetails'); ?>',
                        type: 'POST',
                        data: { po_master_id },
                        success: (response) => {
                                const { milestones, totalAmount } = JSON.parse(response);

                                totalAmountToBePaid = totalAmount;

                                let paymentHtml = '';

                                paymentHtml += `<div class="d-flex justify-content-between font-weight-bold">
                                                                <h6>Balance</h6>
                                                                <h6 id="balanceAmount" data-total-amount="${totalAmount}">${formatCurrencyINR(totalAmount)}</h6>
                                                        </div>`;
                                $("#payment-details").html(paymentHtml);
                        },
                        error: () => {
                                console.error('An error occurred while fetching milestone details.');
                        }
                });
        }

        function formatCurrencyINR(amount) {
                return new Intl.NumberFormat('en-IN', {
                        style: 'currency',
                        currency: 'INR'
                }).format(amount);
        }

        // Function to update balance amount when advance amount changes
        // $('#advanceAmount').on('input', function () {
        //         const advanceAmount = parseFloat($(this).val()) || 0; // Get advance amount or default to 0
        //         const totalAmount = parseFloat($('#balanceAmount').data('total-amount')) || 0; // Get total amount from data attribute

        //         // Validation: Ensure advance amount is not greater than total amount
        //         if (advanceAmount > totalAmount) {
        //                 Swal.fire({
        //                         icon: 'error',
        //                         title: 'Validation Error',
        //                         text: 'Advance amount cannot be greater than the balance amount.',
        //                 });
        //                 // $(this).val(''); // Clear the input field
        //                 $('#balanceAmount').text(formatCurrencyINR(totalAmount)); // Reset balance amount display
        //                 return;
        //         }

        //         const newBalance = totalAmount - advanceAmount; // Calculate new balance
        //         $('#balanceAmount').text(formatCurrencyINR(newBalance)); // Update balance amount display
        // });

        function submitPaymentDetails() {
                const po_master_id = $("#po_master_id").val();
                const advanceAmount = parseFloat($("#advanceAmount").val()) || 0;
                const totalAmount = parseFloat($('#balanceAmount').data('total-amount')) || 0;

                if (!po_master_id || isNaN(po_master_id) || po_master_id <= 0) {
                        return;
                }

                const paymentStatus = advanceAmount === 0 ? "Pending" : advanceAmount === totalAmount ? "Completed" : "Pending";
                const paymentDetails = { po_master_id, advanceAmount, paymentStatus };

                $.ajax({
                        url: '<?php echo site_url('procurement/requisition_controller_v2/submitPaymentDetails'); ?>',
                        type: 'POST',
                        data: paymentDetails,
                        success: (response) => {
                                response = JSON.parse(response);
                                if (response.icon === 'success') {
                                        const newBalance = totalAmount - advanceAmount;
                                        $('#balanceAmount').text(formatCurrencyINR(newBalance));
                                }
                        },
                        error: () => {
                                Swal.fire({
                                        icon: 'error',
                                        title: 'Error',
                                        text: 'An error occurred while submitting payment details.',
                                });
                        }
                });
        }

        function filterVendorsByRequestType() {
                requestType = $('#request_type').val();

                isMilestoneBasedPurchaseOrder = requestType === "Service Milestones";

                if (isMilestoneBasedPurchaseOrder) {
                        $("#step-icon-2").hide();
                        $("#step-icon-3").show();
                } else if (requestType != "") {
                        $("#step-icon-3").hide();
                        $("#step-icon-2").show();
                } else {
                        $("#step-icon-2").hide();
                        $("#step-icon-3").hide();
                }

                let vendorOptions = '<option value="">Select Vendor</option>';
                if (requestType) {
                        let previousVendorId = 0;
                        vendorList.forEach(vendor => {
                                if (requestType === "Service Milestones" && previousVendorId != vendor.vendor_id) {
                                        previousVendorId = vendor.vendor_id;
                                        vendorOptions += `<option value="${vendor.vendor_id}">${vendor.vendor_name}</option>`;
                                } else if (vendor.category_type === requestType) {
                                        vendorOptions += `<option value="${vendor.vendor_id}">${vendor.vendor_name}</option>`;
                                }
                        });
                        $('#vendor_id').prop('disabled', false); // Enable vendor dropdown
                } else {
                        $('#vendor_id').prop('disabled', true); // Disable vendor dropdown
                }

                $('#vendor_id').html(vendorOptions);
        }

        function populateCategoryOptions(itemModalMode = "add") {
                window.localStorage.setItem("itemModalMode", itemModalMode);
                // $('#requisition_category_id').trigger('change');
                $('#requisition_item_id').prop('disabled', false).change();

                if (addedItemsBucket.length) {
                        $('#requisition_sub_category_id').prop('disabled', true).change();
                }

                if (itemModalMode === "edit") {
                        $(".addItemButtons").hide();
                        $(".editItemButtons").show();
                        $("#addProductsModalLabel").text("Edit Item");
                } else {
                        // itemModalMode = "add";
                        $(".editItemButtons").hide();
                        $(".addItemButtons").show();
                        $("#addProductsModalLabel").text("Add Item");
                }

                const categoryDropdown = document.getElementById('requisition_category_id');
                let categoryOptions = '<option value="">Select Category</option>';

                if (requestType) {
                        procurementCategory.forEach(category => {
                                if (category.category_type === requestType) {
                                        categoryOptions += `<option id="cat-option-${category.id}" data-disabled="${!category.has_approvers}" value="${category.id}">${category.category_name}</option>`;
                                }
                        });
                }

                categoryDropdown.innerHTML = categoryOptions;

                // Use the stored category ID if available, otherwise fetch from the database
                if (selectedCategoryId) {
                        categoryDropdown.value = selectedCategoryId;
                        categoryDropdown.disabled = true; // Lock the dropdown if a category is already selected
                }

                $('#requisition_category_id').trigger('change');

                categoryDropdown.addEventListener('change', function () {
                        const selected = this.options[this.selectedIndex];
                        if (selected.dataset.disabled === 'true') {
                                Swal.fire({
                                        icon: 'warning',
                                        title: 'Approver Missing',
                                        text: 'Approvers are not assigned for this category.',
                                });
                                this.selectedIndex = 0; // reset selection
                        }
                });
        }

        // Save the selected category and subcategory in memory and the database when adding the first item
        $('#addProductsModal').on('hide.bs.modal', function () {
                const categoryDropdown = document.getElementById('requisition_category_id');
                const selectedCategory = categoryDropdown.value;

                const subCategoryDropdown = document.getElementById('requisition_sub_category_id');
                const selectedSubCategory = subCategoryDropdown.value;

                if (selectedCategory) {
                        selectedCategoryId = selectedCategory; // Store the category ID in memory
                }

                if (selectedSubCategory) {
                        selectedSubCategoryId = selectedSubCategory; // Store the sub-category ID in memory
                }
        });

        // Trigger the change event when the modal opens
        // $('#addProductsModal').on('show.bs.modal', function () {
        //         console.log(itemModalMode, addedItemsBucket);

        //         populateCategoryOptions();
        //         $('#requisition_category_id').trigger('change');
        // });

        // Add click event to step circles for navigation
        $('.step-circle').click(function () {
                const clickedStep = parseInt($(this).parent().data('step'));

                if (clickedStep <= currentStep) {
                        currentStep = clickedStep;
                        updateStep();
                        handleBackSteps(currentStep);
                }
        });

        // Set default value for Purchase Order Date to today's date
        document.addEventListener('DOMContentLoaded', function () {
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('purchase_order_date').value = today;
        });

        const trackAutoFilledInformation = {
                isaddItemsAdded: false,
                isaddDocumentsAdded: false,
        }

        function getRequestTypeAndVendorName(payload) {
                return $.ajax({
                        url: '<?php echo site_url('procurement/requisition_controller_v2/getRequestTypeAndVendorNameForPurchaseOrderV2'); ?>',
                        type: 'POST',
                        data: payload,
                        success: (response) => {
                                response = JSON.parse(response);
                                if (response.length) {
                                        response = response[0];
                                        $("#request_type").val(response.requestType).trigger("change");
                                        $("#vendor_id").val(response.vendorId).trigger("change");
                                        $("#department_id").val(response.departmentId).trigger("change");
                                }
                        },
                        error: () => {
                                Swal.fire({
                                        icon: 'error',
                                        title: 'Error',
                                        text: 'An error occurred while fetching request type and vendor name.',
                                });
                        }
                });
        }

        function handleTypeOfRequest(typeOfRequest, typeOfRequestId) {
                // Validate typeOfRequest
                switch (typeOfRequest) {
                        case "indent":
                        case "service_contract":
                        case "rate_contract":
                                break;
                        default:
                                // console.log("Invalid typeOfRequest:", typeOfRequest);
                                return; // Exit if typeOfRequest is invalid
                }

                isDirectPOCreation = false;

                // hide items button
                $("#addItemsBtn").hide();

                const poMasterId = $("#po_master_id").val();
                // Handle logic based on the current step
                const payload = {
                        typeOfRequest: typeOfRequest,
                        typeOfRequestId: typeOfRequestId,
                        poMasterId: poMasterId,
                }

                switch (currentStep) {
                        case 1:
                                // get request type and vendor name
                                const details = getRequestTypeAndVendorName(payload);
                                break;
                        case 2:
                                if (trackAutoFilledInformation.isaddItemsAdded) return; // Prevent re-execution if already filled

                                payload["infoType"] = "addItems";
                                fillDetailsToPoV2(payload);
                                break;
                        case 4:
                                if (trackAutoFilledInformation.isaddDocumentsAdded) return; // Prevent re-execution if already filled

                                payload["infoType"] = "addDocuments";
                                fillDetailsToPoV2(payload);
                                break;
                        default:
                                // console.log("Invalid Step:", currentStep);
                                break;
                }
        }

        function fillDetailsToPoV2(payload) {
                const { typeOfRequest, typeOfRequestId, poMasterId, infoType } = payload;
                if (!+typeOfRequestId || !+poMasterId) {
                        return;
                }

                $.ajax({
                        url: '<?php echo site_url('procurement/requisition_controller_v2/fillDetailsToPurchaseOrderV2'); ?>',
                        type: 'POST',
                        data: payload,
                        success: (response) => {
                                response = JSON.parse(response);
                                if (response.status) {
                                        trackAutoFilledInformation[`is${infoType}Added`] = true; // Update the tracking object
                                        // Populate the table with the response data
                                        if (infoType === "addItems") {
                                                createProductList(response.data);
                                        } else if (infoType === "addDocuments") {
                                                // Populate the table with the response data
                                                displaySelectedFiles(response.data);
                                        }
                                } else {
                                        // Swal.fire({
                                        //         icon: response.icon,
                                        //         title: response.title || 'Error',
                                        //         text: response.text || 'Something went wrong!',
                                        // });
                                }
                        },
                        error: () => {
                                Swal.fire({
                                        icon: 'error',
                                        title: 'Error',
                                        text: 'An error occurred while uploading indent items.',
                                });
                        }
                });
        }

        function validateTolerance() {
                let tolerance = parseFloat($("#purchase_order_tolerance").val()) || 0;

                // Clamp the tolerance value between 0 and 100
                tolerance = Math.min(Math.max(tolerance, 0), 100);

                // Set the corrected value back to the input field
                $("#purchase_order_tolerance").val(tolerance);
        }

        function check_if_approvers_assigned(department_selector) {
                let department_id = $("#" + department_selector).val();
                let hasApprovers = $(`select#${department_selector} option:selected`).data('has-approvers');
                if (hasApprovers) {
                        $("#nextBtn").prop('disabled', false);
                } else {
                        Swal.fire({
                                icon: 'warning',
                                title: 'Approver Missing',
                                text: 'Approvers are not assigned for this department.',
                        });
                        $("#nextBtn").prop('disabled', true);
                }
        }

</script>