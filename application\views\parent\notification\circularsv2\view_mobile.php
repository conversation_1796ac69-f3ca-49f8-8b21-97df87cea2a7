<div class="card panel_new_style" style="padding:0px 15px 15px;margin-bottom: 15%">
	<div class="card-header panel_heading_new_style_nopadding text-center">
		<h3 class="card-title panel_title_new_style"><strong><?php echo str_replace('\\', '', $circularData->category); ?></strong></h3>
		<p class="text-center m-0" style="color: #838383;font-style: italic;font-size: 16px">
			<span><?php echo date('l, d M h:i A', strtotime($circularData->sent_on)); ?></span>
		</p>
	</div>
	<div class="card-body unread_box_no_style" style="overflow-wrap: break-word; padding:20px !important;">
		<p style="font-size: 18px;"><strong><?php echo $circularData->title; ?></strong></p>
		<div style="overflow: auto;"><?php echo $circularData->body; ?></div>
		<div id="download-buttons-container"></div>
	</div>
</div>

<div class="visible-xs visible-sm">
	<a href="#" id="backBtn" onclick="back()"><span class="fa fa-mail-reply"></span></a>
</div>

<form method="post" id="circulars" action="<?php echo site_url('parent/circular_inbox') ?>">
	<input type="hidden" name="category_name" value="<?php echo $category; ?>">
	<input type="hidden" name="cid" value="<?php echo $circularData->id; ?>">
</form>

<script type="text/javascript">
	const circularPaths = <?php echo json_encode($circularData->paths); ?>;
	const circularId = "<?php echo $circular_id; ?>";
	const circular = "<?php echo $circular; ?>";
	const siteUrl = "<?php echo site_url(); ?>";

	$(document).ready(function(){
		buildCircularDownloadButtons();
		var is_read = "<?php echo $is_read; ?>";
		// var circular_id = "<?php //echo $circularData->id; ?>";
		var parent_circular_id = "<?php echo $parent_circular_id; ?>";
		if(is_read == 0 && parent_circular_id != '') {
			$.ajax({
				url:'<?php echo site_url('parent/circular_inbox/makeRead') ?>',
				type:'post',
				data : {'parent_circular_id': parent_circular_id},
				success : function(data){
					console.log('circular read');
				}
			});
		}
	});

	function showLoadingIcon(element) {
		const originalContent = element.innerHTML;
		element.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
		element.disabled = true;
		setTimeout(() => {
			element.innerHTML = originalContent;
			element.disabled = false;
		}, 3000); // Reset after 3 seconds or when download completes
	}

	function downloadFile(element) {
		const platform = getPlatformInfo();
		const fileKey = element.getAttribute("data-file-key");
		const fileName = element.getAttribute("data-file-name");
		const filePath = element.getAttribute("data-file-path");
		if (platform === "iOS") {
			window.location.href = `${siteUrl}parent/circular_inbox/downloadMobileCircularAttachment/${circularId}/${fileKey}/${circular}`;
		} else {
			// For Android/others, use normal download
			// const xhr = new XMLHttpRequest();
			// xhr.open("GET", filePath, true);
			// xhr.responseType = "blob";
			// xhr.onload = function () {
			// 	if (xhr.status === 200) {
			// 		const blob = xhr.response;
			// 		const url = window.URL.createObjectURL(blob);
			// 		const a = document.createElement("a");
			// 		a.href = url;
			// 		a.download = fileName;
			// 		a.style.display = "none";
			// 		document.body.appendChild(a);
			// 		a.click();
			// 		document.body.removeChild(a);
			// 		window.URL.revokeObjectURL(url);
			// 	} else {
			// 		console.log("Download failed with status:", xhr.status);
			// 	}
			// };
			// xhr.onerror = function () {
			// 	console.log("An error occurred during the download request.");
			// };
			// xhr.send();

			const a = document.createElement("a");
			a.href = filePath;
			// a.target = "_blank";
			a.rel = "noopener noreferrer";
			a.download = fileName;
			document.body.appendChild(a);
			a.click();
			document.body.removeChild(a);

			// fetch(filePath, {
			// 	mode: 'cors' // Required if Wasabi allows CORS
			// })
			// .then(response => response.blob())
			// .then(blob => {
			// 	const url = URL.createObjectURL(blob);
			// 	const a = document.createElement("a");
			// 	a.href = url;
			// 	a.download = fileName; // set your desired file name
			// 	a.rel = "noopener noreferrer";
			// 	document.body.appendChild(a);
			// 	a.click();
			// 	document.body.removeChild(a);
			// 	URL.revokeObjectURL(url); // Clean up
			// })
			// .catch(error => {
			// 	console.error("Download failed:", error);
			// 	Swal.fire({
			// 		icon: 'error',
			// 		title: 'Download Failed',
			// 		text: 'Something went wrong while downloading the file. Please try again later.',
			// 	});
			// });
		}
	}

	function truncateFileName(name, length = 25) {
		return (name.length > length) ? name.substring(0, length) + '...' : name;
	}

	function buildCircularDownloadButtons() {
		const container = document.getElementById("download-buttons-container"); // Create this div in your HTML
		const platform = getPlatformInfo();
		
		if (!circularPaths || circularPaths.length === 0) return;

		let i = 1;
		for (const [key, path] of Object.entries(circularPaths)) {
			const fileName = path.name.replace(/<br>/g, ' ');
			const displayFileName = truncateFileName(fileName);
			const filePath = path.path;

			const btn = document.createElement("button");
			if(platform == 'iOS'){
				btn.className = "btn btn-primary";
			} else {
				btn.className = "btn btn-warning";
			}
			btn.style = "font-size:16px;border-radius:4px; margin:2px; display: inline-block;";
			btn.id = `attachment${i}`;
			btn.setAttribute("data-file-key", key);
			btn.setAttribute("data-file-path", filePath);
			btn.setAttribute("data-file-name", fileName);
			btn.onclick = function () {
				showLoadingIcon(this);
				downloadFile(this);
				return false;
			};
			btn.innerHTML = `${displayFileName} <i class="fa fa-download"></i>`;
			container.appendChild(btn);
			i++;
		}
	}

	function getPlatformInfo() {
		const userAgent = navigator.userAgent || navigator.vendor || window.opera;
		const ua = userAgent.toLowerCase();
		if (/iphone|ipad|ipod/i.test(ua)) return "iOS";
		if (/android/i.test(ua)) return "Android";
		if (/windows phone/i.test(ua)) return "Windows Phone";
		return "Other";
	}

	// function downloadFile(element, attachmentId) {
	// 	let attachment = document.getElementById(`attachment${attachmentId}`);

	// 	if (!attachment) {
	// 		console.error(`Attachment with ID attachment${attachmentId} not found.`);
	// 		return;
	// 	}

	// 	let originalText = attachment.innerHTML;
	// 	attachment.disabled = true;
	// 	attachment.innerHTML = 'Please Wait...';

	// 	let filePath = element.getAttribute('data-file-path');
	// 	let fileName = element.getAttribute('data-file-name').replace(/<br>/g, ' ');

	// 	console.log("Downloading:", fileName);

	// 	let xhr = new XMLHttpRequest();
	// 	xhr.open('GET', filePath, true);
	// 	xhr.responseType = 'blob';

	// 	xhr.onload = function () {
	// 		if (xhr.status === 200) {
	// 			let blob = xhr.response;
	// 			let link = document.createElement('a');
	// 			let url = window.URL.createObjectURL(blob);

	// 			link.href = url;
	// 			link.download = fileName;
	// 			document.body.appendChild(link);
	// 			link.click();
	// 			document.body.removeChild(link);
	// 			window.URL.revokeObjectURL(url);
	// 		} else {
	// 			console.error('Failed to download file:', xhr.status, xhr.statusText);
	// 		}

	// 		// Restore button state
	// 		attachment.disabled = false;
	// 		attachment.innerHTML = originalText;
	// 	};

	// 	xhr.onerror = function () {
	// 		console.error('Network error while trying to download file.');
			
	// 		// Restore button state on error
	// 		attachment.disabled = false;
	// 		attachment.innerHTML = originalText;
	// 	};

	// 	xhr.send();
	// }

	function back() {
		loader();
		$("#circulars").submit();
	}
</script>
<style type="text/css">
	html{
		background: white;
	}
	body{
		font-size:17px;
	}
	#text_big p span{
		font-size: 20px !important;
	}
	.panel_title_new_style {
		font-size: 18px !important;
		color: #bfbfbf !important;
		font-weight: 500 !important;
	}
</style>