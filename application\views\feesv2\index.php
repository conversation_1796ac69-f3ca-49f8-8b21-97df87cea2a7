<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li>Fee Master</li>
</ul>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <!-- <a class="back_anchor" href="<?php // echo site_url('dashboard'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a>  -->
            Fee Master
          </h3>   
        </div>
      </div>
    </div>
    <div class="card-body pt-1">
      <?php 
        $data['tile_list'] = $tiles;
        $data['heading'] = '';
        echo $this->load->view('commons/secondary_dashboard_tiles.php', $data, true); 
      ?>

      <?php 
        $data['tile_list'] = $daily_tiles;
        $data['heading'] = 'Daily Transaction Reports';
        echo $this->load->view('commons/secondary_dashboard_tiles.php', $data, true); 
      ?>

      <?php 
        $data['tile_list'] = $std_tiles;
        $data['heading'] = 'Student Wise Reports';
        echo $this->load->view('commons/secondary_dashboard_tiles.php', $data, true); 
      ?>
    
      <?php 
      if($this->authorization->isSuperAdmin()){
        $data['tile_list'] = $std_tiles_v2;
        $data['heading'] = 'Student Wise Reports v2';
        echo $this->load->view('commons/secondary_dashboard_tiles.php', $data, true); 
      }
      ?>

      <?php 
        $data['tile_list'] = $cls_tiles;
        $data['heading'] = 'Management Reports';
        echo $this->load->view('commons/secondary_dashboard_tiles.php', $data, true); 
      ?>

      <?php 
        $data['tile_list'] = $online_tiles;
        $data['heading'] = 'Online Transactions';
        echo $this->load->view('commons/secondary_dashboard_tiles.php', $data, true); 
      ?>

      <?php 
        $data['tile_list'] = $other_tiles;
        $data['heading'] = 'Other Reports';
        echo $this->load->view('commons/secondary_dashboard_tiles.php', $data, true); 
      ?>

      <?php 
        $data['tile_list'] = $old_reports;
        $data['heading'] = 'Old Reports';
        echo $this->load->view('commons/secondary_dashboard_tiles.php', $data, true); 
      ?>

      <?php 
        $data['tile_list'] = $school_tiles;
        $data['heading'] = 'School Administration';
        echo $this->load->view('commons/secondary_dashboard_tiles.php', $data, true); 
      ?>

      <?php 
        $data['tile_list'] = $admin_tiles;
        $data['heading'] = 'Administration';
        echo $this->load->view('commons/secondary_dashboard_tiles.php', $data, true); 
      ?>
    </div>
  </div>
</div>

<div class="visible-sm visible-xs visible-md">
  <a href="<?php echo site_url('dashboard');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div> 

<style type="text/css">
  .btn.btn-rounded{
    border-radius: 6px;
  }
</style>