<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON>
 *          <EMAIL>
 *
 * Created:  July 3, 2023
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');
            
class Sales_model_v2 extends CI_Model {
    private $yearId;
    public function __construct() {
        parent::__construct();
        $this->yearId = $this->acad_year->getAcadYearID();
    }

    public function get_all_class(){
        return $this->db->select('id,class_name,board')->get('class')->result();
    }

    public function Kolkata_datetime()
    {
     $timezone = new DateTimeZone("Asia/Kolkata" );
     $date = new DateTime();
     $date->setTimezone($timezone );
     $dtobj = $date->format('Y-m-d H:i:s');
     return $dtobj;
    }

    public function getstudentDetails_classwise($clsId){
        return $this->db->select("sd.id, sy.id as stdYearId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as std_name, c.class_name as clsName,  sd.admission_no, p.mobile_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS parent_name, c.class_name, cs.section_name")
        ->from('student_year sy')
        ->join('student_admission sd','sy.student_admission_id=sd.id')
        ->join("class_section cs", "sy.class_section_id=cs.id",'left')
        ->join("class c", "sy.class_id=c.id",'left')
        ->where('sy.class_id',$clsId)
        ->where('sy.acad_year_id',$this->yearId)
        ->where('sd.admission_status',2)
        ->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'")
        ->join("parent p", "p.id=sr.relation_id")
        ->get()->result();
     }

    public function get_sales_student_detailbyId($admission_no, $std_id){

        $data_info = $this->db->select("sy.promotion_status, sd.id, sy.id as stdYearId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as std_name, sd.admission_no, p.mobile_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS parent_name, c.class_name, ifnull(cs.section_name, '') as section_name, c.id as class")
        ->from('student_year sy')
        ->join('student_admission sd','sy.student_admission_id=sd.id')
        ->join("class_section cs", "sy.class_section_id=cs.id", 'left')
        ->join("class c", "sy.class_id=c.id")
        ->where('sy.acad_year_id',$this->yearId)
        ->where_in('sd.admission_status',[1,2])
        ->where('sy.promotion_status!=', 'JOINED')
        // ->where('sy.promotion_status!=', '5')
        ->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'")
        ->join("parent p", "p.id=sr.relation_id");
        if($admission_no){
          $this->db->where('sd.admission_no', $admission_no);
        }
        if ($std_id) {
          $this->db->where('sd.id', $std_id);
        }
       $x= $this->db->order_by('sd.first_name')->get()->row();

       if($this->settings->getSetting('enable_indus_single_window_approval_process')){
        $single_window = $this->db->select("team_name,date_format(sat.taken_on,'%d-%M-%Y') as taken_on,concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as taken_by,ifnull(sat.remarks,'-') as remarks,sat.status")
        ->from('single_window_approval_tracking sat')
        ->join('staff_master sm','sat.taken_by=sm.id','left')
        ->where('student_id',$std_id)
        ->where('team_name','Accounts')
        ->where('academic_year_id',$this->acad_year->getAcadYearId())
        ->get()->row();

        $x->team_name = '';
        $x->taken_on = '-';
        $x->taken_by = '-';
        $x->remarks = '-';
        $x->status = 'Not started';
        if(!empty($single_window)){
          $x->team_name = $single_window->team_name;
          $x->taken_on = $single_window->taken_on;
          $x->taken_by = $single_window->taken_by;
          $x->remarks = $single_window->remarks;
          $x->status = $single_window->status;
        }
       }
       return $x;
      //  echo '<pre>'; print_r($this->db->last_query($x)); die();
    }

    public function fee_variants_receiptNo(){
      $query = "select receipt_no from sales_transaction order by receipt_no desc limit 1";
      $res = $this->db->query($query);
     
      if($res->num_rows() > 0){
          $maxid= $res->row();
          $val = explode('F0000',$maxid->receipt_no);
          $recIncrement = $val[1]+1;
          $receiptNo = 'F0000' . $recIncrement;
          return $receiptNo;
      }
      return 'F00001';
    }

    public function insert_sales_transaction($std_id, $input, $catId, $products){

      // Array to hold the merged results
      $merged = [];
      foreach ($products as $item) {
          $variant = $item['variants'];
          // If the variant already exists, sum the quantity and amount
          if (isset($merged[$variant])) {
              $merged[$variant]['quantity'] += $item['quantity'];
              $merged[$variant]['amount'] += $item['amount'];
          } else {
              // Otherwise, add the new variant to the merged array
              $merged[$variant] = $item;
          }
      }
      // Reset array keys
      $products = array_values($merged);

      // $input = $this->input->post();

      $items_from_reserved_stock= isset($input['items_from_reserved_stock']) ? $input['items_from_reserved_stock'] : 0;


      $sales_year_id= isset($input['sales_year_id_single']) && $input['sales_year_id_single'] ? $input['sales_year_id_single'] : 0;
      
      $ePayment_type = explode('_', $input['payment_type']);
      $payment_type = $ePayment_type[0];
      $reconciliation_status = $ePayment_type[1];

      $timezone = new DateTimeZone("Asia/Kolkata" );
      $date = new DateTime($input['receipt_date']);
      $time = new DateTime();
      $time->setTimezone($timezone);
      $merge = new DateTime($date->format('Y-m-d') .' ' .$time->format('H:i:s'));
      $receipt_date =  $merge->format('Y-m-d H:i:s'); 
      if (!empty($input['cheque_dd_nb_cc_dd_number'])) {
        $cheque_dd_nb_cc_dd_number = $input['cheque_dd_nb_cc_dd_number'];
      }else if(!empty($input['dd_number'])){
        $cheque_dd_nb_cc_dd_number = $input['dd_number'];
      }else if(!empty($input['cc_number'])){
        $cheque_dd_nb_cc_dd_number = $input['cc_number'];
      }else if(!empty($input['nb_number'])){
        $cheque_dd_nb_cc_dd_number = $input['nb_number'];
      }else{
        $cheque_dd_nb_cc_dd_number = null;
      }

    $tAmount = 0;
    foreach ($products as $key => $val) {
      $tAmount += isset($val['amount']) ? $val['amount'] : 0;
    }

    // echo '<pre>'; print_r($input); die();

      $this->db->trans_start();

      $master_data = array(
          'student_id'=> ($std_id == '')?0:$std_id,
          'proc_im_category_id'=> $catId,
          'payment_type'=> $payment_type,
          'total_amount'=> $tAmount,
          'receipt_date'=> $receipt_date,
          'created_by'=> $this->authorization->getAvatarId(),
          'bank_name'=> (isset($input['bank_name']) == '')? null : $input['bank_name'],
          'bank_branch'=> (isset($input['branch_name']) == '')? null : $input['branch_name'],
          'cheque_dd_number'=> $cheque_dd_nb_cc_dd_number,
          'recon_status'=> $reconciliation_status,
          'cheque_dd_date'=> (!isset($input['bank_date'])) ? null : date('Y-m-d',strtotime($input['bank_date'])),
          'remarks'=> (!isset($input['remarks'])) ? null : $input['remarks'],
          'card_charge_amount'=> (!isset($input['card_charge'])) ? null : $input['card_charge'],
          'sales_type' => $input['sale_type'],
          'student_name' => ($input['new_std_name'] == '')?NULL:$input['new_std_name'],
          'parent_name' => ($input['parent_name'] == '')?NULL:$input['parent_name'],
          'contact_number' => ($input['contact_number'] == '')?NULL:$input['contact_number'],
          'class_name' => ($input['class_name'] == '')?NULL:$input['class_name'],
          'acad_year_id' => $this->yearId,

          'discount_amount' => $input['discounted_amount'] >= 0 ? $input['discounted_amount'] : 0,
          'discount_percentage' => $input['discounted_amount'] >= 0 && $input['discount_mode_type'] == 'Percentage' ? $input['discount_amount_or_percentage_value'] : 0,
          'discount_remarks' => $input['discounted_amount'] >= 0 ? $input['discounted_remarks_hidden'] : NULL
        );
      $this->db->insert('procurement_sales_master',$master_data);
      $stransId = $this->db->insert_id();

      $dataArry = [];
      $status= true;
$procurement_parent_sales_order_details_ids= isset($input['procurement_parent_sales_order_details_ids']) ? $input['procurement_parent_sales_order_details_ids'] : [];
$quantity_sold_arr= isset($input['quantity']) ? $input['quantity'] : [];
$indexValue= 0;




// echo '<pre>'; print_r($input); die();



      foreach ($products as $k =>  $productId) {
        $sales_source_type = 'DIRECT_SALE';
        $sales_order_details_id= NULL;
        $reserved_quantity_fulfilled= NULL;
        // Filling reserved item details in sales trasaction table
        if($items_from_reserved_stock == 1) {
          $sales_source_type = 'RESERVED_ORDER';
            $sales_order_details_id= $procurement_parent_sales_order_details_ids[$indexValue];
          if(!empty($procurement_parent_sales_order_details_ids)) {
              $parent_order_details= $this->db->select("quantity, ifnull(delivered_quantity, 0) as delivered_quantity")->where('id', $procurement_parent_sales_order_details_ids[$indexValue])->get('procurement_parent_sales_order_details')->row();
              if(!empty($parent_order_details)) {
                $reservedQty= $parent_order_details->quantity;
                $deliveredQty= $parent_order_details->delivered_quantity;
                $soldQty= $quantity_sold_arr[$indexValue];

                $reserved_quantity_fulfilled= ($reservedQty - $deliveredQty) < $soldQty ? 1*($reservedQty - $deliveredQty) : 1*($soldQty);
              }
          }

        }

        if($reserved_quantity_fulfilled == NULL) {
          $sales_source_type = 'DIRECT_SALE';
        }

        $dataArry =  array(
          'sales_master_id'=> $stransId,
          'proc_im_subcategory_id'=> $productId['prodcuts'],
          'proc_im_items_id'=> $productId['variants'],
          'quantity'=> $productId['quantity'], 
          'amount'=> $productId['amount'],
          'sales_year_id'=> $sales_year_id,
          'proc_invoice_items_id'=> $input['inv_item_id'][$k],
          'sales_source_type' => $sales_source_type,
          'reserved_quantity_fulfilled' => $reserved_quantity_fulfilled,
          'sales_order_details_id' => $sales_order_details_id
        ); 

        $qty= $this->db->select("current_quantity")->where('id', $input['inv_item_id'][$k])->get('procurement_delivery_challan_items')->row()->current_quantity;
        if($qty >= $productId['quantity']) {
          $this->db->insert('procurement_sales_transactions', $dataArry);
          $proc_sales_trans_id= $this->db->insert_id();

          // Parent order details update
          
          if($items_from_reserved_stock == 1) {
            
            // $procurement_parent_sales_order_details_ids= isset($input['procurement_parent_sales_order_details_ids']) ? $input['procurement_parent_sales_order_details_ids'] : [];
            if(!empty($procurement_parent_sales_order_details_ids)) {
                $parent_order_details= $this->db->select("quantity, ifnull(delivered_quantity, 0) as delivered_quantity")->where('id', $procurement_parent_sales_order_details_ids[$indexValue])->get('procurement_parent_sales_order_details')->row();
                if(!empty($parent_order_details)) {
                  $reservedQty= $parent_order_details->quantity;
                  $deliveredQty= $parent_order_details->delivered_quantity;
                  $soldQty= $quantity_sold_arr[$indexValue];
                  if( ($reservedQty - $deliveredQty) == $soldQty ) {
                    $updateParentOrderDetails= array(
                      'delivery_status' => 'Delivered',
                      'delivered_quantity' => 1*($deliveredQty + $soldQty)
                    );
                  } else { // if 1)  ($reservedQty - $deliveredQty) < $soldQty - Matlab reserved qty se jyada sold kar raha hai; 2)  ($reservedQty - $deliveredQty) > $soldQty - Matlab thi partilly delivered
                    $updateParentOrderDetails= array(
                      'delivery_status' => ($reservedQty - $deliveredQty) < $soldQty ? 'Delivered' : 'Partially Delivered',
                      'delivered_quantity' => ($reservedQty - $deliveredQty) < $soldQty ? $reservedQty : 1*($deliveredQty + $soldQty)
                    );
                  }
                  $this->db->where('id', $procurement_parent_sales_order_details_ids[$indexValue])->update('procurement_parent_sales_order_details', $updateParentOrderDetails);

                  $insertParentOrderDelivery= array(
                    'proc_parent_sales_order_details_id' => $procurement_parent_sales_order_details_ids[$indexValue],
                    'proc_sales_trans_id' => $proc_sales_trans_id,
                    'quantity' => ($reservedQty - $deliveredQty) < $soldQty ? 1*($reservedQty - $deliveredQty) : 1*($soldQty), // If already delivered and sold qty is greather than (reserved - delivered) then quantity = (reserved - delivered) else quantity = sold (Because sold is less than [reserved - delivered])
                    'status' => 'Delivered'
                  );
                  $this->db->insert('procurement_parent_sales_order_details_delivery', $insertParentOrderDelivery);
                }
            }

          }

          // $vQtyUpdate[] =  array(
          //   'id'=> $productId['variants'],
          //   'current_quantity'=> $productId['current_quantity'] -  $productId['quantity']
          // );
          $vQtyUpdate_in_invoice_item[] =  array(
            'id'=> $input['inv_item_id'][$k],
            'current_quantity'=> $qty -  $productId['quantity']
          );
        } else {
          $status= false;
        }
        $indexValue ++;
      }
      
      if($status) {
        // $this->db->update_batch('procurement_itemmaster_items', $vQtyUpdate,'id');
        $this->db->update_batch('procurement_delivery_challan_items', $vQtyUpdate_in_invoice_item,'id');


      }

      $this->db->trans_complete();
      if ($this->db->trans_status() && $status) {
        return $stransId;
      }else{
        $this->db->trans_rollback();
        return '';
      }

    }

    public function update_receipt_sale_transcation($sTransId, $catId, $input){

      $timezone = new DateTimeZone("Asia/Kolkata" );
      $date = new DateTime($input['receipt_date']);
      $time = new DateTime();
      $time->setTimezone($timezone);
      $merge = new DateTime($date->format('Y-m-d') .' ' .$time->format('H:i:s'));
      $receipt_date =  $merge->format('Y-m-d H:i:s'); 

      $receipt_book = $this->db->select('frb.*')
      ->from('procurement_itemmaster_category ipc')
      ->where('ipc.id',$catId)
      ->join('feev2_receipt_book frb','frb.id=ipc.receipt_book_id')
      ->get()->row();
      if (!empty($receipt_book)) {
        $this->db->where('id',$receipt_book->id);
        $this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));

        $receipt_number = $this->fee_library->receipt_format_get_update($receipt_book);

        $this->db->where('id',$sTransId);
        return $this->db->update('procurement_sales_master', array('receipt_no'=>$receipt_number,'receipt_date'=> $receipt_date));
      }else{
        return false;
      }
     

    }

    public function update_receipt_sale_transcation_manual($sTransId, $catId, $input){
        $timezone = new DateTimeZone("Asia/Kolkata" );
        $date = new DateTime($input['receipt_date']);
        $time = new DateTime();
        $time->setTimezone($timezone);
        $merge = new DateTime($date->format('Y-m-d') .' ' .$time->format('H:i:s'));
        $receipt_date =  $merge->format('Y-m-d H:i:s');

        $this->db->where('id',$sTransId);
        return $this->db->update('procurement_sales_master', array('receipt_no'=>$input['manual_receipt'],'receipt_date'=> $receipt_date));
    }


    public function get_sales_receipt_data($stransId, $std_id, $sale_type='existing'){

      $master =  $this->db->select("sm.id as transId, sm.receipt_no, sm.payment_type, sm.card_charge_amount, total_amount, created_by, bank_name, bank_branch, cheque_dd_number, date_format(receipt_date, '%d-%m-%Y') as receipt_date, cheque_dd_date, ipc.category_name, gst_no, pan_no, ipc.receipt_template, sm.sales_type, sm.student_name, sm.remarks, ifnull(sm.class_name,'NA') as class_name, ifnull(sm.parent_name,'') as parent_name, ifnull(sm.discount_amount, 0) as discount_amount")
       ->from('procurement_sales_master sm')
       ->where('sm.id',$stransId) 
       ->join('procurement_itemmaster_category ipc','sm.proc_im_category_id=ipc.id')
       ->join('feev2_receipt_book frb','frb.id=ipc.receipt_book_id')
       ->get()->row();

      //  echo '<pre>'; print_r($master); die();

      $trans = $this->db->select('st.quantity, st.amount, ipm.subcategory_name as product_name, ipv.item_name as variant_name, ipv.hsn_sac')
      ->from('procurement_sales_transactions st')
      ->where('st.sales_master_id',$master->transId)
      ->join('procurement_itemmaster_subcategory ipm','st.proc_im_subcategory_id=ipm.id')
      ->join('procurement_itemmaster_items ipv','st.proc_im_items_id=ipv.id')
      ->get()->result();

      if($sale_type == 'existing') {
        $std_data = $this->db->select("s.admission_no, if(sy.alpha_rollnum is not null and trim(sy.alpha_rollnum) != '', sy.alpha_rollnum, '-') as alpha_rollnum, if(s.enrollment_number is not null and trim(s.enrollment_number) != '', s.enrollment_number, '-') as enrollment_number, p.mobile_no, CONCAT(ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) AS std_name, CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) AS parent_name,CONCAT(ifnull(p1.first_name,''), ' ', ifnull(p1.last_name,'')) AS mother_name, c.class_name, cs.section_name")
          ->from('student_admission s')
          ->join('student_year sy','s.id=sy.student_admission_id')
          ->where('sy.acad_year_id',$this->yearId)
          ->join('class c','c.id=sy.class_id')
          ->join('class_section cs','sy.class_section_id=cs.id','left')
          ->join('student_relation sr','sr.std_id=s.id')
          ->join('student_relation sr1','sr1.std_id=s.id')
          ->where('s.id',$std_id)
          ->where('sr.relation_type','Father')
          ->where('sr1.relation_type','Mother')
          ->join('parent p','p.id=sr.relation_id')
          ->join('parent p1','p1.id=sr1.relation_id')
          ->get()->row();
        $master->std_data = $std_data;
      }

      $master->trans = $trans;
      return $master;

    }

    public function get_sales_receipt_books(){
      return $this->db->get('feev2_receipt_book')->result();
    }

    public function get_prodcuts_all($only_category_admin= 'any_one'){
      $log= $this->authorization->getAvatarStakeHolderId();
      $this->db->select('ipm.id as productId, ipc.id as category_id, ipc.category_name, ipm.subcategory_name as product_name, ifnull(concat(sm.first_name, " ", sm.last_name), "No Admin") as admin')
      ->from('procurement_itemmaster_subcategory ipm')
      ->join('procurement_itemmaster_category ipc','ipm.proc_im_category_id=ipc.id')
      ->join('staff_master sm','ipc.category_administrator=sm.id', 'left')
      ->where('ipc.is_sellable', 1)
      ->where('ipc.status', 1); // In sales module category dropdown we are only taking active category
      if($only_category_admin == 'only_category_admin' && $log != 0 && $log != '0' && intval($log) > 0) {
        $this->db->where("(ipc.category_administrator IS NULL OR ipc.category_administrator = $log OR ipc.category_administrator = '' OR ipc.category_administrator = 0)");
      }
      $result = $this->db->get()->result();

      $cateId =[];
      foreach ($result as $key => $val) {
        $cateId[$val->category_id][]= $val;
      }
      return $cateId;
    }

    public function getstudentallNames(){
      return $this->db->select("sy.promotion_status, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as s_name,ifnull(cs.section_name,'') as section_name,ifnull(c.class_name,'') as class_name, sd.id as id_number, cs.id as class_section_id")
      ->from('student_year sy')
      ->join('student_admission sd','sy.student_admission_id=sd.id')
      ->where_in('admission_status', [1,2]) // Need to take alumini and pending students also
      // ->where('sy.promotion_status!=', '4') // Need to take alumini students also
      ->where('sy.promotion_status!=','JOINED')
      ->where('sy.acad_year_id',$this->yearId)
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->join('class c','sy.class_id=c.id','left')
      ->get()->result();
    }

    public function get_prodcut_varints($pId, $cat_id, $sales_year_id){
      // $result= $this->db->select("id as vId, item_name as varinat_name,current_quantity, threshold_quantity, ifnull(sku_code, '') as sku_code, selling_price, cost_prodcut")
      // ->from('procurement_itemmaster_items')
      // ->where('proc_im_subcategory_id',$pId)
      // ->where('status','1')
      // ->where('current_quantity!=0')
      // ->get()->result();

      $prevYearInClosedItems= []; //not closed items in prev year
      if($sales_year_id > 1) {
        $is_previous_closed= $this->db_readonly->select("pii.proc_im_items_id as proc_im_items_id")
            ->from('procurement_delivery_challan_master pim')
            ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
            ->where('sales_year_id', $sales_year_id - 1)
            ->where('pii.is_closed', 1)
            ->get()->result();
        if(!empty($is_previous_closed)) {
          foreach($is_previous_closed as $key => $val) {
            $prevYearInClosedItems[]= $val->proc_im_items_id;
          }
        }
      }

      $this->db_readonly->select("id as vId, item_name as varinat_name, ifnull(sku_code, '') as sku_code")
      ->where('proc_im_subcategory_id', $pId)->where('status', 1);
      // Commenting this code for not validating if the item are closed for previou year or not :: Means taking all the items either it is closed or inclosed
      // if(!empty($prevYearInClosedItems)) {
      //   $this->db_readonly->where_not_in('id', $prevYearInClosedItems);
      // }
      $item_lists= $this->db_readonly->get('procurement_itemmaster_items')->result();

      foreach($item_lists as $key => $val) {
        $result= $this->db_readonly->select("inv_item.id as inv_item_id, pim.id as inv_master_id, inv_item.current_quantity, inv_item.selling_price, ifnull(inv_item.price, 0) as cost_prodcut,ifnull(inv_item.cgst, 0) as cgst,ifnull(inv_item.sgst, 0) as sgst, ifnull(psy.year_name, '-') as sales_year, ifnull(pim.sales_year_id, '0') as sales_year_id")
        ->from('procurement_delivery_challan_items inv_item')
        ->join('procurement_delivery_challan_master pim', 'inv_item.invoice_master_id= pim.id')
        ->join('procurement_sales_year psy', 'psy.id= pim.sales_year_id', 'left')
        ->where('inv_item.proc_im_items_id',$val->vId)
        // ->where('inv_item.current_quantity > 0')
        ->where('pim.sales_year_id', $sales_year_id)
        ->order_by('inv_item.current_quantity', 'desc')
        ->order_by('pim.dc_type', 'desc')
        ->get()->row();
        
        if(!empty($result)) {
          $val->current_quantity= $result->current_quantity;
          $val->selling_price= $result->selling_price;
          $val->cost_prodcut= $result->cost_prodcut + $result->cgst + $result->sgst;
          $val->sales_year= $result->sales_year;
          $val->sales_year_id= $result->sales_year_id;
          $val->inv_master_id= $result->inv_master_id;
          $val->inv_item_id= $result->inv_item_id;
        } else {
          unset($item_lists[$key]);
        }
  
      }

     
      // echo '<pre>'; print_r($item_lists); die();
      return $item_lists;
    }

    public function get_varints_price_quantity($vId){
     return $this->db->select('ipv.current_quantity, ipv.selling_price as price, current_quantity, threshold_quantity')
      ->from('procurement_itemmaster_items ipv')
       ->where('ipv.id',$vId)
      ->get()->row();
    }

    public function get_daily_transaction_report($from_date, $to_date, $product_variants= 0, $im_subcategory, $im_category, $payment_modes= 0, $class_section){
      if(!empty($class_section)) {
        $students= $this->db_readonly->select("student_admission_id")->where_in('class_section_id', $class_section)->get('student_year')->result();
        $students_id= [];
        if(!empty($students)) {
          foreach($students as $k => $v) {
            array_push($students_id, $v->student_admission_id);
          }
        }
      }
      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));
      $this->db->select("sy.promotion_status, a.friendly_name, sm.id as smId, sm.student_id, sm.receipt_no, sm.payment_type, sm.total_amount, sm.bank_name, sm.bank_branch, sm.cheque_dd_date, sm.remarks, sm.cheque_dd_number, st.quantity, st.amount, CONCAT(ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) AS std_name,CONCAT(ifnull(c.class_name,''), '', ifnull(cs.section_name,'')) AS class_section, ipm.subcategory_name as product_name, ipv.item_name as variants, date_format(sm.receipt_date,'%d-%m-%Y') as receipt_date, sm.sales_type, sm.student_name, sm.parent_name, sm.contact_number, sm.receipt_pdf_path, ifnull(sm.class_name,'NA') as class_name, pInvItem.selling_price, ifnull(sm.discount_amount, 0) as discount_amount, ifnull(sm.discount_remarks, '-') as discount_remarks")
      ->from('procurement_sales_master sm')
      ->where('sm.soft_delete!=1')
      ->join('student_admission s','s.id=sm.student_id', 'left')
      ->join('student_year sy',"s.id=sy.student_admission_id and sy.acad_year_id=$this->yearId", 'left')
      ->join('class c','c.id=sy.class_id', 'left')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->join('procurement_sales_transactions st','sm.id=st.sales_master_id')
      ->join('procurement_itemmaster_subcategory ipm','st.proc_im_subcategory_id=ipm.id') 
      ->join('procurement_itemmaster_items ipv','st.proc_im_items_id=ipv.id')
      ->join('procurement_delivery_challan_items pInvItem','st.proc_invoice_items_id=pInvItem.id')
      ->join('avatar a','a.id= sm.created_by')
      ->order_by('sm.receipt_date','desc');
      if ($fromDate && $toDate) {
        $this->db->where('date_format(sm.receipt_date,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      if ($product_variants) {
        $this->db->where_in('st.proc_im_items_id',$product_variants);
      }

      if ($im_subcategory) {
        $this->db->where_in('st.proc_im_subcategory_id',$im_subcategory);
      }
      if ($im_category) {
        $this->db->where_in('sm.proc_im_category_id',$im_category);
      }

      if ($payment_modes) {
        $this->db->where_in('sm.payment_type',$payment_modes);
      }

      if(!empty($class_section) && !empty($students)) {
        $this->db->where_in('sm.student_id',$students_id);
      }
     $result = $this->db->get()->result();
// echo '<pre>Query: '; print_r($this->db->last_query($result)); die();

     $payment_mode = json_decode($this->input->post('payment_modeJSON'));
      $resData=[];
      foreach ($result as $key => $res) {
        if (!array_key_exists($res->smId, $resData)) {
          switch ($res->payment_type) {
            case '9':
              $type = 'Cash';
              break;
            case '4':
              $type = 'Cheque';
              break;
            case '1':
              $type = 'DD';
              break;
            case '7':
              $type = 'Card';
              break;
            case '8':
              $type = 'Net Banking';
              break;
            case '10':
              $type = 'Online Link';
              break;
            case '11':
              $type = 'UPI - Online';
              break;
              case '15':
                $type = 'Pocket Money';
                break;
            default:
              $type = 'Cash';
              break;
          }
          $resData[$res->smId]['discount_amount'] = $res->discount_amount;
          $resData[$res->smId]['discount_remarks'] = $res->discount_remarks;

          $resData[$res->smId]['receipt_no'] = $res->receipt_no;
          $resData[$res->smId]['student_id'] = $res->student_id;
          $resData[$res->smId]['receipt_date'] = $res->receipt_date;
          $resData[$res->smId]['total_amount'] = $res->total_amount;
          $resData[$res->smId]['payment_type'] = $type;
          $resData[$res->smId]['sales_type'] = $res->sales_type;
          $resData[$res->smId]['smId'] = $res->smId;
          $resData[$res->smId]['promotion_status'] = $res->promotion_status;
          $resData[$res->smId]['std_name'] = ($res->sales_type == 'existing')?$res->std_name:$res->student_name;
          $resData[$res->smId]['class_section'] = ($res->sales_type == 'existing')?$res->class_section:$res->class_name;
          $resData[$res->smId]['products'] = '';
          $resData[$res->smId]['quantity_item'] = 0;
          $resData[$res->smId]['bank_name'] = isset($res->bank_name) ? $res->bank_name : "";
          $resData[$res->smId]['bank_branch'] = isset($res->bank_branch) ? $res->bank_branch : "";
          $resData[$res->smId]['cheque_dd_number'] = isset($res->cheque_dd_number) ? $res->cheque_dd_number : "";
          $resData[$res->smId]['remarks'] = isset($res->remarks) ? $res->remarks : "";
          $resData[$res->smId]['cheque_dd_date'] = $res->cheque_dd_date;
          $resData[$res->smId]['friendly_name'] = $res->friendly_name;
        }     
        // $resData[$res->smId]['total_amount'] += $res->total_amount;
        // $single_amt= number_format($res->amount, 2) .'/'. number_format($res->quantity, 2);
        // $resData[$res->smId]['products'] .= $res->product_name .' - ' .$res->variants . ' ( '.$res->quantity.' * '.number_format($single_amt, 2).' = '.number_format($res->amount, 2).'] <br>' ;
        $resData[$res->smId]['products'] .= $res->product_name .' - ' .$res->variants . ' ( '.$res->quantity.' * '.number_format($res->selling_price,2).' = '.number_format($res->quantity * $res->selling_price, 2).') <br>' ;
        $resData[$res->smId]['quantity_item'] += $res->quantity;
      }
     

        array_multisort(array_column($resData,'receipt_date'), SORT_DESC, array_column($resData, 'receipt_no'),SORT_DESC, $resData);
      return $resData;
    }

     public function get_sales_student_historyby_id($admission_no, $std_id){
      $this->db->select("sd.id,  concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as std_name")
      ->from('student_year sy')
      ->join('student_admission sd','sy.student_admission_id=sd.id')
      ->join("procurement_sales_master sm", "sd.id=sm.student_id")
      ->where('sy.acad_year_id',$this->yearId)
      ->where_in('sd.admission_status',[1,2]);
      if($admission_no){
        $this->db->where('sd.admission_no', $admission_no);
      }
      else  {
        $this->db->where('sd.id', $std_id);
      }
      $data_info = $this->db->get()->row();

      if (!empty($data_info)) {
        return $data_info;
      }else{
        return 0;
      }
    }

    public function get_sales_history_student_wise($student_id){

      $current_sales_year= $this->db_readonly->select("id")
          ->where('is_active', 1)
          ->get('procurement_sales_year')->row();

      $this->db_readonly->select('a.friendly_name, sm.id as transId, sm.receipt_no, sm.student_id, sm.payment_type, total_amount, created_by, bank_name, bank_branch, cheque_dd_number, date_format(receipt_date, "%d-%m-%Y") as receipt_date, cheque_dd_date, ipc.category_name, gst_no, pan_no, ifnull(sm.discount_amount, 0) as discount_amount, ifnull(sm.discount_remarks, "-") as discount_remarks')
       ->from('procurement_sales_master sm')
       ->where('sm.student_id',$student_id)
       ->where('sm.soft_delete !=', 1);
       if(!empty($current_sales_year)) {
        $this->db_readonly->join('procurement_sales_transactions pst','pst.sales_master_id=sm.id')
          ->where('pst.sales_year_id', $current_sales_year->id);
       }
       $this->db_readonly->join('procurement_itemmaster_category ipc','sm.proc_im_category_id=ipc.id')
       ->join('avatar a','a.id=sm.created_by')
       ->join('feev2_receipt_book frb','frb.id=ipc.receipt_book_id');
       if(!empty($current_sales_year)) {
        $this->db_readonly->group_by('sm.id');
       }
       $master =  $this->db_readonly->get()->result();

       if (empty($master)) {
         return false;
       }
      $salesids =[];
      foreach ($master as $key => $val) {
           array_push($salesids, $val->transId);
      } 

      foreach ($salesids as $key => $salId) {
        $result[$salId] = $this->db_readonly->select('st.sales_year_id, st.id as transId, st.quantity, st.amount, ipm.subcategory_name as product_name, ipv.item_name as variant_name, pInvItem.selling_price, pInvItem.price as cost_prodcut')
        ->from('procurement_sales_transactions st')
        ->where('st.sales_master_id',$salId)
        ->join('procurement_itemmaster_subcategory ipm','st.proc_im_subcategory_id=ipm.id')
        ->join('procurement_itemmaster_items ipv','st.proc_im_items_id=ipv.id')
        ->join('procurement_delivery_challan_items pInvItem','st.proc_invoice_items_id=pInvItem.id')
        ->get()->result();
      }

      foreach ($master as $key => &$val) {
        foreach ($result as $salId => $res) {
          if ($val->transId == $salId) {
            $val->trans = $res;
          }
        }
      }     
      return $master;

    }

    public function get_sales_history_student_details($student_id)
    {
        $std_data = $this->db->select("s.admission_no, sy.promotion_status, p.mobile_no, CONCAT(ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) AS std_name, CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) AS parent_name, c.class_name, cs.section_name")
        ->from('student_admission s')
        ->join('student_year sy','s.id=sy.student_admission_id')
        ->where('sy.acad_year_id',$this->yearId)
        ->join('class c','c.id=sy.class_id')
        ->join('class_section cs','sy.class_section_id=cs.id','left')
        ->join('student_relation sr','sr.std_id=s.id')
        ->where('s.id',$student_id)
        ->where('sr.relation_type','Father')
        ->join('parent p','p.id=sr.relation_id')
        ->get()->row();
      return $std_data;
    }


    public function update_sales_path($stransId, $path)
    {
      $this->db->where('id',$stransId);
      $this->db->update('procurement_sales_master',array('receipt_pdf_path'=>$path,'pdf_status' => 0));
      return $this->db->affected_rows();
    }

    public function update_html_receipt_sales($html, $stransId){
      $this->db->where('id',$stransId);
      $this->db->update('procurement_sales_master',array('html_sales'=>$html));
      return $this->db->affected_rows();
    }

    public function updateSalesPdfLink($path, $status) {
      $this->db->where('receipt_pdf_path',$path);
      return $this->db->update('procurement_sales_master', array('pdf_status' => $status));
    }


  public function download_sales_receipt_path($id){
    return $this->db->select('receipt_pdf_path')->where('id', $id)->get('procurement_sales_master')->row()->receipt_pdf_path;
  }

  public function get_html_transaction_for_pdf($transIds){
     return $this->db->select('html_sales')->where('id', $transIds)->get('procurement_sales_master')->row();
  }
  
  public function get_sales_receipt_data_for_pdf($stransId){

    $master =  $this->db->select('sm.id as transId, sm.student_id, sm.receipt_no, sm.payment_type, sm.card_charge_amount, total_amount, created_by, bank_name, bank_branch, cheque_dd_number, date_format(receipt_date, "%d-%m-%Y") as receipt_date, cheque_dd_date, ipc.category_name, gst_no, pan_no, ipc.receipt_template, sm.sales_type, sm.student_name, ifnull(sm.discount_amount, 0) as discount_amount')
     ->from('procurement_sales_master sm')
     ->where('sm.id',$stransId) 
     ->join('procurement_itemmaster_category ipc','sm.proc_im_category_id=ipc.id')
     ->join('feev2_receipt_book frb','frb.id=ipc.receipt_book_id')
     ->get()->row();

    $trans = $this->db->select('st.quantity, st.amount, ipm.subcategory_name as product_name, ipv.item_name as variant_name, ipv.hsn_sac')
    ->from('procurement_sales_transactions st')
    ->where('st.sales_master_id',$master->transId)
    ->join('procurement_itemmaster_subcategory ipm','st.proc_im_subcategory_id=ipm.id')
    ->join('procurement_itemmaster_items ipv','st.proc_im_items_id=ipv.id')
    ->get()->result();


      $std_data = $this->db->select("s.admission_no, if(s.enrollment_number is not null and trim(s.enrollment_number) != '', s.enrollment_number, '-') as enrollment_number, if(sy.alpha_rollnum is not null and trim(sy.alpha_rollnum) != '', sy.alpha_rollnum, '-') as alpha_rollnum, p.mobile_no, CONCAT(ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) AS std_name, CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) AS parent_name, c.class_name, cs.section_name")
        ->from('student_admission s')
        ->join('student_year sy','s.id=sy.student_admission_id')
        ->where('sy.acad_year_id',$this->yearId)
        ->join('class c','c.id=sy.class_id')
        ->join('class_section cs','sy.class_section_id=cs.id','left')
        ->join('student_relation sr','sr.std_id=s.id')
        ->where('s.id',$master->student_id)
        ->where('sr.relation_type','Father')
        ->join('parent p','p.id=sr.relation_id')
        ->get()->row();
      $master->std_data = $std_data;
      
      $master->trans = $trans;
    return $master;

  }

  public function soft_delete_sales_receipt($salesId, $remarks){
    $timezone = new DateTimeZone("Asia/Kolkata" );
    $date = new DateTime();

// Re-covering items
      $item_list= $this->db->select("pst.quantity, pst.proc_im_items_id, pst.proc_invoice_items_id, pInvItems.proc_im_subcategory_id, pInvItems.proc_im_category_id, pInvItems.price, pInvItems.cgst, pInvItems.sgst, pInvItems.selling_price")
      ->from('procurement_sales_transactions pst')
      ->join('procurement_delivery_challan_items pInvItems', 'pInvItems.id= pst.proc_invoice_items_id')
      // ->join('procurement_itemmaster_subcategory pis', 'pis.id= pii.proc_im_subcategory_id')
      ->where('pst.sales_master_id', $salesId)
      ->get()->result();

// Get Delivery Challan
$current_sales_year= $this->db->where('is_active', 1)->get('procurement_sales_year')->row();
   if(!empty($current_sales_year)) {
      $dc= $this->db->select("pim.*")
          ->from('procurement_delivery_challan_items pii')
          ->join('procurement_delivery_challan_master pim', 'pim.id= pii.invoice_master_id')
          // ->join('procurement_delivery_challan_master pim', 'pim.id= pii.invoice_master_id')
          ->where('pii.id', $item_list[0]->proc_invoice_items_id)
          ->get()->row();

      unset($dc->id);
      unset($dc->total_amount);
      $dc->sales_year_id= $current_sales_year->id;
      $dc->dc_type= 'Normal Delete';

$this->db->trans_start();
// Add new challan master as 'Normal Return'
      $this->db->insert('procurement_delivery_challan_master', $dc);
      $invoice_master_id= $this->db->insert_id();
   

        if(!empty($item_list)) {
          foreach($item_list as $key => $val) {
            // $item_update[]= array(
            //   'id' => $val->proc_im_items_id,
            //   'current_quantity' => (intval($val->current_quantity) + intval($val->quantity))
            // );

            $insertInvItems[]= array(
              'invoice_master_id' => $invoice_master_id,
              'proc_im_items_id' => $val->proc_im_items_id,
              'proc_im_subcategory_id' => $val->proc_im_subcategory_id,
              'proc_im_category_id' => $val->proc_im_category_id,
              'price' => $val->price,
              'cgst' => $val->cgst,
              'sgst' => $val->sgst,
              'selling_price' => $val->selling_price,
              'initial_quantity' => 0, // Because initial quantity already somewhere :: while closing sales year it will create a new row as initial quantity in current sales year
              'current_quantity' => intval($val->quantity),
              'is_closed' => 1,
            );
          }
        }
    // End recovering items

        $data =array(
          'soft_delete'=>1, 
          'soft_delete_by'=>$this->authorization->getAvatarId(), 
          'soft_delete_on'=>$date->format('Y-m-d'), 
          'soft_delete_remarks'=>$remarks, 
          'proc_invoice_master_id_if_returned' => $invoice_master_id
        );
       
        $this->db->where('id',$salesId);
        $this->db->update('procurement_sales_master',$data);
        // $this->db->update_batch('procurement_itemmaster_items',$item_update, 'id');
        if(!empty($insertInvItems)) {
          $this->db->insert_batch('procurement_delivery_challan_items',$insertInvItems);
        }
        $this->db->trans_complete();
      
        return $this->db->trans_status();
      } else {
        return 0;
      }
  }

  public function get_varints_name_for_accounts(){
   return $this->db->select('ipv.id, ipv.item_name as name, ipv.vendor_code, a.account')
    ->from('procurement_itemmaster_items ipv')
    ->join('accounts a','ipv.vendor_code=a.tracknpay_vendor_id')
    ->get()->result();
  }

  public function update_sale_vendor_code_by_varints(){
    $this->db->where('id',$this->input->post('vendor_code_id'));
    return $this->db->update('procurement_itemmaster_items',array('vendor_code'=>$this->input->post('vendor_code')));
  }

  public function delete_vendor_account_by_id($id){
    $this->db->where('id',$id);
    return $this->db->update('procurement_itemmaster_items',array('vendor_code'=>''));
  }

  public function get_cancelation_transaction_report($from_date, $to_date){
      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));
      $this->db->select("sm.id as smId, sy.promotion_status, sm.student_id, sm.receipt_no, sm.payment_type, sm.total_amount, st.quantity, st.amount, CONCAT(ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) AS std_name, CONCAT(ifnull(c.class_name,''), ' ', ifnull(cs.section_name,'')) AS class_section, ipm.subcategory_name as product_name, ipv.item_name as variants, date_format(sm.receipt_date,'%d-%m-%Y') as receipt_date, sm.sales_type, sm.student_name, sm.parent_name, sm.contact_number, sm.receipt_pdf_path, date_format(sm.soft_delete_on,'%d-%m-%Y') as soft_delete_on, sm.soft_delete_remarks, ifnull(sm.class_name,'NA') as class_name")
      ->from('procurement_sales_master sm')
      ->where('sm.soft_delete=1')
      ->join('student_admission s','s.id=sm.student_id', 'left')
      ->join('student_year sy',"s.id=sy.student_admission_id and sy.acad_year_id=$this->yearId", 'left')
      ->join('class c','c.id=sy.class_id', 'left')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->join('procurement_sales_transactions st','sm.id=st.sales_master_id')
      ->join('procurement_itemmaster_subcategory ipm','st.proc_im_subcategory_id=ipm.id') 
      ->join('procurement_itemmaster_items ipv','st.proc_im_items_id=ipv.id')
      ->order_by('sm.receipt_date');
      if (!empty($fromDate) && !empty($toDate)) {
        $this->db->where('date_format(sm.soft_delete_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
     $result = $this->db->get()->result();
     $payment_mode = json_decode($this->input->post('payment_modeJSON'));
      $resData=[];
      foreach ($result as $key => $res) {
        if (!array_key_exists($res->smId, $resData)) {
          switch ($res->payment_type) {
            case '9':
              $type = 'Cash';
              break;
            case '4':
              $type = 'Cheque';
              break;
            case '1':
              $type = 'DD';
              break;
            case '7':
              $type = 'Card';
              break;
            case '8':
              $type = 'Net Banking';
              break;
              case '15':
                $type = 'Pocket Money';
                break;
            default:
              $type = 'Cash';
              break;
          }
          $resData[$res->smId]['promotion_status'] = $res->promotion_status;
          $resData[$res->smId]['receipt_no'] = $res->receipt_no;
          $resData[$res->smId]['student_id'] = $res->student_id;
          $resData[$res->smId]['receipt_date'] = $res->receipt_date;
          $resData[$res->smId]['total_amount'] = $res->total_amount;
          $resData[$res->smId]['payment_type'] = $type;
          $resData[$res->smId]['sales_type'] = $res->sales_type;
          $resData[$res->smId]['soft_delete_remarks'] = $res->soft_delete_remarks;
          $resData[$res->smId]['soft_delete_on'] = $res->soft_delete_on;
          $resData[$res->smId]['std_name'] = ($res->sales_type == 'existing')?$res->std_name:$res->student_name;
          $resData[$res->smId]['class_section'] = ($res->sales_type == 'existing')?$res->class_section:$res->class_name;
          $resData[$res->smId]['products'] = '';
        }
        $resData[$res->smId]['products'] .= $res->product_name .'<br>' .$res->variants . ' ( Amount : '.$res->amount .' ) <br>' ;
      }

      if(empty($result)) {
        return ['resData'=> array(), 'status'=> '-1'];
      }

      return ['resData'=> $resData, 'status'=> '1'];
      // return $resData;
    }

    public function get_prodcut_variant_list_report(){
      return $this->db->select('pii.id as vId, pii.item_name as varinat_name, pis.id as subcategoy_id, pic.id as category_id')
      ->from('procurement_itemmaster_items pii')
      ->join('procurement_itemmaster_subcategory pis', "pii.proc_im_subcategory_id= pis.id", 'left')
      ->join('procurement_itemmaster_category pic', "pic.id= pis.proc_im_category_id", 'left')
      ->where('pic.is_sellable', 1)
      ->where('current_quantity!=0')
      ->get()->result();
    }


    public function get_new_student_sales_list_report(){
      return $this->db->select("sm.receipt_no, sm.payment_type, sm.total_amount, sm.remarks, sm.student_name, sm.parent_name, sm.contact_number, sm.class_name, date_format(sm.receipt_date,'%d-%m-%Y') as receipt_date")
      ->from('procurement_sales_master sm')
      ->where('sm.soft_delete!=1')
      ->where('sm.sales_type', 'new')
      ->order_by('sm.receipt_date','desc')
      ->get()->result();
    }

    public function getItemSales() {
      // echo '<pre> Hello Moto'; die();
      $recpt= $_POST['search_by_value'];
      $receipt= explode('___', $recpt);
      $psm_id= 0;
      if(count($receipt) == 2) {
        $receipt_no= $receipt[0];
        $psm_id= $receipt[1];
      } else {
        $receipt_no= $recpt;
      }

      

      $current_sales_year= $this->db_readonly->where('is_active', 1)->get('procurement_sales_year')->row(); // Ye wala
      if(empty($current_sales_year)) { // Ye wala
        return false; // Ye wala
      } // Ye wala

      $this->db_readonly->select("pst.quantity, pst.amount, pst.id as sales_transaction_id, psm.receipt_date, psm.created_on, psm.receipt_pdf_path, pii.id as item_id, pii.item_name, pInvItem.selling_price as unit_selling_price_of_item, ( pInvItem.price + ifnull(pInvItem.sgst, 0) + ifnull(pInvItem.cgst, 0) ) as unit_cost_of_item, psm.student_id as student_id, 'Today' as last_date_of_return_item, psm.sales_type, psm.student_name as new_student_name, psm.parent_name, psm.contact_number, psm.id as proc_sales_master_id, pst.proc_invoice_items_id")
          ->from('procurement_sales_transactions pst')
          ->join('procurement_sales_master psm', "psm.id= pst.sales_master_id")
          ->join('procurement_itemmaster_items pii', "pii.id= pst.proc_im_items_id")
          ->join('procurement_delivery_challan_items pInvItem', "pInvItem.id= pst.proc_invoice_items_id");
      if($receipt_no != '0') {
        $this->db_readonly->where('psm.receipt_no', $receipt_no);
      } else {
        $this->db_readonly->where('psm.id', $psm_id);
      }
      $this->db_readonly->where('pst.sales_year_id', $current_sales_year->id); // restriction on item if it was not purchased in the current sales year
      $a= $this->db_readonly->get()->result();

      // echo '<pre>'; print_r($this->db_readonly->last_query($a)); die();

      // 

// for existing student
        if(!empty($a) && $a[0]->sales_type == 'existing') {
          $std_nameTest= $this->db_readonly->select("concat( ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) as name, sy.promotion_status")
          ->join('student_year sy', 'sa.id = sy.student_admission_id')
          ->where('sa.id', $a[0]->student_id)
          ->order_by('sy.id', 'desc')
          ->get('student_admission sa')->row();

          $std_name=  $std_nameTest->name;
          if($std_nameTest->promotion_status == '4' || $std_nameTest->promotion_status == '5') {
            $std_name= "<font color='red'>$std_name  - Alumini</font>";
          }
        }

      $this->load->library('filemanager');
      if(!empty($a)) {
        foreach($a as $key => $val) {
          if($val->receipt_pdf_path) {
            $a[$key]->receipt_pdf_path= $this->filemanager->getFilePath($val->receipt_pdf_path);
          }
          // for existing and new student
          if($val->sales_type == 'existing') {
            $val->student_name= $std_name;
            // If some items returned already
            $if_returned= $this->db_readonly->select("return_quantity")->where('proc_sales_master_id', $val->proc_sales_master_id)->where('student_id', $val->student_id)->where('proc_im_items_id', $val->item_id)->get('procurement_sales_return');
          } else {
            $val->student_name= $val->new_student_name. " s/o - " .$val->parent_name. " Mobile- " .$val->contact_number;
            $val->student_id= '-1';
            // If some items returned already
            $if_returned= $this->db_readonly->select("return_quantity")->where('proc_sales_master_id', $val->proc_sales_master_id)->where('student_name', $val->new_student_name)->where('parent_name', $val->parent_name)->where('contact_number', $val->contact_number)->get('procurement_sales_return');
          }
  
          if($if_returned->num_rows() > 0) {
            $if_returned= $if_returned->result();
            $already_returned_quantity= 0;
            foreach($if_returned as $ky => $vlu) {
              $already_returned_quantity += $vlu->return_quantity;
            }
            $val->already_returned_quantity= $already_returned_quantity;
          } else {
            $val->already_returned_quantity= '0';
          }
        }
      }
      
      // echo '<pre>'; print_r($a); die();

      if(!empty($a)) {
        return $a;
      }

      return '-1';
    }

    public function submit_return_new(){ 
      $x= $this->input->post();

      $return_ids= array();
      $item_ids_arr= explode(',', $x['item_ids_arr']);
      $return_proc_invoice_items_id= explode(',', $x['return_proc_invoice_items_id']);
      $proc_sales_master_ids= explode(',', $x['proc_sales_master_ids']);
      $return_qt_arr= explode(',', $x['item_return_quantity_arr']);
      $item_unit_price_arr= explode(',', $x['item_unit_price_arr']);
      $item_return_quantity_arr= explode(',', $x['item_return_quantity_arr']);
      $item_refund_arr= explode(',', $x['item_refund_arr']);

      $currSalesYear= $this->db->where('is_active', 1)->get('procurement_sales_year')->row();
      if(empty($currSalesYear)) {
        return false;
      }
      $dc= $this->db->select('pim.*')
          ->from('procurement_delivery_challan_items pii')
          ->join('procurement_delivery_challan_master pim', 'pim.id= pii.invoice_master_id')
          ->where('pii.id', $return_proc_invoice_items_id[0])
          ->get()->row();

      unset($dc->id);
      unset($dc->total_amount);
      $dc->sales_year_id= $currSalesYear->id;
      $dc->dc_type= 'Normal Return';
      
      $this->db->trans_start();

      $this->db->insert('procurement_delivery_challan_master', $dc);
      $invoice_master_id= $this->db->insert_id();

      $return_ids= [];
      foreach($item_ids_arr as $key => $val) {
        if($return_qt_arr[$key] > 0) {
          $invItems= $this->db->where('id', $return_proc_invoice_items_id[$key])->get('procurement_delivery_challan_items')->row();
          $insert_curr_qty_arr_for_nvoice_items[]= array(
            'invoice_master_id' => $invoice_master_id,
            'proc_im_subcategory_id' => $invItems->proc_im_subcategory_id,
            'proc_im_category_id' => $invItems->proc_im_category_id,
            'price' => $invItems->price,
            'cgst' => $invItems->cgst,
            'sgst' => $invItems->sgst,
            'selling_price' => $invItems->selling_price,
            'initial_quantity' => 0,
            'current_quantity' =>intval($return_qt_arr[$key]),
            'proc_im_items_id' => $val
          );

          $y= array(
            'proc_sales_master_id' => $proc_sales_master_ids[$key],
            'student_id' => $x['stuent_id'],
            'student_type' => isset($x['sales_type']) && $x['sales_type'] != '' ? $x['sales_type'] : '',
            'bank_name' => isset($x['bank']) && $x['bank'] != '' ? $x['bank'] : '',
            'bank_branch' => isset($x['branch']) && $x['branch'] != '' ? $x['branch'] : NULL,
            'cheque_dd_number' => isset($x['chq_no']) && $x['chq_no'] != '' ? $x['chq_no'] : NULL,
            'recon_status' => isset($x['contact_number']) && $x['contact_number'] != '' ? $x['contact_number'] : NULL,
            'recon_submitted_on' => date('Y-m-d H:i:s'),
            'cheque_dd_date' => date('Y-m-d'),
            'refund_payment_remarks' => isset($x['payment_remarks']) && $x['payment_remarks'] != '' ? $x['payment_remarks'] : NULL,
            'mode_of_payment' => isset($x['payment_mode']) && $x['payment_mode'] != '' ? $x['payment_mode'] : NULL,
            'proc_im_items_id' => $val,
            'item_unit_price' => $item_unit_price_arr[$key],
            'return_quantity' => $item_return_quantity_arr[$key],
            'refund_amount' => $item_refund_arr[$key],
            'return_reason' => $x['return_reason'],
            'returned_by_id' => $this->authorization->getAvatarStakeHolderId(),

            'sales_year_id' => $x['sales_year_id'],
            'proc_invoice_items_id' => $return_proc_invoice_items_id[$key],
            'proc_invoice_master_id_if_returned' => $invoice_master_id
          );

          $this->db->insert('procurement_sales_return', $y);
          array_push($return_ids, $this->db->insert_id());
        }
      }

      if(!empty($insert_curr_qty_arr_for_nvoice_items)) {
        $this->db->insert_batch('procurement_delivery_challan_items', $insert_curr_qty_arr_for_nvoice_items);
      }
      // die();
      $this->db->trans_complete();
      if($this->db->trans_status() === true){
        return $return_ids;
      }else{
        return array();
      }

    }


    public function submit_return(){
      $x= $this->input->post();

      // echo '<pre>'; print_r($x); die();

      $return_ids= array();
      $this->db->trans_start();
      $item_ids_arr= explode(',', $x['item_ids_arr']);
      $proc_sales_master_ids= explode(',', $x['proc_sales_master_ids']);
      $return_qt_arr= explode(',', $x['item_return_quantity_arr']);
      $insert_curr_qty_arr_for_nvoice_items= [];
      $update_curr_qty_arr_for_nvoice_items= [];
      foreach($item_ids_arr as $key => $val) {
        if(intval($return_qt_arr[$key]) > 0) {
          $cur_qty= $this->db->select("current_quantity")->where('id', $val)->get('procurement_itemmaster_items')->row()->current_quantity;
          $updated_curr_qty= intval($cur_qty) + intval($return_qt_arr[$key]);
          $update_curr_qty_arr[]= array(
            'id' => $val,
            'current_quantity' => $updated_curr_qty
          );

          $invoiceMaster_and_salesYear_id= $this->db->select('pst.proc_invoice_items_id, p_inv_i.current_quantity')
            ->join('procurement_delivery_challan_items p_inv_i', "p_inv_i.id = pst.proc_invoice_items_id")
            ->where('pst.sales_master_id', $proc_sales_master_ids[$key])
            ->where('pst.proc_im_items_id', $val)
            ->where('pst.sales_year_id', $x['sales_year_id'])
            ->get('procurement_sales_transactions pst')->row();
        if(!empty($invoiceMaster_and_salesYear_id)) {
          $latest_proc_invoice_items_id= $invoiceMaster_and_salesYear_id->proc_invoice_items_id;
          $updated_curr_qty_inv= intval($invoiceMaster_and_salesYear_id->current_quantity) + intval($return_qt_arr[$key]);
          $update_curr_qty_arr_for_nvoice_items[]= array(
            'id' => $invoiceMaster_and_salesYear_id->proc_invoice_items_id,
            'current_quantity' => $updated_curr_qty_inv
          );
        } else {
          $vendor_id_for_curr_salesYear= $this->db->select("pInvMaster.vendor_id")
              ->from('procurement_sales_transactions pst')
              ->join('procurement_delivery_challan_items p_inv_i', "p_inv_i.id = pst.proc_invoice_items_id")
              ->join('procurement_delivery_challan_master pInvMaster', "pInvMaster.id = p_inv_i.invoice_master_id")
              ->where('pst.sales_master_id', $proc_sales_master_ids[$key])
              ->where('pst.proc_im_items_id', $val)
              ->get()->row();
              if(!empty($vendor_id_for_curr_salesYear)) {
                $vendor_id_for_curr_salesYear= $vendor_id_for_curr_salesYear->vendor_id;
              } else {
                $vendor_id_for_curr_salesYear= 0;
              }

          $subCat_cat_id= $this->db->select('p_inv_i.id as proc_invoice_items_id, p_inv_i.proc_im_subcategory_id, p_inv_i.proc_im_category_id, p_inv_i.price, p_inv_i.cgst, p_inv_i.sgst, p_inv_i.selling_price')
              ->from('procurement_sales_transactions pst')
              ->join('procurement_delivery_challan_items p_inv_i', "p_inv_i.id = pst.proc_invoice_items_id")
              ->where('pst.sales_master_id', $proc_sales_master_ids[$key])
              ->where('pst.proc_im_items_id', $val)
              ->get()->row();
          $latest_proc_invoice_items_id= $subCat_cat_id->proc_invoice_items_id;

          $check_if_exist_n_curr_salesYear= $this->db->select('id')->where('dc_type', 'Return By Student-Staff')->where('vendor_id', $vendor_id_for_curr_salesYear)->where('sales_year_id',  $x['sales_year_id'])->get('procurement_delivery_challan_master')->row();
          if(!empty($check_if_exist_n_curr_salesYear)) {
            $insert_curr_qty_arr_for_nvoice_items[]= array(
              'invoice_master_id' => $check_if_exist_n_curr_salesYear->id,
              'proc_im_subcategory_id' => $subCat_cat_id->proc_im_subcategory_id,
              'proc_im_category_id' => $subCat_cat_id->proc_im_category_id,
              'price' => $subCat_cat_id->price,
              'cgst' => $subCat_cat_id->cgst,
              'sgst' => $subCat_cat_id->sgst,
              'selling_price' => $subCat_cat_id->selling_price,
              'initial_quantity' =>intval($return_qt_arr[$key]),
              'current_quantity' =>intval($return_qt_arr[$key]),
              'proc_im_items_id' => $val
            );
          } else {
            $this->db->insert('procurement_delivery_challan_master', ['vendor_id' => $vendor_id_for_curr_salesYear, 'sales_year_id' => $x['sales_year_id'], 'dc_type' => 'Return By Student-Staff', 'invoice_no' => 'Return'.date('ymdHis'), 'bill_no' => 'BillRet'.date('YmdHis')]);
            $insert_curr_qty_arr_for_nvoice_items[]= array(
              'invoice_master_id' => $this->db->insert_id(),
              'proc_im_subcategory_id' => $subCat_cat_id->proc_im_subcategory_id,
              'proc_im_category_id' => $subCat_cat_id->proc_im_category_id,
              'price' => $subCat_cat_id->price,
              'cgst' => $subCat_cat_id->cgst,
              'sgst' => $subCat_cat_id->sgst,
              'selling_price' => $subCat_cat_id->selling_price,
              'initial_quantity' =>intval($return_qt_arr[$key]),
              'current_quantity' =>intval($return_qt_arr[$key]),
              'proc_im_items_id' => $val
            );
          }
          
        }
          

          $y= array(
            'proc_sales_master_id' => $proc_sales_master_ids[$key],
            'student_id' => $x['stuent_id'],
            'student_type' => isset($x['sales_type']) && $x['sales_type'] != '' ? $x['sales_type'] : '',
            'bank_name' => isset($x['bank']) && $x['bank'] != '' ? $x['bank'] : '',
            'bank_branch' => isset($x['branch']) && $x['branch'] != '' ? $x['branch'] : NULL,
            'cheque_dd_number' => isset($x['chq_no']) && $x['chq_no'] != '' ? $x['chq_no'] : NULL,
            'recon_status' => isset($x['contact_number']) && $x['contact_number'] != '' ? $x['contact_number'] : NULL,
            'recon_submitted_on' => date('Y-m-d H:i:s'),
            'cheque_dd_date' => date('Y-m-d'),
            'refund_payment_remarks' => isset($x['payment_remarks']) && $x['payment_remarks'] != '' ? $x['payment_remarks'] : NULL,
            'mode_of_payment' => isset($x['payment_mode']) && $x['payment_mode'] != '' ? $x['payment_mode'] : NULL,
            'proc_im_items_id' => $val,
            'item_unit_price' => explode(',', $x['item_unit_price_arr'])[$key],
            'return_quantity' => $return_qt_arr[$key],
            'refund_amount' => explode(',', $x['item_refund_arr'])[$key],
            'return_reason' => $x['return_reason'],
            'returned_by_id' => $this->authorization->getAvatarStakeHolderId(),

            'sales_year_id' => $x['sales_year_id'],
            'proc_invoice_items_id' => $latest_proc_invoice_items_id,
          );
          if($x['sales_type'] != 'existing') {
            array_push($y, array(
              'student_name' => isset($x['new_student_name']) && $x['new_student_name'] != '' ? $x['new_student_name'] : NULL,
              'parent_name' => isset($x['parent_name']) && $x['parent_name'] != '' ? $x['parent_name'] : NULL,
              'contact_number' => isset($x['contact_number']) && $x['contact_number'] != '' ? $x['contact_number'] : NULL
            ));
          }
          if($return_qt_arr[$key] > 0) {
            $this->db->insert('procurement_sales_return', $y);
            array_push($return_ids, $this->db->insert_id());
          }

        } // if qty > 0
      } // foreach loop
      if(!empty($update_curr_qty_arr)) {
        $this->db->update_batch('procurement_itemmaster_items', $update_curr_qty_arr, 'id');
      }
      if(!empty($update_curr_qty_arr_for_nvoice_items)) {
        $this->db->update_batch('procurement_delivery_challan_items', $update_curr_qty_arr_for_nvoice_items, 'id');
      }
      if(!empty($insert_curr_qty_arr_for_nvoice_items)) {
        $this->db->insert_batch('procurement_delivery_challan_items', $insert_curr_qty_arr_for_nvoice_items);
      }
      // die();
      $this->db->trans_complete();
      if($this->db->trans_status() === true){
        return $return_ids;
      }else{
        return array();
      }
    }

    public function getAllReturnReports() {
      $inputs= $this->input->post();
      // Commenting old query
      $this->db_readonly->select("psr.return_quantity, sa.id as sa_id, psr.refund_amount, psr.item_unit_price, psr.return_reason, date_format(psr.return_on, '%d-%m-%Y') as return_date, psr.proc_im_items_id, psr.student_id, case when psr.returned_by_id= 0 then 'Admin' else concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')) end as return_accepted_by, pii.item_name, concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) as student_name")
      ->from('procurement_sales_return psr')
      ->join('staff_master sm', 'sm.id= psr.returned_by_id', 'left')
      ->join('procurement_itemmaster_items pii', 'pii.id= psr.proc_im_items_id', 'left')
      ->join('student_admission sa', 'sa.id= psr.student_id', 'left');
      // ->join('student_year sy', 'sa.id= sy.student_admission_id');
      if(isset($inputs['sales_year_id']) && $inputs['sales_year_id'] != '') {
        $this->db_readonly->where('psr.sales_year_id', $inputs['sales_year_id']);
      }
      if($inputs['selected_filter_id'] == 'student' && isset($inputs['selected_student_id']) && $inputs['selected_student_id'] != '') {
        $this->db_readonly->where('psr.student_id', $inputs['selected_student_id']);
      }
      if($inputs['selected_filter_id'] == 'date' && isset($inputs['from_date']) && $inputs['from_date'] != '' && isset($inputs['to_date']) && $inputs['to_date'] != '') {
        $from= date('Y-m-d', strtotime($inputs['from_date']));
        $to= date('Y-m-d', strtotime($inputs['to_date']));
        $this->db_readonly->where("date_format(psr.return_on, '%Y-%m-%d') >= '$from' and date_format(psr.return_on, '%Y-%m-%d') <= '$to'");
      }
      $a= $this->db_readonly->get()->result();

// Temp solution --> later I will change
if(!empty($a)) {
  foreach($a as $key => $val) {
    $isAlumini= $this->db_readonly->where('student_admission_id', $val->sa_id)->order_by('id', 'desc')->get('student_year')->row();
    // echo '<pre>'; print_r($isAlumini); die();
    if(!empty($isAlumini) && ($isAlumini->promotion_status == '4' || $isAlumini->promotion_status == '5')) {
      $val->student_name= "<font color='red'>$val->student_name - Alumini</font>";
    }
  }
}


  //     // Building a new query
  //     $this->db_readonly->select("
  //     psr.return_quantity, 
  //     psr.refund_amount, 
  //     psr.item_unit_price, 
  //     psr.return_reason, 
  //     DATE_FORMAT(psr.return_on, '%d-%m-%Y') as return_date, 
  //     psr.proc_im_items_id, 
  //     psr.student_id, 
  //     CASE 
  //         WHEN psr.returned_by_id = 0 THEN 'Admin' 
  //         ELSE CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) 
  //     END as return_accepted_by, 
  //     pii.item_name, 
  //     CONCAT(IFNULL(sa.first_name, ''), ' ', IFNULL(sa.last_name, '')) as student_name
  // ");
  
  // // Main table
  // $this->db_readonly->from('procurement_sales_return psr');
  
  // // Join staff_master and procurement_itemmaster_items
  // $this->db_readonly
  //     ->join('staff_master sm', 'sm.id = psr.returned_by_id', 'left')
  //     ->join('procurement_itemmaster_items pii', 'pii.id = psr.proc_im_items_id', 'left')
  //     ->join('student_admission sa', 'sa.id = psr.student_id', 'left');
  
  // // Join only the latest student_year record for each student
  // $this->db_readonly->join(
  //     "(SELECT sy1.student_admission_id, MAX(sy1.updated_at) as latest_update 
  //       FROM student_year sy1 
  //       GROUP BY sy1.student_admission_id
  //     ) latest_sy",
  //     'sa.id = latest_sy.student_admission_id',
  //     'left'
  // );
  
  // // Apply dynamic filters
  // if (isset($inputs['sales_year_id']) && $inputs['sales_year_id'] != '') {
  //     $this->db_readonly->where('psr.sales_year_id', $inputs['sales_year_id']);
  // }
  // if ($inputs['selected_filter_id'] == 'student' && isset($inputs['selected_student_id']) && $inputs['selected_student_id'] != '') {
  //     $this->db_readonly->where('psr.student_id', $inputs['selected_student_id']);
  // }
  // if ($inputs['selected_filter_id'] == 'date' && isset($inputs['from_date']) && $inputs['from_date'] != '' && isset($inputs['to_date']) && $inputs['to_date'] != '') {
  //     $from = date('Y-m-d', strtotime($inputs['from_date']));
  //     $to = date('Y-m-d', strtotime($inputs['to_date']));
  //     $this->db_readonly->where("DATE_FORMAT(psr.return_on, '%Y-%m-%d') >= '$from' AND DATE_FORMAT(psr.return_on, '%Y-%m-%d') <= '$to'");
  // }
  
  // // Ensure no duplicate rows are returned
  // $this->db_readonly->group_by([
  //     'psr.proc_im_items_id', 
  //     'psr.student_id', 
  //     'psr.return_quantity', 
  //     'psr.returned_by_id'
  // ]);
  
  // // Execute the query
  // $a = $this->db_readonly->get()->result();
  





      if(!empty($a)) {
        return ['reports' => $a, 'status' => '1'];
      }

      return ['reports' => array(), 'status' => '-1'];
      // echo '<pre>Return: '; print_r($a); die();
    }

    public function get_class_sections() {
      $r= $this->db_readonly->select("cs.section_name, cs.class_name, cs.id")
      ->from('class_section cs')
      ->join('class c', "c.id= cs.class_id", 'left')
      ->where('c.acad_year_id', $this->yearId)
      ->get()->result();

      return $r;
      // echo '<pre>'; print_r($r); die();
    }

    public function get_students() {
      $r= $this->db_readonly->select("sa.id, concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) as name, sy.promotion_status")
      ->from('student_admission sa')
      ->join('student_year sy', 'sy.student_admission_id= sa.id', 'left')
      ->where('sy.class_section_id', $_POST['class_section_id'])
      // ->where('sy.acad_year_id', $this->yearId)
      // ->where()
      ->get()->result();

      return $r;
    }

    public function check_if_rfid_mapped() {
      $input_rfid= $_POST['rfidValue'];
      $student_id= $this->db_readonly->select("sa.id, sy.promotion_status, TRIM(concat(sa.first_name, ' ', ifnull(sa.last_name, ''))) as std_name, sa.admission_no, cs.class_name, cs.section_name, cs.id as class_section_id")
          ->from('student_admission sa')
          ->join("student_year sy","sy.student_admission_id=sa.id")
          ->join('class_section cs','cs.id= sy.class_section_id')
          ->where("sy.acad_year_id",$this->yearId)
          ->where_in('sa.admission_status', [1,2])
          ->where('sy.promotion_status not in ("JOINED")') // Need alumini : , "4", "5"
        ->where('sa.rfid_number',$input_rfid)
        ->get();
      if($student_id->num_rows() > 0) {
        $std= $student_id->row();
        $p_name= $this->db_readonly->select("TRIM(concat(first_name, ' ', ifnull(last_name, ''))) as name")->where('student_id', $std->id)->get('parent')->row()->name;
        $status= ['status' => '1', 'id' => $std->id, 'std_name' => $std->std_name, 'section_name' => $std->section_name, 'class_name' => $std->class_name, 'admission_no' => $std->admission_no, 'parent_name' => $p_name, 'class_section_id' => $std->class_section_id, 'promotion_status' => $std->promotion_status];
      } else {
        $status= ['status' => '-1', 'id' => '-1', 'std_name' => '-1', 'section_name' => '-1', 'class_name' => '-1', 'admission_no' => '-1', 'parent_name' => '-1', 'class_section_id' => '-1'];
      }

      return $status;
    }

    public function getClassSectionNames() {
      $res=  $this->db_readonly->select("cs.id as class_section_id, cs.class_id, cs.section_name, cs.class_name")
        ->from('class c')
        ->join('class_section cs', "c.id= cs.class_id")
        ->where('c.acad_year_id', $this->yearId)
        ->get()
        ->result();

      return $res;
    }

    public function get_all_the_students_class_section_wise() {
      $res=  $this->db_readonly->select("sy.student_admission_id, concat(sa.first_name, ' ', ifnull(sa.last_name, '')) as std_name, sy.promotion_status")
        ->from('student_year sy')
        ->join('student_admission sa', "sa.id= sy.student_admission_id")
        ->where('sy.acad_year_id', $this->yearId)
        ->where('sy.class_section_id', $_POST['class_section_id'])
        ->where_not_in("sy.promotion_status", ['JOINED']) // alumini , '4', '5'
        ->order_by('sa.first_name')
        ->get()
        ->result();

      return $res;
    }

    public function get_student_id_by_adm_no($adm_no) {
      $res=  $this->db_readonly->select("sa.id, concat(sa.first_name, ' ', ifnull(sa.last_name, '')) as std_name, cs.id as class_section_id, cs.section_name, cs.class_name")
          ->from('student_admission sa')
          ->join("student_year sy","sy.student_admission_id=sa.id")
          ->join('class_section cs','cs.id= sy.class_section_id')
          ->where("sy.acad_year_id",$this->yearId)
          ->where('sa.admission_status', 2)
          ->where('sy.promotion_status not in ("JOINED", "4", "5")')
          ->where('admission_no', $adm_no)
          ->get()
          ->row();

        if(!empty($res)) {
          return ['status' => '1', 'id' => $res->id, 'std_name' => $res->std_name, 'class_section_id' => $res->class_section_id, 'section_name' => $res->section_name, 'class_name' => $res->class_name];
        }
      return ['status' => '-1', 'id' => '0', 'std_name' => '', 'class_section_id' => '', 'section_name' => '', 'class_name' => ''];
    }

    public function get_report_of_a_student($student_id, $selected_category, $selected_subcategories) {
// Item Sales
      $this->db_readonly->select("psm.id as sales_msater_id, psm.proc_im_category_id, psm.receipt_no, psm.student_id, psm.receipt_date, date_format(psm.receipt_date, '%d-%m-%Y %h:%i %p') as receipt_date, psm.receipt_pdf_path, psm.remarks as sales_master_remarks, pst.proc_im_subcategory_id, pst.proc_im_items_id, sum(pst.quantity) as quantity, sum(pst.amount) as amount, pii.item_name, pii.selling_price, pii.cost_prodcut, pis.subcategory_name, pii.unit_type, pic.category_name")
        ->from('procurement_sales_master psm')
        ->join('procurement_sales_transactions pst', "pst.sales_master_id= psm.id")
        ->join('procurement_itemmaster_items pii', "pii.id= pst.proc_im_items_id")
        ->join('procurement_itemmaster_subcategory pis', "pis.id= pst.proc_im_subcategory_id")
        ->join('procurement_itemmaster_category pic', "pic.id= psm.proc_im_category_id")
        ->where('psm.student_id', $student_id)
        ->where('psm.soft_delete', 0);
        if(isset($selected_category) && $selected_category) {
          $this->db_readonly->where('pic.id', $selected_category);
        }
        if(isset($selected_subcategories) && $selected_subcategories && !empty($selected_subcategories)) {
          $this->db_readonly->where_in('pis.id', $selected_subcategories);
        }
        $sales=   $this->db_readonly->group_by('pst.proc_im_items_id')->order_by('pic.id, pis.id')->get()->result();

// Sales Return
      $return= $this->db_readonly->select("psr.student_id, psr.proc_im_items_id, psr.item_unit_price, sum(psr.return_quantity) as return_quantity, sum(psr.refund_amount) as refund_amount, psr.return_reason, date_format(return_on, '%d-%m-%Y %h:%i %p') as return_on, pii.item_name, pii.selling_price, pii.cost_prodcut, pis.subcategory_name, pii.unit_type, pic.category_name")
        ->from('procurement_sales_return psr')
        ->join('procurement_itemmaster_items pii', "pii.id= psr.proc_im_items_id")
        ->join('procurement_itemmaster_subcategory pis', "pis.id= pii.proc_im_subcategory_id")
        ->join('procurement_itemmaster_category pic', "pic.id= pis.proc_im_category_id")
        ->where('psr.student_id', $student_id)
        ->group_by('psr.proc_im_items_id')
        ->order_by('pic.id, pis.id')
        ->get()->result();

        if(!empty($sales) && !empty($return)) {
          return ['status' => 's_r', 'sales' => $sales, 'return' => $return];
        } else if(!empty($sales) && empty($return)) {
          return ['status' => 's', 'sales' => $sales, 'return' => array()];
        } else {
          return ['status' => 'ns_nr', 'sales' => array(), 'return' => array()];
        }
      
    }

    public function get_category_list_report(){
      return $this->db->select('pic.category_name as category_name,  pic.id as category_id')
      ->where('pic.is_sellable', 1)
      ->get('procurement_itemmaster_category pic')->result();
    } 
    
    public function get_sub_category_list_report($categories){
      return $this->db->select('pis.subcategory_name as subcategory_name, pis.id as subcategoy_id, pis.proc_im_category_id')
      // ->where_in('pis.proc_im_category_id', $categories)
      ->get('procurement_itemmaster_subcategory pis')->result();
    }

    public function get_item_list_report($sub_categories){
      return $this->db->select('item_name, id, proc_im_subcategory_id')
      // ->where_in('proc_im_subcategory_id', $sub_categories)
      ->get('procurement_itemmaster_items')->result();
    }

    public function get_student_data_by_class_section($class_section_id) {
      $res= $this->db_readonly->select("TRIM(concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, ''))) as stdName, sa.id, sy.promotion_status")
        ->from('student_admission sa')
        ->join("student_year sy","sy.student_admission_id=sa.id")
        // ->where("sy.acad_year_id",$this->yearId) // Commenting for getting next acad year students also
        ->where('sy.class_section_id', $class_section_id)
        ->where_not_in('sy.promotion_status', array('JOINED'))
        ->order_by('sa.first_name')
        ->get()->result();
      if(!empty($res)) {
        // echo '<pre>'; print_r($res); die();
        return $res;
      }
      return '-1';
    }

    public function get_sales_receipt_template_data(){
      return $this->db->get('procurement_settings')->row();
    }

    public function get_sales_return_data($last_insert_id){
      $data= $this->db->select("psr.*, pii.item_name, pii.unit_type, if(psr.student_type = 'existing', TRIM(concat(sa.first_name, ' ', ifnull(sa.last_name, ''))), TRIM(psr.student_name)) as std_name")
        ->from('procurement_sales_return psr')
        ->join('procurement_itemmaster_items pii', 'pii.id= psr.proc_im_items_id')
        ->join('student_admission sa', 'sa.id= psr.student_id', 'left')
        // ->where()
        ->where('psr.id', $last_insert_id)
        ->get()->result();

        if(!empty($data) && $data[0]->student_type == 'existing') {
          $class_section= $this->db->select("cs.section_name, cs.class_name")
            ->from('student_year sy')
            ->join('class_section cs', 'cs.id= sy.class_section_id')
            ->where('sy.student_admission_id', $data[0]->student_id)
            ->where('sy.acad_year_id', $this->yearId)
            // ->where("sy.promotion_status not in ('4', 'JOINED', '5')")
            ->get()->row();

            $data['cls_sec']= $class_section;
        } else {
          $data['cls_sec']= ['section_name' => 'New', 'class_name' => 'Student'];
        }


        // echo '<pre>'; print_r($data); die();
        return $data;


    }

    public function store_receipt_html_to_return_table($html, $id) {
     

      $this->db->trans_start();
      $this->db->where('id', $id);
      $this->db->update('procurement_sales_return', array('receipt_html_template' => $html));
      $this->db->trans_complete();

      return $this->db->trans_status();

    }

    public function updateSalesReturnPdfPath($id, $path) { // Nedd to conveert into update_batch
        $this->db->where_in('id',$id);
        return $this->db->update('procurement_sales_return', array('receipt_pdf'=> $path, 'pdf_status' => '0'));
    }

    public function updateReturnSalesPdfLink($path, $status) { // Column not added yet
      $this->db->where('receipt_pdf',$path);
			return $this->db->update('procurement_sales_return', array('pdf_status' => $status));
    }

    public function genearte_pdf_for_return_receipt($html, $id) {
        $school = CONFIG_ENV['main_folder'];
        $path = $school . '/sales_return_receipts/' . uniqid() . '-' . time() . ".pdf";

        $bucket = $this->config->item('s3_bucket');

        $status = $this->updateSalesReturnPdfPath($id, $path);

        $page = 'landscape';
        $page_size = 'a4';
     

      // $page = 'landscape';
      $page = '';
      $curl = curl_init();

      $postData = urlencode($html);

          $username = CONFIG_ENV['job_server_username'];
          $password = CONFIG_ENV['job_server_password'];
        $return_url = site_url() . 'Callback_Controller/updateRetrnSalesPdfLink';

        curl_setopt_array($curl, array(
              CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_USERPWD => $username . ":" . $password,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => "path=" . $path . "&bucket=" . $bucket . "&page=" . $page . "&page_size=" . $page_size . "&data=" . $postData . "&return_url=" . $return_url,
        CURLOPT_HTTPHEADER => array(
          "Accept: application/json",
          "Cache-Control: no-cache",
          "Content-Type: application/x-www-form-urlencoded",
          "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
        ),
      ));

      $response = curl_exec($curl);
      $err = curl_error($curl);
      curl_close($curl);

      return $status;

    }

  public function get_receipts_from_sales_master_id($proc_sales_return_id) { // Nedd to conveert into update_batch
    // echo '<pre>'; print($proc_sales_return_id); die();
      $res= $this->db->select("psr.receipt_pdf, psr.pdf_status, pii.item_name")
        ->from('procurement_sales_return psr')
        ->join('procurement_itemmaster_items pii', 'pii.id= psr.proc_im_items_id')
        // ->where("date_format(psr.return_on, '%Y-%m-%d')=", date('Y-m-d'))
        ->where_in('psr.id', explode(',', $proc_sales_return_id))
        ->get()
        ->result();

      return $res;
  }

  public function updateSalesPdfLink_v2($path, $status) {
    $this->db->where('receipt_pdf_path',$path);
    return $this->db->update('procurement_sales_master', array('pdf_status' => $status));
  }

  public function get_item_details_from_item_name($array_of_items, $array_of_prices) {
    $lists= [];
    $unlist= [];
    foreach($array_of_items as $key => $val) {
      $price= $array_of_prices[$key];
      $item_lists= $this->db_readonly->select("pii.id as pii_id, pii.item_name, pis.id as subcategory_id, pis.subcategory_name, pic.id as pic_id, pic.category_name, pii.selling_price, pii.cost_prodcut")
        ->from('procurement_itemmaster_items pii')
        ->join('procurement_itemmaster_subcategory pis', 'pii.proc_im_subcategory_id= pis.id')
        ->join('procurement_itemmaster_category pic', 'pis.proc_im_category_id= pic.id')
        // ->where_in('pii.item_name', $array_of_items)
        ->where('pii.item_name like "%'.$val.'%" and (pii.cost_prodcut= "'.$price.'" or pii.selling_price= "'.$price.'")')
        ->get();
        if($item_lists->num_rows() > 0) {
          array_push($lists, $item_lists->row());
        } else {
          array_push($unlist, $val);
        }

        // echo '<pre>'; print_r($lists); die();
    }

        if($lists) {
          return ['list' => $lists, 'unlist' => $unlist];
        }
        return array();

  }

  public function get_all_sales_details_student_wise() {
    $login= $this->authorization->getAvatarStakeHolderId();
    $input= $this->input->post();
    $student_id= $input['student_id'];
    $this->db_readonly->select("psm.receipt_no, date_format(psm.receipt_date, '%d-%m-%y') as created_on, psm.id, psm.total_amount")
        ->join('procurement_itemmaster_category pic', 'pic.id= psm.proc_im_category_id')
        ->where('psm.student_id', $student_id)
        ->where('psm.soft_delete', 0);
    if($login !== 0 && $login !== '0') {
      $this->db_readonly->where("(pic.category_administrator IS NULL OR pic.category_administrator = '' OR pic.category_administrator = $login)");
    }
    $res= $this->db_readonly->get('procurement_sales_master psm')->result();
      foreach($res as $key => $val) {
        $items= $this->db_readonly->select("pst.quantity, pst.amount, pii.item_name")
            ->from('procurement_sales_transactions pst')
            ->join('procurement_itemmaster_items pii', 'pii.id= pst.proc_im_items_id')
            ->where('pst.sales_master_id', $val->id)
            ->get()->result();
        $val->item_details= $items;
        if(empty($val->item_details)) {
          unset($res[$key]);
        }
      }
      // echo '<pre>'; print_r($res); die();
      return $res;
  }

  public function student_and_parent_details($student_id) {
    $std_details= $this->db->select("sa.id as std_id, if(sy.alpha_rollnum is not null and trim(sy.alpha_rollnum) !='', sy.alpha_rollnum, '-') as alpha_rollnum, if(sa.enrollment_number is not null and trim(sa.enrollment_number) !='', sa.enrollment_number, '-') as enrollment_number, trim(concat(sa.first_name, ' ', ifnull(sa.last_name, ''))) as student_name, cs.section_name, cs.class_name")
      ->from('student_admission sa')
      ->join('student_year sy', 'sy.student_admission_id= sa.id')
      ->join('class_section cs', 'cs.id= sy.class_section_id')
      ->where('sy.acad_year_id', $this->yearId)
      ->where('sa.id', $student_id)
      ->get()->row();

    $parent_details = $this->db->select("p.id as parent_id, p.email as parent_email, p.mobile_no as parent_mobile_no, u.id as user_id, u.token as user_token, IF(u.token IS NULL, 0, 1) as tokenState")
      ->from('student_relation sr')
      ->join('parent p', 'p.id = sr.relation_id')
      ->join('avatar a', 'a.stakeholder_id = p.id')
      ->join('users u', 'u.id = a.user_id')
      ->where_in('sr.relation_type', ['Father', 'Mother'])
      ->where('sr.std_id', $student_id)
      ->group_by('p.id')
      ->get()
      ->result();
    // echo "<pre>";
    // print_r($std_details);
    // print_r($this->db->last_query());
    // die();
    return ['alpha_rollnum' => $std_details->alpha_rollnum, 'enrollment_number' => $std_details->enrollment_number, 'student_name' => $std_details->student_name, 'section_name' => $std_details->section_name, 'class_name' => $std_details->class_name, 'parent_details' => $parent_details];
  }

  public function insert_items_details_by_student_wise($student_id, $proc_qt, $proc_unit_price, $proc_price, $proc_items_id){

    $itemsExplode = explode(',',$proc_items_id);
    $proc_priceExplode = explode(',',$proc_price);
    $proc_qtExplode = explode(',',$proc_qt);
    $proc_unit_priceExplode = explode(',',$proc_unit_price);
    $products = [];
    foreach ($itemsExplode as $key => $itemId) {
      $products[$itemId][] = array(
        'proc_im_items_id' => $itemId,
        'quantity' => $proc_qtExplode[$key],
        'total_amount_item' => trim($proc_priceExplode[$key]),
        'amount' => $proc_unit_priceExplode[$key] * $proc_qtExplode[$key],
      );
    }
    $sqlItems = "select pii.id as item_id, pis.id as subcat_id,  pis.proc_im_category_id, current_quantity from procurement_itemmaster_items pii 
    join procurement_itemmaster_subcategory pis on pii.proc_im_subcategory_id= pis.id
    where pii.id in ($proc_items_id)";
    $result = $this->db->query($sqlItems)->result();

    $resItemsArry =[];
    foreach ($result as $key => $val) {
      $resItemsArry[$val->item_id] = $val;
    }
    $categoryIds=[];
    foreach ($products as $itemId => $value) {
      foreach ($value as $key => $val) {
        if(array_key_exists($itemId, $resItemsArry)){
          $categoryIds[]= array(
            'proc_im_items_id'=>$val['proc_im_items_id'],
            'quantity'=>$val['quantity'],
            'amount'=>$val['amount'],
            'total_amount_item'=>$val['total_amount_item'],
            'subcat_id'=>$resItemsArry[$itemId]->subcat_id,
            'proc_im_category_id'=>$resItemsArry[$itemId]->proc_im_category_id,
            'current_quantity'=>$resItemsArry[$itemId]->current_quantity
          );
        }
      }
    }
    $categorywiseData =[];
    foreach ($categoryIds as $key => $val) {
      $categorywiseData[$val['proc_im_category_id']][] = $val;
    }
    $this->db->trans_start();
    foreach ($categorywiseData as $catId => $val) {
      $totalAmount = 0;
      foreach ($val as $key => $value) {
        $totalAmount += $value['total_amount_item'];
      }
      $master_data = array(
        'student_id'=> ($student_id == '')?0:$student_id,
        'proc_im_category_id'=> $catId,
        'payment_type'=> 9,
        'total_amount'=> $totalAmount,
        'receipt_date'=> date('Y-m-d'),
        'created_by'=> $this->authorization->getAvatarId(),
        'recon_status'=> '0',
        'remarks'=> 'Import from excel sheet',
        'sales_type' => 'existing',
        'acad_year_id' => $this->yearId,
        'receipt_no' =>'0'
      );
     

      $this->db->insert('procurement_sales_master',$master_data);
      $stransId = $this->db->insert_id();

      $dataArry = [];
      foreach ($val as $k =>  $productId) {
        $dataArry[] =  array(
          'sales_master_id'=> $stransId,
          'proc_im_subcategory_id'=> $productId['subcat_id'],
          'proc_im_items_id'=> $productId['proc_im_items_id'],
          'quantity'=> $productId['quantity'], 
          'amount'=> $productId['amount'],
        );
      }
      $this->db->insert_batch('procurement_sales_transactions', $dataArry);

      $vQtyUpdate = [];
      foreach ($val as $k =>  $productId) {
        $vQtyUpdate[] =  array(
          'id'=> $productId['proc_im_items_id'],
          'current_quantity'=> $productId['current_quantity'] -  $productId['quantity'],
        );
      }
      $this->db->update_batch('procurement_itemmaster_items', $vQtyUpdate,'id');
    }
    $this->db->trans_complete();
    if ($this->db->trans_status()) {
      return $student_id;
    }else{
      return false;
    }

  }


  // The Fees module for expense tracking and student reports. Verify both modules any changes 
  public function get_report_of_a_student_latest($student_id, $selected_category, $selected_subcategories, $sales_year_id) {
    // Item Sales
        $this->db_readonly->select("'Issue' as tx_type, '+' as symbol,  psm.id as sales_msater_id, psm.proc_im_category_id, psm.receipt_no, psm.student_id, psm.receipt_date, date_format(psm.receipt_date, '%d-%m-%Y %h:%i %p') as receipt_date, psm.receipt_pdf_path, psm.remarks as sales_master_remarks, pst.proc_im_subcategory_id, pst.proc_im_items_id, sum(pst.quantity) as quantity, sum(pst.amount) as amount, pii.item_name, pInvItem.selling_price, pInvItem.price as cost_prodcut, pis.subcategory_name, pii.unit_type, pic.category_name, '-' as return_on, GROUP_CONCAT(pst.quantity, ' on ', date_format(psm.receipt_date, '%d-%m-%Y')) as sold_on")
          ->from('procurement_sales_master psm')
          ->join('procurement_sales_transactions pst', "pst.sales_master_id= psm.id")
          ->join('procurement_delivery_challan_items pInvItem', "pst.proc_invoice_items_id= pInvItem.id")
          ->join('procurement_itemmaster_items pii', "pii.id= pst.proc_im_items_id")
          ->join('procurement_itemmaster_subcategory pis', "pis.id= pst.proc_im_subcategory_id")
          ->join('procurement_itemmaster_category pic', "pic.id= psm.proc_im_category_id")
          ->where('psm.student_id', $student_id)
          ->where('psm.soft_delete', 0);
          if(isset($sales_year_id) && $sales_year_id && $sales_year_id != '') {
            $this->db_readonly->where('pst.sales_year_id', $sales_year_id);
          }
          if(isset($selected_category) && $selected_category) {
            $this->db_readonly->where('pic.id', $selected_category);
          }
          if(isset($selected_subcategories) && $selected_subcategories && !empty($selected_subcategories)) {
            $this->db_readonly->where_in('pis.id', $selected_subcategories);
          }
          $sales=   $this->db_readonly->group_by('pst.proc_im_items_id')->order_by('pic.category_name, pis.subcategory_name')->get()->result();
  
    // Sales Return
        $this->db_readonly->select("'Return' as tx_type, '-' as symbol, psr.student_id, psr.proc_im_items_id, psr.item_unit_price, sum(psr.return_quantity) as quantity, sum(psr.refund_amount) as amount, psr.return_reason, date_format(return_on, '%d-%m-%Y %h:%i %p') as return_on, pii.item_name, pInvItem.selling_price, pInvItem.price as cost_prodcut, pis.subcategory_name, pii.unit_type, pic.category_name,GROUP_CONCAT(psr.return_quantity, ' on ', date_format(psr.return_on, '%d-%m-%Y')) as return_on, '-' as sold_on")
          ->from('procurement_sales_return psr')
          // ->join('procurement_sales_master psm','psr.proc_sales_master_id=psm.id')
          // ->join('procurement_sales_transactions pst','psm.id=pst.sales_master_id')

          ->join('procurement_delivery_challan_items pInvItem', "psr.proc_invoice_items_id= pInvItem.id")
          ->join('procurement_itemmaster_items pii', "pii.id= psr.proc_im_items_id")
          ->join('procurement_itemmaster_subcategory pis', "pis.id= pii.proc_im_subcategory_id")
          ->join('procurement_itemmaster_category pic', "pic.id= pis.proc_im_category_id")
          ->where('psr.student_id', $student_id)
          ->group_by('psr.proc_im_items_id')
          ->order_by('pic.category_name, pis.subcategory_name');
          if(isset($sales_year_id) && $sales_year_id && $sales_year_id != '') {
            $this->db_readonly->where('psr.sales_year_id', $sales_year_id);
            // $this->db_readonly->where('pst.sales_year_id', $sales_year_id);
          }
          if(isset($selected_category) && $selected_category) {
            $this->db_readonly->where('pic.id', $selected_category);
          }
          if(isset($selected_subcategories) && $selected_subcategories && !empty($selected_subcategories)) {
            $this->db_readonly->where_in('pis.id', $selected_subcategories);
          }
          $return= $this->db_readonly->get()->result();



          // echo '<pre>'; print_r($sales); die();
          // echo '<pre>'; print_r($return); die();

      $final_arr= [];
          $what_returrns_array= []; // to store the return items which is not matched with sales items that means they were from previous sales year
      if(!empty($sales)) {
        foreach($sales as $saleKey => $saleVal) {
          $final_arr[]= $saleVal; // storing issue data
          $isReturned= false;
          $retQty= 0;
          $retAmt= 0;
          $return_on= '';
          if(!empty($return)) {
            foreach($return as $returnKey => $returnVal) {
              if($returnVal->proc_im_items_id == $saleVal->proc_im_items_id) {
                $what_returrns_array[]= $returnVal->proc_im_items_id;

                $final_arr[]= $returnVal; // storing return data
                $retQty= $returnVal->quantity;
                $retAmt= $returnVal->amount;
                $return_on= $returnVal->return_on;
                $isReturned= true;
              }
            }

            
            
          } 







          $newObj= new stdClass();
          if($isReturned) {
            $newObj->tx_type= 'Total';
            $newObj->symbol= '=';
            $newObj->quantity= ( $saleVal->quantity -  $retQty);
            $newObj->amount=  ( $saleVal->amount -  $retAmt);
            $newObj->return_on=  $return_on;
            // echo '<pre>'; print_r($saleVal->amount); 
            // echo '<pre>'; print_r($returnVal->amount); 
            // echo '<pre>'; print_r($newObj->amount); 
            // die();
          } else {
            $newObj->tx_type= 'Total';
            $newObj->symbol= '=';
            $newObj->quantity= $saleVal->quantity;
            $newObj->amount=  $saleVal->amount;
            $newObj->return_on=  '-';
          }
          $final_arr[]= $newObj; // storing total data
        }
      }



      // 
          if(count($what_returrns_array) != count($return)) {// to store the return items which is not matched with sales items that means they were from previous sales year
              foreach($return as $returnKey => $returnVal) {
              if( !in_array($returnVal->proc_im_items_id, $what_returrns_array) ) {
                // $what_returrns_array[]= $returnVal->proc_im_items_id;

                $returnVal->tx_type= "$returnVal->tx_type (Bought from previous year)";
                $returnVal->is_bought_from_prev_sales_year= 1;
                $final_arr[]= $returnVal; // storing return data
                $retQty= $returnVal->quantity;
                $retAmt= $returnVal->amount;
                $return_on= $returnVal->return_on;
                $isReturned= true;
              }
            }


            // $what_returrns_array= [];
            }
// 




         
      // echo '<pre>'; print_r($final_arr); die();
          
          
          
          
          
        //   $merged= array_merge($sales, $return);
        // // Function to compare items based on proc_im_items_id
        //   function compareItems($a, $b) {
        //     if ($a->proc_im_items_id == $b->proc_im_items_id) {
        //         return 0;
        //     }
        //     return ($a->proc_im_items_id < $b->proc_im_items_id) ? -1 : 1;
        //   }
        //   usort($merged, 'compareItems');
          // $final_arr= [];
          // $final_amount= 0;
          // $final_quantity= 0;
          // for($i= 0; $i < count($merged); $i++) {
          //   if($merged[$i]->tx_type == 'Issue') {
          //     $final_amount += $merged[$i]->amount;
          //     $final_quantity += $merged[$i]->quantity;
          //   } else {
          //     $final_amount -= $merged[$i]->amount;
          //     $final_quantity -= $merged[$i]->quantity;
          //   }

          //   if($i == 0) {
          //     array_push($final_arr,  $merged[$i]);
          //     if(count($merged) > 1) {
          //         if( $merged[$i]->proc_im_items_id !=  $merged[$i + 1]->proc_im_items_id ) {
          //           $newObj= new stdClass();
          //           $newObj->tx_type= 'Total';
          //           $newObj->symbol= '=';
          //           $newObj->quantity= $final_quantity;
          //           $newObj->amount= $final_amount;
          //           array_push($final_arr, $newObj);
          //           $final_amount= 0;
          //           $final_quantity= 0;
          //         }
          //     } else if(count($merged) == 1){
          //       $newObj= new stdClass();
          //       $newObj->tx_type= 'Total';
          //       $newObj->symbol= '=';
          //       $newObj->quantity= $final_quantity;
          //       $newObj->amount= $final_amount;
          //       array_push($final_arr, $newObj);
          //       $final_amount= 0;
          //       $final_quantity= 0;
          //     }

          //   } else if($i < count($merged) - 1) {
          //     array_push($final_arr,  $merged[$i]);
          //     if( $merged[$i]->proc_im_items_id !=  $merged[$i + 1]->proc_im_items_id ) {
          //       $newObj= new stdClass();
          //       $newObj->tx_type= 'Total';
          //       $newObj->symbol= '=';
          //       $newObj->quantity= $final_quantity;
          //       $newObj->amount= $final_amount;
          //       array_push($final_arr, $newObj);
          //       $final_amount= 0;
          //       $final_quantity= 0;
          //     }
          //   } else if($i == count($merged) - 1) {
          //     array_push($final_arr,  $merged[$i]);
          //     $newObj= new stdClass();
          //     $newObj->tx_type= 'Total';
          //     $newObj->symbol= '=';
          //     $newObj->quantity= $final_quantity;
          //     $newObj->amount= $final_amount;
          //     array_push($final_arr, $newObj);
          //     $final_amount= 0;
          //     $final_quantity= 0;
          //   }
          // }

          // echo '<pre>'; print_r($final_arr); die();

          if(!empty($final_arr)) {
            return ['sab_thik_hai_kya' => '1', 'final_arr' => $final_arr];
          }
          return ['sab_thik_hai_kya' => '0', 'final_arr' => []];
        
  }  

  public function get_category_wise_student_sales_report() {
    $input= $this->input->post();
    $category= $input['category'];
    $subcategories= $input['subcategories'];
    $items= $input['items'];

// Getting Items
   $this->db_readonly->select("pic.category_name, pis.subcategory_name, pii.item_name, pii.id as item_id")
      ->from('procurement_itemmaster_category pic');
    if($category != 0) {
      $this->db_readonly->where('pic.id', $category);
    }
    $this->db_readonly->join('procurement_itemmaster_subcategory pis', 'pis.proc_im_category_id = pic.id');
    if($subcategories != 0 && count($subcategories) > 0) {
      $this->db_readonly->where_in('pis.id', $subcategories);
    }
    $this->db_readonly->join('procurement_itemmaster_items pii', 'pii.proc_im_subcategory_id= pis.id');
    if($items != 0 && count($items) > 0) {
      $this->db_readonly->where_in('pii.id', $items);
    }
    $sales_obj= $this->db_readonly->order_by('pic.category_name, pis.subcategory_name, pii.item_name')->get()->result();

    if(!empty($sales_obj)) {
      foreach($sales_obj as $key => $val) {
// Find students who purchased items
        $students= $this->db_readonly->select("psm.student_id as student_id, sa.enrollment_number, concat(sa.first_name, ' ', ifnull(sa.last_name, '')) as name, sa.admission_no, cs.section_name, cs.class_name")
          ->from('procurement_sales_transactions pst')
          ->join('procurement_sales_master psm', 'psm.id= pst.sales_master_id')
          ->where('pst.proc_im_items_id', $val->item_id)
          ->join('student_admission sa', 'sa.id= psm.student_id')
          ->join('student_year sy', 'sy.student_admission_id= sa.id')
          ->join('class_section cs', 'cs.id= sy.class_section_id')
          ->group_by('psm.student_id')
          ->order_by('sa.first_name, sa.last_name')
          ->get()->result();

        $std_arr= [];
          if(!empty($students)) {
            foreach($students as $stdkey => $stdval) {
              $stdObj= $this->calculate_amt_and_qty_student_wise($stdval->student_id, $val->item_id);
              if(!empty($stdObj)) {
                $stdObj->name= $stdval->name;
                $stdObj->admission_no= $stdval->admission_no;
                $stdObj->section_name= $stdval->section_name;
                $stdObj->class_name= $stdval->class_name;
                $stdObj->enrollment_number= $stdval->enrollment_number;
                $std_arr[]= $stdObj;
              }
            }
          }
          $val->students= $std_arr;
      }

    }

    foreach($sales_obj as $key => $val) {
      if(empty($val->students)) {
        unset($sales_obj[$key]);
      }
    }

    return $sales_obj;

  }

  private function calculate_amt_and_qty_student_wise($student_id, $item_id) {
    // Item Sales
        $this->db_readonly->select("sum(pst.quantity) as quantity, sum(pst.amount) as amount")
          ->from('procurement_sales_master psm')
          ->join('procurement_sales_transactions pst', "pst.sales_master_id= psm.id")
          ->where('psm.student_id', $student_id)
          ->where('pst.proc_im_items_id', $item_id)
          ->where('psm.soft_delete', 0);
        $sales=   $this->db_readonly->group_by('pst.proc_im_items_id')->get()->row();
  
    // Sales Return
        $return= $this->db_readonly->select("sum(psr.return_quantity) as quantity, sum(psr.refund_amount) as amount")
          ->from('procurement_sales_return psr')
          ->where('psr.student_id', $student_id)
          ->where('psr.proc_im_items_id', $item_id)
          ->group_by('psr.proc_im_items_id')
          ->get()->row();

      $stdObj= new stdClass();
      if(!empty($sales)) {
        if(!empty($return)) {
          $qty= $sales->quantity - $return->quantity;
          $amt= $sales->amount - $return->amount;
        } else {
          $qty= $sales->quantity;
          $amt= $sales->amount;
        }
        $stdObj->quantity= $qty;
        $stdObj->amount= $amt;
      }

      return $stdObj;
        
  } 

  public function get_items_and_students_ids(){
    $input= $this->input->post();

    $this->db_readonly->select('psm.student_id')
    ->from('procurement_sales_master psm')
    ->join('procurement_sales_transactions pst','psm.id=pst.sales_master_id');
    if($input['items'] != 0 && count($input['items']) > 0) {
      $this->db_readonly->where_in('pst.proc_im_items_id', $input['items']);
    }
    if($input['subcategories'] != 0 && count($input['subcategories']) > 0) {
      $this->db_readonly->where_in('pst.proc_im_subcategory_id', $input['subcategories']);
    }
    if($input['category'] != 0) {
      if($input['report_format'] == 'student-wise') {
        $this->db_readonly->where_in('psm.proc_im_category_id', $input['category']);
      } else {
        $this->db_readonly->where('psm.proc_im_category_id', $input['category']);
      }
    }
    $studentItems= $this->db_readonly->group_by('psm.student_id')->get()->result();

    $student_ids= [];
    foreach($studentItems as $key => $val) {
      $student_ids[]= $val->student_id;
    }
    return $student_ids;
  }

  public function get_items_and_students_list() {
    $input= $this->input->post();
    $category= $input['category'];
    $subcategories= $input['subcategories'];
    $items= $input['items'];

// Items
    $this->db_readonly->select("distinct(pst.proc_im_items_id) as item_id, pic.category_name, pis.subcategory_name, pii.item_name")
      ->from('procurement_sales_transactions pst')
      ->join('procurement_itemmaster_items pii', 'pii.id= pst.proc_im_items_id');
    if($items != 0 && count($items) > 0) {
      $this->db_readonly->where_in('pii.id', $items);
    }
    $this->db_readonly->join('procurement_itemmaster_subcategory pis', 'pis.id= pii.proc_im_subcategory_id');
    if($subcategories != 0 && count($subcategories) > 0) {
      $this->db_readonly->where_in('pis.id', $subcategories);
    }
    $this->db_readonly->join('procurement_itemmaster_category pic', 'pic.id= pis.proc_im_category_id');
    if($category != 0) {
      $this->db_readonly->where('pic.id', $category);
    }
    $items= $this->db_readonly->group_by('pst.proc_im_items_id')->order_by('pic.category_name')->order_by('pis.subcategory_name')->order_by('pii.item_name')->get()->result();

    $item_ids= [];
    foreach($items as $key => $val) {
      $item_ids[]= $val->item_id;
    }
   if(!empty($item_ids)) {
    $students= $this->db_readonly->select("distinct(psm.student_id) as student_id")
    ->from('procurement_sales_transactions pst')
    ->join('procurement_sales_master psm', 'psm.id= pst.sales_master_id')
    ->where_in('pst.proc_im_items_id', $item_ids)
    ->group_by('psm.student_id')
    ->get()->result();

    $student_ids= [];
    foreach($students as $key => $val) {
      $student_ids[]= $val->student_id;
    }
    return ['items' => $items, 'students' => $student_ids];
   }
   return ['items' => [], 'students' => []];
  }

  private function get_student_wise_sales_calculate_amt_and_qty($student_arr, $category, $subcategories, $items, $sales_year_id){
    $select= "psm.id master_id, sum(pst.quantity) as quantity, sum(pst.amount) as amount, psm.student_id, pic.category_name";
    if($items != 0 && count($items) > 0) {
      $select .= ",pii.item_name";
    } else {
      $select .= ",'-' as item_name";
    }
    if($subcategories != 0 && count($subcategories) > 0) {
      $select .= ",pis.subcategory_name";
    } else {
      $select .= ",'-' as subcategory_name";
    }

    if($items != 0 && count($items) > 0) {
      $select_return= "psr.student_id, psr.proc_sales_master_id as master_id, sum(psr.return_quantity) as quantity, sum(psr.refund_amount) as amount,concat(psr.student_id,'_',pii.id) as stdKey";
      $select .= ", concat(psm.student_id,'_',pii.id) as stdKey";
    } else if($subcategories != 0 && count($subcategories) > 0) {
      $select_return= "psr.student_id, psr.proc_sales_master_id as master_id, sum(psr.return_quantity) as quantity, sum(psr.refund_amount) as amount,concat(psr.student_id,'_',pis.id) as stdKey";
      $select .= ", concat(psm.student_id,'_',pis.id) as stdKey";
    } else {
      $select_return= "psr.student_id, psr.proc_sales_master_id as master_id, sum(psr.return_quantity) as quantity, sum(psr.refund_amount) as amount,concat(psr.student_id,'_',pic.id) as stdKey";
      $select .= ", concat(psm.student_id,'_',pic.id) as stdKey";
    }

    $this->db_readonly->select($select)
      ->from('procurement_sales_master psm')
      ->join('procurement_sales_transactions pst', "pst.sales_master_id= psm.id")
      ->join('procurement_itemmaster_items pii','pst.proc_im_items_id=pii.id')
      ->join('procurement_itemmaster_subcategory pis','pst.proc_im_subcategory_id=pis.id')
      ->join('procurement_itemmaster_category pic','psm.proc_im_category_id=pic.id')
      ->where_in('psm.student_id', $student_arr)
      ->where('pst.sales_year_id', $sales_year_id);
      // ->where('psm.soft_delete', 0);
    if($items != 0 && count($items) > 0) {
      $this->db_readonly->where_in('pii.id', $items);
    }
    if($subcategories != 0 && count($subcategories) > 0) {
      $this->db_readonly->where_in('pis.id', $subcategories);
    }
    if($category != 0) {
      $this->db_readonly->where('pic.id', $category);
    }

    $grp_by_return= '';
    $group_by= '';
    if($items != 0 && count($items) > 0) {
      $grp_by_return= 'psr.proc_im_items_id';
      $group_by= 'pst.proc_im_items_id';
    } else if($subcategories != 0 && count($subcategories) > 0) {
      $grp_by_return= 'pis.id';
      $group_by= 'pis.id';
    } else if($category != 0) {
      $grp_by_return= 'pic.id';
      $group_by= 'pic.id';
    }
    $grp_by_return= ' psr.student_id';
    $group_by .= ',psm.student_id';

    $sales=   $this->db_readonly->group_by($group_by)->get()->result();


    // Sales Return
    $this->db_readonly->select($select_return)
      ->from('procurement_sales_return psr')
      // ->join('procurement_sales_master psm','psr.proc_sales_master_id=psm.id')
      // ->join('procurement_sales_transactions pst','psm.id=pst.sales_master_id')

      ->join('procurement_itemmaster_items pii','psr.proc_im_items_id=pii.id')
      ->join('procurement_itemmaster_subcategory pis','pii.proc_im_subcategory_id=pis.id')
      ->join('procurement_itemmaster_category pic','pis.proc_im_category_id=pic.id')
      ->where_in('psr.student_id', $student_arr)
      ->where('psr.sales_year_id', $sales_year_id);
      // ->where('pst.sales_year_id', $sales_year_id);
    if($items != 0 && count($items) > 0) {
      $this->db_readonly->where_in('psr.proc_im_items_id', $items);
    }
    if($subcategories != 0 && count($subcategories) > 0) {
      $this->db_readonly->where_in('pis.id', $subcategories);
    }
    if($category != 0) {
      $this->db_readonly->where('pic.id', $category);
    }
    $return= $this->db_readonly->group_by($grp_by_return)->get()->result();

    //  echo '<pre>'; print_r($this->db_readonly->last_query($return)); echo '</pre>'; die();

    $students= $this->db_readonly->select("sa.id as student_id, sy.promotion_status, sa.enrollment_number, concat(sa.first_name, ' ', ifnull(sa.last_name, '')) as name, sa.admission_no, cs.section_name, cs.class_name")
      ->from('student_admission sa')
      ->join('student_year sy', 'sy.student_admission_id= sa.id')
      ->join('class c', 'c.id= sy.class_id')
      ->join('class_section cs', 'cs.id= sy.class_section_id','left')
      ->where('sy.acad_year_id', $this->yearId)
      ->where_in('sa.id', $student_arr)
      ->get()->result();

      $returnMasterId = [];
      if(!empty($return)){
        foreach ($return as $key => $val) {
          $returnMasterId[$val->stdKey] =$val;
        }
      }


      $salesArry = [];
      foreach ($sales as $key => $val) {
        $val->final_quantity = $val->quantity;
        $val->final_amount = $val->amount;
        if(array_key_exists($val->stdKey, $returnMasterId)){
          $val->final_quantity =  $val->quantity - $returnMasterId[$val->stdKey]->quantity;
          $val->final_amount = $val->amount - $returnMasterId[$val->stdKey]->amount;
        }
        $salesArry[][$val->student_id] = $val;
      }     
      $studentTemp = [];
      foreach ($salesArry as $std_id => $items) {
        foreach ($students as $key => $std) {
          if(array_key_exists($std->student_id, $items)){
            $objClass = new stdClass();
            $objClass->name = $std->name;
            $objClass->promotion_status = $std->promotion_status;
            $objClass->enrollment_number = $std->enrollment_number;
            $objClass->admission_no = $std->admission_no;
            $objClass->class_name = $std->class_name;
            $objClass->section_name = $std->section_name;
            $objClass->final_quantity =  $items[$std->student_id]->final_quantity;
            $objClass->final_amount = round($items[$std->student_id]->final_amount,2);
            $objClass->category_name = $items[$std->student_id]->category_name;
            $objClass->subcategory_name = $items[$std->student_id]->subcategory_name;
            $objClass->item_name = $items[$std->student_id]->item_name;
            $studentTemp[] = $objClass;
          }
         
        }
      }
      return $studentTemp;
  }
  public function get_item_wise_sales_report() {
    $input= $this->input->post();
    // $item_id= $input['item_id'];
    $student_arr= $input['student_arr'];
    $category= $input['category'];
    $subcategories= $input['subcategories'];
    $items= $input['items'];
    $sales_year_id= $input['sales_year_id'];
    $report_format= $input['report_format'];

    if($report_format == 'student-wise') {
      return $this->get_student_wise_sales_calculate_amt_and_qty_category_wise($student_arr, $category, $subcategories, $items, $sales_year_id);
    } else {
      return $this->get_student_wise_sales_calculate_amt_and_qty($student_arr, $category, $subcategories, $items, $sales_year_id);
    }

          
  }

  public function test_with_queries() {
  // // Steps for Invoice Tables:
  //   // 1) Find all categories
  //   // 2) get all items categories
  //   // 3) check if category added in invoice master:  if added, check items are added in invoice items:if not added then add items otherwisee continue to next iteration
  //   // 4) if category not added add it in invoice master, store the id and check if items added initially
  //   // 5) if not, add it otherwise continue to next 

  //   $categories= $this->db->select('id as category_id')->order_by('id')->get('procurement_itemmaster_category')->result();
  //   $cat_items= new stdClass();
  //   foreach($categories as $key => $val) {
  //     $cat_id= $val->category_id;
  //     $items= $this->db->select("pii.id as item_id, pis.id as subcategory_id, pis.proc_im_category_id as category_id, pii.selling_price, pii.cost_prodcut, pii.total_quantity, pii.current_quantity, pii.initial_quantity, pii.blocked_quantity")
  //       ->from('procurement_itemmaster_items pii')
  //       ->join('procurement_itemmaster_subcategory pis', 'pis.id= pii.proc_im_subcategory_id')
  //       ->where('pis.proc_im_category_id', $cat_id)
  //       ->get()->result();

  //       $cat_items->$cat_id= $items;
  //   }
// // Invoice Transaction Start
// $this->db->trans_start();
//       $vendor_id= $this->db->select('id')->where('vendor_name', 'Initial Quantity')->get('procurement_vendor_master')->row();
//       if(!empty($vendor_id)) {
//         $vendor_id= $vendor_id->id;
//       } else {
//         $this->db->insert('procurement_vendor_master', ['vendor_name' => 'Initial Quantity', 'vendor_code' => 'Next Element', 'contact_first_name' => 'Next Element', 'email' => '<EMAIL>', 'contact_number' => '9876543210', 'customer_service_number' => '9876543210']);
//         $vendor_id= $this->db->insert_id();
//       }
//       $insert_invoice_master= array(
//         'vendor_id' => $vendor_id,
//         'sales_year_id' => 1
//       );
//       $this->db->insert('procurement_delivery_challan_master', $insert_invoice_master);
//       $invoice_master_id= $this->db->insert_id();

//     $invoice_items_insert= [];
//     foreach($cat_items as $category_id => $item_details) {
//       if(!empty($item_details)) {
//         foreach($item_details as $key => $val) {
//           $invoice_items_insert[]= array(
//             'invoice_master_id' => $invoice_master_id,
//             'proc_im_items_id' => $val->item_id,
//             'proc_im_subcategory_id' =>$val->subcategory_id,
//             'proc_im_category_id' =>$val->category_id,
//             'selling_price' => $val->selling_price,
//             'initial_quantity' => $val->initial_quantity,
//             'current_quantity' => $val->current_quantity,
//             'price' => $val->cost_prodcut,
//             // 'total_quantity' => $val->total_quantity,
//             'cgst' => 0.00,
//             'sgst' => 0.00,
//             'blocked_quantity' => $val->blocked_quantity
//           );
//         }
//       }
//     }
//     if(!empty($invoice_items_insert)) {
//       $this->db->insert_batch('procurement_delivery_challan_items', $invoice_items_insert);
//     }
// $this->db->trans_complete();

// Sales Master Start [Now not required]
//     $procurement_delivery_challan_master= $this->db->get('procurement_delivery_challan_master')->result();
//     $inv_master= new stdClass();
//     foreach($procurement_delivery_challan_master as $inv_key => $inv_val) {
//       $cat= $inv_val->proc_im_category_id;
//      $inv_master->$cat= $inv_val;
//     }

//     $update__procurement_sales_master= [];
//     $procurement_sales_master= $this->db->get('procurement_sales_master')->result();
//     foreach($procurement_sales_master as $sales_key => $sales_val) {
//       $cat= $sales_val->proc_im_category_id;
//       $inv_master_id= $inv_master->$cat->id;
//       $update__procurement_sales_master[]= array(
//         'id' => $sales_val->id,
//         'proc_invoice_master_id' => $inv_master_id,
//         'sales_year_id' => 1,
//         'proc_im_category_id' => $sales_val->proc_im_category_id
//       );
//     }

// $this->db->trans_start();
//     if(!empty($update__procurement_sales_master)) {
//       $this->db->update_batch('procurement_sales_master', $update__procurement_sales_master, 'id');
//     }
// $this->db->trans_complete();




// Back Filled Sales Transaction Table Satrt
    $invoce_items= $this->db->select("pii.proc_im_items_id, pii.id as inv_item_id, pii.invoice_master_id, pim.sales_year_id")
      ->from('procurement_delivery_challan_items pii')
      ->join('procurement_delivery_challan_master pim', 'pim.id= pii.invoice_master_id')
      // ->group_by('pii.proc_im_items_id')
      ->get()->result();

    $inv_master= new stdClass();
    foreach($invoce_items as $inv_key => $inv_val) {
      $proc_im_items_id= $inv_val->proc_im_items_id;
     $inv_master->$proc_im_items_id= $inv_val;
    }

    $update__procurement_sales_transaction= [];
    $procurement_sales_transactions= $this->db->get('procurement_sales_transactions')->result();
    foreach($procurement_sales_transactions as $sales_key => $sales_val) {
      $item_id= $sales_val->proc_im_items_id;
      $inv_item_id= $inv_master->$item_id->inv_item_id;
      $sales_year_id= $inv_master->$item_id->sales_year_id;

      $update__procurement_sales_transaction[]= array(
        'id' => $sales_val->id,
        // 'proc_invoice_items_id' => $inv_item_id,
        'sales_year_id' => $sales_year_id
      );
    }

$this->db->trans_start();
    if(!empty($update__procurement_sales_transaction)) {
      $this->db->update_batch('procurement_sales_transactions', $update__procurement_sales_transaction, 'id');
    }
$this->db->trans_complete();




// Back Filled Sales Return Table Satrt
    $update__procurement_sales_return= [];
    $procurement_sales_returns= $this->db->get('procurement_sales_return')->result();
    foreach($procurement_sales_returns as $sales_key => $sales_val) {
      $item_id= $sales_val->proc_im_items_id;
      $inv_item_id= $inv_master->$item_id->inv_item_id;
      $sales_year_id= $inv_master->$item_id->sales_year_id;

      $update__procurement_sales_return[]= array(
        'id' => $sales_val->id,
        // 'proc_invoice_items_id' => $inv_item_id,
        'sales_year_id' => $sales_year_id
      );
    }

$this->db->trans_start();
    if(!empty($update__procurement_sales_return)) {
      $this->db->update_batch('procurement_sales_return', $update__procurement_sales_return, 'id');
    }
$this->db->trans_complete();



// Back Filled procurement_item_allocations_staff Table Satrt
$update___procurement_item_allocations_staff= [];
$procurement_alloction_staff= $this->db->get('procurement_item_allocations_staff')->result();
if(!empty($procurement_alloction_staff))
foreach($procurement_alloction_staff as $sales_key => $sales_val) {
  $item_id= $sales_val->proc_im_items_id;
  $inv_item_id= $inv_master->$item_id->inv_item_id;
  $sales_year_id= $inv_master->$item_id->sales_year_id;

  $update___procurement_item_allocations_staff[]= array(
    'id' => $sales_val->id,
    // 'proc_invoice_items_id' => $inv_item_id,
    'sales_year_id' => $sales_year_id
  );
}

$this->db->trans_start();
if(!empty($update___procurement_item_allocations_staff)) {
  $this->db->update_batch('procurement_item_allocations_staff', $update___procurement_item_allocations_staff, 'id');
}
$this->db->trans_complete();



// Back Filled procurement_request_items Table Satrt
$update___procurement_request_items= [];
$procurement_request_items= $this->db->get('procurement_request_items')->result();
if(!empty($procurement_request_items))
foreach($procurement_request_items as $sales_key => $sales_val) {
  $item_id= $sales_val->proc_im_items_id;
  $inv_item_id= $inv_master->$item_id->inv_item_id;
  $sales_year_id= $inv_master->$item_id->sales_year_id;

  $update___procurement_request_items[]= array(
    'id' => $sales_val->id,
    // 'proc_invoice_items_id' => $inv_item_id,
    'sales_year_id' => $sales_year_id
  );
}

$this->db->trans_start();
if(!empty($update___procurement_request_items)) {
  $this->db->update_batch('procurement_request_items', $update___procurement_request_items, 'id');
}
$this->db->trans_complete();

    echo '<pre>'; print_r($this->db->trans_status()); die();
     
  }

  public function get_sales_year() {
    return $this->db_readonly->get('procurement_sales_year')->result();
  }

  public function is_prev_sales_year_closed() {
    $curr_salesYear= $this->db_readonly->where('is_active', 1)->get('procurement_sales_year')->row();
    if(empty($curr_salesYear)) {
      return '-1'; // No active sales year
    } else {
      if($curr_salesYear->id > 1) {
        $is_prev_closed= $this->db_readonly
            ->from('procurement_delivery_challan_master pim')
            ->join('procurement_delivery_challan_items pii', 'pim.id= pii.invoice_master_id')
            ->where('pii.is_closed', 1)
            ->where('pim.sales_year_id', $curr_salesYear->id - 1)
            ->get()->row();
        if(!empty($is_prev_closed)) {
          return '-2'; // Not closed all items
        } else {
          $is_added= $this->db_readonly
              ->from('procurement_delivery_challan_master pim')
              ->join('procurement_delivery_challan_items pii', 'pim.id= pii.invoice_master_id')
              // ->where('pii.is_closed', 1)
              ->where('pim.sales_year_id', $curr_salesYear->id - 1)
              ->get()->row();
          if(!empty($is_added)) {
            return '1';
          } else {
            return '-2';
          }
          
        }
      } else {
        return '1';
      }
    }
  }

  public function get_acad_years() {
    return $this->db_readonly->where('id >=', $this->yearId)->get('academic_year')->result();
  }

  public function onchange_acad_year_get_classes() {
    return $this->db_readonly->select("cs.id, c.class_name, cs.section_name")->join('class_section cs', 'c.id= cs.class_id')->where('c.acad_year_id', $_POST['choose_acad_year'])->get('class c')->result();
  }

  public function remove_item_from_transaction() {
    // echo '<pre>'; print_r($_POST); die();
    // $total= intval($_POST['total_amount']) - intval($_POST['amount']);
    $this->db->trans_start();
    $total1= $this->db->select('total_amount')->where('id', $_POST['master_id'])->get('procurement_sales_master')->row();

    if(!empty($total1)) {
      $total= $total1->total_amount;
      $nTot= $total - $_POST['amount'];
      $this->db->where('id', $_POST['master_id'])->update('procurement_sales_master', ['total_amount' => $nTot]);
    }

    $current= $this->db->select('pii.current_quantity, pii.id')
      ->from('procurement_sales_transactions pst')
      ->join('procurement_delivery_challan_items pii', 'pst.proc_invoice_items_id = pii.id')
      ->where('pst.id', $_POST['trans_id'])->get()->row();
    if(!empty($current)) {
      $updated_qty= intval($current->current_quantity) + intval($_POST['quantity']);
      $this->db->where('id', $current->id)->update('procurement_delivery_challan_items', ['current_quantity' => $updated_qty]);
    }

    $this->db->where('id', $_POST['trans_id'])->delete('procurement_sales_transactions');
    $this->db->trans_complete();
    return $this->db->trans_status();
  }

  public function get_all_categories() {
    return $this->db_readonly->get('procurement_itemmaster_category')->result();
  }

  public function get_predefined_templates() {
    $tmpl= $this->db_readonly->get('procurement_predefined_template')->result();
    
    if(!empty($tmpl)) {
      foreach($tmpl as $key => $val) {
        $item_ids= json_decode($val->items_json, true);
        
        if(!empty($item_ids)) { 
          $items= [];
          foreach($item_ids as $k => $v) {
            $item_obj= new stdClass();
            $item_obj->item_id= $v['id'];
            $item_obj->item_name= $v['name']. ' ('.$v['quantity'].')';
            $items[]= $item_obj;
          }
          unset($val->items_json);
          $val->items= $items;
         
        } else {
          $val->items= [];
        }

      }
    }
    return $tmpl;
  }

  public function onchange_category() {
    return $this->db_readonly->select("pii.id, pii.item_name, pis.subcategory_name")
      ->join('procurement_itemmaster_items pii', 'pii.proc_im_subcategory_id = pis.id')
      ->where('pis.proc_im_category_id', $_POST['category'])
      ->order_by('pis.subcategory_name, pii.item_name')
      ->get('procurement_itemmaster_subcategory pis')->result();
  }

  // public function onchange_subcategory() {
  //   return $this->db_readonly->where('proc_im_subcategory_id', $_POST['subcategory'])->get('procurement_itemmaster_items')->result();
  // }

  public function add_templates() {
    // echo '<pre>'; print_r($_POST); die();
    $input= $this->input->post();
    $ids= $input['items'];
    $names= $input['names'];
    $quantities= $input['quantities'];
    $json_arr= [];
    if(!empty($ids)) {
      foreach($ids as $key => $id) {
        $json_obj= new stdClass();
        $json_obj->id= $id;
        $json_obj->name= $names[$key];
        $json_obj->quantity= $quantities[$key];
        $json_arr[]= $json_obj;
      }
      
      return $this->db->insert('procurement_predefined_template', ['name' => $input['name'], 'items_json' => json_encode($json_arr)]);
    }
    return false;
  }

  public function remove_item_from_template() {
    $items= $this->db->where('id', $_POST['template_id'])->get('procurement_predefined_template')->row();
    if(!empty($items))  {
      $items_arr= json_decode($items->items_json);

      if(!empty($items_arr)) {
        foreach($items_arr as $key => $val) {
          if(isset($val->id) && $val->id == $_POST['item_id']) {
            if(is_array($items_arr)) {
              unset($items_arr[$key]);
            } else {
              unset($items_arr->$key);
            }
          }
        }
      }
      
      return $this->db->where('id', $_POST['template_id'])->update('procurement_predefined_template', ['items_json' => json_encode($items_arr)]);
    }
    return false;
  }

  public function edit_temp_name() {
    return $this->db->where('id', $_POST['template_id'])->update('procurement_predefined_template', ['name' => $_POST['rename_template']]);
  }

  public function get_pre_defined_templates() {
    return $this->db_readonly->get('procurement_predefined_template')->result();
  }

  public function get_defined_templates_item() {
    $id= $_POST['pre_defined_template'];
    $json= $this->db_readonly->where('id', $id)->get('procurement_predefined_template')->row();
    if(!empty($json)) {
      $zer0_curr_qty_items= [];
      $expected_qty_not_in_stock= [];
      $items_obbj= json_decode($json->items_json, true);
      $items= [];
      $item_quantities= [];
      $id_qty_obj= new stdClass();
      if(!empty($items_obbj)) {
        foreach($items_obbj as $ki => $kv) {
          $items[]= $kv['id'];
          $item_quantities[]= $kv['quantity'];
          $id= $kv['id'];
          $id_qty_obj->$id= $kv['quantity'];
        }
      }

      // echo '<pre>'; print_r($id_qty_obj); die();

      if(!empty($items))
      $x= $this->db_readonly->select("pii.id as iid, pii.item_name, pis.subcategory_name, pic.category_name")
          ->from('procurement_itemmaster_items pii')
          ->join('procurement_itemmaster_subcategory pis', 'pis.id= pii.proc_im_subcategory_id')
          ->join('procurement_itemmaster_category pic', 'pic.id= pis.proc_im_category_id')
          ->where_in('pii.id', $items)
          ->where('pic.status', 1) // Categories from template are active
          ->where('pii.status', 1)
          ->order_by('pii.item_name')
          ->group_by('pii.id')
          ->get()->result();
      if(isset($x) && !empty($x)) {
        $sales_year_id= $_POST['sales_year_id'];
        foreach($x as $key => $val) {
          $y= $this->db_readonly->select("pim.id as pInvMasterId, pii.id as pInvItemsId, pii.proc_im_subcategory_id, pii.proc_im_category_id, pii.selling_price, (ifnull(pii.price, 0) + ifnull(pii.cgst, 0) + ifnull(pii.sgst, 0)) as cost_product, pii.current_quantity")
              ->from('procurement_delivery_challan_master pim')
              ->join('procurement_delivery_challan_items pii', 'pim.id= pii.invoice_master_id')
              ->where('pii.is_closed', 1)
              ->where('pii.current_quantity > ', 0)
              ->where('pii.proc_im_items_id', $val->iid)
              ->where('pim.sales_year_id', $sales_year_id)
              // ->where('pii.selling_price > ', 0)
              ->order_by('pim.dc_type', 'desc')
              ->get()->row();
          if(!empty($y)) {
            $val->pInvMasterId= $y->pInvMasterId;
            $val->pInvItemsId= $y->pInvItemsId;
            $val->proc_im_subcategory_id= $y->proc_im_subcategory_id;
            $val->proc_im_category_id= $y->proc_im_category_id;
            $val->selling_price= $y->selling_price;
            $val->cost_product= $y->cost_product;
            $val->current_quantity= $y->current_quantity;

            $item_id1= $val->iid;
            $val->accepted_quantity= $id_qty_obj->$item_id1;
            if($y->current_quantity < $id_qty_obj->$item_id1) {
              $val->expected_quantity= $id_qty_obj->$item_id1;
              $expected_qty_not_in_stock[]= $val;
              $val->accepted_quantity= $y->current_quantity;
            }
          } else {
            $zer0_curr_qty_items[]= $val;
            unset($x[$key]);
          }
        }
        return ['items' => $x, 'item_have_curr_qty_zero' => $zer0_curr_qty_items, 'expected_qty_not_in_stock' => $expected_qty_not_in_stock];
      }

      return [];

    }
    return [];
  }

//   public function add_id_cards_for_2023_24() {
//     $all_student_ids= $this->db->select('sa.id')
//                 ->from('student_admission sa')
//                 ->join('student_year sy', "sy.student_admission_id=sa.id")
//                 ->where('sa.admission_status', 2)
//                 ->where('sy.acad_year_id', 23) // Acad Year Upadte if required
//                 ->where("sy.promotion_status!='JOINED'")
//                 ->where("sy.promotion_status!='4'")
//                 ->where("sy.promotion_status!='5'")
//                 ->get()->result();
    
//     $all_student_ids_arr= [];
//     foreach($all_student_ids as $key => $val) {
//       $all_student_ids_arr[]= $val->id;
//     }

//     $feev2_receipt_generation= $this->db->where('id', 11)->get('feev2_receipt_book')->row();  // id Upadte if required
//     $running_number= intval($feev2_receipt_generation->running_number);
//     $infix= $feev2_receipt_generation->infix;
//     $feev2_receipt_generation_id= $feev2_receipt_generation->id; // Need to update after completing sales

//     $item_detail= $this->db->select('pInvItems.current_quantity, pInvItems.id as pInvItems_id, pii.id as item_id, pis.id as proc_im_subcategory_id, pis.proc_im_category_id, pInvItems.selling_price, 1 as sales_year_id')   // sales_year_id Upadte if required
//         ->from('procurement_itemmaster_items pii')
//         ->join('procurement_delivery_challan_items pInvItems', 'pInvItems.proc_im_items_id = pii.id')
//         ->join('procurement_itemmaster_subcategory pis', 'pis.id = pii.proc_im_subcategory_id')
//         ->where('pii.id', 1490) // Item id Upadte if required
//         ->get()->row();


// $this->db->trans_start();
//     foreach($all_student_ids_arr as $key => $val) {
//       $running_number ++;

//       $master_insert= array(
//         'proc_im_category_id' => $item_detail->proc_im_category_id,
//         'receipt_no' => $infix. $running_number,
//         'student_id' => $val,
//         'payment_type' => 9,  // payment type Upadte if required (9 for cash)
//         'total_amount' => $item_detail->selling_price,
//         'receipt_date' => date('Y-m-d H:i:s'),
//         'created_by' => 1, // Avatar id Upadte if required
//         'created_on' => date('Y-m-d H:i:s'),
//         'recon_status' => 0,
//         'cheque_dd_date' =>  date('Y-m-d'),
//         'soft_delete' => 0,
//         'sales_type' =>'existing',
//         'pdf_status' => 0,
//         'acad_year_id' => 23 // Avatar id Upadte if required
//       );

//       $this->db->insert('procurement_sales_master', $master_insert);
//       $master_id= $this->db->insert_id();
      
//       $transaction_inert= array(
//         'sales_master_id' => $master_id,
//         'proc_im_subcategory_id' => $item_detail->proc_im_subcategory_id,
//         'proc_im_items_id' => $item_detail->item_id,
//         'proc_invoice_items_id' => $item_detail->pInvItems_id,
//         'quantity' => 1,
//         'amount' => $item_detail->selling_price,
//         'sales_year_id' => $item_detail->sales_year_id
//       );

//       $this->db->insert('procurement_sales_transactions', $transaction_inert);
//       usleep(100000);  // Delay of 100 milliseconds: usleep accepts in microseconds
//     }

//     // feev2_receipt_book update
//     $this->db->where('id', $feev2_receipt_generation_id)->update('feev2_receipt_book', ['running_number' => $running_number]); // 

//      // Quantity update
//      $this->db->where('id',  $item_detail->pInvItems_id)->update('procurement_delivery_challan_items', ['current_quantity' => intval($item_detail->current_quantity) - count($all_student_ids_arr)]); // 

//     $this->db->trans_complete();

//     if(!$this->db->trans_status()){
//       $this->db->trans_rollback();
//     } else {
//       $this->db->trans_commit();
//     }

//     return $this->db->trans_status();


//     // echo '<pre>'; print_r($all_student_ids_arr); die();
//   }

  public function single_window_details($student_id,$category_name){
    $exist_row = $this->db->select('id')->from('single_window_approval_tracking')->where('student_id',$student_id)->where('team_name',$category_name)->get()->row();
    if(!empty($exist_row->id)){
      return 0;
    }
    $data = array(
      'student_id' => $student_id,
      'team_name' => $category_name,
      'status' => 'Approved',
      'taken_on' => $this->Kolkata_datetime(),
      'taken_by' => $this->authorization->getAvatarStakeHolderId(),
      'academic_year_id'=> $this->acad_year->getAcadYearId(),
      'remarks'=> ''
    );
  
    return $this->db->insert('single_window_approval_tracking',$data);
  }
  public function get_circular_data(){
    return $this->db->select("*")->where('name', 'purchase items')->get('email_template')->row();
  }

  public function get_circular_data_new($name){
    return $this->db->select("*")->where('name', $name)->get('email_template')->row();
  }

  public function get_sales_details($sTransId) {
      if($sTransId == ''){
        return null;
      }
      $this->db->select('psm.id as master_id, psm.receipt_no as receipt_no, date_format(psm.receipt_date, "%d-%b-%y") as receipt_date, psm.total_amount as total_amount');
      $this->db->from('procurement_sales_master psm');
      // $this->db->where('psm.student_id', $std_id);
      $this->db->where('psm.id', $sTransId);
      // $this->db->order_by('psm.created_on', 'DESC');
      // $this->db->order_by('psm.receipt_date', 'DESC');
      // $this->db->limit(1);
      $sales_query = $this->db->get();
      
      if ($sales_query->num_rows() > 0) {
          $sales_details = $sales_query->row();

          $this->db->select('concat(pis.subcategory_name, ">>", pii.item_name) as item_name, pst.amount as amount, pst.quantity as quantity');
          $this->db->from('procurement_sales_transactions pst');
          $this->db->join('procurement_sales_year psy', 'psy.id = pst.sales_year_id');
          $this->db->join('procurement_itemmaster_items pii', 'pii.id = pst.proc_im_items_id');
          $this->db->join('procurement_itemmaster_subcategory pis', 'pis.id = pst.proc_im_subcategory_id');
          // $this->db->where('pst.sales_master_id', $sales_details->master_id);
          $this->db->where('pst.sales_master_id', $sTransId);
          $this->db->where('psy.is_active', 1);
          $items_query = $this->db->get();
          $items_details = $items_query->result();

          $result = [
              'sales_details' => $sales_details,
              'items_details' => $items_details
          ];
          return $result;
      } else {
          return null;
      }
  }

  public function get_return_details($return_ids) {
    $x= $this->db->select("psr.item_unit_price, psr.return_quantity, psr.refund_amount, psr.return_reason, date_format(psr.return_on, '%d-%m-%Y') as return_on, pii.item_name, pii.unit_type, psy.year_name, date_format(psm.created_on, '%d-%m-%Y') as created_on")
        ->from('procurement_sales_return psr')
        ->join('procurement_sales_master psm', 'psm.id= psr.proc_sales_master_id')
        ->join('procurement_itemmaster_items pii', 'pii.id= psr.proc_im_items_id')
        ->join('procurement_sales_year psy', 'psy.id= psr.sales_year_id')
        ->where_in('psr.id', $return_ids)
        ->get()->result();

    return $x;
  }

  function delete_temp_name() {
    $id= $_POST['template_id'];
    return $this->db->where('id', $id)->delete('procurement_predefined_template');
  }

  public function get_student_wise_missing_report($input){
    $prefix_student_name = $this->settings->getSetting('prefix_student_name');

      if ($prefix_student_name == "roll_number") {
        $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name";
      } else if ($prefix_student_name == "enrollment_number") {
        $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name";
      } else if ($prefix_student_name == "admission_no") {
        $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name";
      } else if ($prefix_student_name == "registration_no") {
        $std_name = "CONCAT(ifnull(sa.registration_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name";
      } else if ($prefix_student_name == "alpha_rollnum") {
          $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name";
      }else {
        $std_name = "CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS name";
      }

      $prefix_order_by = $this->settings->getSetting('prefix_order_by');
      $order_by = 'sa.first_name';
      if ($prefix_order_by == "roll_number") {
        $order_by = 'sy.roll_no';
      }else if($prefix_order_by == "enrollment_number"){
        $order_by = 'sa.enrollment_number';
      }else if($prefix_order_by == "admission_no"){
        $order_by = 'sa.admission_no';
      }else if($prefix_order_by == "alpha_rollnum"){
        $order_by = 'sy.alpha_rollnum';
      }
      $this->db_readonly->select("sy.promotion_status, sa.admission_no,$std_name,
      concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as class_section,CASE 
      WHEN MAX(CASE WHEN ppso.status = 'submitted' THEN 1 ELSE 0 END) = 1 THEN 'Placed'
      ELSE 'Not-Placed'
  END AS `order_status`");
      $this->db_readonly->from('student_admission sa');
      $this->db_readonly->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id= $this->yearId");
      $this->db_readonly->join('class_section cs','sy.class_section_id=cs.id');
      $this->db_readonly->join('class c','sy.class_id=c.id');
      $this->db_readonly->join('procurement_parent_sales_orders ppso','ppso.student_admission_id=sa.id','left');
      $this->db_readonly->where('cs.id',$input['class_section_id']);
      $this->db_readonly->where('sa.admission_status',2);
      // $this->db_readonly->where('sy.promotion_status!=', '4');
      // $this->db_readonly->where('sy.promotion_status!=', '5');
      if($input['order_status']=='placed'){
       $this->db_readonly->where('ppso.status','submitted');
      } elseif($input['order_status']=='not_placed'){
       $this->db_readonly->group_start();
       $this->db_readonly->where('ppso.status', 'cancled');
       $this->db_readonly->or_where('ppso.status IS NULL');
       $this->db_readonly->group_end();
      }
      $this->db_readonly->group_by('sa.id');
      $this->db_readonly->order_by('cs.class_name, cs.section_name,'.$order_by);
      return $this->db_readonly->get()->result();
  }

  public function get_student_wise_order_history($student_id){
    $this->db_readonly->select("*,date_format(created_on,'%d-%b-%Y %h:%i %p') as created_on");
    $this->db_readonly->from('procurement_parent_sales_orders');
    $this->db_readonly->where('student_admission_id',$student_id);
    $student_order_details=$this->db_readonly->get()->result();

    if(!empty($student_order_details)){
      $primaryIds = [];
      foreach ($student_order_details as $key => $value) {
          array_push($primaryIds, $value->id);
      }

      $this->db_readonly->select("CONCAT(IFNULL(pis.subcategory_name, ''), ' - ', IFNULL(pii.item_name, '')) AS item_name,procurement_parent_sales_order_id,ppsod.quantity");
      $this->db_readonly->from('procurement_parent_sales_order_details ppsod');
      $this->db_readonly->join('procurement_itemmaster_items pii','pii.id=ppsod.proccurment_item_id');
      $this->db_readonly->join('procurement_itemmaster_subcategory pis','pis.id=pii.proc_im_subcategory_id');
      $this->db_readonly->where_in('ppsod.procurement_parent_sales_order_id',$primaryIds);
      $all_order_details=$this->db_readonly->get()->result();
      $items_ids = [];
      foreach ($all_order_details as $key => $value) {
          $items_ids[$value->procurement_parent_sales_order_id][] = $value;
      }
      foreach ($student_order_details as $key => $val) {
        if(array_key_exists($val->id,$items_ids)){
             $val->items = $items_ids[$val->id];
        }else{
         $val->items = [];
        }
     }
    }else{
        foreach ($student_order_details as $key => $value) {
          $student_order_details->items="";
      }
    }
    return $student_order_details;
  }

  public function get_individual_item_list($ppso_id){
    $this->db_readonly->select("CONCAT(IFNULL(pis.subcategory_name, ''), ' - ', IFNULL(pii.item_name, '')) AS item_name,ppsod.price,ppsod.quantity,custom_name");
    $this->db_readonly->from('procurement_itemmaster_subcategory pis');
    $this->db_readonly->join('procurement_itemmaster_items pii','pis.id=pii.proc_im_subcategory_id');
    $this->db_readonly->join('procurement_parent_sales_order_details ppsod','ppsod.proccurment_item_id=pii.id');
    $this->db_readonly->join('procurement_parent_sales_orders ppso','ppso.id=ppsod.procurement_parent_sales_order_id');
    $this->db_readonly->where('ppso.id',$ppso_id);
    $this->db_readonly->order_by('ppsod.id','asc');
    return $this->db_readonly->get()->result();
  }

  public function get_category_items(){
    $this->db_readonly->select('pic.id,pic.category_name');
    $this->db_readonly->from('procurement_parent_sales_order_details ppsod');
    $this->db_readonly->join('procurement_itemmaster_items pii','pii.id=ppsod.proccurment_item_id');
    $this->db_readonly->join('procurement_itemmaster_subcategory pis','pis.id=pii.proc_im_subcategory_id');
    $this->db_readonly->join('procurement_itemmaster_category pic','pic.id=pis.proc_im_category_id');
    $this->db_readonly->group_by('pic.id');
    return $this->db_readonly->get()->result();
  }

  public function get_parent_ordered_items($input){
    $this->db_readonly->select("pii.id,pic.category_name,CONCAT(IFNULL(pis.subcategory_name, ''), ' - ', IFNULL(pii.item_name, '')) AS item_name,SUM(quantity) AS total_quantity");  
    $this->db_readonly->from('procurement_itemmaster_subcategory pis');  
    $this->db_readonly->join('procurement_itemmaster_items pii','pii.proc_im_subcategory_id=pis.id');  
    $this->db_readonly->join('procurement_parent_sales_order_details ppsod','ppsod.proccurment_item_id=pii.id');  
    $this->db_readonly->join('procurement_parent_sales_orders ppso','ppso.id=ppsod.procurement_parent_sales_order_id');  
    $this->db_readonly->join('procurement_itemmaster_category pic','pic.id=pis.proc_im_category_id');  
    $this->db_readonly->where('ppso.status','submitted');
    if(! empty($input['acad_year_id'])){
      $this->db_readonly->where('ppso.acad_year_id',$input['acad_year_id']);
    }
    if($input['category_id']){
    $this->db_readonly->where('pis.proc_im_category_id', $input['category_id']);
    }
    if($input['subcategories']){
      $this->db_readonly->where_in('pis.id', $input['subcategories']);
      }
    $this->db_readonly->group_by('pii.id');
    return $this->db_readonly->get()->result();
  }

  public function get_parent_orders_report_order_wise($input){
    $fromDate = date('Y-m-d',strtotime($input['from_date']));
    $toDate =date('Y-m-d',strtotime($input['to_date']));
    
    $prefix_student_name = $this->settings->getSetting('prefix_student_name');

    if ($prefix_student_name == "roll_number") {
      $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name";
    } else if ($prefix_student_name == "enrollment_number") {
      $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name";
    } else if ($prefix_student_name == "admission_no") {
      $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name";
    } else if ($prefix_student_name == "registration_no") {
      $std_name = "CONCAT(ifnull(sa.registration_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name";
    } else if ($prefix_student_name == "alpha_rollnum") {
        $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name";
    }else {
      $std_name = "CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS name";
    }

    $prefix_order_by = $this->settings->getSetting('prefix_order_by');
    $order_by = 'sa.first_name';
    if ($prefix_order_by == "roll_number") {
      $order_by = 'sy.roll_no';
    }else if($prefix_order_by == "enrollment_number"){
      $order_by = 'sa.enrollment_number';
    }else if($prefix_order_by == "admission_no"){
      $order_by = 'sa.admission_no';
    }else if($prefix_order_by == "alpha_rollnum"){
      $order_by = 'sy.alpha_rollnum';
    }

    $this->db_readonly->select("sy.promotion_status, ppso.id,ppso.order_number,ppso.status,$std_name,
    concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as class_section,sa.admission_no,date_format(ppso.created_on,'%d-%b-%Y %h:%i %p') as created_on,ppso.cancel_reason");
    $this->db_readonly->from('procurement_parent_sales_orders ppso');
    $this->db_readonly->join('student_admission sa','sa.id=ppso.student_admission_id');
    $this->db_readonly->join('student_year sy',"sy.student_admission_id=sa.id and sy.acad_year_id=$this->yearId");
    $this->db_readonly->join('class_section cs',"sy.class_section_id=cs.id");
    $this->db_readonly->join('class c',"sy.class_id=c.id");
    if ($fromDate && $toDate) {
      $this->db_readonly->where('date_format(ppso.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate. '"');
    }
    if($input['item_status']=='placed'){
      $this->db_readonly->where('ppso.status','submitted');
    }elseif ($input['item_status']=='cancelled') {
      $this->db_readonly->where('ppso.status','canceled');
    }elseif ($input['item_status']=='delivered') {
      $this->db_readonly->where('ppso.status','delivered');
    }
    $this->db_readonly->order_by('cs.class_name, cs.section_name,'.$order_by);

    return $this->db_readonly->get()->result();
  }

  public function get_parent_orders_report_item_wise($input){
    $fromDate = date('Y-m-d',strtotime($input['from_date']));
    $toDate =date('Y-m-d',strtotime($input['to_date']));

    $prefix_student_name = $this->settings->getSetting('prefix_student_name');

    if ($prefix_student_name == "roll_number") {
      $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name";
    } else if ($prefix_student_name == "enrollment_number") {
      $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name";
    } else if ($prefix_student_name == "admission_no") {
      $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name";
    } else if ($prefix_student_name == "registration_no") {
      $std_name = "CONCAT(ifnull(sa.registration_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name";
    } else if ($prefix_student_name == "alpha_rollnum") {
        $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name";
    }else {
      $std_name = "CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS name";
    }

    $prefix_order_by = $this->settings->getSetting('prefix_order_by');
    $order_by = 'sa.first_name';
    if ($prefix_order_by == "roll_number") {
      $order_by = 'sy.roll_no';
    }else if($prefix_order_by == "enrollment_number"){
      $order_by = 'sa.enrollment_number';
    }else if($prefix_order_by == "admission_no"){
      $order_by = 'sa.admission_no';
    }else if($prefix_order_by == "alpha_rollnum"){
      $order_by = 'sy.alpha_rollnum';
    }

    $this->db_readonly->select("sy.promotion_status, ppsod.id,ppso.order_number,$std_name,
    CONCAT(IFNULL(pis.subcategory_name, ''), ' - ', IFNULL(pii.item_name, '')) AS item_name,
    concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as class_section,ppsod.quantity");
    $this->db_readonly->from('procurement_parent_sales_order_details ppsod');
    $this->db_readonly->join('procurement_parent_sales_orders ppso','ppso.id=ppsod.procurement_parent_sales_order_id');
    $this->db_readonly->join('student_admission sa','sa.id=ppso.student_admission_id');
    $this->db_readonly->join('student_year sy',"sy.student_admission_id=sa.id and sy.acad_year_id=$this->yearId");
    $this->db_readonly->join('class_section cs','sy.class_section_id=cs.id');
    $this->db_readonly->join('class c','sy.class_id=c.id');
    $this->db_readonly->join('procurement_itemmaster_items pii','pii.id=ppsod.proccurment_item_id');
    $this->db_readonly->join('procurement_itemmaster_subcategory pis','pis.id=pii.proc_im_subcategory_id');

    if ($fromDate && $toDate) {
      $this->db_readonly->where('date_format(ppso.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate. '"');
    }
    if($input['item_status']=='placed'){
      $this->db_readonly->where('ppso.status','submitted');
    }elseif ($input['item_status']=='cancelled') {
      $this->db_readonly->where('ppso.status','canceled');
    }elseif ($input['item_status']=='delivered') {
      $this->db_readonly->where('ppso.status','delivered');
    }
    $this->db_readonly->order_by('cs.class_name, cs.section_name,'.$order_by);

    return $this->db_readonly->get()->result();
  }

  public function get_acad_year_list(){
    $this->db_readonly->select('id,acad_year');
    $this->db_readonly->from('academic_year');
    $this->db_readonly->where('year_to_show',1);
    return $this->db_readonly->get()->result();
  }

  function getPreReservedItemsCategoriesWise() {
    $student_id= $this->input->post('student_id');
    $sales_year_id= $this->input->post('sales_year_id');

    $data= $this->db_readonly->select("ppsod.procurement_parent_sales_order_id, ppsod.id as procurement_parent_sales_order_details_id, ppsod.proccurment_item_id, ppsod.quantity as reserved_quantity, ppsod.delivered_quantity, pii.item_name, pis.subcategory_name, pii.proc_im_subcategory_id, pis.proc_im_category_id, pic.category_name, (ifnull(ppsod.quantity, 0) - ifnull(ppsod.delivered_quantity, 0)) as net_purchasable_qty")
        ->from('procurement_parent_sales_orders ppso')
        ->join('procurement_parent_sales_order_details ppsod', 'ppso.id = ppsod.procurement_parent_sales_order_id')
        ->join('procurement_itemmaster_items pii', 'pii.id = ppsod.proccurment_item_id')
        ->join('procurement_itemmaster_subcategory pis', 'pis.id = pii.proc_im_subcategory_id')
        ->join('procurement_itemmaster_category pic', 'pic.id = pis.proc_im_category_id')
        ->where('ppso.student_admission_id', $student_id)
        ->where('ppso.acad_year_id', $this->yearId)
        ->where_in('ppso.status', ['submitted'])
        ->where("(ifnull(ppsod.quantity, 0) - ifnull(ppsod.delivered_quantity, 0)) > 0")
        ->order_by('pic.id')
        ->get()->result();

    $grouped_array = [];
    if(!empty($data)) {
      foreach ($data as $item) {
          $category_name = $item->category_name;
          if (!isset($grouped_array[$category_name])) {
              $grouped_array[$category_name] = [];
          }
          $grouped_array[$category_name][] = $item;
      }
    }

    // echo '<pre>'; print_r($grouped_array); die();
    return $grouped_array;
  }

  function get_reserved_items_with_quantity() {
    $input= $this->input->post();
    $proccurment_item_id= isset($input['proccurment_item_id']) ? $input['proccurment_item_id'] : [];
    $net_purchasable_qty= isset($input['net_purchasable_qty']) ? $input['net_purchasable_qty'] : [];
    $procurement_parent_sales_order_details_id= isset($input['procurement_parent_sales_order_details_id']) ? $input['procurement_parent_sales_order_details_id'] : [];
    

    if(empty($proccurment_item_id)) {
      return [];
    }

    $zer0_curr_qty_items= [];
    $expected_qty_not_in_stock= [];
    $items= [];
    $item_quantities= [];
    $id_qty_obj= new stdClass();

    foreach($proccurment_item_id as $key => $val) {
      $items[]= $val;
      $item_quantities[]= $net_purchasable_qty[$key];
      $id= $val;
      $id_qty_obj->$id= $net_purchasable_qty[$key];
    }

    $x= new stdClass();
    if(!empty($items)) {
      $x= $this->db_readonly->select("pii.id as iid, pii.item_name, pis.subcategory_name, pic.category_name")
          ->from('procurement_itemmaster_items pii')
          ->join('procurement_itemmaster_subcategory pis', 'pis.id= pii.proc_im_subcategory_id')
          ->join('procurement_itemmaster_category pic', 'pic.id= pis.proc_im_category_id')
          ->where_in('pii.id', $items)
          ->where('pii.status', 1)
          ->order_by('pii.item_name')
          ->group_by('pii.id')
          ->get()->result();
      if(!empty($x)) {
        foreach($x as $xKey => $xVal) {
          $index = array_search($xVal->iid, $proccurment_item_id);
          if ($index !== false) {
            $xVal->procurement_parent_sales_order_details_id= $procurement_parent_sales_order_details_id[$index];
          }
        }
      }
    }
      if(isset($x) && !empty($x)) {
        $sales_year_id= $this->input->post('sales_year_id');
        foreach($x as $key => $val) {
          $y= $this->db_readonly->select("pim.id as pInvMasterId, pii.id as pInvItemsId, pii.proc_im_subcategory_id, pii.proc_im_category_id, pii.selling_price, (ifnull(pii.price, 0) + ifnull(pii.cgst, 0) + ifnull(pii.sgst, 0)) as cost_product, pii.current_quantity")
              ->from('procurement_delivery_challan_master pim')
              ->join('procurement_delivery_challan_items pii', 'pim.id= pii.invoice_master_id')
              ->where('pii.is_closed', 1)
              ->where('pii.current_quantity > ', 0)
              ->where('pii.proc_im_items_id', $val->iid)
              ->where('pim.sales_year_id', $sales_year_id)
              // ->where('pii.selling_price > ', 0)
              ->order_by('pim.dc_type', 'desc')
              ->get()->row();
          if(!empty($y)) {
            $val->pInvMasterId= $y->pInvMasterId;
            $val->pInvItemsId= $y->pInvItemsId;
            $val->proc_im_subcategory_id= $y->proc_im_subcategory_id;
            $val->proc_im_category_id= $y->proc_im_category_id;
            $val->selling_price= $y->selling_price;
            $val->cost_product= $y->cost_product;
            $val->current_quantity= $y->current_quantity;

            $item_id1= $val->iid;
            $val->accepted_quantity= $id_qty_obj->$item_id1;
            if($y->current_quantity < $id_qty_obj->$item_id1) {
              $val->expected_quantity= $id_qty_obj->$item_id1;
              $expected_qty_not_in_stock[]= $val;
              $val->accepted_quantity= $y->current_quantity;
            }
          } else {
            $zer0_curr_qty_items[]= $val;
            unset($x[$key]);
          }
        }
        // echo '<pre>'; print_r($x); die();
        return ['items' => $x, 'item_have_curr_qty_zero' => $zer0_curr_qty_items, 'expected_qty_not_in_stock' => $expected_qty_not_in_stock];
      }

      return [];
  }

  public function get_sales_student_details_by__($input_id, $input_type){

    $this->db->select("sy.promotion_status, if(sy.alpha_rollnum is not null AND sy.alpha_rollnum != '', sy.alpha_rollnum, '-') as alpha_rollnum, if(sy.roll_no is not null AND sy.roll_no != '', sy.roll_no, '-') as roll_no, sd.id, sy.id as stdYearId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as std_name, sd.admission_no, p.mobile_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS parent_name, c.class_name, ifnull(cs.section_name, '') as section_name, c.id as class")
    ->from('student_year sy')
    ->join('student_admission sd','sy.student_admission_id=sd.id')
    ->join("class_section cs", "sy.class_section_id=cs.id", 'left')
    ->join("class c", "sy.class_id=c.id")
    ->where('sy.acad_year_id',$this->yearId)
    ->where_in('sd.admission_status',[1,2])
    ->where('sy.promotion_status!=', 'JOINED')
    // ->where('sy.promotion_status!=', '5')
    ->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'")
    ->join("parent p", "p.id=sr.relation_id");

    if($input_type == 'enrollment_no'){
      $this->db->where("sd.enrollment_number is not null and sd.enrollment_number != ''");
      $this->db->where('sd.enrollment_number', $input_id);
    }
    else if ($input_type == 'roll_no') {
      $this->db->where("sy.roll_no is not null and sy.roll_no != ''");
      $this->db->where('sy.roll_no', $input_id);
    }
    else if ($input_type == 'alpha_roll_no') {
      $this->db->where("sy.alpha_rollnum is not null and sy.alpha_rollnum != ''");
      $this->db->where('sy.alpha_rollnum', $input_id);
    }

   $x= $this->db->order_by('sd.first_name')->get()->row();

   if(empty($x)) {
    return [];
   }
   $std_id= $x->id;

  $prefix= $this->settings->getSetting('prefix_student_name');
  if (strpos($prefix, 'roll_number') !== false && $x->roll_no != '-') {
    $x->std_name= "$x->roll_no - $x->std_name";
  }
  if (strpos($prefix, 'alpha_rollnum') !== false && $x->alpha_rollnum != '-') {
    $x->std_name= "$x->alpha_rollnum - $x->std_name";
  }

  //  echo '<pre>'; print_r($x); die();

   if($this->settings->getSetting('enable_indus_single_window_approval_process')){
    $single_window = $this->db->select("team_name,date_format(sat.taken_on,'%d-%M-%Y') as taken_on,concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as taken_by,ifnull(sat.remarks,'-') as remarks,sat.status")
    ->from('single_window_approval_tracking sat')
    ->join('staff_master sm','sat.taken_by=sm.id','left')
    ->where('student_id',$std_id)
    ->where('team_name','Accounts')
    ->where('academic_year_id',$this->acad_year->getAcadYearId())
    ->get()->row();

    $x->team_name = '';
    $x->taken_on = '-';
    $x->taken_by = '-';
    $x->remarks = '-';
    $x->status = 'Not started';
    if(!empty($single_window)){
      $x->team_name = $single_window->team_name;
      $x->taken_on = $single_window->taken_on;
      $x->taken_by = $single_window->taken_by;
      $x->remarks = $single_window->remarks;
      $x->status = $single_window->status;
    }
   }
   return $x;
  //  echo '<pre>'; print_r($this->db->last_query($x)); die();
}

public function insert_sales_transaction_unique_category($std_id, $input, $catId, $products){
  // echo '<pre>PRE bro: '; print_r($std_id); 
  // echo '<pre>PRE bro: '; print_r($catId); 
  // echo '<pre>PRE bro: '; print_r($products); 
  // echo '<pre>PRE bro: '; print_r($input); echo '</pre>'; return 1;

      // Array to hold the merged results
      $merged = [];
      foreach ($products as $item) {
          $variant = $item['variants'];
          // If the variant already exists, sum the quantity and amount
          if (isset($merged[$variant])) {
              $merged[$variant]['quantity'] += $item['quantity'];
              $merged[$variant]['amount'] += $item['amount'];
          } else {
              // Otherwise, add the new variant to the merged array
              $merged[$variant] = $item;
          }
      }
      // Reset array keys
      $products = array_values($merged);

      // $input = $this->input->post();

      $items_from_reserved_stock= isset($input['items_from_reserved_stock']) ? $input['items_from_reserved_stock'] : 0;


      $sales_year_id= isset($input['sales_year_id_single']) && $input['sales_year_id_single'] ? $input['sales_year_id_single'] : 0;
      
      $ePayment_type = explode('_', $input['payment_type']);
      $payment_type = $ePayment_type[0];
      $reconciliation_status = $ePayment_type[1];

      $timezone = new DateTimeZone("Asia/Kolkata" );
      $date = new DateTime($input['receipt_date']);
      $time = new DateTime();
      $time->setTimezone($timezone);
      $merge = new DateTime($date->format('Y-m-d') .' ' .$time->format('H:i:s'));
      $receipt_date =  $merge->format('Y-m-d H:i:s'); 
      if (!empty($input['cheque_dd_nb_cc_dd_number'])) {
        $cheque_dd_nb_cc_dd_number = $input['cheque_dd_nb_cc_dd_number'];
      }else if(!empty($input['dd_number'])){
        $cheque_dd_nb_cc_dd_number = $input['dd_number'];
      }else if(!empty($input['cc_number'])){
        $cheque_dd_nb_cc_dd_number = $input['cc_number'];
      }else if(!empty($input['nb_number'])){
        $cheque_dd_nb_cc_dd_number = $input['nb_number'];
      }else{
        $cheque_dd_nb_cc_dd_number = null;
      }

    $tAmount = 0;
    foreach ($products as $key => $val) {
      $tAmount += isset($val['amount']) ? $val['amount'] : 0;
    }

    // echo '<pre>'; print_r($input); die();

      $this->db->trans_start();

      $master_data = array(
          'student_id'=> ($std_id == '')?0:$std_id,
          'proc_im_category_id'=> $catId,
          'payment_type'=> $payment_type,
          'total_amount'=> $tAmount,
          'receipt_date'=> $receipt_date,
          'created_by'=> $this->authorization->getAvatarId(),
          'bank_name'=> (isset($input['bank_name']) == '')? null : $input['bank_name'],
          'bank_branch'=> (isset($input['branch_name']) == '')? null : $input['branch_name'],
          'cheque_dd_number'=> $cheque_dd_nb_cc_dd_number,
          'recon_status'=> $reconciliation_status,
          'cheque_dd_date'=> (!isset($input['bank_date'])) ? null : date('Y-m-d',strtotime($input['bank_date'])),
          'remarks'=> (!isset($input['remarks'])) ? null : $input['remarks'],
          'card_charge_amount'=> (!isset($input['card_charge'])) ? null : $input['card_charge'],
          'sales_type' => $input['sale_type'],
          'student_name' => ($input['new_std_name'] == '')?NULL:$input['new_std_name'],
          'parent_name' => ($input['parent_name'] == '')?NULL:$input['parent_name'],
          'contact_number' => ($input['contact_number'] == '')?NULL:$input['contact_number'],
          'class_name' => ($input['class_name'] == '')?NULL:$input['class_name'],
          'acad_year_id' => $this->yearId,

          'discount_amount' => $input['discounted_amount'] >= 0 ? $input['discounted_amount'] : 0,
          'discount_percentage' => $input['discounted_amount'] >= 0 && $input['discount_mode_type'] == 'Percentage' ? $input['discount_amount_or_percentage_value'] : 0,
          'discount_remarks' => $input['discounted_amount'] >= 0 ? $input['discounted_remarks_hidden'] : NULL
        );
      $this->db->insert('procurement_sales_master',$master_data);
      $stransId = $this->db->insert_id();

// echo '<pre>'; print_r($stransId); echo '</pre>';

      $dataArry = [];
      $status= true;
$procurement_parent_sales_order_details_ids= isset($input['procurement_parent_sales_order_details_ids']) ? $input['procurement_parent_sales_order_details_ids'] : [];
$quantity_sold_arr= isset($input['quantity']) ? $input['quantity'] : [];
$indexValue= 0;




// echo '<pre>'; print_r($input); die();



      foreach ($products as $k =>  $productId) {
        $sales_source_type = 'DIRECT_SALE';
        $sales_order_details_id= NULL;
        $reserved_quantity_fulfilled= NULL;
        // Filling reserved item details in sales trasaction table
        if($items_from_reserved_stock == 1) {
          $sales_source_type = 'RESERVED_ORDER';
            $sales_order_details_id= $procurement_parent_sales_order_details_ids[$indexValue];
          if(!empty($procurement_parent_sales_order_details_ids)) {
              $parent_order_details= $this->db->select("quantity, ifnull(delivered_quantity, 0) as delivered_quantity")->where('id', $procurement_parent_sales_order_details_ids[$indexValue])->get('procurement_parent_sales_order_details')->row();
              if(!empty($parent_order_details)) {
                $reservedQty= $parent_order_details->quantity;
                $deliveredQty= $parent_order_details->delivered_quantity;
                $soldQty= $quantity_sold_arr[$indexValue];

                $reserved_quantity_fulfilled= ($reservedQty - $deliveredQty) < $soldQty ? 1*($reservedQty - $deliveredQty) : 1*($soldQty);
              }
          }

        }

        if($reserved_quantity_fulfilled == NULL) {
          $sales_source_type = 'DIRECT_SALE';
        }

        $dataArry =  array(
          'sales_master_id'=> $stransId,
          'proc_im_subcategory_id'=> $productId['prodcuts'],
          'proc_im_items_id'=> $productId['variants'],
          'quantity'=> $productId['quantity'], 
          'amount'=> $productId['amount'],
          'sales_year_id'=> $sales_year_id,
          'proc_invoice_items_id'=> $input['inv_item_id'][$k],
          'sales_source_type' => $sales_source_type,
          'reserved_quantity_fulfilled' => $reserved_quantity_fulfilled,
          'sales_order_details_id' => $sales_order_details_id
        ); 

        $qty= $this->db->select("current_quantity")->where('id', $input['inv_item_id'][$k])->get('procurement_delivery_challan_items')->row()->current_quantity;
        if($qty >= $productId['quantity']) {
          $this->db->insert('procurement_sales_transactions', $dataArry);
          $proc_sales_trans_id= $this->db->insert_id();

          // Parent order details update
          
          if($items_from_reserved_stock == 1) {
            
            // $procurement_parent_sales_order_details_ids= isset($input['procurement_parent_sales_order_details_ids']) ? $input['procurement_parent_sales_order_details_ids'] : [];
            if(!empty($procurement_parent_sales_order_details_ids)) {
                $parent_order_details= $this->db->select("quantity, ifnull(delivered_quantity, 0) as delivered_quantity")->where('id', $procurement_parent_sales_order_details_ids[$indexValue])->get('procurement_parent_sales_order_details')->row();
                if(!empty($parent_order_details)) {
                  $reservedQty= $parent_order_details->quantity;
                  $deliveredQty= $parent_order_details->delivered_quantity;
                  $soldQty= $quantity_sold_arr[$indexValue];
                  if( ($reservedQty - $deliveredQty) == $soldQty ) {
                    $updateParentOrderDetails= array(
                      'delivery_status' => 'Delivered',
                      'delivered_quantity' => 1*($deliveredQty + $soldQty)
                    );
                  } else { // if 1)  ($reservedQty - $deliveredQty) < $soldQty - Matlab reserved qty se jyada sold kar raha hai; 2)  ($reservedQty - $deliveredQty) > $soldQty - Matlab thi partilly delivered
                    $updateParentOrderDetails= array(
                      'delivery_status' => ($reservedQty - $deliveredQty) < $soldQty ? 'Delivered' : 'Partially Delivered',
                      'delivered_quantity' => ($reservedQty - $deliveredQty) < $soldQty ? $reservedQty : 1*($deliveredQty + $soldQty)
                    );
                  }
                  $this->db->where('id', $procurement_parent_sales_order_details_ids[$indexValue])->update('procurement_parent_sales_order_details', $updateParentOrderDetails);

                  $insertParentOrderDelivery= array(
                    'proc_parent_sales_order_details_id' => $procurement_parent_sales_order_details_ids[$indexValue],
                    'proc_sales_trans_id' => $proc_sales_trans_id,
                    'quantity' => ($reservedQty - $deliveredQty) < $soldQty ? 1*($reservedQty - $deliveredQty) : 1*($soldQty), // If already delivered and sold qty is greather than (reserved - delivered) then quantity = (reserved - delivered) else quantity = sold (Because sold is less than [reserved - delivered])
                    'status' => 'Delivered'
                  );
                  $this->db->insert('procurement_parent_sales_order_details_delivery', $insertParentOrderDelivery);
                }
            }

          }

          // $vQtyUpdate[] =  array(
          //   'id'=> $productId['variants'],
          //   'current_quantity'=> $productId['current_quantity'] -  $productId['quantity']
          // );
          $vQtyUpdate_in_invoice_item[] =  array(
            'id'=> $input['inv_item_id'][$k],
            'current_quantity'=> $qty -  $productId['quantity']
          );
        } else {
          $status= false;
        }
        $indexValue ++;
      }
      
      if($status) {
        // $this->db->update_batch('procurement_itemmaster_items', $vQtyUpdate,'id');
        $this->db->update_batch('procurement_delivery_challan_items', $vQtyUpdate_in_invoice_item,'id');


      }

      $this->db->trans_complete();
      if ($this->db->trans_status() && $status) {
        return $stransId;
      }else{
        $this->db->trans_rollback();
        return '';
      }

    }

    private function get_student_wise_sales_calculate_amt_and_qty_category_wise($student_arr, $category, $subcategories, $items, $sales_year_id) {
      // echo '<pre>'; print_r($category); die();
      $select= "psm.id master_id, sum(pst.quantity) as quantity, sum(pst.amount) as amount, psm.student_id, pic.category_name";
      if($items != 0 && count($items) > 0) {
        $select .= ",pii.item_name";
      } else {
        $select .= ",'-' as item_name";
      }
      if($subcategories != 0 && count($subcategories) > 0) {
        $select .= ",pis.subcategory_name";
      } else {
        $select .= ",'-' as subcategory_name";
      }

      if($items != 0 && count($items) > 0) {
        $select_return= "psr.student_id, psr.proc_sales_master_id as master_id, sum(psr.return_quantity) as quantity, sum(psr.refund_amount) as amount,concat(psr.student_id,'_',pii.id) as stdKey";
        $select .= ", concat(psm.student_id,'_',pii.id) as stdKey";
      } else if($subcategories != 0 && count($subcategories) > 0) {
        $select_return= "psr.student_id, psr.proc_sales_master_id as master_id, sum(psr.return_quantity) as quantity, sum(psr.refund_amount) as amount,concat(psr.student_id,'_',pis.id) as stdKey";
        $select .= ", concat(psm.student_id,'_',pis.id) as stdKey";
      } else {
        $select_return= "psr.student_id, psr.proc_sales_master_id as master_id, sum(psr.return_quantity) as quantity, sum(psr.refund_amount) as amount,concat(psr.student_id,'_',pic.id) as stdKey";
        $select .= ", concat(psm.student_id,'_',pic.id) as stdKey";
      }

      $this->db_readonly->select($select)
        ->from('procurement_sales_master psm')
        ->join('procurement_sales_transactions pst', "pst.sales_master_id= psm.id")
        ->join('procurement_itemmaster_items pii','pst.proc_im_items_id=pii.id')
        ->join('procurement_itemmaster_subcategory pis','pst.proc_im_subcategory_id=pis.id')
        ->join('procurement_itemmaster_category pic','psm.proc_im_category_id=pic.id')
        ->where_in('psm.student_id', $student_arr)
        ->where('pst.sales_year_id', $sales_year_id);
        // ->where('psm.soft_delete', 0);
      if($items != 0 && count($items) > 0) {
        $this->db_readonly->where_in('pii.id', $items);
      }
      if($subcategories != 0 && count($subcategories) > 0) {
        $this->db_readonly->where_in('pis.id', $subcategories);
      }
      if($category != 0) {
          $this->db_readonly->where_in('pic.id', $category);
      }

      $grp_by_return= '';
      $group_by= '';
      if($items != 0 && count($items) > 0) {
        $grp_by_return= 'psr.proc_im_items_id';
        $group_by= 'pst.proc_im_items_id';
      } else if($subcategories != 0 && count($subcategories) > 0) {
        $grp_by_return= 'pis.id';
        $group_by= 'pis.id';
      } else if($category != 0) {
        $grp_by_return= 'pic.id';
        $group_by= 'pic.id';
      }
      $grp_by_return= ' psr.student_id';
      $group_by .= ',psm.student_id';

      $sales=   $this->db_readonly->group_by($group_by)->get()->result();


      // Sales Return
      $this->db_readonly->select($select_return)
        ->from('procurement_sales_return psr')
        // ->join('procurement_sales_master psm','psr.proc_sales_master_id=psm.id')
        // ->join('procurement_sales_transactions pst','pst.sales_master_id=psm.id')

        ->join('procurement_itemmaster_items pii','psr.proc_im_items_id=pii.id')
        ->join('procurement_itemmaster_subcategory pis','pii.proc_im_subcategory_id=pis.id')
        ->join('procurement_itemmaster_category pic','pis.proc_im_category_id=pic.id')
        ->where_in('psr.student_id', $student_arr)
        ->where('psr.sales_year_id', $sales_year_id);
      if($items != 0 && count($items) > 0) {
        $this->db_readonly->where_in('psr.proc_im_items_id', $items);
      }
      if($subcategories != 0 && count($subcategories) > 0) {
        $this->db_readonly->where_in('pis.id', $subcategories);
      }
      if($category != 0) {
          $this->db_readonly->where_in('pic.id', $category);
      }
      $return= $this->db_readonly->group_by($grp_by_return)->get()->result();

      // echo '<pre>'; print_r($return); die();

      $students= $this->db_readonly->select("sa.id as student_id, sy.promotion_status, sa.enrollment_number, concat(sa.first_name, ' ', ifnull(sa.last_name, '')) as name, sa.admission_no, cs.section_name, cs.class_name")
        ->from('student_admission sa')
        ->join('student_year sy', 'sy.student_admission_id= sa.id')
        ->join('class c', 'c.id= sy.class_id')
        ->join('class_section cs', 'cs.id= sy.class_section_id','left')
        ->where('sy.acad_year_id', $this->yearId)
        ->where_in('sa.id', $student_arr)
        ->get()->result();

        $returnMasterId = [];
        if(!empty($return)){
          foreach ($return as $key => $val) {
            $returnMasterId[$val->stdKey] =$val;
          }
        }


        $salesArry = [];
        foreach ($sales as $key => $val) {
          $val->final_quantity = $val->quantity;
          $val->final_amount = $val->amount;
          if(array_key_exists($val->stdKey, $returnMasterId)){
            $val->final_quantity =  $val->quantity - $returnMasterId[$val->stdKey]->quantity;
            $val->final_amount = $val->amount - $returnMasterId[$val->stdKey]->amount;
          }
          $salesArry[][$val->student_id] = $val;
        }     
        $studentTemp = [];
        foreach ($salesArry as $std_id => $items) {
          foreach ($students as $key => $std) {
            if(array_key_exists($std->student_id, $items)){
              $objClass = new stdClass();
              $objClass->student_id = $std->student_id;
              $objClass->name = $std->name;
              $objClass->promotion_status = $std->promotion_status;
              $objClass->enrollment_number = $std->enrollment_number;
              $objClass->admission_no = $std->admission_no;
              $objClass->class_name = $std->class_name;
              $objClass->section_name = $std->section_name;
              $objClass->final_quantity =  $items[$std->student_id]->final_quantity;
              $objClass->final_amount = round($items[$std->student_id]->final_amount,2);
              $objClass->category_name = $items[$std->student_id]->category_name;
              $objClass->subcategory_name = $items[$std->student_id]->subcategory_name;
              $objClass->item_name = $items[$std->student_id]->item_name;
              $studentTemp[] = $objClass;
            }
          
          }
        }




        // 
      // Formating it as group by students
      $structuredArray = [];

      foreach ($studentTemp as $item) {
          $studentId = $item->student_id;
          // If this student hasn't been added to the structuredArray array yet
          if (!isset($structuredArray[$studentId])) {
              $structuredArray[$studentId] = [
                  'student_id' => $studentId,
                  'name' => $item->name,
                  'promotion_status' => $item->promotion_status,
                  'enrollment_number' => $item->enrollment_number,
                  'admission_no' => $item->admission_no,
                  'class_name' => $item->class_name,
                  'section_name' => $item->section_name,
                  'categoryInformations' => []
              ];
          }
          
          // Add category information to this student's categoryInformations array
          $structuredArray[$studentId]['categoryInformations'][] = [
              'final_quantity' => $item->final_quantity,
              'final_amount' => $item->final_amount,
              'category_name' => $item->category_name,
              'subcategory_name' => $item->subcategory_name,
              'item_name' => $item->item_name
          ];
      }

      // Removing student_id from index and add normal index starts from zero
      $structuredArray = array_values($structuredArray);
      
      return $structuredArray;
    }

} 