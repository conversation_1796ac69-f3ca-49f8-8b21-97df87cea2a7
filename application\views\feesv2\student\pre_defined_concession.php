<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard') ?>">Fee Dashboard</a></li>
  <li class="active">Pre-defined Concession</li>
</ul>

<div class="col-md-12 col_new_padding">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-7 pl-0">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard') ?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
            Pre-defined Concession
          </h3>
        </div>
      </div>
    </div>
    <div class="card-body">
      <form enctype="multipart/form-data" method="post" action="<?php echo site_url('feesv2/fees_student/insert_pre_defined_concession') ?>" id="pre-concession-form" data-parsley-validate="" class="form-horizontal">
        <div class="col-md-6 col-md-offset-2">
          <div class="form-group">
              <label for="name" class="col-md-3">Pre-Defined Name <font color="red">*</font></label>
              <div class="col-md-9">
                <input class="form-control" placeholder="Enter Pre-Defined Name" name="pre_defined_concession" id="pre_defined_concession" type="text" required="">
                <span class="help-block">Ex: Staff, Sibling Concession</span>
              </div>
          </div>

          <div class="form-group">
            <label class="col-md-3">Concession Mode</label>
            <div class="col-md-9">
              <select class="form-control" name="concession_mode" id="concessionmode" >
                <option value="amount">Amount</option>
                <option value="percentage">Percentage</option>
              </select>
            </div>
          </div>
          <script type="text/javascript">
            $('#concessionmode').on('change',function(){
              var concessionmode = $('#concessionmode').val();
              if (concessionmode == 'percentage') {
                $('#changemode').html('Percentage');
              }else{
                $('#changemode').html('Amount');
              }
            });
          </script>
          <div class="form-group">
            <label class="col-md-3"><span id="changemode">Amount <font color="red">*</font></span></label>
            <div class="col-md-9">
              <input type="number" required name="pre_concession_amount" class="form-control" placeholder="Enter amount">
            </div>
          </div>

          <div class="form-group">
            <label class="col-md-3"><span id="changemode">Remarks </span></label>
            <div class="col-md-9">
              <input type="text" name="pre_concession_remarks" class="form-control" placeholder="Reason for concession">
            </div>
          </div>

          <div class="mt-3">
            <center>
              <button type="submit" style="width: 9rem; border-radius: .45rem;margin-left: 15rem;" class="btn btn-primary">Submit</button>     
              <!-- <button type="button" class="btn btn-danger" style="width: 9rem; border-radius: .45rem;" data-dismiss="modal">Close</button> -->
            </center>
          </div>
        </div>
      </form>
    </div>
    <div class="card-body">
      <h3>Details</h3>
      <div class="pre_defined_concession_table" style="overflow-x: auto;">
        <table id="pre_defined_concession_data" class="table table-bordered">
          <thead>
            <tr>
              <th>#</th>
              <th>Name</th>
              <th>Amount</th>
              <th>Concession mode</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            <?php if (!empty($preDefined_con)) { ?>
              <?php $i=1; foreach ($preDefined_con as $key => $val): ?>
                <tr>
                  <td><?php echo $i++; ?></td>
                  <td><?php echo $val->name ?></td>
                  <td><?php echo $val->amount ?></td>
                  <td><?php echo $val->concession_mode ?></td>
                  <td>
                   <?php if($val->status == 1){ ?>
                      <label class="switch">
                        <input type="checkbox" onclick="pre_defined_status_active('<?php echo $val->id ?>','0')" checked >
                        <span></span>
                      </label>
                    <?php }else{ ?>
                      <label class="switch">
                        <input type="checkbox" onclick="pre_defined_status_active('<?php echo $val->id ?>','1')" >
                        <span></span>
                      </label>
                    <?php } ?>
                  </td>
                </tr>
              <?php endforeach ?>
            <?php } ?>

          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<style>
    .pre_defined_concession_table {
        margin: 12px 0;
        overflow-x: auto;
        max-height: 500px;
        /* Custom scrollbar thickness */
    }

    .pre_defined_concession_table::-webkit-scrollbar {
        height: 14px;
        width: 14px;
    }

    .pre_defined_concession_table::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 8px;
    }

    .pre_defined_concession_table::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 8px;
    }

    /* For Firefox */
    .pre_defined_concession_table {
        scrollbar-width: thick;
        scrollbar-color: #cbd5e1 #f1f5f9;
    }

    #pre_defined_concession_data {
        position: relative;
    }

    #pre_defined_concession_data thead th {
        position: sticky !important;
        top: 0;
        background-color: #f1f5f9;
        color: #111827;
        font-size: 11px;
        font-weight: 500;
        z-index: 11;
    }
</style>

<script type="text/javascript">
  
  function pre_defined_status_active(stngId, value) {
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_student/active_pre_defined_concession'); ?>',
      type: "post",
      data:{'stngId':stngId, 'value':value},
      success: function (data) {
        if (data.length=='1') {
          location.reload();
        }else{
          location.reload();
        }
      },
      error: function (err) {
        console.log(err);
      }
    });
  }

</script>