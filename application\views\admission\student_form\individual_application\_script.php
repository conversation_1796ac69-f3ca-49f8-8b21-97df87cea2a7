<script>
    $(document).ready(function() {
        $('#dob_dtpicker').datetimepicker({
            viewMode: 'days',
            format: 'DD-MM-YYYY'
        });
        Application_Details();
        var currentStatus = $('#currentStatus').html();
        var internal_status = $('#internal_status').val();
        if (currentStatus == 'Draft' || currentStatus == 'Student added to ERP' || currentStatus =='Offer Released') {
            $('.releaedAfterHide').hide();
        }
        update_button_colors(internal_status);
    });

    var noOfComponents = $('#no_of_components').val();
    var noOfinstallments = $('#no_of_installments').val();
    var total_amount =$('#grand_total').val();

    function updateVariables() {
        noOfComponents = $('#no_of_components').val();
        noOfinstallments = $('#no_of_installments').val();
        total_amount =$('#grand_total').val();
    }

    function verify_document(document_id, document_name, path) {
        $('#verify_document').modal('show');
        $("#verify_document iframe").attr("src", path);
        $('#document_id').val(document_id);
    }

    function document_verification_done(verification_status) {
        var document_id = $('#document_id').val();
        if (verification_status == 'Rejected') {
            document_verification_rejected(document_id);
        } else {
            document_verification_approved(document_id);
        }

    }

    function document_verification_approved(document_id) {
        var af_id = '<?php echo $afId ?>';
        bootbox.confirm({
            title: `Confirmation`,
            message: `Are you sure to mark this document as verified?`,
            className: "medium",
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-success'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-danger'
                }
            },
            callback: function(result) {
                if (result) {
                    $.ajax({
                        url: '<?php echo site_url('admission_process/document_vefication'); ?>',
                        type: 'post',
                        data: {
                            'document_id': document_id,
                            'verification_status': 'Verified',
                            'af_id': af_id
                        },
                        success: function(data) {
                            if (data) {
                                $(function() {
                                    new PNotify({
                                        title: 'Success',
                                        text: 'Successfully done',
                                        type: 'success',
                                    });
                                });
                                $('#verify_document').modal('hide');
                                // $('#verify_btn_'+document_id).hide();
                                // $('#verification_status_'+document_id).html('<b style="color:Green">Verified</b>');
                                // $('#verification_remarks_'+document_id).html('');
                                // $('#upload_btn_'+document_id).hide();
                                // get_document_verify_data(document_id);
                                get_admission_document_by_id();
                            } else {
                                $(function() {
                                    new PNotify({
                                        title: 'Error',
                                        text: 'Something went wrong',
                                        type: 'error',
                                    });
                                });
                            }
                        }
                    });
                }
            }
        });
    }

function document_verification_rejected(document_id) {
    var af_id = '<?php echo $afId ?>';
    bootbox.confirm({
        title: `Confirmation`,
        message: `<label>Remarks</label><input class="form-control" placeholder="Enter the remaks" id="verification_remarks" required>`,
        className: "medium",
        buttons: {
            confirm: {
                label: 'Submit',
                className: 'btn-success'
            },
            cancel: {
                label: 'Close',
                className: 'btn-danger'
            }
        },
        callback: function(result) {
            if (result) {
                var verification_remarks = $('#verification_remarks').val();
                if (verification_remarks == '') {
                    alert('Enter the remarks');
                    return false;
                }
                $.ajax({
                    url: '<?php echo site_url('admission_process/document_vefication'); ?>',
                    type: 'post',
                    data: {
                        'document_id': document_id,
                        'verification_status': 'Rejected',
                        'verification_remarks': verification_remarks,
                        'af_id': af_id
                    },
                    success: function(data) {
                        if (data) {
                            $(function() {
                                new PNotify({
                                    title: 'Success',
                                    text: 'Successfully done',
                                    type: 'success',
                                });
                            });
                            $('#verify_document').modal('hide');
                            // $('#verify_btn_'+document_id).hide();
                            // $('#verification_status_'+document_id).html('<b style="color:Red">Rejected</b>');
                            // $('#verification_remarks_'+document_id).html(verification_remarks);
                            // $('#upload_btn_'+document_id).show();
                            // get_document_verify_data(document_id);
                            get_admission_document_by_id();
                        } else {
                            $(function() {
                                new PNotify({
                                    title: 'Error',
                                    text: 'Something went wrong',
                                    type: 'error',
                                });
                            });
                        }

                    }

                });
            }
        }
    });
}

function get_document_verify_data(document_id) {
    $.ajax({
        url: '<?php echo site_url('admission_process/get_document_verify_data'); ?>',
        type: 'post',
        data: {
            'document_id': document_id
        },
        success: function(data) {
            var resData = JSON.parse(data);
            if (resData != '') {
                $('#verified_by_' + document_id).html(resData[0].verified_by);
                $('#verified_on_' + document_id).html(resData[0].verified_on);
            }
        }
    });
}

function update_button_colors(internal_status) {
    var app_details_tab = $('#app_details_tab');
    var previous_schooling_details_tab = $('#previous_schooling_details_tab');
    var attached_documents_tab = $('#attached_documents_tab');
    var application_payment_details_tab = $('#application_payment_details_tab');
    var follow_up_tab = $('#follow_up_tab');
    var release_offers_tab = $('#release_offers_tab');
    var admission_confirmed_tab = $('#admission_confirmed_tab');

    var app_details_icon = $('#app_details_tab_icon');
    var previous_schooling_details_icon = $('#previous_schooling_details_icon');
    var attached_documents_icon = $('#attached_documents_icon');
    var application_payment_details_icon = $('#application_payment_details_icon');
    var follow_up_icon = $('#follow_up_icon');
    var release_offers_icon = $('#release_offers_icon');
    var admission_confirmed_icon = $('#admission_confirmed_icon');

    // console.log(internal_status);
    switch (internal_status) {
        case 'Student added to ERP':
            _color_a_tab(app_details_tab, app_details_icon, 'DONE_GREEN');
            _color_a_tab(previous_schooling_details_tab, previous_schooling_details_icon, 'DONE_GREEN');
            _color_a_tab(attached_documents_tab, attached_documents_icon, 'DONE_GREEN');
            _color_a_tab(application_payment_details_tab, application_payment_details_icon, 'DONE_GREEN');
            _color_a_tab(follow_up_tab, follow_up_icon, 'DONE_GREEN');
            _color_a_tab(release_offers_tab, release_offers_icon, 'DONE_GREEN');
            _color_a_tab(admission_confirmed_tab, admission_confirmed_icon, 'DONE_GREEN');
            break;
        case 'Offer Released':
        case 'Offer Rejected':
            _color_a_tab(app_details_tab, app_details_icon, 'DONE_GREEN');
            _color_a_tab(previous_schooling_details_tab, previous_schooling_details_icon, 'DONE_GREEN');
            _color_a_tab(attached_documents_tab, attached_documents_icon, 'DONE_GREEN');
            _color_a_tab(application_payment_details_tab, application_payment_details_icon, 'DONE_GREEN');
            _color_a_tab(follow_up_tab, follow_up_icon, 'DONE_GREEN');
            if (internal_status == 'Offer Rejected') {
                _color_a_tab(release_offers_tab, release_offers_icon, 'DONE_RED');
            } else {
                _color_a_tab(release_offers_tab, release_offers_icon, 'DONE_GREEN');
            }
            _color_a_tab(admission_confirmed_tab, admission_confirmed_icon, 'NONE');
            break;
        case 'Admit':
        case 'Rejected':
        case 'Duplicate':
            _color_a_tab(app_details_tab, app_details_icon, 'DONE_GREEN');
            _color_a_tab(previous_schooling_details_tab, previous_schooling_details_icon, 'DONE_GREEN');
            _color_a_tab(attached_documents_tab, attached_documents_icon, 'DONE_GREEN');
            _color_a_tab(application_payment_details_tab, application_payment_details_icon, 'DONE_GREEN');
            if (internal_status == 'Rejected' || internal_status == 'Duplicate') {
                _color_a_tab(follow_up_tab, follow_up_icon, 'DONE_RED');
            } else {
                _color_a_tab(follow_up_tab, follow_up_icon, 'DONE_GREEN');
            }
            _color_a_tab(release_offers_tab, release_offers_icon, 'NONE');
            _color_a_tab(admission_confirmed_tab, admission_confirmed_icon, 'NONE');
            break;
        case 'Follow up':
        case 'Application Amount Paid':
        case 'Submitted':
            _color_a_tab(app_details_tab, app_details_icon, 'DONE_GREEN');
            _color_a_tab(previous_schooling_details_tab, previous_schooling_details_icon, 'DONE_GREEN');
            _color_a_tab(attached_documents_tab, attached_documents_icon, 'DONE_GREEN');
            _color_a_tab(application_payment_details_tab, application_payment_details_icon, 'DONE_GREEN');
            _color_a_tab(follow_up_tab, follow_up_icon, 'NONE');
            _color_a_tab(release_offers_tab, release_offers_icon, 'NONE');
            _color_a_tab(admission_confirmed_tab, admission_confirmed_icon, 'NONE');
            break;
        case 'Draft':
            break;
    }
}

function _color_a_tab(tab_obj, icon_obj, status) {
    switch (status) {
        case 'DONE_GREEN':
            tab_obj.addClass('green_button');
            tab_obj.removeClass('red_button');
            icon_obj.addClass('green_icon');
            icon_obj.removeClass('red_icon');
            break;
        case 'DONE_RED':
            tab_obj.addClass('red_button');
            tab_obj.removeClass('green_button');
            icon_obj.addClass('red_icon');
            icon_obj.removeClass('green_icon');
            break;
        default:
            tab_obj.removeClass('red_button');
            tab_obj.removeClass('green_button');
            icon_obj.removeClass('green_icon');
            icon_obj.removeClass('red_icon');
            break;
    }
}

function calculateAge(birthday) {
    var today = new Date();
    var age = today.getFullYear() - birthday.getFullYear();
    var m = today.getMonth() - birthday.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthday.getDate())) {
        age--;
    }
    return age;
}

function Application_Details() {
    $('#application').show();
    $('#title').html('Application Details');
    $('#printIcon').hide();
    $('#follow_up').hide();
    $('#attached_documents').hide();
    $('#view_details').show();
    $('#fees_collection').hide();
    $('#printArea').hide();
    $('#print_btn').show();
    $('#seat_allotment').hide();
    $('#previous_school_details').hide();
    $('#joining_forms_card').hide();
    $('#edit_history_data').hide();
    $('#closeApplication').hide();

    // var stu_id = $("#stu_id").val();
    var adm_id = '<?php echo $afId ?>';
    $.ajax({
        url: '<?php echo site_url('Admission_process/get_stu_basic_data'); ?>',
        type: 'post',
        data: {
            'stu_id': adm_id
        },
        success: function(data) {
            // console.log(data);
            var stu_data = $.parseJSON(data);
            var columns = Object.keys(stu_data);

            if (stu_data.enquiry_id != 0) {
                $('#link_to_enquiry').hide();
                $('#view_enquiry_details').show();
            }
            //console.log(columns);
            for (var c = 0; c < columns.length; c++) {
                var valueDisplay = stu_data[columns[c]];
                // if(columns[c] == 'dob'){
                //   valueDisplay = moment(stu_data[columns[c]]).format('DD MMM, YYYY');
                // }
                if (stu_data[columns[c]] == '' || stu_data[columns[c]] == null || stu_data[columns[c]] ==
                    '1970-01-01') {
                    valueDisplay = '-';
                }
                if (columns[c] == 'f_dob' && stu_data['f_dob'] != '-') {
                    var dob = stu_data['f_dob'];
                    var dobDate = new Date(dob);
                    var age = calculateAge(dobDate);
                    valueDisplay += ' (Age: ' + age + ')';
                }
                if (columns[c] == 'm_dob' && stu_data['m_dob'] != '-') {
                    var dob = stu_data['m_dob'];
                    var dobDate = new Date(dob);
                    var age = calculateAge(dobDate);
                    valueDisplay += ' (Age: ' + age + ')';
                }
                if (columns[c] == 'student_signature' || columns[c] == 'm_signature' || columns[c] ==
                    'f_signature' || columns[c] == 'g_photo_uri' || columns[c] == 'family_photo' || columns[
                        c] == 'father_photo' || columns[c] == 'mother_photo') {
                    if (stu_data[columns[c]] == null && (columns[c] == 'g_photo_uri' || columns[c] ==
                            'family_photo' || columns[c] == 'father_photo' || columns[c] == 'mother_photo'
                            )) {
                        stu_data[columns[c]] =
                            "https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/Admission process/student_image.jpg";
                    } else if (stu_data[columns[c]] == null && (columns[c] == 'student_signature' ||
                            columns[c] == 'm_signature' || columns[c] == 'f_signature')) {
                        stu_data[columns[c]] =
                            "https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/contract_signature.png";
                    }
                    if (columns[c] == 'f_signature') {
                        valueDisplay =
                            '<img style="width:40px; height:40px;" id="father_img_signature" src="' +
                            stu_data[columns[c]] + '" alt="Avatar">';
                        valueDisplay +=
                            '<i style="top: 10.5rem;right: 6.7rem;" id="father_signature_edit" class="fa fa-camera pull-right"></i>';
                        valueDisplay +=
                            `<input hidden="hidden"  type="file" id="father_signature" class="file" data-preview-file-type="jpeg" name="student_photo" accept="image/*">`;
                        valueDisplay +=
                            `<span id="fileuploadError1" style="color:red;display: block;padding-top:5px;padding-bottom:5px;"></span> <button id="father_signature_button" type="button" onclick="save_profile_photo(${adm_id},'father_sign','${stu_data[columns[c]]}')" style="display: none;margin-left:90px;margin-bottom:5px;width:100px;" class="btn btn-primary">Save</button>`;
                    }

                    if (columns[c] == 'g_photo_uri') {
                        valueDisplay =
                            '<img style="width:40px; height:40px;" id="guardian_img_signature" src="' +
                            stu_data[columns[c]] + '" alt="Avatar">';
                        valueDisplay +=
                            '<i style="top: 10.5rem;right: 6.7rem;" id="guardian_signature_edit" class="fa fa-camera pull-right"></i>';
                        valueDisplay +=
                            `<input hidden="hidden"  type="file" id="guardian_signature" class="file" data-preview-file-type="jpeg" name="student_photo" accept="image/*">`;
                        valueDisplay +=
                            `<span id="fileuploadError2" style="color:red;display: block;padding-top:5px;padding-bottom:5px;"></span> <button id="guardian_signature_button" type="button" onclick="save_profile_photo(${adm_id},'guardian_photo','${stu_data[columns[c]]}')" style="display: none;margin-left:90px;margin-bottom:5px;width:100px;" class="btn btn-primary">Save</button>`;
                    }

                    if (columns[c] == 'father_photo') {
                        valueDisplay = '<img style="width:40px; height:40px;" id="father_img" src="' +
                            stu_data[columns[c]] + '" alt="Avatar">';
                        valueDisplay +=
                            '<i style="top: 10.5rem;right: 6.7rem;" id="father_photo_edit" class="fa fa-camera pull-right"></i>';
                        valueDisplay +=
                            `<input hidden="hidden"  type="file" id="father_photo" class="file" data-preview-file-type="jpeg" name="father_photo" accept="image/*">`;
                        valueDisplay +=
                            `<span id="fileuploadError3" style="color:red;display: block;padding-top:5px;padding-bottom:5px;"></span> <button id="father_photo_upload_button" type="button" onclick="save_profile_photo(${adm_id},'father_photo','${stu_data[columns[c]]}')" style="display: none;margin-left:90px;margin-bottom:5px;width:100px;" class="btn btn-primary">Save</button>`;
                    }

                    if (columns[c] == 'mother_photo') {
                        valueDisplay = '<img style="width:40px; height:40px;" id="mother_img" src="' +
                            stu_data[columns[c]] + '" alt="Avatar">';
                        valueDisplay +=
                            '<i style="top: 10.5rem;right: 6.7rem;" id="mother_photo_edit" class="fa fa-camera pull-right"></i>';
                        valueDisplay +=
                            `<input hidden="hidden"  type="file" id="mother_photo" class="file" data-preview-file-type="jpeg" name="mother_photo" accept="image/*">`;
                        valueDisplay +=
                            `<span id="fileuploadError4" style="color:red;display: block;padding-top:5px;padding-bottom:5px;"></span> <button id="mother_photo_upload_button" type="button" onclick="save_profile_photo(${adm_id},'mother_photo','${stu_data[columns[c]]}')" style="display: none;margin-left:90px;margin-bottom:5px;width:100px;" class="btn btn-primary">Save</button>`;
                    }

                    if (columns[c] == 'family_photo') {
                        valueDisplay = '<img style="width:40px; height:40px;" id="family_img" src="' +
                            stu_data[columns[c]] + '" alt="Avatar">';
                        valueDisplay +=
                            '<i style="top: 10.5rem;right: 6.7rem;" id="family_photo_edit" class="fa fa-camera pull-right"></i>';
                        valueDisplay +=
                            `<input hidden="hidden"  type="file" id="family_photo" class="file" data-preview-file-type="jpeg" name="family_photo" accept="image/*">`;
                        valueDisplay +=
                            `<span id="fileuploadError5" style="color:red;display: block;padding-top:5px;padding-bottom:5px;"></span> <button id="family_photo_upload_button" type="button" onclick="save_profile_photo(${adm_id},'family_photo','${stu_data[columns[c]]}')" style="display: none;margin-left:90px;margin-bottom:5px;width:100px;" class="btn btn-primary">Save</button>`;
                    }

                    if (columns[c] == 'student_signature') {
                        valueDisplay = '<img style="width:40px; height:40px;" id="stud_sign_img" src="' +
                            stu_data[columns[c]] + '" alt="Avatar">';
                        valueDisplay +=
                            '<i style="top: 10.5rem;right: 6.7rem;" id="stud_sign_photo_edit" class="fa fa-camera pull-right"></i>';
                        valueDisplay +=
                            `<input hidden="hidden"  type="file" id="stud_sign_photo" class="file" data-preview-file-type="jpeg" name="stud_sign_photo" accept="image/*">`;
                        valueDisplay +=
                            `<span id="fileuploadError6" style="color:red;display: block;padding-top:5px;padding-bottom:5px;"></span> <button id="stud_sign_photo_upload_button" type="button" onclick="save_profile_photo(${adm_id},'stud_sign','${stu_data[columns[c]]}')" style="display: none;margin-left:90px;margin-bottom:5px;width:100px;" class="btn btn-primary">Save</button>`;
                    }

                    $('#show-edit-admission_student-' + columns[c]).hide();
                    // }
                }
                $('#admission_student-' + columns[c]).html(valueDisplay);
                $('#edit-admission_student-' + columns[c]).attr("data-" + columns[c], stu_data[columns[c]]);
            }
            $('#enq_class_name option').each(function() {
                if ($(this).text() == stu_data.grade_applied_for) {
                    $(this).attr('selected', 'selected');
                }
            });

            $('#s_name').html(stu_data.std_name);
            $('#for_name').html(stu_data.std_name);
            $('#apl_num').html(stu_data.application_no);
            $('#grade').html(stu_data.grade_applied_for);
            $('#for_grade').html(stu_data.grade_applied_for);
            $('#s_email').html(stu_data.student_email_id);
            $('#mob_num').html(stu_data.student_mobile_no);
            $('#search_by_name').val(stu_data.std_name);
            $('#search_by_email').val(stu_data.f_email_id);
            $('#search_by_mobnumber').val(stu_data.f_mobile_no);
            $('#mother_email').val(stu_data.m_email_id);
            $('#mother_mobilNo').val(stu_data.m_mobile_no);
            $('#enq_student_name').val(stu_data.std_name);
            $('#enq_mob_num').val(stu_data.f_mobile_no);
            $('#enq_parent_name').val(stu_data.f_name);
            $('#enq_email_id').val(stu_data.f_email_id);


            if (stu_data.std_photo_uri == null || stu_data.std_photo_uri == '') {
                var imgpath = '<?php echo base_url('assets/img/sample_boy_image.png')?>';
            } else {
                var imgpath = stu_data.std_photo_uri;
            }
            $('#profile_section').html(`
                    <img id="stud_image" style="width:120px; height:150px;" src="${imgpath}" alt="Avatar"><br>
                    <input type="hidden" id="student_photo_url" value="${imgpath}">
                `);
        },
        error: function(err) {
            console.log(err);
        }
    });
}

function openSelect(file) {
    $(file).trigger('click');
}
var loadFile = function(event) {
    var reader = new FileReader();
    reader.onload = function() {
        var output = document.getElementById('output');
        output.src = reader.result;
    };
    reader.readAsDataURL(event.target.files[0]);
};

function previous_school_details() {
    var school_label = '<?php echo $this->settings->getSetting('your_word_for_institute') ?>';
    if (school_label == '') {
        $('#title').html('Previous School Details');
    } else {
        $('#title').html(`Previous ${school_label} Details`);
    }

    $('#printIcon').hide();
    $('#follow_up').hide();
    $('#attached_documents').hide();
    $('#view_details').hide();
    $('#fees_collection').hide();
    $('#printArea').hide();
    $('#print_btn').hide();
    $('#seat_allotment').hide();
    $('#previous_school_details').show();
    $('#moveToErp').hide();
    $('#joining_forms_card').hide();
    $('#edit_history_data').hide();
    $('#closeApplication').hide();
    get_previous_school_details_data();
}

function attached_documents() {
    $('#title').html('Attached Documents');
    $('#printIcon').hide();
    $('#follow_up').hide();
    $('#attached_documents').show();
    $('#view_details').hide();
    $('#fees_collection').hide();
    $('#printArea').hide();
    $('#print_btn').hide();
    $('#seat_allotment').hide();
    $('#previous_school_details').hide();
    $('#moveToErp').hide();
    $('#joining_forms_card').hide();
    $('#edit_history_data').hide();
    $('#closeApplication').hide();
    get_admission_document_by_id();
    // var admCurrentStat = $('#currentStatus').html();
    // if (admCurrentStat != 'Draft' && admCurrentStat != 'Offer Released' && admCurrentStat != 'Student added to ERP') {
    //     $('#add_documents').show();
    //     return false;
    // }else{
    //     $('#add_documents').hide();
    // }
}

function payment_details() {
    $('#title').html('Payment Details');
    $('#printIcon').hide();
    $('#follow_up').hide();
    $('#attached_documents').hide();
    $('#view_details').hide();
    $('#fees_collection').show();
    $('#printArea').show();
    $('#print_btn').hide();
    $('#seat_allotment').hide();
    $('#previous_school_details').hide();
    $('#moveToErp').hide();
    $('#joining_forms_card').hide();
    $('#edit_history_data').hide();
    $('#closeApplication').hide();
}

function follow_up() {
    $('#title').html('Follow Up');
    $('#printIcon').hide();
    $('#follow_up').show();
    $('#attached_documents').hide();
    $('#view_details').hide();
    $('#fees_collection').hide();
    $('#printArea').hide();
    $('#print_btn').hide();
    $('#seat_allotment').hide();
    $('#previous_school_details').hide();
    $('#moveToErp').hide();
    $('#joining_forms_card').hide();
    $('#edit_history_data').hide();
    $('#closeApplication').hide();


    let followUpStatus = <?php echo json_encode($follow_up_status); ?>; // Convert PHP array to JSON
    let admissionPickStatus =
        <?php echo $this->settings->getSetting('admission_pick_status_from_table') ? 'true' : 'false'; ?>;
    let statusPermission = <?php echo json_encode($status_permission); ?>;
    var staff_id = <?php echo $this->authorization->getAvatarStakeHolderId() ?>;

    let selectBox = $("#FollowupStatus");
    selectBox.empty(); // Clear previous options
    selectBox.append('<option value="">Select Status</option>'); // Add default option

    $.each(followUpStatus, function(key, value) {
        let status = admissionPickStatus ? value.user_status : value;

        if (status !== 'Draft') {
            let disabled = "";
            let permissionStr = "";

            if (status === 'Admit' || status === 'Rejected') {
                if (!
                    <?php echo json_encode($this->authorization->isAuthorized('ADMISSION.CAN_ADMIT_REJECT')); ?>
                    ) {
                    disabled = 'disabled';
                    permissionStr = '(Need Permission)';
                }
            }

            if (status.toLowerCase() === 'completed interactions') {
                if (!
                    <?php echo json_encode($this->authorization->isAuthorized('ADMISSION.ENABLE_COMPLETED_INTERACTIONS')); ?>
                    ) {
                    disabled = 'disabled';
                    permissionStr = '(Need Permission)';
                }
            }

            // if (Array.isArray(statusPermission[status]) && staff_id != 0 && !statusPermission[status].includes(staff_id)) {
            //   console.log('in for loop '+status)
            //     disabled = 'disabled';
            //     permissionStr = '(Need Permission)';
            // }

            let option = `<option ${disabled} value="${status}">${status} ${permissionStr}</option>`;
            selectBox.append(option);
        }
    });

    selectBox.append('<option value="Closed-not interested">Closed-not interested</option>');

    var admCurrentStat = $('#currentStatus').html();
    $('#FollowupStatus').val(admCurrentStat).change();
    if (admCurrentStat != 'Draft' && admCurrentStat != 'Offer Released' && admCurrentStat != 'Student added to ERP') {
        $('#followupAnchor').show();
        actionTabs(0);
        return false;
    } else {
        $('#followForm').removeClass('active');
        $('#followhistory').addClass('active');
        $('#followup_form').hide();
        actionTabs(1);
        $('#followupAnchor').hide();
    }
}
function get_admission_document_by_id() {
    var af_id = '<?php echo $afId ?>';
    var adm_setting_id = '<?php echo $admission_setting_id; ?>';
    var document_input_version = '<?php echo $config_val['document_input_version'] ?>';
    var documentsList = <?php echo json_encode($document_list); ?>;
    const select = document.getElementById('selected_document');

    $.ajax({
        url: '<?php echo site_url('admission_process/get_admission_document_by_id'); ?>',
        type: 'post',
        data: { af_id, adm_setting_id },
        success: function(data) {
            var docsArry = $.parseJSON(data);
            select.innerHTML = '';
            
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = 'Select Document';
            select.appendChild(defaultOption);

            if (!docsArry || docsArry.length === 0) {
                $('#documents_table').html('<h3 class="no-data-display">No Documents Uploaded</h3>');
            }

            if (document_input_version === 'V1') {
                if (docsArry && docsArry.length > 0) construct_V1_document_table(docsArry);
                documentsList = JSON.parse(documentsList);
                const filtered = documentsList.filter(doc => !uploaded_documents_list.includes(doc));
                filtered.forEach(doc => {
                    const option = document.createElement('option');
                    option.value = doc;
                    option.textContent = doc;
                    select.appendChild(option);
                });
            } else {
                if (docsArry && docsArry.length > 0) construct_V2_document_table(docsArry);
                const filtered = documentsList.filter(doc => !uploaded_documents_list.includes(doc.name));
                filtered.forEach(doc => {
                    const option = document.createElement('option');
                    option.value = doc.name + '_' + doc.view_type;
                    option.textContent = doc.name;
                    select.appendChild(option);
                });
            }
        }
    });
}

const uploaded_documents_list = [];
function construct_V1_document_table(docsArry) {
    var document_verification = '<?php echo $document_verification_in_admissions ?>';
    var currentStatus = $('#currentStatus').html();
    var html = '';
    html += `<table class="table table-bordered">
                <thead>
                <tr>
                  <th>#</th>
                  <th>Name</th>`;
    if (document_verification == 1) {
        html += ` <th>Verification Status</th>
                  <th>Verification Remarks</th>
                  <th>Verified By</th>
                  <th>Verified On</th>`;
    }
    html += `<th>Action</th>
                  </tr>
            </thead>
            <tbody id="doc_id">`;
    var k = 1;
    for (var i in docsArry) {
        uploaded_documents_list.push(docsArry[i].document_type)
        var path = '<?php echo site_url('admission_process/download?path=') ?>' + docsArry[i].document_uri;
        html += `<tr>`;
        html += `<td>${k++}</td>`;
        html += `<td>${docsArry[i].document_type}</td>`;

        if (document_verification == 1) {
            var text_color = '';
            if (docsArry[i].document_verification_status == 'Verified') {
                text_color = 'style="color:green"';
            } else if (docsArry[i].document_verification_status == 'Rejected') {
                text_color = 'style="color:red"';
            }
            html += `<td ${text_color}>${docsArry[i].document_verification_status}</td>`;
            html += `<td>${docsArry[i].verification_remarks}</td>`;
            html += `<td>${docsArry[i].verified_by}</td>`;
            var verified_on = docsArry[i].verified_on;
            if (docsArry[i].verified_on == null) {
                verified_on = '-';
            }
            html += `<td>${verified_on}</td>`;
        }
        html += `<td>
                      <div class="dropdown">
                            <a class="btn btn-info nav-link dropdown-toggle" href="#" role="button" aria-haspopup="true" aria-expanded="false" style="border-radius: 0.2rem;padding:8px 37px" onclick="open_dropdown(${docsArry[i].id})">
                            More
                            </a>
                            <div class="dropdown-content"  id="dropdown_menu_${docsArry[i].id}" style="position:static">
                            <a class="" href="${path}">Download</a>
                            <a class="" href="${docsArry[i].document_uri}" target="_blank">View</a>`;
        if (document_verification == 1 && currentStatus != 'Draft' && currentStatus && 'Student added to ERP' &&
            currentStatus != 'Offer Released') {
            html +=
                `<a class="" id="verify_btn_${docsArry[i].id}" href="#" data-placement="top" data-toggle="tooltip" data-original-title="Verify" data-toggle="modal" data-target="#verify_document" onclick="verify_document('${docsArry[i].id}','${docsArry[i].document_type}','${docsArry[i].document_uri}')">Verify/Reject </a>`;
            if (docsArry[i].document_verification_status != 'Verified') {
                html +=
                    `<a class="" id="upload_btn_${docsArry[i].id}" data-toggle="modal" data-target="#upload_document" onclick="re_upload_document('${docsArry[i].id}','${docsArry[i].document_type}','')">Re-upload</a>`;
            }
        }
        html += `</div>
                        </div>
                      </td>`;
        html += `</tr>`;
    }
    html += `</tbody>
                      </table>`;
    $('#documents_table').html(html)
}

function construct_V2_document_table(docsArry) {
    var document_verification = '<?php echo $document_verification_in_admissions ?>';
    var currentStatus = $('#currentStatus').html();
    var html = '';
    html += `<div class="table-responsive" id="report-container-summary">
                <table class="table table-bordered">
                <thead>
                <tr>
                  <th width="5">#</th>
                  <th >Name</th>
                  <th>Document type</th>
                  <th>Additional items</th>`
    if (document_verification == 1) {
        html += ` <th >Verification Status</th>
                  <th >Verification Remarks</th>
                  <th >Verified By</th>
                  <th >Verified On</th>`;
    }
    html += `
                  <th>Action</th></tr>
            </thead>
            <tbody id="doc_id">`;
    var k = 1;
    for (var i in docsArry) {
        uploaded_documents_list.push(docsArry[i].document_type)
        var path = '<?php echo site_url('admission_process/download?path=') ?>' + docsArry[i].document_uri;
        html += `<tr>`;
        html += `<td>${k++}</td>`;
        html += `<td>${docsArry[i].document_type}</td>`;
        html += `<td>${docsArry[i].attached_document_type}</td>`;
        if (docsArry[i].name_as_per_aadhar != null && docsArry[i].name_as_per_aadhar != '') {
            html +=
                `<td><p>Name As per Aadhar :${docsArry[i].name_as_per_aadhar}</p><p>Aadhar Number :${docsArry[i].aadhar_number}</p></td>`;
        } else if (docsArry[i].pan_card_number != null && docsArry[i].pan_card_number != '') {
            html += `<td><p>PAN Card Number :${docsArry[i].pan_card_number}</p></td>`;
        } else {
            html += `<td></td>`;
        }
        if (document_verification == 1) {
            var text_color = '';
            if (docsArry[i].document_verification_status == 'Verified') {
                text_color = 'style="color:green"';
            } else if (docsArry[i].document_verification_status == 'Rejected') {
                text_color = 'style="color:red"';
            }
            html += `<td ${text_color}><b>${docsArry[i].document_verification_status}</b></td>`;
            html += `<td>${docsArry[i].verification_remarks}</td>`;
            html += `<td>${docsArry[i].verified_by}</td>`;
            var verified_on = docsArry[i].verified_on;
            if (docsArry[i].verified_on == null) {
                verified_on = '-';
            }
            html += `<td>${verified_on}</td>`;
        }
        html += `<td>
                          <div class="dropdown">
                            <a class="btn btn-info nav-link dropdown-toggle" href="#" role="button" aria-haspopup="true" aria-expanded="false" style="border-radius: 0.2rem;padding:8px 37px" onclick="open_dropdown(${docsArry[i].id})">
                            More
                            </a>
                            <div class="dropdown-content"  id="dropdown_menu_${docsArry[i].id}" style="position:static">
                                <a  href="${path}">Download</a>
                                <a  href="${docsArry[i].document_uri}" target="_blank">View</a>`;

        if (document_verification == 1 && currentStatus != 'Draft' && currentStatus && 'Student added to ERP' &&
            currentStatus != 'Offer Released') {
            html +=
                `<a id="verify_btn_${docsArry[i].id}" href="#" data-placement="top" data-toggle="tooltip" data-original-title="Verify" data-toggle="modal" data-target="#verify_document" onclick="verify_document('${docsArry[i].id}','${docsArry[i].document_type}','${docsArry[i].document_uri}')">Verify/Reject </a>`;
            if (docsArry[i].document_verification_status != 'Verified') {
                html +=
                    `<a  id="upload_btn_${docsArry[i].id}" data-toggle="modal" data-target="#upload_document" onclick="re_upload_document('${docsArry[i].id}','${docsArry[i].document_type}','${docsArry[i].view_type}')">Re-upload</a>`;
            }
        }
        html += `</div>
                        </div>`;
        html += `</td>`;
        html += `</tr>`;
    }
    html += `</tbody>
                      </table>
                      </div>`;
    $('#documents_table').html(html);
    add_scroller('report-container-summary');
}

function open_dropdown(id) {
    var dropdownContent = document.querySelector('#dropdown_menu_' + id);
    if (dropdownContent.style.display === 'block') {
        dropdownContent.style.display = 'none';
    } else {
        $('.dropdown-content').hide();
        dropdownContent.style.display = 'block';
    }
    //  dropdownContent.style.display = (dropdownContent.style.display === 'block') ? 'none' : 'block';
}

$(document).on("click", function(event) {
    if (event.target.id != 'action_btn') {
        var dropdownContent = document.querySelector('#dropdown_menu');
        dropdownContent.style.display = (dropdownContent.style.display === 'block') ? 'none' : '';
    }
});

function show_dropdown() {
    var dropdownContent = document.querySelector('#dropdown_menu');
    dropdownContent.style.display = (dropdownContent.style.display === 'block') ? 'none' : 'block';
}

function seat_allotment() {
    $('#title').html(' Release Seat Allottment / Admission ');
    $('#seat_allotment').show();
    $('#printIcon').hide();
    $('#follow_up').hide();
    $('#attached_documents').hide();
    $('#view_details').hide();
    $('#fees_collection').hide();
    $('#printArea').hide();
    $('#print_btn').hide();
    $('#moveToErp').hide();
    $('#previous_school_details').hide();
    $('#joining_forms_card').hide();
    $('#edit_history_data').hide();
    $('#closeApplication').hide();
    var application_fees_amount = '<?php echo $config_val['admission_fee_amount'] ?>';
    var payment_status = '<?php echo $application_fee_status->payment_status ?>';
    if (application_fees_amount != '0.00' && payment_status != 'SUCCESS') {
        $('#admStatusshow').show();
        $('#admStatusshow').html('<h3 id="donot_release_offer">Collect the Application Fees Amount To Continue.</h3>');
        $('#admReleaseshow').hide();
        return false
    }
    tabwise_list_data('release_offer');
    var admCurrentStat = $('#currentStatus').html();
    var internal_status = $('#internal_status').val();
    if (admCurrentStat != 'Admit' && admCurrentStat != 'Offer Released' && admCurrentStat != 'Student added to ERP') {
        $('#admStatusshow').show();
        $('#admReleaseshow').hide();
        return false;
    } else {
        check_document_verification_status();
    }
}

function check_document_verification_status() {
    var afId = '<?php echo $afId ?>';
    var documents_verication = '<?php echo $document_verification_in_admissions  ?>';
    var admCurrentStat = $('#currentStatus').html();
    $.ajax({
        url: '<?php echo site_url('admission_process/get_student_documents'); ?>',
        type: 'post',
        data: {
            'afId': afId
        },
        success: function(data) {
            var docsArry = $.parseJSON(data);
            var check_doc_verified = 'verified';
            for (var i in docsArry) {
                if (docsArry[i].document_verification_status == 'Pending' || docsArry[i]
                    .document_verification_status == 'Rejected') {
                    check_doc_verified = 'no_verified';
                    break;
                }
            }
            if (documents_verication == 1 && check_doc_verified == 'no_verified' && admCurrentStat !=
                'Offer Released' && admCurrentStat != 'Student added to ERP') {
                $('#admStatusshow').show();
                $('#admReleaseshow').hide();
                $('#admStatusshow').html(
                    "<h3>Can't Release Offer. There are one or more documents pending verification.</h3>"
                    );

            } else {
                $('#admStatusshow').hide();
                $('#admReleaseshow').show();
                get_release_offer_details();
            }
        }
    });
}

function enquiry_data() {
    $('#title').html('Enquiry data');
    $('#printIcon').hide();
    $('#follow_up').hide();
    $('#attached_documents').hide();
    $('#view_details').hide();
    $('#fees_collection').hide();
    $('#printArea').hide();
    $('#print_btn').hide();
    $('#seat_allotment').hide();
    $('#previous_school_details').hide();
    $('#moveToErp').hide();
    $('#joining_forms_card').hide();
    $('#edit_history_data').hide();
    $('#closeApplication').hide();
}

function admission_confirmed() {
    $('#title').html(' Move to Admission Confirmed ');
    $('#printIcon').hide();
    $('#follow_up').hide();
    $('#attached_documents').hide();
    $('#view_details').hide();
    $('#fees_collection').hide();
    $('#printArea').hide();
    $('#print_btn').hide();
    $('#seat_allotment').hide();
    $('#moveToErp').show();
    $('#joining_forms_card').hide();
    $('#edit_history_data').hide();
    $('#previous_school_details').hide();
    $('#closeApplication').hide();
    get_admission_movetoerpStatus();
}

function close_application() {
    $('#title').html('Close Application');
    $('#printIcon').hide();
    $('#follow_up').hide();
    $('#attached_documents').hide();
    $('#view_details').hide();
    $('#fees_collection').hide();
    $('#printArea').hide();
    $('#print_btn').hide();
    $('#seat_allotment').hide();
    $('#moveToErp').hide();
    $('#joining_forms_card').hide();
    $('#edit_history_data').hide();
    $('#previous_school_details').hide();
    $('#closeApplication').show();
    var curr_status = $('#currentStatus').html();
    if (curr_status == 'Closed-not interested') {
        $('#closeApplication').html('<h3 class="no-data-display">This Application is Closed.</h3>');
    }
    var admission_id = '<?php echo $afId ?>';
    $.ajax({
        url: '<?php echo site_url('admission_flow/get_fees_assigned_details'); ?>',
        type: 'post',
        data: {
            'admission_id': admission_id
        },
        success: function(data) {
            var resdata = $.parseJSON(data);
            if ((resdata.credentials == '' || resdata.credentials == null) && (resdata.feeDetails == '' ||
                    resdata.feeDetails == null)) {
                $('#closer_submit_btn').removeAttr('disabled');
            }
            if (resdata.credentials != '' && resdata.credentials != null) {
                $('#credentials_confirmation').html(construct_credential_table(resdata.credentials));
            } else {
                $('#credentials_confirmation').html('<b style="color:green">Yes</b>');
            }
            if (resdata.feeDetails != '' && resdata.feeDetails != null) {
                $('#fees_confirmation').html(construct_fees_assigned_data(resdata.feeDetails, resdata
                    .student));
            } else {
                $('#fees_confirmation').html('<b style="color:green">Yes</b>');
            }


        }
    });
}

function construct_fees_assigned_data(feeDetails, student_id) {

    var url = '<?php echo site_url('feesv2/fees_collection/fee_student_blueprints_v1/') ?>' + student_id;
    var val = '<p>Un-assign the fees to close this application</p>';
    var authorised = '<?php echo $this->authorization->isAuthorized('FEESV2.COLLECT_FEES') ?>';
    if (authorised) {
        val = ` To Un-assign <b><a href="${url}" target="_blank"> Click here</a></b>`;
    }

    var html = '';
    html += `<b style="color:red">No </b> . ${val}`;

    return html;
}

function construct_credential_table(credentials) {
    var val = '<p>Deactivate the credentials to close this application</p>';
    var authorised = '<?php echo $this->authorization->isAuthorized('STUDENT.PROVISION_PARENTS_CREDENTIALS') ?>';
    for (var i in credentials) {
        var url = '<?php echo site_url('parent_activation/student_provisionbyid/') ?>' + credentials[i]
            .std_id;
    }
    if (authorised) {
        val = ` To Deactivate Credentials <b><a href="${url}" target="_blank"> Click here</a></b>`;
    }

    var html = '';
    html += `<b style="color:red">No </b> . ${val}`;
    return html;
}

function joining_forms() {
    $('#title').html(' Joining Forms ');
    $('#printIcon').hide();
    $('#follow_up').hide();
    $('#attached_documents').hide();
    $('#view_details').hide();
    $('#fees_collection').hide();
    $('#printArea').hide();
    $('#print_btn').hide();
    $('#seat_allotment').hide();
    $('#moveToErp').hide();
    $('#joining_forms_card').show();
    $('#edit_history_data').hide();
    $('#previous_school_details').hide();
    $('#closeApplication').hide();
    display_joining_form_status();
}

function edit_history() {
    $('#title').html('History');
    $('#printIcon').hide();
    $('#follow_up').hide();
    $('#attached_documents').hide();
    $('#view_details').hide();
    $('#fees_collection').hide();
    $('#printArea').hide();
    $('#print_btn').hide();
    $('#seat_allotment').hide();
    $('#moveToErp').hide();
    $('#joining_forms_card').hide();
    $('#edit_history_data').show();
    $('#previous_school_details').hide();
    $('#closeApplication').hide();
    get_edit_history_tracking_data();

}

function get_edit_history_tracking_data() {
    var afId = '<?php echo $afId ?>';

    $.ajax({
        url: '<?php echo site_url('admission_process/get_edit_history_tracking_data'); ?>',
        type: 'post',
        data: {
            'afId': afId
        },
        success: function(data) {
            var data = $.parseJSON(data);
            $('#edit_history_table').html(_contruct_edit_history(data.edit_history, data.followup_history));
        }
    });

}

function _contruct_edit_history(edited_data, followup_history) {
    var html = '';
    if (edited_data == '' && followup_history == '') {
        html += '<h4 class="no-data-display"><center>No Changes done</center></h4>';
    } else {
        html += `
                    <table id="" class="table table-bordered" >
                        <thead>
                          <tr>
                          <th width="5%">#</th>
                          <th width="10%">Changed on</th>
                          <th width="10%">Tab</th>
                          <th width="10%">Field</th>
                          <th width="10%">Old data</th>
                          <th width="10%">New Data</th>
                          <th width="10%">Changed By</th>
                          </tr>
                        </thead>
                    `;
        html += `<tbody>`;
        var i = 0;
        for (i = 0; i < edited_data.length; i++) {
            var data = edited_data[i];

            var old_data = data['old_data'];
            var new_data = data['new_data'];
            if (data['column_name'] == 'student_photo' || data['column_name'] == 'father_sign' || data['column_name'] ==
                'guardian_photo') {
                old_data = '<img  style="width:50px; height:50px;" src="' + data['old_data'] + '" alt="Avatar">';
                new_data = '<img  style="width:50px; height:50px;" src="' + data['new_data'] + '" alt="Avatar">';
            }

            if (data['old_data'] != '' && (data['column_name'] == 'Document Added' || data['column_name'] ==
                    'Document Re Uploaded')) {
                old_data =
                    `<a href="${data['old_data']}" class="btn btn-secondary" target="_blank">View <i class="fa fa-eye"></i></a>`;
            }

            if (data['new_data'] != '' && (data['column_name'] == 'Document Added' || data['column_name'] ==
                    'Document Re Uploaded')) {
                new_data =
                    `<a href="${data['new_data']}" class="btn btn-secondary" target="_blank">View <i class="fa fa-eye"></i></a>`;
            }

            switch (data['column_name']) {
                case 'gender':
                    if (new_data == 'M') {
                        new_data = 'Male';
                    } else {
                        new_data = 'Female';
                    }
                    break;
                case 'boarding':
                    if (new_data == '1') {
                        new_data = 'Day Scholar';
                    } else if (new_data == '2') {
                        new_data = 'Regular Boarder';
                    } else {
                        new_data = 'Weekly Boarder';
                    }
                    break;
                case 'dob':
                    new_data = moment(new_data).format('DD-MM-YYYY');
                    break;
                case 'physical_disability':
                    if (new_data == 'N') {
                        new_data = 'No';
                    } else {
                        new_data = 'Yes';
                    }
                    break;
                case 'learning_disability':
                    if (new_data == 'N') {
                        new_data = 'No';
                    } else {
                        new_data = 'Yes';
                    }
                    break;
                default:
                    break;
            }

            html += `
                          <tr>
                          <td>${i+1}</td>
                          <td>${data['edited_on']}</td>
                          <td>${data['tab_name']}</td>
                          <td>${data['column_name']}</td>
                          <td>${old_data}</td>
                          <td>${new_data}</td>
                          <td>${data['edited_by']}</td>
                          </tr>`;
        }
        // if(i == 0) i=1;
        for (var j = 0; j < followup_history.length; j++) {
            var data = followup_history[j];

            html += `
                          <tr>
                          <td>${i+1}</td>
                          <td>${data['edited_on']}</td>
                          <td>Follow Up</td>
                          <td>Status</td>
                          <td>-</td>
                          <td>${data['status']}</td>
                          <td>${data['edited_by']}</td>
                          </tr>`;
            i++;
        }
    }
    html += `</tbody>
                    </table>
				`;

    return html;

}

function display_joining_form_status() {
    var afId = '<?php echo $afId ?>';

    $.ajax({
            url: '<?php echo site_url('admission_process/get_joining_forms_status'); ?>',
            type: 'POST',
            data: {
                'afId': afId
            },
        })
        .success(function(data) {
            var data = $.parseJSON(data);
            // console.log(data);
            if (data.length > 0) {
                $('#joining_forms_div').html(_construct_joining_forms_table(data));
            } else {
                $('#joining_forms_div').html(`<div class="d-flex align-items-center justify-content-center">
                        <div class="text-center p-4">
                            <span class="animate__animated animate__fadeIn d-block mb-4">
                                <div class="icon-container" style="margin: 0 auto;width:40%">
                                    <?php $this->load->view('svg_icons/offer_not_released_icon.svg') ?>
                                </div>
                            </span>
                            <h3 class="fw-bold mb-2"><b>Offer not released</b></h3>
                            <span class="text-muted" style="font-size: 14px;">Offer hasn't been released yet. Once it's available, you'll be able to view all related details.</span>
                        </div>
                    </div>`);
            }
        });
}

function _construct_joining_forms_table(documents) {
    if (documents.lenght == 0) {
        return `No Joining Documents`;
    }

    var output = `
        <table class="table table-bordered">
          <thead>
            <tr>
              <th style="width: 4%;">#</th>
              <th style="width: 20%;">Form Name</th>
              <th style="width: 16%;">Uploaded On</th>
              <th style="width: 18%;">Download</th>
              <th style="width: 16%;">Status</th>
              <th style="width: 16%;">Upload</th>
            </tr>
          </thead>
          <tbody>
      `;

    documents.forEach((obj, i) => {
        var consent_form_path = `NA`;
        var delete_button = '';
        if (obj.consent_form_path && obj.status == 'Submitted' && obj.fromtemplate != 0) {
            consent_form_path =
                `<a class="btn btn-info" data_placement="top" data-toggle="tooltip" data-original-title="Download" href="${obj.consent_form_path}" download target="_blank">Download <i class="fa fa-cloud-download"></i></a>`;
            // if(obj.avatar_id == 4)
            delete_button =
                `<a style="margin-top: 1rem;" onclick="deletedocument_row_parent(${obj.submission_id}, ${obj.id}, ${i+1},${obj.email_template_id})" id="removeButtonId" class="remove btn btn-danger  btn-sm"><i class="fa fa-trash-o"></i></a>`;
        }

        if (obj.submitted_on && obj.status == 'Submitted') {
            var date = new Date(obj.submitted_on).toString().split(" ").splice(0, 4);
            var month = date.at(1);
            var year = date.at(3);
            var day = date.at(2);
            var submitted_on = `${day} ${month}, ${year}`;
        }
        // console.log(obj);
        output += `
          <tr>
            <td>${i+1}</td>
            <td>${obj.template_name}</td>
            <td id="submit${i+1}">${submitted_on || 'NA'}</td>
            <td id="download${i+1}">${consent_form_path} ${delete_button}</td>`;
        // <td id="status${i+1}">${obj.status}</td>

        if (obj.consent_mode != 'physical_signature' && obj.agree_disagree == 'Agreed') {
            output += `<td id="status${i+1}">Submitted <br>[<span style="color: green;"> Agreed</span> ]</td>`;
        } else if (obj.consent_mode != 'physical_signature' && obj.agree_disagree == 'Not agreed') {
            output +=
            `<td id="status${i+1}">Submitted <br>[<span style="color: red;"> Not Agreed</span> ]</td>`;
        } else {
            output += `<td id="status${i+1}">${obj.status}</td>`;
        }

        if (obj.status == 'Not Submitted')
            output += `
            <td id="upload${i+1}">
              <input type="file" onchange="upload_document_file_path_staff(${i+1}, ${obj.id}, ${obj.submission_id},${obj.email_template_id})" name="document_file_path" class="documentUpload" id="doc-upload${i+1}" accept="application/pdf"/>
                <div id="afterSuccessUploadShow${i+1}">
                </div>
                <span id="percentage_doc_completed${i+1}" style="font-size: 20px; display: none;">0 %</span>
            </td>
          </tr>`;

        else
            output += `
            <td id="upload${i+1}">NA</td>
          </tr>`;
    });

    output += `
          </tbody>
        </table>    
        `;

    return output;
}

function deletedocument_row_parent(submisssion_id, template_id, i, email_template_id) {

    bootbox.prompt({
        title: 'Reason for form deletion',
        inputType: 'text',
        placeholder: 'Enter Reasons',
        buttons: {
            confirm: {
                label: 'Save',
                className: 'btn-success'
            }

        },
        callback: function(result) {
            if (result) {
                $.ajax({
                    url: '<?php echo site_url('admission_process/deletedocument_row_parent'); ?>',
                    type: 'post',
                    data: {
                        'submisssion_id': submisssion_id,
                        'reason_of_rejection': result
                    },
                    success: function(data) {
                        var upload_input =
                            `<input type="file" onchange="upload_document_file_path_staff(${i}, ${template_id}, ${submisssion_id},${email_template_id})" name="document_file_path" class="documentUpload" id="doc-upload${i}" accept="application/pdf"/>
                                <div id="afterSuccessUploadShow${i}">
                                </div>
                                <span id="percentage_doc_completed${i}" style="font-size: 20px; display: none;">0 %</span>`;

                        $("#submit" + i).text(`NA`);
                        $("#download" + i).text('NA');

                        $("#status" + i).text(`Not Submitted`);
                        $("#upload" + i).html(upload_input);
                    }
                });
            }
        }
    }).find("div.modal-dialog").addClass("dialog_width");

}

// anish
function upload_document_file_path_staff(i, document_for, submission_id, email_template_id) {
    if (email_template_id) {
        bootbox.confirm({
            title: "Upload",
            message: "Once you upload email will be send to parent.Do you want to continue..?",
            className: "medium",
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-success'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-danger'
                }
            },
            callback: function(result) {
                if (result) {
                    var file = $('#doc-upload' + i).prop('files')[0];
                    completed_promises = 0;
                    current_percentage = 0;
                    total_promises = 1;
                    in_progress_promises = total_promises;
                    saveFileToStorage_staff(file, document_for, i, submission_id);
                }
            }
        });
    } else {
        var file = event.target.files[0];
        var file1 = $('#doc-upload' + i).prop('files')[0];
        completed_promises = 0;
        current_percentage = 0;
        total_promises = 1;
        in_progress_promises = total_promises;
        saveFileToStorage_staff(file, document_for, i, submission_id);
    }
}

function saveFileToStorage_staff(file, document_for, i, submission_id) {
    $('#percentage_doc_completed' + i).show();
    $('#doc-upload' + i).attr('disabled', 'disabled');
    $("#document_submit").prop('disabled', true);
    $.ajax({
        url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {
            'filename': file.name,
            'file_type': file.type,
            'folder': 'consent_form'
        },
        success: function(response) {
            single_file_progress_staff(0);

            var response = JSON.parse(response);
            var path = response.path;
            var signedUrl = response.signedUrl;

            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type,
                    "x-amz-acl": "public-read"
                },
                processData: false,
                data: file,
                xhr: function() {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function(e) {
                        // For uploads
                        if (e.lengthComputable) {
                            single_file_progress_staff(e.loaded / e.total * 100 | 0, i);
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    // return false;
                    $('#percentage_doc_completed' + i).hide();
                    save_form_url_staff(path, document_for, i, submission_id);
                    $('#doc-upload' + i).removeAttr('disabled');
                    // $('.file-preview').css('opacity', '1');
                    $("#document_submit").prop('disabled', false);

                },
                error: function(err) {
                    reject(err);
                }
            });
        },
        error: function(err) {
            reject(err);
        }
    });

}

function single_file_progress_staff(percentage, i) {
    if (percentage == 100) {
        in_progress_promises--;
        if (in_progress_promises == 0) {
            current_percentage = percentage;
        }
    } else {
        if (current_percentage < percentage) {
            current_percentage = percentage;
        }
    }
    $("#percentage_doc_completed" + i).html(`${current_percentage} %`);
    return false;
}

function save_form_url_staff(path, document_for, i, submission_id) {
    var af_id = '<?php echo $afId ?>';
    $.ajax({
        url: '<?php echo site_url('admission_process/upload_joining_form_by_staff'); ?>',
        type: 'post',
        data: {
            'path': path,
            'document_for': document_for,
            'af_id': af_id
        },
        success: function(data) {
            // console.log(data);
            var url = data.trim();
            // var link= 
            if (data != '') {
                $('#doc-upload' + i).attr('disabled', 'disabled');
                var d = new Date().toLocaleDateString('en-us', {
                    day: "numeric",
                    month: "short",
                    year: "numeric"
                });
                $("#submit" + i).text(`${d}`);
                $("#download" + i).html(
                    `<a class="btn btn-info" data_placement="top" data-toggle="tooltip" data-original-title="Download" href="${url}" download target="_blank">Download <i class="fa fa-cloud-download"></i></a>   <a style="margin-top: 1rem;" onclick="deletedocument_row_parent(${submission_id}, ${document_for}, ${i})" id="" class="remove btn btn-danger  btn-sm"><i class="fa fa-trash-o"></i></a>`
                    );

                $("#status" + i).text(`Submitted`);
                $("#upload" + i).text(`NA`);



            }

        }
    });
}

function change_followup_action() {
    var followup_action = $('#followup_action').val();
    if (followup_action === 'Email') {
        $('#email').show();
        $('#sms').hide();
        $('#email_content').html('');
        $('#sms_content').html('');
    } else if (followup_action === 'SMS') {
        $('#email').hide();
        $('#sms').show();
        $('#email_content').html('');
        $('#sms_content').html('');
    } else {
        $('#email').hide();
        $('#sms').hide();
        $('#email_content').html('');
        $('#sms_content').html('');
    }
}

function get_templ_content_and_forms_email() {
    var emailtemplateId = $('#emailtemplateId').val();
    if (emailtemplateId == '') {
        $('#email_content').html('');
    }

    var afId = '<?php echo $afId ?>';
    $.ajax({
            url: '<?php echo site_url('admission_process/get_admission_email_content'); ?>',
            type: 'POST',
            data: {
                'afId': afId,
                'emailtemplateId': emailtemplateId
            },
        })
        .done(function(data) {
            // console.log(data);
            if (data == 0) {
                $('#email_content').html('');
                return false;
            }
            var reData = $.parseJSON(data);
            construct_emails(reData, afId);
        });
}

function get_admission_movetoerpStatus() {
    var afId = '<?php echo $afId ?>';
    $.ajax({
            url: '<?php echo site_url('admission_process/get_admission_movetoerp_status'); ?>',
            type: 'POST',
            data: {
                'afId': afId
            },
        })
        .done(function(data) {
            var resStatus = $.parseJSON(data);
            $('#ApplicationStatus').html(resStatus.application_payment_status);
            $('#feesStatus').html(resStatus.onetime_fee_status);
            $('#admissionStatus').html(resStatus.application_current_status);
            if (resStatus.application_current_status == 'Student added to ERP') {
                // $('#confirmMovetErp').html('Successfully Move to ERP');
                // $("#confirmMovetErp").prop("onclick", null).off("click");
                $("#confirmMovetErp").css("display", 'none');
                $("#currentStatus").html(resStatus.application_current_status);
            }
            if (resStatus.onetime_fee_status == 'COLLECTED') {
                $('#confirmMovetErp').removeAttr('disabled');
                $('#admissionStudentId').val(resStatus.student_id);
            } else {
                $('#confirmMovetErp').attr('disabled', 'disabled');
            }
            get_update_button_colors();
        });
}

function confirmtMovetoERP() {
    var afId = '<?php echo $afId ?>';
    var studentAdmissionId = $('#admissionStudentId').val();
    bootbox.confirm({
        title: "Confirm",
        message: "Admission confirmed. Do you want to continue?",
        className: "medium",
        buttons: {
            confirm: {
                label: 'Yes',
                className: 'btn-success'
            },
            cancel: {
                label: 'No',
                className: 'btn-danger'
            }
        },
        callback: function(result) {
            if (result) {
                // Disable button and change text
                $("#confirmMovetErp").html('Please wait...');
                $("#confirmMovetErp").attr('disabled', 'disabled');
                
                $.ajax({
                    url: '<?php echo site_url('admission_flow/confirm_admission_student'); ?>',
                    type: 'post',
                    data: {
                        'studentAdmissionId': studentAdmissionId,
                        'afId': afId
                    },
                    success: function(data) {
                        get_admission_movetoerpStatus();
                    },
                    error: function() {
                        // Re-enable button and restore text in case of error
                        $("#confirmMovetErp").removeAttr('disabled');
                        $("#confirmMovetErp").html('Confirm');
                    }
                });
            }
        }
    });
}

function construct_emails(reData, afId) {

    var emailSubject = reData.email_subject;
    if (reData.email_subject == undefined)
        emailSubject = 'Email subject not added';

    var registered_email = reData.registered_email;
    if (reData.registered_email == undefined)
        registered_email = 'From email not assigned';
    var content = reData.content;
    content = content.replace('%%student_name%%', reData.to_email.student_name);
    // console.log(content);
    var display = 'block';
    if (reData.content == undefined)
        display = 'none';

    var html = '';
    html += '<div class="col-md-12" style="margin-top: 10px;">';
    html += '<div class="form-group">';
    html += '<label class="col-md-3 control-label">From: </label>';
    html += '<div class="col-md-9">';
    html += '<input readonly type="text" value="' + registered_email +
        '" name="registered_email" id="registered_email" class="form-control">';
    html += '</div>';
    html += '</div>';

    html += '<div class="form-group">';
    html += '<label class="col-md-3 control-label">To: </label>';
    html += '<div class="col-md-9">';
    html += '<input type="text" name="to_mails" id="to_emails" class="form-control" value="' + reData.to_email.fmail +
        ',' + reData.to_email.mMail + '">';
    html +=
        '<span class="help-block">To send email to multiple recipients, enter their email ids as a comma-separated list. Eg: <EMAIL>, <EMAIL></span>';
    html += '</div>';
    html += '</div>';

    html += '<div class="form-group">';
    html += '<label class="col-md-3 control-label">Subject: </label>';
    html += '<div class="col-md-9" style="margin-bottom:12px;">';
    html += '<input type="text" value="' + emailSubject +
        '" name="email_subject" id="email_subject" class="form-control">';
    html += '</div>';
    html += '</div>';
    html += '<div class="form-group" style="display:' + display + '">';
    html += '<label class="col-md-3 control-label">Message: </label>';
    html += '<div class="col-md-9">';
    html += '<textarea name="template_content" id="template_content" class="summernote template">' + content +
        '</textarea>';
    html += '</div>';
    html += '</div>';
    html += '</div>';
    $('#email_content').html('');
    $('#email_content').html(html);
    $('.template').summernote({
        tabsize: 2,
        height: 200
    });
}

function get_templ_content_and_forms_sms() {
    var smstemplateId = $('#smstemplateId').val();

    var afId = '<?php echo $afId ?>';
    $.ajax({
            url: '<?php echo site_url('admission_process/get_admission_sms_content'); ?>',
            type: 'POST',
            data: {
                'afId': afId,
                'smstemplateId': smstemplateId
            },
        })
        .done(function(data) {
            // console.log(data);
            if (data == 0) {
                $('#sms_content').html('');
                return false;
            }
            var reData = $.parseJSON(data);
            construct_sms(reData, afId);
        });
}

function construct_sms(reData, afId) {
    var sms_content = reData.content;
    sms_content = sms_content.replace('%%student_name%%', reData.to_email.student_name);

    // console.log(content);
    var sms = '';
    sms += '<input  type="hidden" value="' + afId + '" name="enquiry_id" id="enquiry_id" class="form-control">';

    sms += '<div class="col-md-12">';
    sms += '<div class="form-group">';
    sms += '<label class="col-md-3 control-label">Message : </label>';
    sms += '<div class="col-md-9" style="margin-bottom:12px;">';
    sms += '<textarea name="template_content" id="template_content" class="form-control">' + sms_content +
    '</textarea>';
    sms += '</div>';
    sms += '</div>';
    sms += '</div>';
    $('#sms_content').html('');
    $('#sms_content').html(sms);
}

function terms_check(e) {
    confirmations = "Are you sure you want to collect fee for the application?";
    $('#Error').hide();
    $('#terms').removeAttr('required');
    bootbox.dialog({
        title: "Confirm",
        message: confirmations,

        className: "medium",
        buttons: {
            cancel: {
                label: "Cancel",
                className: 'btn-danger cls',
                callback: function() {
                    // url = '<?php // echo site_url('admissions/instructions') ?>';
                    // $('#created-form').attr('action',url);
                    // $('#created-form').submit();
                    // return false;
                }
            },
            ok: {
                label: "Yes",
                className: 'btn-info',
                callback: function() {
                    var $form = $('#collect_fee-form');
                    if ($form.parsley().validate()) {
                        var form = $('#collect_fee-form')[0];
                        var formData = new FormData(form);
                        $.ajax({
                            url: '<?php echo site_url('Admission_process/collect_offline_fee_for_application'); ?>',
                            type: 'post',
                            data: formData,
                            processData: false,
                            contentType: false,
                            success: function(data) {
                                location.reload();
                            }
                        });
                    }

                    // url = '<?php echo site_url('admission_process/collect_offline_fee_for_application') ?>';
                    // $('#collect_fee-form').attr('action',url);
                    // $('#collect_fee-form').submit();
                }
            }
        },
        complete: function() {
            payment_details();
        }
    });

}

function email_sms_popup_details(follow_up_type, follow_up_action, source_id) {
    $.ajax({
        url: '<?php echo site_url('admission_process/get_email_sms_pop_details'); ?>',
        data: {
            'follow_up_type': follow_up_type,
            'follow_up_action': follow_up_action,
            'source_id': source_id
        },
        type: "post",
        success: function(data) {
            var data = JSON.parse(data);
            // console.log(data);
            $('#dynamic-content').html(construct_table(data));
        },
        error: function(err) {
            console.log(err);
        }
    });
}

function construct_table(data) {
    var html = '';
    html += '<tr>';
    html += '<td><strong>Follow up action: </strong>' + data.follow_up_action + '</td>';
    html += '</tr>';
    if (data.follow_up_action === 'Email') {
        html += '<tr>';
        html += '<td><strong>Email Ids: </strong>' + data.email_ids + '</td>';
        html += '</tr>';
        html += '<tr>';
        html += '<td><strong>Email Subject: </strong>' + data.email_subject + '</td>';
        html += '</tr>';
    }
    if (data.follow_up_action === 'Email' || data.follow_up_action === 'SMS') {
        // html+='<tr>';
        // html+='<td><strong>Template Name: </strong>'+data.template_name+'</td>';
        // html+='</tr>';
        html += '<tr>';
        html += '<td><strong>Message: </strong>' + data.template_content + '</td>';
        html += '</tr>';
    }
    html += '<tr>';
    if (data.remarks != null) {
        html += '<td><strong>Remarks: </strong>' + data.remarks + '</td>';
        html += '</tr>';
    }

    return html;
}

function actionTabs(e) {
    if (e == 0) {
        $('#followup_form').show();
        $('#followup_history').hide();
        $('#followForm').addClass('active');
        $('#followhistory').removeClass('active');

        $('#faAction1').addClass('fa-angle-down');
        $('#faAction2').removeClass('fa-angle-down');
        $('#faAction2').addClass('fa-angle-up');
        get_admission_next_status();
    } else {
        get_admission_followup_details();
        $('#followup_form').hide();
        $('#followup_history').show();
        $('#followhistory').addClass('active');
        $('#followForm').removeClass('active');

        $('#faAction2').addClass('fa-angle-down');
        $('#faAction1').removeClass('fa-angle-down');
        $('#faAction1').addClass('fa-angle-up');
    }

}

function get_admission_next_status() {
    var curr_status = $('#currentStatus').html();
    $.ajax({
        url: '<?php echo site_url('admission_process/get_admission_next_status') ?>',
        type: 'post',
        data: {
            'curr_status': curr_status
        },
        success: function(result) {
            var res = JSON.parse(result);
            if (res.status_next == null || res.status_next == '') {
                return;
            }
            var statusArray = [];
            if (res.status_next) {
                try {
                    statusArray = JSON.parse(res.status_next); // Convert string to array
                } catch (e) {
                    console.error("Error parsing status_next:", e);
                    return;
                }
            }
            if (!Array.isArray(statusArray)) {
                console.error("Invalid response format. Expected an array.");
                return;
            }
            console.log(statusArray)
            let options = document.querySelectorAll("#FollowupStatus option");

            options.forEach(option => {
                if (statusArray.includes(option.value)) {
                    option.hidden = false; // Show allowed options
                } else {
                    option.hidden = true; // Hide others
                }
            });
        }
    });
}

function print_voucher() {
    $('.col-md-6').css('width', '50%');
    $('.col-md-6').css('float', 'left');
    var restorepage = document.body.innerHTML;
    var printcontent = document.getElementById('printArea').innerHTML;
    document.body.innerHTML = printcontent;
    window.print();
    document.body.innerHTML = restorepage;
}

function printPerf() {
    // Get the content of the element to print
    var printContents = document.getElementById('printArea').innerHTML;

    // Open a new window for printing
    var printWindow = window.open('', '_blank', 'height=600,width=800');

    // Write the content and additional styles into the new window
    printWindow.document.write('<html><head><title>Print</title>');

    // Include any global stylesheets that affect the layout
    printWindow.document.write('<link rel="stylesheet" href="path_to_your_stylesheet.css" type="text/css" />');

    // Additional inline styles for printing
    printWindow.document.write('<style>');
    printWindow.document.write('@media print {');

    // Ensure columns have a border, padding, and proper spacing
    printWindow.document.write(
        '.col-md-6 { width: 50%; float: left; border: 1px solid black; padding: 10px; box-sizing: border-box; margin-bottom: 15px; }'
        );

    // Add a border for the logo and spacing around it
    printWindow.document.write(
        '.logo { border: 1px solid black; padding: 5px; display: inline-block; margin-bottom: 20px; }');

    // In case the logo is an image element, target it directly with spacing
    printWindow.document.write(
        'img.logo { border: 1px solid black; padding: 5px; display: block; max-width: 100%; height: auto; margin-bottom: 20px; }'
        );

    // Line height and spacing for text elements
    printWindow.document.write('p, div, span { line-height: 1.5; margin-bottom: 15px; }');

    // Add general spacing around body content
    printWindow.document.write('body { margin: 0; padding: 20px; }');

    printWindow.document.write('</style>');

    printWindow.document.write('</head><body>');
    printWindow.document.write(printContents);
    printWindow.document.write('</body></html>');

    // Close the document and trigger print
    printWindow.document.close();

    // After the content is fully loaded, focus the window and trigger printing
    printWindow.onload = function() {
        printWindow.focus(); // Focus on the print window
        printWindow.print(); // Open the print dialog
        printWindow.close(); // Close the print window after printing
    };
}

function toTitleCase(str) {
    return str.toLowerCase().replace(/\b\w/g, (char) => char.toUpperCase());
}


function edit_each_row_student_data(column) {
    // console.log(column);
    $('#save_get_column_value').val(column);

    var get_data = event.currentTarget.dataset
    $('#old_data').val(get_data[column]);
    // console.log(get_data);
    // var colum_name = get_data['labe_name'].replace('_',' ');


    let columnMapping = {
        "std name": "student name",
        "f name": "father name",
        "m name": "mother name",
        "addr": "address",
        "gros": "gross",
        "county": "country",
        "g name": "guardian name"
    };
    var colum_name = get_data['labe_name'].replaceAll('_', ' ');
    colum_name = toTitleCase(columnMapping[colum_name] || colum_name);

    $('#edit_columnName').html(colum_name);
    if (column == 'grade_applied_for' || column == 'gender' || column == 'nationality' || column ==
        'student_blood_group' || column == 'religion' || column == 'category' ||
        column == 'physical_disability' || column == 'learning_disability' || column ==
        'curriculum_currently_studying' || column == 'esl_english_as_second_language' || column ==
        'second_language_currently_studying' || column == 'special_needs_description' || column == 'boarding' ||
        column == 's_present_country' || column == 'm_county' || column == 'f_county' || column == 'm_company_county' ||
        column == 'f_company_county' || column == 'joining_period' || column == 'student_quota' || column ==
        'has_sibling' || column == 'stream' || column == 'combination') {
        $('#edit_student_form_group').html(construct_dropdown(column, get_data));
    } else if (column == 'dob' || column == 'f_dob' || column == 'm_dob') {
        $('#edit_student_form_group').html(construct_date(column, get_data));
    } else {
        $('#edit_student_form_group').html(construct_text(column, get_data));
    }

}

function construct_text(column, targetdata) {
    var textvalue = targetdata[column] !== undefined ? targetdata[column] : '';
    var required = column === 'student_middle_name' ? '' : 'required';

    // Define the mapping for replacement
    let columnMapping = {
        "std name": "student name",
        "f name": "father name",
        "m name": "mother name",
        "addr": "address",
        "gros": "gross",
        "county": "country",
        "g name": "guardian name"
    };

    // Ensure `labe_name` exists and process it
    var labelReplace = targetdata['labe_name'] ? targetdata['labe_name'].replaceAll('_', ' ') : '';

    // Replace the label if it exists in the mapping
    labelReplace = columnMapping[labelReplace.toLowerCase()] || labelReplace;

    // Convert label to title case
    labelReplace = toTitleCase(labelReplace);

    // Construct the HTML
    var html = ` 
        <div class="form-group">
            <label class="control-label col-md-3">${labelReplace}</label>
            <div class="col-md-6">
                <input type="text" ${required} class="form-control" value="${textvalue}" 
                    name="${targetdata['input_name']}" placeholder="${targetdata['input_name']}" id="${column}">
            </div>
        </div>`;

    return html;
}

function construct_date(column, targetdata) {
    var date = moment(targetdata[column], 'DD-MM-YYYY');
    var dateValue = date.format('YYYY-MM-DD');
    var labelReplace = targetdata['labe_name'].replace('_', ' ');
    var html = '';
    html += `<div class="form-group">
            <label class="col-md-3 control-label">${labelReplace}</label>
            <div class="col-md-8">
              <div class='input-group date'>
                <input value="${dateValue}" type='date' id='${column}' name="${targetdata['input_name']}" class="form-control" />
              </div>
            </div>
          </div>`;
    return html;
}

function construct_dropdown(column, targetdata) {
    var labelReplace = targetdata['labe_name'].replace('_', ' ');
    if (column == 'gender') {
        var html = `<div class="form-group">
                  <label class="control-label col-md-3">${labelReplace}</label>
                  <div class="col-md-8">
                      <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                          <option value="">Select Gender</option>`;
        var mselected = '';
        if (targetdata[column] == 'Male') {
            mselected = 'selected';
        } else {
            mselected = '';
        }
        var fselected = '';
        if (targetdata[column] == 'Female') {
            fselected = 'selected';
        } else {
            fselected = '';
        }
        html += '<option ' + mselected + ' value="M">Male</option>';
        html += '<option ' + fselected + ' value="F">Female</option>'
        html += `</select>
                  </div>
              </div>`;

        return html;
    }
    if (column == 'has_sibling') {
        var html = `<div class="form-group">
                  <label class="control-label col-md-3">${labelReplace}</label>
                  <div class="col-md-8">
                      <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                          <option value="">Select</option>`;
        var yesselected = '';
        if (targetdata[column] == 'Yes') {
            yesselected = 'selected';
        } else {
            yesselected = '';
        }
        var noselected = '';
        if (targetdata[column] == 'No') {
            noselected = 'selected';
        } else {
            noselected = '';
        }
        html += '<option ' + yesselected + ' value="1">Yes</option>';
        html += '<option ' + noselected + ' value="0">No</option>'
        html += `</select>
                  </div>
              </div>`;

        return html;
    }
    var nationalityItem = '<?php echo json_encode($this->config->item('nationality')) ?>';
    var bloodGroupItem = '<?php echo json_encode($this->config->item('blood_groups')) ?>';

    var religionItem = '<?php echo $this->settings->getSetting('religion') ?>';
    if (religionItem == '') {
        var religionItem = '<?php echo json_encode($this->config->item('religions')) ?>';
    }
    var category = '<?php echo json_encode($this->settings->getSetting('category')) ?>';
    var boarding = '<?php echo json_encode($this->settings->getSetting('boarding')) ?>';
    var country = '<?php echo json_encode($this->config->item('country')) ?>';
    var studentQuota = '<?php echo json_encode($this->settings->getSetting('quota')) ?>';

    var grade_list = '<?php echo json_encode($grade_list) ?>';
    var grades = $.parseJSON(grade_list);
    var nationality = $.parseJSON(nationalityItem);
    var bloodgroups = $.parseJSON(bloodGroupItem);
    var religions = JSON.parse(religionItem);
    var category = $.parseJSON(category);
    var boardingList = $.parseJSON(boarding);
    var countryList = $.parseJSON(country);
    var student_quota = $.parseJSON(studentQuota);
    if (column == 'nationality') {
        var html = `<div class="form-group">
                  <label class="control-label col-md-3">${labelReplace}</label>
                  <div class="col-md-8">
                      <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                          <option value="">Select Nationality</option>`;
        for (var n = 0; n < nationality.length; n++) {
            var nationalitySelected = '';
            if (targetdata[column] == nationality[n]) {
                nationalitySelected = 'selected';
            } else {
                nationalitySelected = '';
            }
            html += '<option ' + nationalitySelected + ' value="' + nationality[n] + '">' + nationality[n] +
            '</option>';
        }
        html += `</select>
                  </div>
              </div>`;

        return html;
    }

    if (column == 's_present_country' || column == 'm_county' || column == 'f_county' || column == 'm_company_county' ||
        column == 'f_company_county') {
        var html = `<div class="form-group">
                  <label class="control-label col-md-3">${labelReplace}</label>
                  <div class="col-md-8">
                      <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                          <option value="">Select Nationality</option>`;
        for (var n = 0; n < countryList.length; n++) {
            var countrySelected = '';
            if (targetdata[column] == countryList[n]) {
                countrySelected = 'selected';
            } else {
                countrySelected = '';
            }
            html += '<option ' + countrySelected + ' value="' + countryList[n] + '">' + countryList[n] + '</option>';
        }
        html += `</select>
                  </div>
              </div>`;

        return html;
    }
    if (column == 'boarding') {
        var html = `<div class="form-group">
                <label class="control-label col-md-4">${labelReplace}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">`;
        var selected1 = '';
        if (targetdata[column] == 'Day Scholar') {
            selected1 = 'selected';
        }
        var selected2 = '';
        if (targetdata[column] == 'Regular Boarder') {
            selected2 = 'selected';
        }
        var selected3 = '';
        if (targetdata[column] == 'Weekly Boarder') {
            selected3 = 'selected';
        }
        html += '<option  value="">Select Preferred Boarding Type</option>';
        html += '<option ' + selected1 + ' value="1">Day Scholar</option>';
        html += '<option ' + selected2 + ' value="2">Regular Boarder</option>';
        html += '<option ' + selected3 + ' value="3">Weekly Boarder</option>';
        html += `</select>
                </div>
            </div>`;

        return html;
    }
    if (column == 'student_blood_group') {
        var html = `<div class="form-group">
                  <label class="control-label col-md-4">${labelReplace}</label>
                  <div class="col-md-6">
                      <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                          <option value="">Select Blood Group</option>`;
        for (var n = 0; n < bloodgroups.length; n++) {
            var bloogGroupSelected = '';
            if (targetdata[column] == bloodgroups[n]) {
                bloogGroupSelected = 'selected';
            } else {
                bloogGroupSelected = '';
            }
            html += '<option ' + bloogGroupSelected + ' value="' + bloodgroups[n] + '">' + bloodgroups[n] + '</option>';
        }
        html += `</select>
                  </div>
              </div>`;

        return html;
    }

    if (column == 'religion') {
        var html = `<div class="form-group">
                  <label class="control-label col-md-2">${labelReplace}</label>
                  <div class="col-md-6">
                      <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                          <option value="">Select Religion</option>`;
        for (var n = 0; n < religions.length; n++) {
            var religionsSelected = '';
            if (targetdata[column] == religions[n]) {
                religionsSelected = 'selected';
            } else {
                religionsSelected = '';
            }
            html += '<option ' + religionsSelected + ' value="' + religions[n] + '">' + religions[n] + '</option>';
        }
        html += `</select>
                  </div>
              </div>`;

        return html;
    }
    if (column == 'category') {
        var html = `<div class="form-group">
                <label class="control-label col-md-4">${labelReplace}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                      <option value="">Select </option>`;
        for (var cat in category) {

            var catSelected = '';
            if (targetdata[column] == category[cat]) {
                catSelected = 'selected';
            } else {
                catSelected = '';
            }
            html += '<option ' + catSelected + ' value="' + cat + '">' + category[cat] + '</option>';
        }

        html += `</select>
                </div>
            </div>`;

        return html;
    }
    if (column == 'student_quota') {

        var html = `<div class="form-group">
                <label class="control-label col-md-4">${labelReplace}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                      <option value="">Select </option>`;
        for (var quota in student_quota) {

            var quotaSelected = '';
            if (targetdata[column] == student_quota[quota]) {
                quotaSelected = 'selected';
            } else {
                quotaSelected = '';
            }
            html += '<option ' + quotaSelected + ' value="' + quota + '">' + student_quota[quota] + '</option>';
        }

        html += `</select>
                </div>
            </div>`;

        return html;
    }
    if (column == 'physical_disability') {
        var html = `<div class="form-group">
                <label class="control-label col-md-4">${labelReplace}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                        <option value="">Select </option>`;
        var selectedYes = '';
        if (targetdata[column] == 'Yes') {
            selectedYes = 'selected';
        }
        var selectedNo = '';
        if (targetdata[column] == 'No') {
            selectedNo = 'selected';
        }
        html += '<option  value="-1">Not Specified</option>';
        html += '<option ' + selectedNo + ' value="N">No</option>';
        html += '<option ' + selectedYes + ' value="Y">Yes</option>';
        html += `</select>
                </div>
            </div>`;

        return html;
    }
    if (column == 'joining_period') {
        var html = `<div class="form-group">
                <label class="control-label col-md-4">${labelReplace}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">`;
        var selectedTerm1 = '';
        if (targetdata[column] == 'Term 1- Aug to Dec') {
            selectedTerm1 = 'selected';
        }
        var selectedTerm2 = '';
        if (targetdata[column] == 'Term 2 – Jan – June') {
            selectedTerm2 = 'selected';
        }
        html += '<option  value="">Not Specified</option>';
        html += '<option ' + selectedTerm1 + ' value="Term 1- Aug to Dec">Term 1- Aug to Dec</option>';
        html += '<option ' + selectedTerm2 + ' value="Term 2 – Jan – June">Term 2 – Jan – June</option>';
        html += `</select>
                </div>
            </div>`;

        return html;
    }


    if (column == 'learning_disability') {
        var html = `<div class="form-group">
                <label class="control-label col-md-4">${labelReplace}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                        <option value="">Select </option>`;
        var selectedYes = '';
        if (targetdata[column] == 'Yes') {
            selectedYes = 'selected';
        }
        var selectedNo = '';
        if (targetdata[column] == 'No') {
            selectedNo = 'selected';
        }
        html += '<option  value="-1">Not Specified</option>';
        html += '<option ' + selectedNo + ' value="N">No</option>';
        html += '<option ' + selectedYes + ' value="Y">Yes</option>';
        html += `</select>
                </div>
            </div>`;

        return html;
    }
    if (column == 'esl_english_as_second_language') {
        var html = `<div class="form-group">
                <label class="control-label col-md-4">${labelReplace}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                        <option value="">Select </option>`;
        var selectedYes = '';
        if (targetdata[column] == 'Yes') {
            selectedYes = 'selected';
        }
        var selectedNo = '';
        if (targetdata[column] == 'No') {
            selectedNo = 'selected';
        }
        html += '<option  value="-1">Not Specified</option>';
        html += '<option ' + selectedNo + ' value="No">No</option>';
        html += '<option ' + selectedYes + ' value="Yes">Yes</option>';
        html += `</select>
                </div>
            </div>`;

        return html;
    }



    if (column == 'curriculum_currently_studying') {

        var currBoard = ['IB', 'CBSE', 'ICSE', 'State', 'Home School', 'IGCSE', 'IBDP', 'NIOS'];
        var html = `<div class="form-group">
                <label class="control-label col-md-4">${labelReplace}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                      <option value="">Select </option>`;
        for (var cBC in currBoard) {
            var currBoardSelected = '';
            if (targetdata[column] == currBoard[cBC]) {
                currBoardSelected = 'selected';
            } else {
                currBoardSelected = '';
            }
            html += '<option ' + currBoardSelected + ' value="' + currBoard[cBC] + '">' + currBoard[cBC] + '</option>';
        }

        html += `</select>
                </div>
            </div>`;

        return html;
    }

    if (column == 'second_language_currently_studying') {
        var secondLanguage = ['Kannada', 'English', 'Hindi', 'German', 'French'];
        var html = `<div class="form-group">
                <label class="control-label col-md-4">${labelReplace}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                      <option value="">Select </option>`;
        for (var slang in secondLanguage) {
            var currBoardSelected = '';
            if (targetdata[column] == secondLanguage[slang]) {
                currBoardSelected = 'selected';
            } else {
                currBoardSelected = '';
            }
            html += '<option ' + currBoardSelected + ' value="' + secondLanguage[slang] + '">' + secondLanguage[slang] +
                '</option>';
        }

        html += `</select>
                </div>
            </div>`;

        return html;
    }

    if (column == 'grade_applied_for') {
        var html = `<div class="form-group">
                <label class="control-label col-md-4">${labelReplace}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                      <option value="">Select </option>`;
        for (var grade in grades) {

            var gradeSelected = '';
            if (targetdata[column] == grades[grade].class_name) {
                gradeSelected = 'selected';
            } else {
                gradeSelected = '';
            }
            html += '<option ' + gradeSelected + ' value="' + grades[grade].class_name + '">' + grades[grade]
                .class_name + '</option>';
        }

        html += `</select>
                </div>
            </div>`;

        return html;
    }
    if (column == 'special_needs_description') {

        var speicalNeeds = ['ADHD', 'Dyslexia', 'Speech delay', 'Autism any other'];

        var html = `<div class="form-group">
                <label class="control-label col-md-4">${labelReplace}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                      <option value="">Select </option>`;
        for (var spN in speicalNeeds) {
            var currBoardSelected = '';
            if (targetdata[column] == speicalNeeds[spN]) {
                currBoardSelected = 'selected';
            } else {
                currBoardSelected = '';
            }
            html += '<option ' + currBoardSelected + ' value="' + speicalNeeds[spN] + '">' + speicalNeeds[spN] +
                '</option>';
        }

        html += `</select>
                </div>
            </div>`;

        return html;
    }

    if (column == 'stream') {
        var streamItems = '<?php echo json_encode($streams) ?>';
        var Streams = $.parseJSON(streamItems);
        var html = `<div class="form-group">
                <label class="control-label col-md-4">${labelReplace}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                      <option value="">Select </option>`;
        for (var stream in Streams) {
            var streamSelected = '';
            if (targetdata[column] == stream) {
                streamSelected = 'selected';
            } else {
                streamSelected = '';
            }
            html += '<option ' + streamSelected + ' value="' + stream + '">' + stream + '</option>';
        }

        html += `</select>
                </div>
            </div>`;

        return html;
    }

    if (column == 'combination') {
        console.log(targetdata[column]);
        var streamItems = '<?php echo json_encode($streams) ?>';
        var Streams = $.parseJSON(streamItems);
        var stream_opted = $('#admission_student-stream').html();
        var html = `<div class="form-group">
                <label class="control-label col-md-4">${labelReplace}</label>
                <div class="col-md-6">
                    <select class="form-control" name="${targetdata['input_name']}" id="${column}">
                      <option value="">Select </option>`;
        console.log(Streams[stream_opted]);
        for (var com in Streams[stream_opted]) {
            var combselected = '';
            if (targetdata[column] == Streams[stream_opted][com].name) {
                combselected = 'selected';
            } else {
                combselected = '';
            }
            html += '<option ' + combselected + ' value="' + Streams[stream_opted][com].id + '">' + Streams[
                stream_opted][com].name + '</option>';
        }

        html += `</select>
                </div>
            </div>`;

        return html;
    }


}

function construct_address(column, targetdata) {
    var adm_id = '<?php echo $afId ?>';
    $.ajax({
        url: '<?php echo site_url('admission_process/get_student_address_by_user'); ?>',
        type: 'post',
        data: {
            // 'input_name': targetdata['input_name'],
            'af_id': adm_id
        },
        success: function(data) {
            var res_data = $.parseJSON(data);
            $('#edit_student_form_group').html(construct_address_form(res_data, targetdata['input_name']));
        }
    });
}

function construct_address_form(address, address_type) {
    var birth_taluk = '';
    var birth_district = '';
    var f_addr = '';
    var f_district = '';
    var f_state = '';
    var f_pincode = '';
    var m_addr = '';
    var m_district = '';
    var m_state = '';
    var m_pincode = '';
    var m_position = '';
    var m_company_name = '';
    var m_company_addr = '';
    var f_position = '';
    var f_company_name = '';
    var f_company_addr = '';

    if (address_type != null) {
        birth_taluk = address.birth_taluk;
        birth_district = address.birth_district;
    }
    if (address_type != null) {
        f_addr = address.f_addr;
        f_district = address.f_district;
        f_state = address.f_state;
        f_pincode = address.f_pincode;
    }
    if (address_type != null) {
        m_addr = address.m_addr;
        m_district = address.m_district;
        m_state = address.m_state;
        m_pincode = address.m_pincode;
    }
    if (address_type != null) {
        m_position = address.m_position;
        m_company_name = address.m_company_name;
        m_company_addr = address.m_company_addr;
    }
    if (address_type != null) {
        f_position = address.f_position;
        f_company_name = address.f_company_name;
        f_company_addr = address.f_company_addr;
    }

    var html = '';
    html += `<input type="hidden" name="update_address">`;
    if (address_type == 'birth_place') {
        html += ` <input type="hidden" name="address_type" value="birth_place">
                <div class="form-group">
                    <label class="col-md-2 control-label" for="birth_taluk">Birth Taluk</label>  
                    <div class="col-md-8">
                        <input type="text" class="form-control" value="${birth_taluk}" name="birth_taluk" id="birth_taluk">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="birth_district">Birth District</label>  
                    <div class="col-md-8">
                        <input type="text" class="form-control" value="${birth_district}" name="birth_district" id="birth_district">
                    </div>
                </div>`;
        return html;
    }
    if (address_type == 'f_addr') {
        html += `<input type="hidden" name="address_type" value="f_addr">
                <div class="form-group">
                    <label class="col-md-3 control-label" for="f_addr">Father's Address</label>  
                    <div class="col-md-8">
                        <input type="text" class="form-control" value="${f_addr}" name="f_addr" id="f_addr">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="f_district">Father's District</label>  
                    <div class="col-md-8">
                        <input type="text" class="form-control" value="${f_district}" name="f_district" id="f_district">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="f_state">Father's State</label>  
                    <div class="col-md-8">
                        <input type="text" class="form-control" value="${f_state}" name="f_state" id="f_state">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="f_pincode">Father's Pincode</label>  
                    <div class="col-md-8">
                        <input type="text" class="form-control" value="${f_pincode}" name="f_pincode" id="f_pincode">
                    </div>
                </div>`;
        return html;
    }
    if (address_type == 'm_addr') {
        html += `<input type="hidden" name="address_type" value="m_addr">
                <div class="form-group">
                    <label class="col-md-3 control-label" for="m_addr">Mother's Address</label>  
                    <div class="col-md-8">
                        <input type="text" class="form-control" value="${m_addr}" name="m_addr" id="m_addr">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="m_district">Mother's District</label>  
                    <div class="col-md-8">
                        <input type="text" class="form-control" value="${m_district}" name="m_district" id="m_district">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="m_state">Mother's State</label>  
                    <div class="col-md-8">
                        <input type="text" class="form-control" value="${m_state}" name="m_state" id="m_state">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="m_pincode">Mother's Pincode</label>  
                    <div class="col-md-8">
                        <input type="text" class="form-control" value="${m_pincode}" name="m_pincode" id="m_pincode">
                    </div>
                </div>`;
        return html;
    }
    if (address_type == 'm_office_add') {
        html += ` <input type="hidden" name="address_type" value="m_office_add">
                <div class="form-group">
                    <label class="col-md-3 control-label" for="m_position">Mother's Position</label>  
                    <div class="col-md-8">
                        <input type="text" class="form-control" value="${m_position}" name="m_position" id="m_position">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="m_company_name">Mother's Company Name</label>  
                    <div class="col-md-8">
                        <input type="text" class="form-control" value="${m_company_name}" name="m_company_name" id="m_company_name">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="m_company_addr">Mother's Company Address</label>  
                    <div class="col-md-8">
                        <input type="text" class="form-control" value="${m_company_addr}" name="m_company_addr" id="m_company_addr">
                    </div>
                </div>`;
        return html;
    }
    if (address_type == 'f_office_add') {
        html += `<input type="hidden" name="address_type" value="f_office_add">
                <div class="form-group">
                    <label class="col-md-3 control-label" for="f_position">Father's Position</label>  
                    <div class="col-md-8">
                        <input type="text" class="form-control" value="${f_position}" name="f_position" id="f_position">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="f_company_name">Father's Company Name</label>  
                    <div class="col-md-8">
                        <input type="text" class="form-control" value="${f_company_name}" name="f_company_name" id="f_company_name">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="f_company_addr">Father's Company Address</label>  
                    <div class="col-md-8">
                        <input type="text" class="form-control" value="${f_company_addr}" name="f_company_addr" id="f_company_addr">
                    </div>
                </div>`;
        return html;
    }


}

function submit_admission_followup() {
    var form = $('#followup_form');
    if (!form.parsley().validate()) {
        return 0;
    }

    //Disable the create button
    $("#submit_followup").html('Please wait...');
    $("#submit_followup").prop('disabled', true);

    var form = $('#followup_form')[0];
    var formData = new FormData(form);
    var adm_id = '<?php echo $afId ?>';
    $.ajax({
        url: '<?php echo site_url('admission_process/submit_follow_up_details/'); ?>' + adm_id,
        type: 'post',
        data: formData,
        processData: false,
        contentType: false,
        // async: false,
        success: function(data) {
            var followupStatus = data.trim();
            follow_up();
            $("#submit_followup").html('Submit');
            $("#submit_followup").prop('disabled', false);
            $('#followup_form').trigger("reset");
            if (followupStatus == -1) {
                Swal.fire({
                    icon: "error",
                    title: "Offer Released for this Student",
                    showConfirmButton: true,
                    timer: 2500
                });
            } else {
                Swal.fire({
                    // position: "top-end",
                    icon: "success",
                    title: "Follow-up details have been updated.",
                    showConfirmButton: true,
                    timer: 2500
                });
                $('#currentStatus').html(followupStatus);
                change_followup_action();
            }
        },
        error: function(err) {},
        complete: function() {
            get_admission_followup_details();
            get_update_button_colors();
            follow_up();
        }
    });
}

function get_update_button_colors() {
    var curr_status = $('#currentStatus').html();
    $.ajax({
        url: '<?php echo site_url('admission_process/get_mapped_status/'); ?>',
        type: 'post',
        data: {
            'curr_status': curr_status
        },
        async: false,
        success: function(data) {
            var data = $.parseJSON(data);
            if (data != null) {
                var internal_status = '';
                if (data.internal_status)
                    internal_status = data.internal_status;
                update_button_colors(internal_status);
            }

        }
    });
}



$(document).keypress(function(e) {
    if ($("#student_edit_by_user").hasClass('show') && (e.keycode == 13 || e.which == 13)) {
        save_student_profile_by_user();
    }
});

function save_student_profile_by_user() {
    var columnName = $('#save_get_column_value').val();
    var profileSelectionTab = $('#profile_selection_tab').val();

    var adm_id = '<?php echo $afId ?>';

    var $form = $('#save_form_student_data_by_user');
    if ($form.parsley().validate()) {
        var form = $('#save_form_student_data_by_user')[0];
        var formData = new FormData(form);
        $('#studentProfileSaveButton').val('Please wait ...').attr('disabled', 'disabled');
        $.ajax({
            url: '<?php echo site_url('Admission_process/save_student_profile_by_user/'); ?>' + adm_id,
            type: 'post',
            data: formData,

            processData: false,
            contentType: false,

            success: function(data) {

                $('#student_edit_by_user').modal('hide');
                if (data.trim()) {

                    $('#savedLabel-' + columnName).show();
                    setTimeout(function() {
                        $('#savedLabel-' + columnName).fadeIn('slow');
                        $('#savedLabel-' + columnName).fadeOut();
                    }, 2000);
                } else {
                    $('#unsavedLabel-' + columnName).show();

                    setTimeout(function() {
                        $('#unsavedLabel-' + columnName).fadeIn('slow');
                        $('#unsavedLabel-' + columnName).fadeOut();
                    }, 2000);
                }
                $('#studentProfileSaveButton').val('Save').removeAttr('disabled');
                Application_Details(profileSelectionTab);
            }
        });
    }
}

function get_release_offer_details() {
    var acad_year = '<?php echo $this->acad_year->getAcadYearID() ?>';
    var gradeAppliedFor = $('#grade').html();
    $.ajax({
        url: '<?php echo site_url('student/student_controller/getAcadClassess/'); ?>',
        type: 'post',
        data: {
            'acad_year': acad_year
        },
        success: function(data) {

            $('#gradeId').html('');
            var class_st = $.parseJSON(data);
            // console.log(class_st)
            var output = '';
            output += '<option value="">-Select Class-</option>';
            var len = class_st.length;
            for (var i = 0, j = len; i < j; i++) {
                var selected = '';
                if (gradeAppliedFor == class_st[i].class_name) {
                    selected = 'selected';
                }
                output += '<option ' + selected + ' value="' + class_st[i].id + '">' + class_st[i]
                    .class_name + '</option>';
            }
            $('#gradeId').html(output);
            getclassSection();
            getcombinations();
            select_offers();
        }
    });



}

function select_offers() {
    $.ajax({
        url: '<?php echo site_url('admission_process/get_offer'); ?>',
        type: 'post',
        success: function(data) {
            var offers = $.parseJSON(data);
            // return;
            // var output_html = ''
            // for (var i = 0; i < offers.length; i++) {
            //     output_html += '<tr>';
            //     output_html += '<td>' + (i + 1) + '</td>';
            //     output_html += '<td id="offer_name_' + offers[i].id + '">' + (offers[i].offer_name) +
            //         '</td>';
            //     if (parseInt(offers[i].offer_amount) == 0) {
            //         output_html +=
            //             `<td id="offer_amount_${offers[i].id}"><input class="form-control" id="custom_amount_${offers[i].id}" onkeyup="enable_check_box('${offers[i].id}')"></td>`;
            //         output_html += '<td><input type="checkbox" style="margin:0" id="check_offers' + offers[
            //                 i].id + '" name="check_offers[' + offers[i].id + ']" value="' + offers[i].id +
            //             '" class="admission_offer" disabled></td>';
            //     } else {
            //         output_html += '<td id="offer_amount_' + offers[i].id + '">' + (offers[i]
            //             .offer_amount) + '</td>';
            //         output_html += '<td><input type="checkbox" style="margin:0" id="check_offers' + offers[
            //                 i].id + '" name="check_offers[' + offers[i].id + ']" value="' + offers[i].id +
            //             '" class="admission_offer"></td>';
            //     }
            //     //  output_html +='<td><input type="checkbox" style="margin:0" id="check_offers'+offers[i].id+'" name="check_offers['+offers[i].id+']" value="'+offers[i].id+'" class="admission_offer"></td>';
            //     output_html += '</tr>';
            // }
            var html = ''
            for (var i = 0; i < offers.length; i++) {
                html += '<option id="'+offers[i].offer_name+'" value="' + offers[i].id + ',' + offers[i].offer_amount + '">' + offers[i].offer_name + ' ('+ +offers[i].offer_amount+')'+ '</option>';
            }
            $('#check_offers').html(html);
            // $('#offers_list').html(output_html);
            $('#check_offers').selectpicker({
                liveSearch: true,
                liveSearchPlaceholder: 'Search fields...'
            });
        }
    });
}


function custom_offer_selected() { 
    var selectedOptions = $('#check_offers option:selected');
    var showCustomOffer = false;

    selectedOptions.each(function() {
        var text = $(this).text().trim();
        if (text.startsWith('Custom')) { // Change here: match if starts with Custom
            showCustomOffer = true;
            return false; // Stop looping once found
        }
    });

    if (showCustomOffer) {
        $('#custom_offer_display').show();
        $('#custom_offer').attr('required', 'required');
    } else {
        $('#custom_offer_display').hide();
        $('#custom_offer').removeAttr('required');
    }
}

$('#custom_offer').on('input', function() {
    var customAmount = $(this).val();
    var customId_arr = $('#Custom').val();
    var customId = customId_arr.split(',')[0];

    // Find the option with the text starting with 'Custom'
    var customOption = $('#check_offers option').filter(function() {
        return $(this).text().trim().startsWith('Custom');
    });

    if (customOption.length > 0) {
        // Update the custom option's text and value
        customOption.text('Custom (' + customAmount + ')');
        customOption.val(customId + ',' + customAmount);
    }

    // Trigger update for selectpicker
    $('#check_offers').selectpicker('refresh');
});

// var offer_selected = [];

// function select_admission_offers() {
//     var html = '';
//     $('.admission_offer:checked').each(function() {
//         var id = $(this).val();
//         var offerName = $("#offer_name_" + id).text();
//         var offerAmount = $("#offer_amount_" + id).text();
//         if (offerAmount == '') {
//             offerAmount = $("#custom_amount_" + id).val();
//         }
//         if (offer_selected.indexOf(id) == -1) {
//             html += '<div onclick="removeName(' + id +
//                 ')" style="border-bottom: 1px solid #ccc;font-size: 16px;" class="contact-names removeClass' +
//                 id + ' "><input type="hidden" name="offer_ids[]" value="' + id + ',' + offerAmount + '"><b>' +
//                 offerName + ':</b> ' + offerAmount +
//                 '&nbsp;&nbsp;<span class="fa fa-times remove" style="font-size: 18px;color:red" ></span><input type="hidden" name="offer_amount[]" value="' +
//                 offerAmount + '"></div>';
//             offer_selected.push(id);
//         }
//         $('#offer-preview').modal('hide');
//     });
//     $("#offerSelectedDisplay").append(html);
// }

function enable_check_box(id) {
    var amount = $('#custom_amount_' + id).val();
    if (amount) {
        $('#check_offers' + id).prop('checked', true);
        // $('#check_offers'+id).removeAttr('disabled');
    } else {
        $('#check_offers' + id).prop('checked', false);
        // $('#check_offers'+id).attr('disabled','disabled');
    }
}

function check_all(check) {
    if ($(check).is(':checked')) {
        $(".admission_offer").prop('checked', true);
    } else {
        $(".admission_offer").prop('checked', false);
    }
}

function removeName(id) {
    var index = offer_selected.indexOf(id);
    offer_selected.splice(index, 1);
    $(".removeClass" + id).remove();
}


function tabwise_list_data(val) {
    if (val === 'release_offer') {
        check_release_offer();
        document.querySelectorAll('.step').forEach(function (step) {
            step.classList.remove('active');
        });
        document.querySelectorAll('.tab-pane').forEach(function (pane) {
            pane.classList.remove('active');
        });

        document.getElementById('offer_tab').classList.add('active');
        const offerTab = document.getElementById('offer_tab');
        offerTab.className = 'step text-center active';

        document.getElementById('step1').classList.add('active');

    } else if (val === 'fees') {
        
        document.querySelectorAll('.step').forEach(function (step) {
            step.classList.remove('active');
        });
        document.querySelectorAll('.tab-pane').forEach(function (pane) {
            pane.classList.remove('active');
        });
        const feesTab = document.getElementById('fees_tab');
        feesTab.className = 'step text-center active';

        document.getElementById('step2').classList.add('active');
        check_fees_amount();

    } else if (val === 'credential') {
        document.querySelectorAll('.step').forEach(function (step) {
            step.classList.remove('active');
        });

        document.querySelectorAll('.tab-pane').forEach(function (pane) {
            pane.classList.remove('active');
        });
        const credentialsTab = document.getElementById('credentials_tab');
        credentialsTab.className = 'step text-center active';

        document.getElementById('step3').classList.add('active');
        get_student_details_for_credentials();
    }
}

// function send_credentials_from_admission() {
//     var methodType = $('#sendCredentials').val();
//     var admission_id = '<?php //echo $afId ?>';
//     $.ajax({
//       url: '<?php //echo site_url('admission_flow/get_credentials_student_parent_data'); ?>',
//       type: 'post',
//       data: {'admission_id':admission_id},
//       success: function(data) {
//        // console.log(data);
//         var resdata = $.parseJSON(data);
//         var pids =[];
//         $.each(resdata, function(index, value) {
//             pids.push(value.pid+'_'+methodType);
//         });
//         console.log(pids);
//         $('#summary_credentials').modal('show');
//         sendProvisionLink(pids,'activation');
//       }
//     });
// }

function sendProvisionLink(pids, provision = 'activation', alreadyProv = 0) {
    var af_id = '<?php echo $afId?>';
    var fees_auto_assign = '<?php echo $config_val['fees_auto_assign_default_credentials'] ?>';
    $('#summary_credentials').show();
    $('#modal-loader').show();
    if (pids.length > 0) {
        $('#dynamic-content-credentials').html(''); // blank before load.
        $("#warnMsg").html('');
        $.ajax({
                url: '<?php echo site_url('admission_flow/getPreview_credentials_admission_flow'); ?>',
                type: 'POST',
                data: {
                    'pids': pids,
                    'process': provision,
                    'af_id': af_id
                },
                dataType: 'html'
            })
            .done(function(data) {
                var data = $.parseJSON(data);
                var previewData = data.preview;
                var credits_available = data.credits_available;
                //$('#studentDetails').text('Student Details: ' + previewData);
                var html = '';
                if (previewData.length == 0) {
                    html += '<h4><i class="glyphicon glyphicon-info-sign"></i> Please select students...</h4>';
                } else {
                    html +=
                        '<thead><tr><th>#</th><th style="width:30%;">Name</th><th>Number</th><th>Message</th></tr></thead><tbody>';
                    for (var i = 0; i < previewData.length; i++) {
                        html += '<tr>';
                        html += '<td>' + (i + 1) + '</td>';
                        html += '<td>' + previewData[i].name + '</td>';
                        html += '<td>' + previewData[i].message_by + '<input type="hidden" name="codes[' + previewData[i].pid + ']" value="' + previewData[i].code + '">' + '<input type="hidden" name="mobiles[' + previewData[i].pid + ']" value="' + previewData[i].message_by + '">' + '</td>';
                        html +=
                            `<td>${previewData[i].message}<input type="hidden" name="messages[${previewData[i].std_id}_${previewData[i].pid}_${previewData[i].relation_type}_${previewData[i].user_id}_${previewData[i].send_type}]" value='${previewData[i].message}'></td>`;
                        html += '</tr>';
                    }
                    html += '</tbody></table>';
                }
                $('#dynamic-content-credentials').html(html); // load here
                if (fees_auto_assign != '' && credentials_released != 1) {
                    send_provision_credentials_form_submit();
                }
                // $('#dynamic-content').html(previewData); // load here
                if (alreadyProv > 0) {
                    $("#warnMsg").html(alreadyProv + ' already provisioned numbers are not taken.');
                }
                $('#modal-loader').hide(); // hide loader  
                if (credits_available == 1) {
                    //enable confirm button only if credits available
                    $("#confirmBtn").attr('disabled', false);
                    $("#credit-err").hide();
                } else {
                    $("#confirmBtn").attr('disabled', true);
                    $("#credit-err").show();
                }
            })
            .fail(function() {
                $('#dynamic-content-credentials').html(
                    '<i class="glyphicon glyphicon-info-sign"></i> Something went wrong, Please try again...');
                $('#modal-loader').hide();
            });
    } else {
        $('#modal-loader').hide();
        $("#warnMsg").html('');
        if (alreadyProv > 0) {
            $("#warnMsg").html(alreadyProv + ' already provisioned member(s) is/are not shown here.');
        }
        $("#confirmBtn").hide();
        $('#dynamic-content-credentials').html(
            '<i class="glyphicon glyphicon-info-sign"></i> Please select students...');
    }
}

function check_fees_amount() {
    var admission_id = '<?php echo $afId ?>';
    var direct_connection = '<?php echo $this->settings->getSetting('directly_linkTo_feesAssign_siblingConnected'); ?>';
    var fees_auto_assign = '<?php echo $config_val['fees_auto_assign_default_credentials'] ?>';
    $.ajax({
        url: '<?php echo site_url('admission_flow/check_fees_amount_for_student'); ?>',
        type: 'post',
        data: {
            'admission_id': admission_id
        },
        success: function(data) {
            var resdata = $.parseJSON(data);
            if (resdata == -1) {
                $('#assign_fee_new').hide();
                $("#fees_assinged_view").show();
                $("#fees_assinged_view").html(
                    `<div class="d-flex align-items-center justify-content-center">
                        <div class="text-center p-4">
                            <span class="animate__animated animate__fadeIn d-block mb-4">
                                <div class="icon-container" style="width: 40%;
                                    margin: 0 auto;">
                                    <?php $this->load->view('svg_icons/fee_structure_not_configured.svg') ?>
                                </div>
                            </span>
                            <h3 class="fw-bold mb-2"><b>No Fees Type Configured</b></h3>
                            <span class="text-muted" style="font-size: 16px;">No fee type has been configured for your account. Please proceed to the next step.</span>
                        </div>
                    </div>`);
            return false;
            }
            
            if (resdata.student == null) {
                $('#assign_fee_new').hide();
                $("#fees_assinged_view").show();
                $("#fees_assinged_view").html(
                    `<div class="d-flex align-items-center justify-content-center">
                        <div class="text-center p-4">
                            <span class="animate__animated animate__fadeIn d-block mb-4">
                                <div class="icon-container" style="margin: 0 auto;width:40%">
                                    <?php $this->load->view('svg_icons/offer_not_released_icon.svg') ?>
                                </div>
                            </span>
                            <h3 class="fw-bold mb-2"><b>Offer not released</b></h3>
                            <span class="text-muted" style="font-size: 16px;color: #000;">Offer hasn't been released yet. Once it's available, you'll be able to view all related details.</span>
                        </div>
                    </div>`);
                return false;
            }
            if (resdata.student['is_rte'] == 1 || resdata.student['transfer'] == 1 || resdata.student[
                    'has_staff'] == 1) {
                var html = '';
                if (resdata.student['is_rte'] == 1) {
                    html +=
                        '<h5 style="text-align:center"><b>Special Case Student:</b> This Student is RTE Student</h5>';
                }
                if (resdata.student['transfer'] == 1) {
                    html +=
                        '<h5 text-align:center><b>Transfer Student:</b> This Student is Transfer Student</h5>'
                }
                if (resdata.student['has_staff'] == 1) {
                    html += '<h5 text-align:center><b>Staff Kid:</b> This Student is Staff Kid</h5>';
                }
                html +=
                    '<center><div class="no-data-display" style="margin-top:20px">Not Applicable for Special case Student </div></center>';
                $('#assign_fee_new').hide();
                $("#fees_assinged_view").show();
                $("#fees_assinged_view").html(html);
                return false;
            }
            // return false;


            if (direct_connection == 1) {
                if (resdata.feeDetails != '') {
                    $("#fees_assinged_view").show();
                    $("#fees_assinged_view").html(construct_linkto_fee_assigned(resdata.student, resdata
                        .feeDetails));
                } else {
                    $("#fees_assinged_view").show();
                    $("#fees_assinged_view").html(construct_linkto_fee_assigned(resdata.student_id, resdata
                        .feeDetails));
                }

            } else {
                if (resdata.feeDetails != '') {
                    $('#assign_fee_new').hide();
                    $("#fees_assinged_view").show();
                    $("#fees_assinged_view").html(construct_fee_assigned_view_table(resdata.feeDetails));
                    if (fees_auto_assign != '') {
                        tabwise_list_data('credential');
                    }
                } else {
                    $("#fees_assinged_view").hide();
                    $('#assign_fee_new').show();
                    $('#blueprint_id').val(resdata.bpId);
                    $('#student_id').val(resdata.student_id);
                    $('#student_admid').val(resdata.student_id);
                    $('#customId').html(construct_custom_friendlyname_details(resdata.cohort_details,
                        resdata.cohort_id));
                    $('#ins_types').html(construct_installment_types_details(resdata.installments_types));
                    custom_enable_component();
                }
            }

        }
    });
}

function construct_linkto_fee_assigned(student_id, feeDetails) {

    var disabled = 'disabled';
    var authorised = '<?php echo $this->authorization->isAuthorized('FEESV2.COLLECT_FEES') ?>';
    if (authorised) {
        disabled = '';
    }
    var feev1settings = '<?php echo $this->settings->getSetting('fee_collection_v1') ?>';
    if (feev1settings == 1) {
        var feesUrl = '<?php echo site_url('feesv2/fees_collection/fee_student_blueprints_v1/') ?>' + student_id;
    } else {
        var feesUrl = '<?php echo site_url('feesv2/fees_collection/fee_student_blueprints/') ?>' + student_id;
    }

    var url = '<?php echo site_url('feesv2/fees_collection/fee_student_blueprints_v1/') ?>' + student_id;

    var disabled1 = 'disabled';
    var authorised1 = '<?php echo $this->authorization->isAuthorized('STUDENT.CONNECT_SIBLINGS') ?>';
    if (authorised1) {
        disabled1 = '';
    }
    var html = '';

    // html += `<div class="col-md-12" style="text-align:center;margin-top:10px;"><div class="col-md-6"><h5>Click here to Assign the fees </div><div class="col-md-2"><a href="${feesUrl}" class="btn btn-info" ${disabled}target="_blank">Assign Fees</a></h5></div></div>`;

    // html += `<div class="col-md-12" style="margin-top:15px;text-align:center"><div class="col-md-6" ><h5>Click here to Connect Sibling</div><div class="col-md-2"> <a href="<?php //echo site_url('student/sibling_staff_controller/connect_siblingbyid/') ?>${student_id}" target="_blank" class="btn btn-info" ${disabled1}>Connect Sibling</a></h5></div></div>`;

    html += `<div class="text-center mb-4">
    <h3 class=""><b>Assign Fees & Connect Siblings</b></h3>
    <p class="text-muted">Complete these steps to proceed with the process</p>
  </div>

  <div class="row justify-content-center">

    <div class="col-md-6 mb-3">
            <div class="step-card text-center">
                <div class="d-flex align-items-center justify-content-start mb-2">
                <div class="step-icon-wrapper" style="margin-right: 15px;">
                    <?php $this->load->view('svg_icons/connect_sibling.svg') ?>
                </div>
                <div class="step-title h5 mb-2 text-start">Connect Sibling</div>
            </div>
            <p class="step-description text-start" style="text-align:left">Link the student with their siblings to manage family-wise records</p>
            <a  class="btn step-button-outline mt-2" ${disabled1} onclick="get_sibling_data(${student_id})">
                Connect Sibling
            </a>
        </div>
    </div>

    <div class="col-md-6 mb-3">
        <div class="step-card text-center">
            <div class="d-flex align-items-center justify-content-start mb-2">
                <div class="step-icon-wrapper" style="margin-right: 15px;">
                    <?php $this->load->view('svg_icons/admissions_assign_fees.svg') ?>
                </div>
                <div class="step-title h5 mb-2 text-start">Assign Fees</div>
            </div>
            <p class="step-description text-start" style="text-align:left">Set up the fee structure and payment schedule for the student's admission</p>
            <a href="${feesUrl}" class="btn step-button-filled mt-2" ${disabled} target="_blank">Assign Fees</a>
        </div>
    </div>

</div>`;

    $('#fees_assinged_view').html(html);
}

function get_sibling_data(student_id){
    $.ajax({
        url: '<?php echo site_url('admission_flow/get_sibling_data'); ?>',
        type: 'post',
        data: {
            'student_id': student_id
        },
        success: function(data) {
            var resdata = $.parseJSON(data);
            if(resdata == ''){
                Swal.fire({
                title: 'Siblings Details',
                html: `
                    <div class="print-confirmation-modal">
                        <div class="col-md-12 mb-3">
                            <div class="step-card text-center">
                                <div class="d-flex align-items-center justify-content-start mb-2">
                                    <p>No student matched . <a href="<?php echo site_url('student/sibling_staff_controller') ?>" target="_blank">Click here</a> to connect sibling.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                cancelButtonColor: '#d33',
                cancelButtonText: '<i class="fa fa-times mr-1"></i> Cancel',
                width: '550px',
                customClass: {
                    title: 'print-confirmation-title',
                    popup: 'print-confirmation-popup',
                    confirmButton: 'print-confirmation-confirm',
                    cancelButton: 'print-confirmation-cancel'
                }
            })
            $('.swal2-confirm').hide();
            }else{
                _showing_sibling_data(resdata,student_id);
            }
        }
    });
}

function _showing_sibling_data(resdata,student_id){
    var html ='<div style="max-height:400px; overflow-y:auto;">';
    for(var i = 0; i < resdata.length; i++){
        var linked_disable = '';
        var msg = '';
        if(resdata[i].sibling_id){
            var linked_disable = 'disabled';
            msg = '<span style="font-size: 14px; line-height: 1;">(Connected)</span>';
        }
        html += `<div class="print-confirmation-modal">
                    <div class="col-md-12 mb-3">
                        <div class="step-card">
                        
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <div class="step-title h5 mb-2" style="font-size:15px">
                            ${resdata[i].student_name}
                            </div>
                        </div>

                        <p class="step-description text-start">Class Section: ${resdata[i].class_section}</p>
                        <p class="step-description text-start">Admission number: ${resdata[i].admission_no}</p>

                       <div class="d-flex justify-content-end align-items-center mt-2" style="gap: 6px;">
                            ${msg}
                            <input type="checkbox"
                                class="form-check-input mt-0"
                                id="check_${resdata[i].sdt_id}"
                                name="sibling_select[]"
                                value="${resdata[i].sdt_id}" ${linked_disable}>
                        </div>
                        </div>
                    </div>
                </div>`;
    }
    html += '</div>'; 
    Swal.fire({
            title: 'Siblings Details',
            html: html,
            showCancelButton: true,
            confirmButtonColor: '#4CAF50',
            cancelButtonColor: '#d33',
            confirmButtonText: '<i class="fa fa-check mr-1"></i> Connect',
            cancelButtonText: '<i class="fa fa-times mr-1"></i> Cancel',
            width: '550px',
            customClass: {
                title: 'print-confirmation-title',
                popup: 'print-confirmation-popup',
                confirmButton: 'print-confirmation-confirm',
                cancelButton: 'print-confirmation-cancel'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                connect_sibbling(student_id);
            }
        });
}

function connect_sibbling(student_id){
    const checkboxes = document.querySelectorAll('input[name="sibling_select[]"]:checked');
    const selectedIds = Array.from(checkboxes).map(cb => cb.value);

    if (selectedIds.length === 0) {
        Swal.fire({
            icon: "error",
            title: "No Student Selected",
            showConfirmButton: false,
            timer: 2500
        })
        return false;
    }
    Swal.fire({
        title: 'Processing',
        text: 'Updating order status...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
    selectedIds.push(student_id);
    $.ajax({
        url: '<?php echo site_url('admission_flow/LinkAccounts'); ?>',
        type: 'post',
        data: {
            'std_ids': selectedIds
        },
        success: function(data) {
            if(data){
                Swal.fire({
                    icon: "success",
                    title: "Sibling Connected Successfully",
                    showConfirmButton: false,
                    timer: 2500
                })
            }
        }
    });

}

function construct_fee_assigned_view_table1() {
    var html = '';
    html += '<table class="table table-bordered">';
    html += '<thead>';
    html += `<th>#</th>
               <th>Blueprint Name</th>
               <th>Fee Amount</th>
               <th>Fee Paid</th>
               <th>Concession</th>
               <th>Balance</th>
               <th>Fine</th>
               <th>Action</th>`;
    html += '</thead>';
    html += `<tbody>
               <tr>
               <td>1.</td>
               <td>${feeDetails.blueprint_name}</td>
               <td>${feeDetails.total_fee}</td>
               <td>${feeDetails.total_fee_paid}</td>
               <td>${feeDetails.total_concession_amount}</td>
               <td>${feeDetails.balance}</td>
               <td>${feeDetails.total_fine_amount}</td>`;

    var url = '<?php //echo site_url('feesv2/fees_collection/fee_student_blueprints_v1/') ?>' + student_id;
    var disabled = 'disabled';
    var authorised = '<?php //echo $this->authorization->isAuthorized('FEESV2.COLLECT_FEES') ?>';
    if (authorised) {
        disabled = '';
    }
    html += `<td><a href="${url}" class="btn btn-danger" style="border-radius:0.2rem" ${disabled}>Un-Assign</a></td>`;
    html += `</tr>
               </tbody>`;
    html += '</table>';
    html +=
        '<p style="color:red">Fees is Assigned for this Student.Un-Assign the fees then you can close this application.</p>';
}

function construct_fee_assigned_view_table(fees) {
    var send_payment_link = ' <?php echo $enable_payment_link_tab ?>';

    // var htmlfees = '';
    // htmlfees += '<table class="table table-bordered">';
    // htmlfees += '<tr>';
    // htmlfees += '<th>Blueprint Name</th>';
    // htmlfees += '<td>' + fees.blueprint_name + '</td>';
    // htmlfees += '</tr>';
    // htmlfees += '<tr>';
    // htmlfees += '<th>Fee Amount</th>';
    // htmlfees += '<td>' + fees.total_fee + '</td>';
    // htmlfees += '</tr>';
    // htmlfees += '<tr>';
    // htmlfees += '<th>Payment Status</th>';
    // htmlfees += '<td>' + fees.payment_status + '</td>';
    // htmlfees += '</tr>';

    // htmlfees += '<tr>';
    // htmlfees += '<th>Due Date</th>';
    // htmlfees += '<td>' + fees.pay_date_format + '<a href="javascript:void(0)" onclick="paytodate_change_the_date(' +
    //     fees.cohort_student_id + ')" class="btn btn-lightblue" style="float:right">Change</a></td>';
    // htmlfees += '</tr>';
    // htmlfees += '</table>';
    // if (send_payment_link == 1) {
    //     if (fees.payment_link != '') {
    //         htmlfees +=
    //             `<br><div class="col-md-12 p-0"><label class="col-md-4 col-xs-12 control-label"> Payment link</label>${fees.payment_link}
    //         <p style="margin-top:10px"><label class="col-md-4 col-xs-12 control-label"></label><a class='btn btn-warning' style="color:white;border-radius:0px"  onclick='generate_payment_link("${fees.blueprint_id}","Re generate")' >Re generate Payment Link</a></p></div><br>`;
    //     } else {
    //         htmlfees +=
    //             `<br><div class="col-md-12 p-0"><label class="col-md-4 col-xs-12 control-label"> Payment link</label><a class='btn btn-success' style="color:white;border-radius:0px"  onclick='generate_payment_link("${fees.blueprint_id}","Generate")' >Generate Payment Link</a></div><br>`;
    //     }

    //     htmlfees +=
    //         `<div class="col-md-12 p-0" style="margin-top:10px"><label class="col-md-4 col-xs-12 control-label">Send Payment link to parent</label><a class='btn btn-primary' style="color:white;border-radius:0px"  onclick='send_payment_link("${fees.total_fee}","${fees.payment_link}")' >Send Payment Link as email</a></div>`;
    // }


    var html = `<div class="status-card d-flex justify-content-between align-items-center">
                  <div>
                  <h5 class="mb-1 header">${fees.blueprint_name}</h5>
                  </div>
                  <div class="text-end">
                  <span class="text-muted" style="font-size: larger;">Offer released on</span><br>
                  <strong>${fees.offer_released_date}</strong>
                  </div>
              </div>`;
            html += `<div class="details-card">
                <h5 class="mb-3"><b>Fees Details</b></h5>
                <div class="row" style="margin-left: -10px;">
                    <div class="col-md-3 mb-2">
                        <div class="label-title">Fee Amount</div>
                        <div class="label-value">₹${fees.total_fee}</div>
                    </div>
                    <div class="col-md-3 mb-2">
                        <div class="label-title">Payment Status</div>
                        <div class="label-value">${fees.payment_status}</div>
                    </div>
                    <div class="col-md-3 mb-2">
                        <div class="label-title">Due date</div>
                        <div class="label-value d-flex align-items-center">
                            <span>${fees.pay_date_format =='00-00-0000' ? 'Not Set' : fees.pay_date_format}</span>&nbsp;&nbsp;&nbsp;
                            <a class="change-link ms-2" href="#" onclick="paytodate_change_the_date(${fees.cohort_student_id},'${fees.pay_date_format}')">Change</a>
                        </div>
                    </div>
                </div>
            </div>`;

            if (send_payment_link == 1) {
                html += `<div class="details-card">
                            <h5 class="mb-3">Payment Link</h5>
                            <div class="row">
                                <div class="col-md-12">`;

                if (fees.payment_link != '') {
                    html += `
                        <div class="d-flex align-items-center mb-3">
                            <button class="btn custom-outline-btn me-4" onclick='generate_payment_link("${fees.blueprint_id}", "Re generate")' style="width:150px;margin-right:30px">Re-Generate</button>
                            <input type="text" class="form-control" value="${fees.payment_link}" readonly />
                        </div>
                        <button class="btn custom-filled-btn d-flex align-items-center gap-1"
                                onclick='send_payment_link("${fees.total_fee}","${fees.payment_link}")'>
                            <i class="fa fa-email"></i> Send Payment Link as email
                        </button>`;
                    } else {
                        html += `
                            <button class="btn btn-success" style="width:150px" onclick='generate_payment_link("${fees.blueprint_id}", "Generate")'>Generate Payment Link</button>`;
                    }

                    html += `</div>
                            </div>
                        </div>`;
                }

    return html;
}

function construct_custom_friendlyname_details(friendlyname, cohort_id) {
    var html = '';
    for (var ch_id in friendlyname) {
        var cohort_selected = '';
        if (ch_id == cohort_id) {
            cohort_selected = 'selected';
        }
        html += ' <option ' + cohort_selected + ' value="' + ch_id + '">' + friendlyname[ch_id] + '</option>';
    }
    html += '<option value="CUSTOM">Custom</option>';
    return html;
}

function generate_payment_link(bp_id, generation_type) {
    var af_id = '<?php echo $afId?>';
    bootbox.confirm({
        title: generation_type + " Payment Link",
        message: "Do you want to " + generation_type + " payment link for this student ?",
        className: "medium",
        buttons: {
            confirm: {
                label: 'Yes',
                className: 'btn-success'
            },
            cancel: {
                label: 'No',
                className: 'btn-danger'
            }
        },
        callback: function(result) {
            if (result) {
                $.ajax({
                    url: '<?php echo site_url('admission_flow/get_payment_link'); ?>',
                    type: 'post',
                    data: {
                        'af_id': af_id,
                        'bpId': bp_id,
                        'generation_type': generation_type
                    },
                    success: function(data) {
                        var res = JSON.parse(data);
                        if (res) {
                            $(function() {
                                new PNotify({
                                    title: 'Success',
                                    text: 'Successfully generated',
                                    type: 'success',
                                });
                            });
                            check_fees_amount();
                        } else {
                            $(function() {
                                new PNotify({
                                    title: 'Error',
                                    text: 'Something went wrong',
                                    type: 'error',
                                });
                            });
                        }
                    }
                });
            }
        }
    });
}

function send_payment_link(amount, link) {
    var methodType = 'email';
    var admission_id = '<?php echo $afId?>';
    $.ajax({
        url: '<?php echo site_url('admission_flow/get_student_parent_data'); ?>',
        type: 'post',
        data: {
            'admission_id': admission_id
        },
        success: function(data) {
            // console.log(data);
            var resdata = $.parseJSON(data);
            var pids = [];
            $.each(resdata, function(index, value) {
                pids.push(value.pid + '_' + methodType);
            });
            $('#send_payment_link_to_parent').modal('show');
            sendPaymentLink(pids, amount, link);
        }
    });
}

function sendPaymentLink(pids, amount, link, ) {
    if (pids.length > 0) {
        $.ajax({
                url: '<?php echo site_url('admission_flow/getPreview_email_template'); ?>',
                type: 'POST',
                data: {
                    'pids': pids,
                    'amount': amount,
                    'link': link
                },
                dataType: 'html'
            })
            .done(function(data) {

                var data = $.parseJSON(data);
                var previewData = data.preview;

                var html = '';
                if (previewData.length == 0) {
                    html += '<h4><i class="glyphicon glyphicon-info-sign"></i> Please select students...</h4>';
                } else {
                    html +=
                        '<thead><tr><th>#</th><th style="width:20%;">Name</th><th>Email</th><th>Message</th></tr></thead><tbody>';
                    for (var i = 0; i < previewData.length; i++) {
                        html += '<tr>';
                        html += '<td>' + (i + 1) + '</td>';
                        html += '<td>' + previewData[i].name + '</td>';
                        html += '<td>' + previewData[i].message_by + '</td>';
                        html += '<td>' + previewData[i].message + '<input type="hidden" name="messages[' +
                            previewData[i].std_id + '_' + previewData[i].pid + '_' + previewData[i].relation_type +
                            '_' + previewData[i].user_id + '_' + previewData[i].send_type + ']" value="' +
                            previewData[i].message + '"></td>';
                        html += '</tr>';
                    }
                    html += '</tbody></table>';
                }
                $('#payment_link_email_content').html(html); // load here
            })
            .fail(function() {
                $('#payment_link_email_content').html(
                    '<i class="glyphicon glyphicon-info-sign"></i> Something went wrong, Please try again...');

            });
    } else {
        $("#confirmBtn").hide();
        $('#payment_link_email_content').html('<i class="glyphicon glyphicon-info-sign"></i> No data');
    }
}

function send_link_as_email() {
    var form = $('#send_link_as_email')[0];
    var formData = new FormData(form);
    // return false;
    $.ajax({
        url: '<?php echo site_url('admission_flow/send_payment_link_toParent'); ?>',
        type: 'post',
        data: formData,
        processData: false,
        contentType: false,
        success: function(data) {
            var resData = JSON.parse(data);
            if (resData.result == 1) {
                $('#send_payment_link_to_parent').modal('hide');
            } else {
                $('#send_payment_link_to_parent').modal('show');
            }
            $('#confirmBtn').prop('disabled', false);
        }
    });
}

function construct_installment_types_details(instype) {
    var html = '';
    for (var typeId in instype) {
        html += ' <option value="' + instype[typeId].feev2_blueprint_installment_types_id + '">' + instype[typeId]
            .type_name + '</option>';
    }
    return html;
}

function custom_enable_component() {
    var select = $('#customId').val();
    var bpId = $('#blueprint_id').val();
    $('#cohort_id').val(select);
    if (select === 'CUSTOM') {
        $('#cohort_status').val(select);
        $('#custom_h3').html('Custom Fee Structure')
        var blueprint_id = bpId;
        $.ajax({
            url: '<?php echo site_url('feesv2/fees_collection/get_all_installments_blueprint_wise') ?>',
            type: 'post',
            data: {
                'blueprint_id': blueprint_id
            },
            success: function(data) {
                var data = JSON.parse(data);
                installments_data(data[0].instypeId);
                var output = '';
                for (var i = 0; i < data.length; i++) {
                    output += '<option value="' + data[i].instypeId + '">' + data[i].type_name +
                    '</option>';
                }
                $('#ins_types_custom').html(output);
                $('#ins_types_custom_wrapper').show();
                $('#installment_type_display').hide();

            }
        });
        $('.customEnable').prop('readonly', false);
        // installments_data(0);
    } else {
        $('#cohort_status').val('STANDARD');
        var instypes = $('#ins_types').val();
        $('.customEnable').prop('readonly', true);
        $('#installment_type_display').show();
        $('#ins_types_custom_wrapper').hide();
        installments_data(instypes);
    }
}
function installments_data(instypes) {
      var select = $('#customId').val();
    $.ajax({
      url:'<?php echo site_url('feesv2/fees_collection/get_installment_type_wisedata') ?>',
      type:'post',
      data : {'instypes':instypes,'select':select},
      success : function(result){
        var res = JSON.parse(result);
        var data = res.insData;
        var fine = res.fine_amount;
        var html  = '';
        var grand_total = 0;
        var j=1;
        // html += '<table class="table table-bordered" style="margin-top:2rem">';
        html +='<input type="hidden" id="no_of_installments" value="'+Object.keys(data).length+'">';
        $.each(data, function(key, comp) {
    var fineAmountArry = fine[key];
    var fineAmount = '';
    $.each(fineAmountArry, function(index, value) {
        if (value.fine_amount != 0) {
            fineAmount = '<span class="text-danger float-end">Fine: ₹' + value.fine_amount + '</span>';
        }
        html += '<input type="hidden" name="fine_amount[' + index + ']" value="' + value.fine_amount + '">';
    });

    html += '<span class="fw-semibold mb-3" style="font-size: 14px;"><b>' + key + '</b></span>';
    html += '<div class="row mb-3">';

    var total_amount = 0;
    var k = 1;

    html += '<input type="hidden" id="no_of_components" value="' + comp.length + '">';
    html += '<div class="col-md-7">';

    $.each(comp, function(i, component) {
        if (component.is_concession_eligible == 1) {
            total_amount += parseInt(component.compAmount);
            grand_total += parseInt(component.compAmount);
        }

        html += '<div class="row align-items-center mb-3" style="padding:4px">';
        html += '<div class="col-6" style="font-size: 13px;font-style: normal;">' + component.compName + '</div>';
        html += '<div class="col-6">';
        html += '<input readonly="" required onkeyup="onkyeupfunction()" id="ins_amount_' + j + '_' + k +
                '" class="form-control text-end customEnable input_cls" type="text" name="comp_amount[' +
                component.feev2_installment_id + '][' + component.feev2_blueprint_component_id +
                ']" value="' + component.compAmount + '" style="background-color:white">';
        html += '<input type="hidden" name="concession_amount[' + component.feev2_installment_id +
                '][' + component.feev2_blueprint_component_id + ']" value="0">';
        html += '</div></div>';
        k++;
    });

    html += '<div class="row mb-2" style="padding:4px">';
    html += '<div class="col-6 fw-medium" style="font-size: 13px;font-style: normal;">Total</div>';
    html += '<div class="col-6 text-end" id="total_ins_amount_'+j+'">₹' + total_amount + '</div>';
    html += '</div>';
    html += '<hr>';
    html += '<div class="row" style="padding:4px">';
    html += '<div class="col-6 fw-bold"><b>Grand Total</b></div>';
    html += '<div class="col-6 text-end fw-bold">₹<b id="grandTotal">' + total_amount + '</b></div></b>';
    html += '</div>';
    html += '</div>';

    // Right column controls
    html += '<div class="col-md-5" style="margin-top: 5px;">';
    html += '<div class="ms-md-4 mt-4 mt-md-0">';
    html += '<div class="mb-3 d-flex align-items-center justify-content-between">';
    html += '<label class="fw-semibold mb-0" style="width:50%;font-size:13px">Due Date</label>';
    html += '<div class="input-group">';
    html += `<input class="form-control input_cls" style="height:40px" type="date" name="pay_date" id="payment_date" placeholder="Select Date">`;
    html += '</div>';
    html += '</div>';

    html += ' <div style="width: 2px; background-color: black;"></div>';
    
    html += '<div class="d-flex justify-content-between align-items-center mb-3" style="margin-top: 25px">';
    html += '<label class="fw-semibold mb-0" for="publish_fees" style="font-size:13px">Publish Fees</label>';
    html += '<label class="switch-ios">';
    html += '<input type="checkbox" id="publish_fees" name="publish_fees" checked>';
    html += '<span class="slider-ios"></span>';
    html += '</label>';
    html += '</div>';

    html += '<div class="d-flex justify-content-between align-items-center mb-3" style="margin-top: 25px">';
    html += '<label class="fw-semibold mb-0" for="online_fees" style="font-size:13px">Online Fees</label>';
    html += '<label class="switch-ios">';
    html += '<input type="checkbox" id="online_fees" name="online_fees" checked>';
    html += '<span class="slider-ios"></span>';
    html += '</label>';
    html += '</div>';


    html += '</div></div>'; // col-md-5
    html += '</div>'; // row
    j++;
});

html += '<input type="hidden" id="grand_total" value="' + grand_total + '">';
html += '</div>'; // container

$('#ajax_construct_popup').html(html);
      updateVariables();
      total_installment_calculation();
      after_ajax_custom_enabled(select);
    }
  });
}

function total_installment_calculation() {

    for (var j = 1; j <= noOfinstallments; j++) {
        var insAmount = 0;
        var cocAmount = 0;
        for (var i = 1; i <= noOfComponents; i++) {
            $('#ins_amount_' + j + '_' + i + '').each(function() {
                insAmount += +this.value;
            });
            // $('#con_'+j+'_'+i+'').each(function() {
            //     cocAmount += +this.value;
            // });
        }
        // $('#total_ins_amount_'+j).html(insAmount);
        // $('#total_ins_con_amount_'+j).html(cocAmount);
    }
}

function after_ajax_custom_enabled(select) {
    if (select === 'CUSTOM') {
        $('.customEnable').prop('readonly', false);
    }
    // else{
    //   $('.customEnable').prop('readonly',true);
    // }
}

function onkyeupfunction() {
    var grandTotal = 0;
    for (var j = 1; j <= noOfinstallments; j++) {
        var insAmount = 0;
        var cocAmount = 0;
        for (var i = 1; i <= noOfComponents; i++) {
            $('#ins_amount_' + j + '_' + i + '').each(function() {
                insAmount += +this.value;
            });
        }
        grandTotal += parseFloat(insAmount);
        $('#total_ins_amount_' + j).html(insAmount);
    }
    $('#grandTotal').html(grandTotal);
}
</script>

<script type="text/javascript">
function prepare_student_table(std, index, cohortsFilter) {
    var html = '';
    var j = 0;
    for (var i in std) {
        $('#admission_one_time_fees').val(std[i].total_fee);
    }

}

var flag = false;

function check_release_offer() {
    var admission_id = '<?php echo $afId ?>';
    var fees_auto_assign = '<?php echo $config_val['fees_auto_assign_default_credentials'] ?>';
    var document_verification = '<?php echo $this->settings->getSetting('document_verification_in_admissions'); ?>';
    $.ajax({
        url: '<?php echo site_url('admission_flow/check_admission_offer_release'); ?>',
        type: 'post',
        data: {
            'admission_id': admission_id
        },
        success: function(data) {
            var res = $.parseJSON(data);
            if (res.check == 1 && fees_auto_assign != '' && document_verification != 1) {
                $('#admReleaseshow').show();
                $('#admStatusshow').hide();
                get_release_offer_details();
            } else if (res.check == 1 && fees_auto_assign != '' && document_verification == 1) {
                check_document_verification_status();
            } else if (res.check == 0) {
                flag = true;
                var detail = res.details;
                $('#release_gradeName').html(detail.class_name);
                $('#release_sectionName').html(detail.section_name);
                $('#offer_released_date').html(detail.offer_released_date);
                $('#boarding_selected').html(detail.boarding);
                $('#is_rte_student').html(detail.is_rte);
                $('#transport_selected').html(detail.has_transport);
                $('#offer_release_joining_date').html(detail.date_of_joining);
                $('#offer_released_staffkid_or_transfer').html(detail.staff_transfered);

                $('#release_seatNumber').html(detail.seat_allotment_no);
                $('#release_seatAllotmentDate').html(detail.allotment_date);
                var offers = res.offers;
                var htmtOffer = '';
                if(offers.length == 1){
                    var k = 1;
                    for (var o in offers) {
                        htmtOffer += '<div class="contact-names">' +
                            offers[o].offer_name + ':' + offers[o].offer_amount + '</div>';
                        k++;
                    }
                    
                }else if(offers.length > 1){

                    var tooltipContent = '';
                    for (var o in offers) {
                        tooltipContent += `<div>${offers[o].offer_name} - ${offers[o].offer_amount}</div>`;
                    }

                    htmtOffer += `<div class="contact-names d-flex align-items-center">
                        <span style="margin-right: 8px;">${offers.length} Offers Selected</span>
                        
                        <div class="position-relative info-icon-wrapper" style="width: 24px; height: 24px;">
                        <?php $this->load->view('svg_icons/info.svg') ?>
                            <div class="info-tooltip-content">
                                ${tooltipContent}
                            </div>
                        </div>
                    </div>`;
                }
                $('#offer_apply_details').html(htmtOffer);
                $('#relase_offer_form').hide();
                $('#success_released').show();
            } else {
                $('#relase_offer_form').show();
                $('#success_released').hide();
            }
        }
    });
}

function getclassSection() {
    var classid = $("#gradeId").val();
    var acad_year = '<?php echo $this->acad_year->getAcadYearID() ?>';
    $.ajax({
        url: '<?php echo site_url('student/student_controller/getClassess/'); ?>',
        type: 'post',
        data: {
            'classid': classid,
            'acad_year': acad_year
        },
        success: function(data) {
            // console.log(data);
            $('#classSectionId').html('');
            var class_st = $.parseJSON(data);
            var output = '';
            output += '<option value="">-Select Section-</option>';
            var len = class_st.length;
            for (var i = 0, j = len; i < j; i++) {
                output += '<option value="' + class_st[i].id + '">' + class_st[i].section_name +
                '</option>';
            }
            $('#classSectionId').html(output);
        }
    });
}

function getcombinations() {
    var classid = $("#gradeId").val();
    var acad_year = '<?php echo $this->acad_year->getAcadYearID() ?>';
    var combination_selected = $('#admission_student-combination').html();
    $.ajax({
        url: '<?php echo site_url('admission_flow/getCombinations_new'); ?>',
        type: 'post',
        data: {
            'classid': classid,
            'acad_year': acad_year
        },
        success: function(data) {
            $('#combination').html('');
            var comb = $.parseJSON(data);
            var output = '';
            output += '<option value="">-Select Combination-</option>';
            var len = comb.length;
            for (var i = 0, j = len; i < j; i++) {
                var selected = '';
                if(combination_selected == comb[i].combination){
                    selected = 'selected';
                }
                if (comb[i].combination != '' && comb[i].combination != 0) {
                    output += '<option value="' + comb[i].id + '" '+selected+'>' + comb[i].combination +
                        '</option>';
                }
            }
            $('#combination').html(output);
        }
    });

}

$(document).ready(function() {
    var maxDate = new Date();
    maxDate.setDate(maxDate.getDate());
    var minDate = new Date();
    minDate.setFullYear(minDate.getFullYear() - 1);
    var dateNow = new Date();

    $('#joining_date').datetimepicker({
        viewMode: 'days',
        format: "DD-MM-YYYY"
    });

    $(document).on('click', function(event) {
        var $target = $(event.target);
        if (!$target.closest('.bootstrap-select').length && $('.bootstrap-select').hasClass('open')) {
            $('.bootstrap-select').removeClass('open show'); 
            $('.dropdown-menu').removeClass('show'); 
        }
    });

});
function release_offers() {
    var $form = $('#relase_offer_form');
    var rte_status = $('#rte_id').val();
    if ($form.parsley().validate()) {
        var stdName = $('#s_name').html();
        bootbox.confirm({
            title: "Confirm",
            message: "Initiating Release offer to " + stdName + ". Do you want to continue?",
            className: "medium",
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-success'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-danger'
                }
            },
            callback: function(result) {
                if (result) {
                    $('#loader').show();
                    $('#relase_offer_form').css('opacity', '0.5');
                    var form = $('#relase_offer_form')[0];
                    var formData = new FormData(form);
                    $('#relase_offer_button').html('Please wait ...').attr('disabled', 'disabled');
                    $.ajax({
                        url: '<?php echo site_url('admission_flow/move_student_data_to_temp'); ?>',
                        type: 'post',
                        data: formData,
                        // async: false,
                        processData: false,
                        contentType: false,
                        // cache : false,
                        success: function(data) {
                            if (data) {
                                if (rte_status == 1 || rte_status == 3) {
                                    $('#currentStatus').html('Student added to ERP');
                                } else {
                                    $('#currentStatus').html('Offer Released');
                                }
                                $('.releaedAfterHide').hide();
                                $('#loader').hide();
                                tabwise_list_data('release_offer');
                                $('#relase_offer_form').css('opacity', '1');
                                tabwise_list_data('fees');
                            }

                        },
                        complete: function() {
                            get_update_button_colors();
                        }
                    });
                }
            }
        });
    }
}

function nextTab(elem) {
    $(elem).next().find('a[data-toggle="tab"]').click();
}

function generatePdf_seat_allotment() {
    var admission_form_id = $('#admission_form_id').val();
    var admission_setting_id = $('#admission_setting_id').val();
    var seat_allotment_no = $('#seat_allotment_no').val();
    var seat_allotment_date = $('#seat_allotment_date').val();
    if (seat_allotment_no == '') {
        return false;
    }
    $.ajax({
        url: '<?php echo site_url('admission_process/seat_allotment_generate_pdf'); ?>',
        type: "post",
        data: {
            'admission_form_id': admission_form_id,
            'seat_allotment_no': seat_allotment_no,
            'admission_setting_id': admission_setting_id,
            'seat_allotment_date': seat_allotment_date
        },
        success: function(data) {
            location.reload();
        },
        error: function(err) {
            console.log(err);
        }
    });
}

function assign_admission_fees() {
    var grandTotal = $('#grandTotal').html();
    if(grandTotal == 0){
        bootbox.alert({
            title: "Alert",
            message: "No Fees Found",
            className: "medium",
            buttons: {
                ok: {
                    label: 'Ok',
                    className: 'btn-info'
                }
            }
        });
        return false;
    }
    var payDate = $('#payment_date').val();
    if (payDate == '') {
        bootbox.confirm({
            title: "Confirm",
            message: "Pay date not set. Do you want to continue?",
            className: "medium",
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-success'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-danger'
                }
            },
            callback: function(result) {
                if (result) {
                    assign_cohort_fees_structure();
                }
            }
        });
    } else {
        assign_cohort_fees_structure();
    }
}

function assign_cohort_fees_structure() {
    $('#fees_assing_button').prop('disabled', true).html('Please wait..');
    $('#loader1').show();
    $('#assign_fee_new').css('opacity', '0.5');
    var form = $('#assign_fee_new')[0];
    var formData = new FormData(form);
    $.ajax({
        url: '<?php echo site_url('feesv2/fees_student_v2/assign_fee_strucutre_new'); ?>',
        type: 'post',
        data: formData,
        // async: false,
        processData: false,
        contentType: false,
        success: function(data) {
            var fee = JSON.parse(data);
            var cohortStudentId = fee.cohort_student_id;
            if (cohortStudentId != 0) {
                $(function() {
                    new PNotify({
                        title: 'Success',
                        text: 'Assigned Successfully',
                        type: 'success',
                    });
                });
            }
            $('#loader1').hide();
            tabwise_list_data('fees');
            $('#assign_fee_new').css('opacity', '1');
            var active = $('.wizard .nav-tabs li.active');
            active.next().removeClass('disabled');
            nextTab(active);

            $('#fees_assing_button').prop('disabled', false).html('Assign');

        }
    });
}
</script>
<style type="text/css">
.medium {
    width: 600px;
    margin: auto;
}
</style>


<script type="text/javascript">
function send_provision_credentials_form_submit() {
    // $('#confirmBtn').prop('disabled',true).html('Please wait..');
    $('#loader1').show();
    var form = $('#send_provision_credentials')[0];
    var formData = new FormData(form);
    $.ajax({
        url: '<?php echo site_url('admission_flow/send_parent_provision_credentials_from_admissions'); ?>',
        type: 'post',
        data: formData,
        // async: false,
        processData: false,
        contentType: false,
        success: function(data) {
            var resData = JSON.parse(data);
            // console.log(resData);
            // return false;
            if (resData.result == 1) {
                $(function() {
                    new PNotify({
                        title: 'Success',
                        text: resData.message,
                        type: 'success',
                    });
                });
            } else if (resData.result == -1) {
                $(function() {
                    new PNotify({
                        title: 'Info',
                        text: resData.message,
                        type: 'info',
                    });
                });
            } else {
                $(function() {
                    new PNotify({
                        title: 'Error',
                        text: resData.message,
                        type: 'error',
                    });
                });
            }
            $('#summary_credentials').hide();
            $('.modal-backdrop').removeClass('modal-backdrop fade show');
            $('#loader1').hide();
            tabwise_list_data('credential');
            $('#confirmBtn').prop('disabled', false).html('Send Credentials');
        }
    });
}

function get_admission_followup_details() {
    var adm_id = '<?php echo $afId ?>';
    $.ajax({
        url: '<?php echo site_url('admission_process/get_admission_followup_details/'); ?>' + adm_id,
        type: 'post',
        success: function(data) {
            var follow_admission = $.parseJSON(data);
            // console.log(follow_admission);

            $('#followup_history').html(_construct_followup_data(follow_admission));
        },
        error: function(err) {
            console.log(err);
        }
    });
}

function _construct_followup_data(follow_admission) {
    // console.log(follow_admission.length);
    var html_output = '';

    if (follow_admission.length == 0) {
        html_output += `<div class="no-data-display">No Followups</div>`;
        return html_output;
    } else {
        html_output += `
          <h4>Admission Follow up history</h4>
            <table class="table table-bordered">
                <thead>
                <tr>
                    <th style="width:5%;">#</th>
                    <th>Date</th>
                    <th>Action taken</th>
                    <th>Follow up by</th>
                    <th>Remarks</th>
                    <th>Status</th>
                    <th>Next follow up date</th>
                    <th>Document</th>
                </tr>
                </thead>
                `;

        for (var i = 0; i < follow_admission.length; i++) {
            var data = follow_admission[i];
            // console.log(follow_admission);
            var followupBy = data['first_name'];
            if (data['first_name'] == null) {
                followupBy = '-';
            }
            var next_follow_date = data['next_follow_date'];
            if (data['next_follow_date'] == null) {
                next_follow_date = '-';
            }
            var document = '-';
            if (data['document_path']) {
                document =
                    `<a class="btn btn-secondary" href="#" onclick="openDocumentModal('${data['document_path']}')">View</a>`;
            }
            html_output += `
                    <tr>
                        <td>${i+1}</td>
                        <td>${data['created_on']}</td>
                        <td>
                        <a href="#" onclick="email_sms_popup_details('Admission','${data['follow_up_action']}',' ${data['id']}')" data-toggle="modal" data-target="#summary">
                        ${data['follow_up_action']}
                        </a>
                        </td>
                        <td style="text-transform: uppercase;">${followupBy}</td>
                        <td>${data['remarks']}</td>
                        <td>${data['status']}</td>
                        <td>${next_follow_date}</td>
                        <td>${document}</td>
                    </tr>
                  `;
        }
        html_output += `</table>`;

    }

    return html_output;
}

function openDocumentModal(documentPath) {
    // Set the document path inside the iframe
    document.getElementById("documentFrame").src = documentPath;

    // Show the Bootstrap modal
    var documentModal = new bootstrap.Modal(document.getElementById("documentModal"));
    documentModal.show();
}
</script>

<script>
var index = 0;
var total_students = 0;
var completed = 0;

function get_student_details_for_credentials() {
    var fees_auto_assign = '<?php echo $config_val['fees_auto_assign_default_credentials'] ?>';
    $("#progress").show();
    var admission_id = '<?php echo $afId ?>';
    $.ajax({
        url: '<?php echo site_url('admission_flow/get_credentials_student_parent_data'); ?>',
        type: 'post',
        data: {
            'admission_id': admission_id
        },
        success: function(data) {
            $("#progress").hide();
            var jsonData = JSON.parse(data);
            if (jsonData == 0) {
                $('.construct_sms_email').html(`<div class="d-flex align-items-center justify-content-center">
                        <div class="text-center p-4">
                            <span class="animate__animated animate__fadeIn d-block mb-4">
                                <div class="icon-container" style="margin: 0 auto;width:40%">
                                    <?php $this->load->view('svg_icons/offer_not_released_icon.svg') ?>
                                </div>
                            </span>
                            <h3 class="fw-bold mb-2"><b>Offer not released</b></h3>
                            <span class="text-muted" style="font-size: 16px;">Offer hasn't been released yet. Once it's available, you'll be able to view all related details.</span>
                        </div>
                    </div>`);
                return false;
            }
            var stdData = jsonData;
            if (index == 0) {
                constructFeeHeader(stdData);
            }
            completed += Object.keys(stdData).length;
            var progress = document.getElementById('progress-ind');
            progress.style.width = (completed / total_students) * 100 + '%';

            prepare_student_table(stdData, index);
            if (fees_auto_assign != '' && credentials_released != 1) {
                $('#construct_sms_email').css('opacity', '1');
                var active = $('.wizard .nav-tabs li.active');
                active.next().removeClass('disabled');
                nextTab(active);
                send_provision_credentials();
            }
        }
    });
}

var credentials_released = 0;

function constructFeeHeader(stdData) {
    var fees_auto_assign = '<?php echo $config_val['fees_auto_assign_default_credentials'] ?>';
    var check = '';
    var disabled = 'disabled';
    var email_select = '';
    var sms_select = '';
    if (fees_auto_assign != '' && credentials_released != 1) {
        check = 'checked';
        disabled = '';
    }
    if (fees_auto_assign == 'Email') {
        email_select = 'selected';
    } else if (fees_auto_assign == 'SMS') {
        sms_select = 'selected';
    }
    var html = `<table class="table table-bordered" id="student_provision">
            <thead>
                <tr>
                    <th rowspan="2">#</th>
                    <th rowspan="2">Student Name</th>
                    <th rowspan="2">Relation</th>
                    <th rowspan="2">Parent Name</th>
                    <th rowspan="2" style="width:10%">
                        <select class="form-control" onchange="send_method_type(this.value)" name="send_method_type">
                            <option value="email" ${email_select}>Email</option>
                            <option value="sms" ${sms_select}>SMS</option>
                        </select>
                    </th>
                    <th rowspan="2">Status</th>
                    <th>
                        <input type="button" onclick="send_provision_credentials()" id="sendButton" ${disabled}  class="btn" value="Send" style="border-radius:8px;color:#fff;background-color:#002e6b">
                    </th>
                    <th>
                        <input type="button" onclick="re_send_provision_credentials()" id="re-sendButton" disabled="true"  class="btn" value="Re-Send" style="border-radius:8px;color:#fff;background-color:#002e6b">
                    </th>
                    <th>
                        <input type="button" onclick="deactivate_provision_credentials()" id="deactivateButton" disabled="true"  class="btn" value="Deactivate" style="border-radius:8px;color:#fff;background-color:#002e6b">
                    </th>
                </tr>
                <tr>
                    <th>Send
                        <input type="checkbox" ${check} name="selectAll" value="send" onclick="check_all_credential(this, value)" id="sendAll" class="check">
                    </th>
                    <th>Re-Send
                        <input type="checkbox" name="selectAll" value="re_send" onclick="check_all_credential(this, value)" id="re_sendAll" class="check">
                    </th>
                    <th>Deactivate
                        <input type="checkbox" name="selectAll" value="deactivate" onclick="check_all_credential(this, value)" id="deactivateAll" class="check">
                    </th>
                </tr>
            </thead>
            </tbody>
        </table>`;

    $('.construct_sms_email').html(html);
}

function prepare_student_table(stdData, index) {
    var fees_auto_assign = '<?php echo $config_val['fees_auto_assign_default_credentials'] ?>';
    var check = '';
    var email_select = '';
    var sms_select = '';
    if (fees_auto_assign != '') {
        check = 'checked';
    }
    if (fees_auto_assign == 'Email') {
        email_select = 'selected';
    } else if (fees_auto_assign == 'SMS') {
        sms_select = 'selected';
    }

    var srNo = index * 20;
    var m = 0;
    var html = '';

    html += '<tbody>';
    for (var k in stdData) {
        var activeDisabled = 0;
        if (stdData[k].Active == 1) {
            activeDisabled = 1;
        }

        html += '<tr>';
        html += '<td>' + (m + 1 + srNo) + '</td>';
        html += '<td>' + stdData[k].studentName + '</td>';
        html += '<td>' + stdData[k].relation_type + '</td>';
        html += '<td>' + stdData[k].parentName + '</td>';
        //Display select box for choosing SMS or Email
        if (stdData[k].oldUid != null && stdData[k].userId != stdData[k].oldUid) {
            html += '<td>Sibling Connected</td>';
        } else {
            html +=
                '<td><select class="form-control method_type_dropdown" name="select_send_options" id="method_type_indiv_' +
                stdData[k].pid + '"><option value="email" ' + email_select + '>Email</option><option value="sms" ' +
                sms_select + '>SMS</option></select></td>';
        }

        //Display select box for choosing SMS or Email
        if (stdData[k].oldUid != null && stdData[k].userId != stdData[k].oldUid) {
            loggedStatus = 'Sibling Connected';
        } else {
            if (stdData[k].loggedin_atleast_once == 1) {
                loggedStatus = '<button type="button" class="btn btn-info btn-xs">Logged-in</button>';
            } else if (stdData[k].Active == 1) {
                loggedStatus = '<button type="button" class="btn btn-info btn-xs">Activated</button> ';
            } else {
                loggedStatus = '<button type="button" class="btn btn-danger btn-xs">Not activated</button>';
            }
        }
        html += '<td>' + loggedStatus + '</td>';

        //Display Send button
        if (stdData[k].oldUid != null && stdData[k].userId != stdData[k].oldUid) {
            html += '<td>Sibling connected</td>';
        } else {
            if (activeDisabled) {
                credentials_released = 1;
                html += '<td>Activated</td>';
            } else {
                html +=
                    `<td><input type="checkbox" ${check} onclick="check_smsIndividual()" name="send_credentials" value="${stdData[k].pid}" class="sendCheck"></td>`;
            }
        }

        //Display Resend button
        if (stdData[k].oldUid != null && stdData[k].userId != stdData[k].oldUid) {
            html += '<td>Sibling connected</td>';
        } else {
            if (activeDisabled) {
                html +=
                    `<td><input type="checkbox" onclick="check_reSMSIndividual()" name="re_send_credentials" value="${stdData[k].pid}" class="re_sendCheck"></td>`;
            } else {
                html += '<td>Not Activated</td>';
            }
        }

        //Display Deactivate button
        if (stdData[k].oldUid != null && stdData[k].userId != stdData[k].oldUid) {
            html += `<td>Sibling connected</td>`;
        } else {
            html +=
                `<td><input type="checkbox" onclick="check_deactivateIndividual()" name="deactivate_credentials" value="${stdData[k].userId}" class="deactivate_sendCheck"></td>`;
        }

        html += '</tr>';

        m++;
    }
    $('#student_provision').append(html);
    index++;
}

function check_all_credential(check, value) {
    if (value == 'send') {
        isCheckedBySend(check)
    }
    if (value == 're_send') {
        isCheckedByReSend(check)
    }

    if (value == 'deactivate') {
        isCheckedByDeactivate(check)
    }
}

function isCheckedBySend(check) {
    if ($(check).is(':checked')) {
        $('.sendCheck').prop('checked', true);
        $('#sendButton').prop('disabled', false);
        $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop({
            "disabled": "true",
            "checked": ""
        });
    } else {
        $('.sendCheck').prop('checked', false);
        $('#sendButton').prop('disabled', true);
        $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled', false);
    }
}

function isCheckedByReSend(check) {
    if ($(check).is(':checked')) {
        $('.re_sendCheck').prop('checked', true);
        $('#re-sendButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll').prop({
            "disabled": "true",
            "checked": ""
        });
    } else {
        $('.re_sendCheck').prop('checked', false);
        $('#re-sendButton').prop('disabled', true);
        $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled', false);
    }
}

function isCheckedByDeactivate(check) {
    if ($(check).is(':checked')) {
        $('.deactivate_sendCheck').prop('checked', true);
        $('#deactivateButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll').prop({
            "disabled": "true",
            "checked": ""
        });
    } else {
        $('.deactivate_sendCheck').prop('checked', false);
        $('#deactivateButton').prop('disabled', true);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll').prop('disabled', false);
    }
}

function send_method_type(value) {
    if (value == 'sms') {
        $('.method_type_dropdown').find("option[value='sms']").attr('selected', 'selected');
        $('.method_type_dropdown').find("option[value='email']").removeAttr('selected', 'selected');
    } else {
        $('.method_type_dropdown').find("option[value='email']").attr('selected', 'selected');
        $('.method_type_dropdown').find("option[value='sms']").removeAttr('selected', 'selected');
    }
}

function check_smsIndividual() {
    if ($("input[class='sendCheck']:checked").length > 0) {
        $('#sendButton').prop('disabled', false);
        $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop({
            "disabled": "true",
            "checked": ""
        });
    } else {
        $('.sendCheck').prop('checked', false);
        $('#sendButton').prop('disabled', true);
        $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled', false);
    }
}

function check_reSMSIndividual() {
    if ($("input[class='re_sendCheck']:checked").length > 0) {
        $('#re-sendButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll').prop({
            "disabled": "true",
            "checked": ""
        });
    } else {
        $('.re_sendCheck').prop('checked', false);
        $('#re-sendButton').prop('disabled', true);
        $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled', false);
    }
}

function check_deactivateIndividual() {
    if ($("input[class='deactivate_sendCheck']:checked").length > 0) {
        $('#deactivateButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll').prop({
            "disabled": "true",
            "checked": ""
        });
    } else {
        $('.deactivate_sendCheck').prop('checked', false);
        $('#deactivateButton').prop('disabled', true);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll').prop('disabled', false);
    }
}

function send_provision_credentials() {
    var pids = [];
    var smsType = [];
    $('.sendCheck:checked').each(function() {
        pids.push($(this).val() + '_' + $('#method_type_indiv_' + $(this).val()).val());

    });
    if (pids.length <= 0) {
        return false;
    }
    $('#summary').modal('show');
    sendProvisionLink(pids, 'activation');
    $("#process").val('activation');
    $("#smsType").val('send');
}

function re_send_provision_credentials() {
    var pids = [];
    var smsType = [];
    $('.re_sendCheck:checked').each(function() {
        pids.push($(this).val() + '_' + $('#method_type_indiv_' + $(this).val()).val());

    });
    if (pids.length <= 0) {
        return false;
    }
    $('#summary').modal('show');
    sendProvisionLink(pids, 'activation');
    $("#process").val('activation');
    $("#smsType").val('send');
}

function paytodate_change_the_date(cohort_student_id,due_date) {
    let formattedDate = '';
    if (due_date && typeof due_date === 'string') {
        let dateParts = due_date.split("-");
        if (dateParts.length === 3) {
            formattedDate = dateParts[2] + "-" + dateParts[1] + "-" + dateParts[0];
        }
    }
    console.log(formattedDate);
    bootbox.prompt({
        size: 'small',
        title: 'Extend Due Date',
        inputType: 'date',
        className: 'bootbox-input-date widthAdjust',
       value: formattedDate,
        callback: function(result) {
            if (result != '') {
                $.ajax({
                    url: '<?php echo site_url('admission_process/update_pay_to_date'); ?>',
                    data: {
                        'cohort_student_id': cohort_student_id,
                        'pay_date': result
                    },
                    type: 'post',
                    success: function(data) {
                        if (data) {
                            $(function() {
                                new PNotify({
                                    title: 'Success',
                                    text: 'Payment date changed Successfully',
                                    type: 'success',
                                });
                            });
                        } else {
                            $(function() {
                                new PNotify({
                                    title: 'Error',
                                    text: 'Something went wrong',
                                    type: 'error',
                                });
                            });
                        }
                        check_fees_amount();

                    },
                    error: function(err) {
                        console.log(err);
                    }
                });
            }


            /* result = String containing user input if OK clicked or null if Cancel clicked */
        }
    });

}

function deactivate_provision_credentials() {
    var userIds = [];
    $('.deactivate_sendCheck:checked').each(function() {
        userIds.push($(this).val());
    });
    if (userIds.length <= 0) {
        return false;
    }

    bootbox.confirm({
        title: "Deactivating User",
        message: "You are deactivating the total user " + userIds.length + ". Are you sure?",
        className: 'medium',
        buttons: {
            confirm: {
                label: 'Yes',
                className: 'btn-success'
            },
            cancel: {
                label: 'No',
                className: 'btn-danger'
            }
        },
        callback: function(result) {
            if (result) {
                $.ajax({
                        url: '<?php echo site_url('parent_activation/deactivate_provision_credentials_by_user_id'); ?>',
                        type: 'POST',
                        data: {
                            'userIds': userIds
                        }
                    })
                    .done(function(data) {
                        if (data) {
                            get_student_details_for_credentials();
                        } else {
                            bootbox.alert("Something went wrong");
                        }
                    })
                    .fail(function() {
                        bootbox.alert("Something went wrong");
                    });
            }
        }
    });
}

$(document).on('click', '#father_signature_edit', function() {
    $('#father_signature').trigger('click');
    $('#father_signature').change(function() {
        var src = $(this).val();
        // var isFileOk = validatePhoto(this.files[0])
        if (src && validatePhoto1(this.files[0], 'fileuploadError1')) {
            $("#fileuploadError1").html("");
            readURL1(this);
            $("#father_signature_button").show();
        } else {
            this.value = null;
            $('#father_signature_button').hide();
        }
    });
});

function readURL1(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            $('#father_img_signature').attr('src', e.target.result);
            $('#father_img_signature').css('opacity: 0.5');
        }
        reader.readAsDataURL(input.files[0]);
    }
}

function validatePhoto1(file, errorId) {
    if (file.size > 10000000 || file.fileSize > 10000000) {
        $("#" + errorId).html("Allowed file size exceeded. (Max. 10 MB)")
        return false;
    }
    if (file.type != 'image/jpeg' && file.type != 'image/jpg' && file.type != 'image/png') {
        $("#" + errorId).html("Allowed file types are jpeg, jpg and png");
        return false;
    }
    return true;
}


$(document).on('click', '#guardian_signature_edit', function() {
    $('#guardian_signature').trigger('click');
    $('#guardian_signature').change(function() {
        var src = $(this).val();
        // var isFileOk = validatePhoto(this.files[0])
        if (src && validatePhoto2(this.files[0], 'fileuploadError2')) {
            $("#fileuploadError2").html("");
            readURL2(this, 'guardian');
            $("#guardian_signature_button").show();
        } else {
            this.value = null;
            $('#guardian_signature_button').hide();
        }
    });
});

$(document).on('click', '#father_photo_edit', function() {
    $('#father_photo').trigger('click');
    $('#father_photo').change(function() {
        var src = $(this).val();
        // var isFileOk = validatePhoto(this.files[0])
        if (src && validatePhoto2(this.files[0], 'fileuploadError3')) {
            $("#fileuploadError3").html("");
            readURL2(this, 'father_photo');
            $("#father_photo_upload_button").show();
        } else {
            this.value = null;
            $('#father_photo_upload_button').hide();
        }
    });
});

$(document).on('click', '#mother_photo_edit', function() {
    $('#mother_photo').trigger('click');
    $('#mother_photo').change(function() {
        var src = $(this).val();
        // var isFileOk = validatePhoto(this.files[0])
        if (src && validatePhoto2(this.files[0], 'fileuploadError4')) {
            $("#fileuploadError4").html("");
            readURL2(this, 'mother_photo');
            $("#mother_photo_upload_button").show();
        } else {
            this.value = null;
            $('#mother_photo_upload_button').hide();
        }
    });
});

$(document).on('click', '#family_photo_edit', function() {
    $('#family_photo').trigger('click');
    $('#family_photo').change(function() {
        var src = $(this).val();
        // var isFileOk = validatePhoto(this.files[0])
        if (src && validatePhoto2(this.files[0], 'fileuploadError5')) {
            $("#fileuploadError5").html("");
            readURL2(this, 'family_photo');
            $("#family_photo_upload_button").show();
        } else {
            this.value = null;
            $('#family_photo_upload_button').hide();
        }
    });
});

$(document).on('click', '#stud_sign_photo_edit', function() {
    $('#stud_sign_photo').trigger('click');
    $('#stud_sign_photo').change(function() {
        var src = $(this).val();
        // var isFileOk = validatePhoto(this.files[0])
        if (src && validatePhoto2(this.files[0], 'fileuploadError5')) {
            $("#fileuploadError5").html("");
            readURL2(this, 'stud_sign_photo');
            $("#stud_sign_photo_upload_button").show();
        } else {
            this.value = null;
            $('#stud_sign_photo_upload_button').hide();
        }
    });
});

function readURL2(input, photo_type) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            if (photo_type == 'guardian') {
                $('#guardian_img_signature').attr('src', e.target.result);
                $('#guardian_img_signature').css('opacity: 0.5');
            } else if (photo_type == 'father_photo') {
                $('#father_img').attr('src', e.target.result);
                $('#father_img').css('opacity: 0.5');
            } else if (photo_type == 'mother_photo') {
                $('#mother_img').attr('src', e.target.result);
                $('#mother_img').css('opacity: 0.5');
            } else if (photo_type == 'family_photo') {
                $('#family_img').attr('src', e.target.result);
                $('#family_img').css('opacity: 0.5');
            } else if (photo_type == 'stud_sign_photo') {
                $('#stud_sign_img').attr('src', e.target.result);
                $('#stud_sign_img').css('opacity: 0.5');
            }

        }
        reader.readAsDataURL(input.files[0]);
    }
}

function validatePhoto2(file, errorId) {
    if (file.size > 10000000 || file.fileSize > 10000000) {
        $("#" + errorId).html("Allowed file size exceeded. (Max. 10 MB)")
        return false;
    }
    if (file.type != 'image/jpeg' && file.type != 'image/jpg' && file.type != 'image/png') {
        $("#" + errorId).html("Allowed file types are jpeg, jpg and png");
        return false;
    }
    return true;
}

$('#fileupload').change(function() {
    var src = $(this).val();
    // var isFileOk = validatePhoto(this.files[0])
    if (src && validatePhoto(this.files[0], 'fileupload')) {
        $("#fileuploadError").html("");
        readURL(this);
        $("#student_profile_photo").show();
    } else {
        this.value = null;
        $('#student_profile_photo').hide();
    }
});

function validatePhoto(file, errorId) {
    if (file.size > 10000000 || file.fileSize > 10000000) {
        $("#" + errorId + "Error").html("Allowed file size exceeded. (Max. 10 MB)")
        return false;
    }
    if (file.type != 'image/jpeg' && file.type != 'image/jpg' && file.type != 'image/png') {
        $("#" + errorId + "Error").html("Allowed file types are jpeg, jpg and png");
        return false;
    }
    return true;
}

function readURL(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            $('#stud_image').attr('src', e.target.result);
            $('#stud_image').css('opacity: 0.5');
        }
        reader.readAsDataURL(input.files[0]);
    }
}

function save_profile_photo(af_id, type, old_path) {
    // $('#percentage-completed_student').show();
    if (type == 'student_photo') {
        var file_data = $('#fileupload').prop('files')[0];
        $('#stud_image').css('opacity', '0.3');
        $("#student_profile_photo").prop('disabled', true).html(
            '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>');
        old_path = $('#student_photo_url').val();
    } else if (type == 'father_sign') {
        var file_data = $('#father_signature').prop('files')[0];
        $('#father_img_signature').css('opacity', '0.3');
        $("#father_signature_button").prop('disabled', true).html(
            '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>');
    } else if (type == 'guardian_photo') {
        var file_data = $('#guardian_signature').prop('files')[0];
        $('#guardian_img_signature').css('opacity', '0.3');
        $("#guardian_signature_button").prop('disabled', true).html(
            '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>');
    } else if (type == 'father_photo') {
        var file_data = $('#father_photo').prop('files')[0];
        $('#father_photo').css('opacity', '0.3');
        $("#father_photo_upload_button").prop('disabled', true).html(
            '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>');
    } else if (type == 'mother_photo') {
        var file_data = $('#mother_photo').prop('files')[0];
        $('#mother_photo').css('opacity', '0.3');
        $("#mother_photo_upload_button").prop('disabled', true).html(
            '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>');
    } else if (type == 'family_photo') {
        var file_data = $('#family_photo').prop('files')[0];
        $('#family_photo').css('opacity', '0.3');
        $("#family_photo_upload_button").prop('disabled', true).html(
            '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>');
    } else if (type == 'stud_sign') {
        var file_data = $('#stud_sign_photo').prop('files')[0];
        $('#stud_sign_photo').css('opacity', '0.3');
        $("#stud_sign_photo_upload_button").prop('disabled', true).html(
            '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>');
    }

    completed_promises = 0;
    current_percentage = 0;
    total_promises = 1;
    in_progress_promises = total_promises;
    save_FileToStorage(file_data, af_id, type, old_path);
}

function save_FileToStorage(file, af_id, type, old_path) {
    $.ajax({
        url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {
            'filename': file.name,
            'file_type': file.type,
            'folder': 'Admission_form_document'
        },
        success: function(response) {
            single_file_progress(0);
            response = $.parseJSON(response);
            var path = response.path;
            var signedUrl = response.signedUrl;
            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type,
                    "x-amz-acl": "public-read"
                },
                processData: false,
                data: file,
                xhr: function() {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function(e) {
                        // For uploads
                        if (e.lengthComputable) {
                            single_file_progress(e.loaded / e.total * 100 | 0);
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    savePhoto(path, af_id, file, type, old_path);
                    // $('#percentage-completed_student').hide();
                    if (type == 'student_photo') {
                        $('#stud_image').css('opacity', '1');
                    } else if (type == 'father_sign') {
                        $('#father_img_signature').css('opacity', '1');
                    } else if (type == 'guardian_sign') {
                        $('#guardian_img_signature').css('opacity', '1');
                    }

                },
                error: function(err) {
                    // console.log(err);
                    reject(err);
                }
            });
        },
        error: function(err) {
            reject(err);
        }
    });
}

function savePhoto(orginalsizepath, af_id, file_data, type, old_path) {
    var form_data = new FormData();
    form_data.append('file', file_data);
    form_data.append('af_id', af_id);
    form_data.append('high_quality', orginalsizepath);
    form_data.append('type', type);
    form_data.append('old_photo_url', old_path);
    $.ajax({
        url: '<?php echo site_url('admission_process/save_student_profile_photo') ?>',
        type: 'post',
        data: form_data,
        cache: false,
        contentType: false,
        processData: false,
        success: function(data) {
            $("#student_profile_photo").html('Saved');
            setTimeout(function() {

                if (type == 'student_photo') {
                    $("#student_profile_photo").prop('disabled', false).html('Save');
                    $("#student_profile_photo").hide('500');
                } else if (type == 'father_sign') {
                    $("#father_signature_button").prop('disabled', false).html('Save');
                    $("#father_signature_button").hide('500');
                } else if (type == 'guardian_photo') {
                    $("#guardian_signature_button").prop('disabled', false).html('Save');
                    $("#guardian_signature_button").hide('500');
                } else if (type == 'father_photo') {
                    $("#father_photo_upload_button").prop('disabled', false).html('Save');
                    $("#father_photo_upload_button").hide('500');
                } else if (type == 'mother_photo') {
                    $("#mother_photo_upload_button").prop('disabled', false).html('Save');
                    $("#mother_photo_upload_button").hide('500');
                } else if (type == 'family_photo') {
                    $("#family_photo_upload_button").prop('disabled', false).html('Save');
                    $("#family_photo_upload_button").hide('500');
                } else if (type == 'stud_sign') {
                    $("#stud_sign_photo_upload_button").prop('disabled', false).html('Save');
                    $("#stud_sign_photo_upload_button").hide('500');
                }
            }, 2000);
        }
    });
}

function single_file_progress(percentage) {
    if (percentage == 100) {
        in_progress_promises--;
        if (in_progress_promises == 0) {
            current_percentage = percentage;
        }
    } else {
        if (current_percentage < percentage) {
            current_percentage = percentage;
        }
    }
    return false;
}

function get_previous_school_details_data() {
    var af_id = '<?php echo $afId ?>';
    var admission_setting_id = '<?php echo $admission_setting_id ?>';
    $.ajax({
        url: '<?php echo site_url('admission_process/get_previous_school_details_data') ?>',
        type: 'post',
        data: {
            'af_id': af_id,
            'admission_setting_id': admission_setting_id
        },
        success: function(data) {
            var resData = $.parseJSON(data);
            var subjectmarks = resData.submarks;
            $('#previus_school_display_data').html(construct_school_details_data(resData));
            $('#total_marks_school_display_data').html(subjectmarks);
        }
    });
}

function construct_school_details_data(schooldata) {
    var configVal = $.parseJSON(schooldata.config_val.prev_eduction_info);
    var html = '';
    if (configVal == null) {
        html += `<h3 class="no-data-display">No Previous Education</h3>`;
        return html;
    }
    var configYear = configVal.year;
    var editdata = schooldata.edit_data;

    for (var i = 0; i < configYear.length; i++) {
        var showicon = '<span class="fa fa-plus " style="font-size: 16px;"></span>';
        var editschool_name = '';
        var editclass_name = '';
        var editboard_name = '';
        var editschool_address = '';
        var editschool_mediumofinstruction = '';
        var editschool_expelledorsuspended = '';
        var editschool_expelledreason = '';
        var editschool_transferreason = '';
        var previous_yeartableid = '';
        var report_card = '';
        var class_label = '<?php echo $this->settings->getSetting('your_word_for_class') ?>';
        var school_label = '<?php echo $this->settings->getSetting('your_word_for_institute') ?>';
        if (class_label == '') {
            class_label = 'Class';
        }
        if (school_label == '') {
            school_label = 'School';
        }
        if (editdata.hasOwnProperty(configYear[i])) {
            showicon = '<span class="fa fa-pencil " style="font-size: 16px;"></span>';

            editschool_name = editdata[configYear[i]].school_name;
            editclass_name = editdata[configYear[i]].class;
            editboard_name = editdata[configYear[i]].board;
            editschool_address = editdata[configYear[i]].school_address;
            editschool_mediumofinstruction = editdata[configYear[i]].medium_of_instruction;
            editschool_schoolType = editdata[configYear[i]].type_of_school;
            editschool_expelledorsuspended = editdata[configYear[i]].expelled_or_suspended;
            editschool_expelledreason = editdata[configYear[i]].expelled_or_suspended_description;
            previous_school_ratings = editdata[configYear[i]].previous_school_ratings;
            editschool_transferreason = editdata[configYear[i]].transfer_reason;
            previous_yeartableid = editdata[configYear[i]].apsId;
            report_card = editdata[configYear[i]].report_card

        }
        // console.log(marks[0].sub_name.name);
        html += `<div class="panel panel-info" id="config${configYear[i]}">
          <div class="panel-heading ui-draggable-handle">
              <h3 class="panel-title">Year - ${configYear[i]}`;

        if (editdata.hasOwnProperty(configYear[i])) {

            $("#add_edit_class_to_show_popup").addClass(`edit_uploader_${configYear[i]}`);
            html +=
                `  <a  href="" class="new_circleShape_res" style="background-color: #fe970a;" data-toggle="modal" data-target=".edit_uploader_${configYear[i]}" onclick="previous_year_edit_add_data('${configYear[i]}', 'edit', '${editschool_name}', '${editclass_name}', '${editschool_address}', '${editschool_mediumofinstruction}', '${editschool_expelledreason}', '${editschool_transferreason}', '${editboard_name}', '${editschool_expelledorsuspended}')">`;
        } else {
            $("#add_edit_class_to_show_popup").addClass(`add_uploader_${configYear[i]}`);
            html +=
                `  <a  href="" class="new_circleShape_res" style="background-color: #fe970a;" data-toggle="modal" data-target=".add_uploader_${configYear[i]}" onclick="previous_year_edit_add_data('${configYear[i]}', 'add', '${editschool_name}', '${editclass_name}', '${editschool_address}', '${editschool_mediumofinstruction}', '${editschool_expelledreason}', '${editschool_transferreason}', '${editboard_name}', '${editschool_expelledorsuspended}')">`;
        }


        html += `    ${showicon}
                  </a>
              </h3>
          </div>`;
        html += `<div class="panel-body">`;
        if (editdata.hasOwnProperty(configYear[i]))
            html += `<div class="col-md-4" style="padding-left:0px"> <table class="table table-bordered">
                        <thead><tr><th>${school_label} Name</th></tr></thead>
                        <thead><tr><th>${class_label}</th></tr></thead>
                        <thead><tr><th>Board</th></tr></thead>
                        <thead><tr><th>${school_label} Address</th></tr></thead>
                        <thead><tr><th>Type of School</th></tr></thead>
                        <thead><tr><th>Medium of Instruction</th></tr></thead>
                        <thead><tr><th>Expelled or Suspended</th></tr></thead>
                        <thead><tr><th>Expelled Reason</th></tr></thead>
                        <thead><tr><th>Transfer Reason</th></tr></thead>
                         <thead><tr><th>Previous school ratings</th></tr></thead>
                      </table></div>

                    <div class="col-md-8" style="margin-left: -2.2rem;"> <table class="table table-bordered">
                      <tbody><tr><td>${editschool_name || 'Not Provided'}</td></tr></tbody>
                      <tbody><tr><td>${editclass_name || 'Not Provided'}</td></tr></tbody>
                      <tbody><tr><td>${editboard_name || 'Not Provided'}</td></tr></tbody>
                      <tbody><tr><td>${editschool_address || 'Not Provided'}</td></tr></tbody>
                      <tbody><tr><td>${editschool_schoolType || 'Not Provided'}</td></tr></tbody>
                      <tbody><tr><td>${editschool_mediumofinstruction || 'Not Provided'}</td></tr></tbody>
                      <tbody><tr><td>${editschool_expelledorsuspended || 'Not Provided'}</td></tr></tbody>
                      <tbody><tr><td>${editschool_expelledreason || 'Not Provided'}</td></tr></tbody>
                      <tbody><tr><td>${editschool_transferreason || 'Not Provided'}</td></tr></tbody>
                      <tbody><tr><td>${previous_school_ratings || 'Not Provided'}</td></tr></tbody>
                    </table></div>
              `;
        if (report_card) {

            html += `  <a target="_blank" class="btn btn-primary" href="${report_card}">
                View Report Card
                </a>`;
        } else {
            html += `<span class="no-data-display">No Document attached</span>`;
        }



        html += ` </p>`;
        html += `</div>
        
        </div>     
       `;

    }
    return html;
}

var edit_or_add, current_year;

function previous_year_edit_add_data(year, edit_add, editschool_name, editclass_name, editschool_address,
    editschool_mediumofinstruction, editschool_expelledreason, editschool_transferreason, editboard_name,
    editschool_expelledorsuspended) {
    $("#schooling_school_new").val(editschool_name);
    $("#old_school_name").val(editschool_name);
    $("#school_address_new").val(editschool_address);
    $("#old_school_address").val(editschool_address);
    $("#medium_of_instruction_new").find(":selected").text(editschool_mediumofinstruction);
    $("#old_medium_of_instruction").val(editschool_mediumofinstruction);
    $("#schooling_class_new").val(editclass_name);
    $("#old_schooling_class").val(editclass_name);
    $("#schooling_board_new").find(":selected").text(editboard_name);
    $("#old_schooling_board").val(editboard_name);
    $("#transfer_reason_id_new").val(editschool_transferreason);
    $("#old_transfer_reason_id").val(editschool_transferreason);
    if (editschool_expelledorsuspended == 'Yes') {
        $("#expelled_or_suspended-1").removeAttr('checked');
        $("#expelled_or_suspended-0").attr('checked', 'true');
        $("#expelled_or_suspended_id").show();
    }
    $("#expelled_or_suspended_description_new").val(editschool_expelledreason);
    $("#old_expelled_or_suspended").val(editschool_expelledreason);

    $("#yearName").text(year);
    edit_or_add = edit_add;
    current_year = year;
}

function submit_previous_school_details() {
    var af_id = '<?php echo $afId ?>';
    var schoolYear = current_year;

    var $form = $('#school_previous_form');
    var form = $('#school_previous_form')[0];
    var formData = new FormData(form);
    formData.append('schoolYear', schoolYear);
    formData.append('af_id', af_id);
    formData.append('edit_or_add', edit_or_add);

    // Update and insert both 
    $.ajax({
        url: '<?php echo site_url('admission_process/update_previous_school_details_year_wise'); ?>',
        type: 'post',
        data: formData,
        processData: false,
        contentType: false,
        cache: false,
        success: function(data) {
            get_previous_school_details_data();
            $("#add_edit_class_to_show_popup").modal('hide');


        }
    });
}

function re_upload_document(document_id, document_name, view_type) {
    $('#upload_document form').trigger("reset");
    $('#re_afterSuccessUploadShow').hide();
    $('#re_percentage_doc_completed').html();
    $('#re_upload_document').removeAttr('disabled');
    $('#upload_document_id').val(document_id);
    $('#reupload_document_name').val(document_name);
    $('#view_type').val(view_type);
    $('#document_header').html('Re Upload ' + document_name);
    var adm_setting_id = '<?php echo $admission_setting_id; ?>';
    if (view_type == 'aadhar') {
        $('#document_modal_body').html(construct_aadhar_details(adm_setting_id));
    } else if (view_type == 'pan') {
        $('#document_modal_body').html(construct_pan_details(adm_setting_id));
    } else {
        $('#document_modal_body').html(construct_document_upload_details());
    }
}

function construct_aadhar_details(adm_setting_id) {
    var html = '';
    var template = '<?php echo site_url('admission_controller/download_declaration/') ?>' + adm_setting_id + '/' +
        'Aadhar Card Declaration/' + 'aadhar';
    html += `<div class="col-md-12" id="has_document" style="margin:10px 0px">
                    <label style="padding-left: 15px;" class="col-md-4" id="">Do you have Aadhar Card?</label>
                    <div class="col-md-8"> 
                        <label class="radio-inline " for="btn-1" style="padding-top:0px">
                            <input  type="radio" data-parsley-group="block1" name="has_document" id="btn-1" value="1"  onclick="show_aadhar_details(1)">
                            Yes
                        </label>
                        <label class="radio-inline" for="btn-0" style="padding-top:0px">
                            <input type="radio" data-parsley-group="block1" name="has_document" id="btn-0" value="0" onclick="show_aadhar_details(0)">
                            No
                        </label>
                    </div>
                </div>
                <div class="col-md-12" id="show_aadhar_data" style="margin-left: 15px 0;display:none">
                    <div class="col-md-12 p-0" style="padding-left:5px">
                        <label for="name_in_aadhar" class="col-md-4">Name As per Aadhar <font color="red">*</font></label>
                        <div class="col-md-6">
                            <div class="input-group">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                            <input class="form-control remove-required" id="name_in_aadhar" placeholder="Enter the name as per Aadhar" name="name_as_per__aadhar" type="text" data-parsley-error-message="Should contain only alphabets or spaces" data-parsley-pattern="^[a-zA-Z. ]+$">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 p-0" style="margin-top: 15px;padding-left:5px">
                        <label for="aadhar_number" class="col-md-4">Aadhar Number <font color="red">*</font></label>
                        <div class="col-md-6" >
                            <div class="input-group">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                            <input class="form-control remove-required" id="aadhar_number" placeholder="Enter the Aadhar Number" name="aadhar_number" type="number" data-parsley-minlength="12" data-parsley-maxlength="12" data-parsley-type="number" data-parsley-error-message="Should contain 12 Numbers">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 p-0" style="margin-top: 15px;padding-left:5px;">
                        <label for="" class="col-md-4">Upload Aadhar Document <font color="red">*</font></label>
                        <div class="col-md-6">
                            <input class="form-control" id="aadhar_doc_file" name="document_file" type="file" accept="application/pdf, image/png, image/jpeg">
                        </div>
                    </div>
                </div>
                <div class="col-md-12" id="no_aadhar_document" style="display:none">
                    <label style="padding-left: 15px;" class="col-md-4" id="attach_label">Have you applied for Aadhar Card?</label>
                    <div class="col-md-8"> 
                        <label class="radio-inline" for="btn-2" style="padding-top:0px">
                            <input  type="radio" data-parsley-group="block2" name="applied_form_document" id="btn-2" value="1"  onclick="upload_acknowledgement_details(1)">
                            Yes
                        </label>
                        <label class="radio-inline" for="btn-3" style="padding-top:0px">
                            <input type="radio" data-parsley-group="block2" name="applied_form_document" id="btn-3" value="0" onclick="upload_acknowledgement_details(0)">
                            No
                        </label>
                    </div>
                </div>
                <div class="col-md-12" id="show_acknowledgement_data" style="display:none">
                    <div class="col-md-12 p-0" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Upload Acknowledgement <font color="red">*</font></label>
                        <div class="col-md-6">
                            <input class="form-control remove-required" id="acknowledgement_file" name="acknowledgement_file" type="file" accept="application/pdf, image/png, image/jpeg">
                        </div>
                    </div>
                </div>
                <div class="col-md-12 p-0" id="download_aadhar_acknowledgement" style="display:none">
                    <div class="col-md-12" style="margin-top: 15px;">
                        <label for="" class="col-md-4">Download Declaration</label>
                        <div class="col-md-6">
                            <a href="${template}" class="btn btn-info">Download <i class="fa fa-download"></i></a>
                            <span class="help-block">Download the declaration, sign and upload it</span>
                        </div>
                    </div>
                    <div class="col-md-12" style="margin-top: 15px;">
                        <label for="" class="col-md-4">Upload Declaration <font color="red">*</font></label>
                        <div class="col-md-6">
                            <input class="form-control remove-required" id="aadhar_declaration" name="declaration_file" type="file" accept="application/pdf, image/png, image/jpeg">
                        </div>
                    </div>
                </div>`;
    return html;
}

function construct_pan_details(adm_setting_id) {
    var html = '';
    var template = '<?php echo site_url('admission_controller/download_declaration/') ?>' + adm_setting_id + '/' +
        'PAN Card Declaration/' + 'pan';
    html += `<div class="col-md-12" id="has_pan_document" style="margin:10px 0px">
                    <label style="padding-left: 15px;" class="col-md-4" id="">Do you have PAN Card?</label>
                    <div class="col-md-8"> 
                        <label class="radio-inline" for="panbtn-1" style="padding-top:0px">
                            <input type="radio" data-parsley-group="block3" name="has_document" id="panbtn-1" value="1"  onclick="show_pan_details(1)">
                            Yes
                        </label>
                        <label class="radio-inline" for="panbtn-0" style="padding-top:0px">
                            <input type="radio" data-parsley-group="block3" name="has_document" id="panbtn-0" value="0" onclick="show_pan_details(0)">
                            No
                        </label>
                    </div>
                </div>
                <div class="col-md-12" id="show_pancard_data" style="margin-left: 15px 0;display:none">
                    <div class="col-md-12 p-0" style="padding-left:5px">
                        <label for="pan_number" class="col-md-4">PAN Card Number <font color="red">*</font></label>
                        <div class="col-md-6" >
                            <div class="input-group">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                            <input class="form-control remove-required" id="pan_card_number" placeholder="Enter the PAN Card Number" name="pan_card_number"  data-parsley-minlength="10" data-parsley-maxlength="10" pattern="[A-Z0-9]" data-parsley-error-message="Should contain 10 Numbers">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 p-0" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Upload PAN Card Document <font color="red">*</font></label>
                        <div class="col-md-6">
                            <input class="form-control remove-required" id="pancard_doc_file" name="document_file" type="file" accept="application/pdf, image/png, image/jpeg">
                        </div>
                    </div>
                </div>
                <div class="col-md-12" id="no_pan_document" style="margin-top:15px;display:none">
                    <label style="padding-left: 15px;" class="col-md-4" id="">Have you applied for PAN Card?</label>
                    <div class="col-md-8"> 
                        <label class="radio-inline" for="pan_btn-2" style="padding-top:0px">
                            <input  type="radio" data-parsley-group="block2" name="applied_form_document" id="pan_btn-2" value="1"  onclick="upload_pan_acknowledgement_details(1)">
                            Yes
                        </label>
                        <label class="radio-inline" for="pan_btn-3" style="padding-top:0px">
                            <input type="radio" data-parsley-group="block2" name="applied_form_document" id="pan_btn-3" value="0" onclick="upload_pan_acknowledgement_details(0)">
                            No
                        </label>
                    </div>
                </div>
                <div class="col-md-12" id="show_pan_acknowledgement_data" style="display:none">
                    <div class="col-md-12 p-0" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Upload Acknowledgement <font color="red">*</font></label>
                        <div class="col-md-6">
                            <input class="form-control remove-required" id="pan_acknowledgement" name="acknowledgement_file" type="file" accept="application/pdf, image/png, image/jpeg">
                        </div>
                    </div>
                </div>
                <div class="col-md-12" id="download_pancard_acknowledgement" style="display:none">
                    <div class="col-md-12 p-0" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Download Declaration</label>
                        <div class="col-md-6">
                            <a href="${template}" class="btn btn-info">Download <i class="fa fa-download"></i></a>
                            <span class="help-block">Download the declaration, sign and upload it</span>
                        </div>
                    </div>
                    <div class="col-md-12 p-0" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Upload Declaration <font color="red">*</font></label>
                        <div class="col-md-6">
                            <input class="form-control remove-required" id="pan_declaration" name="declaration_file" type="file" accept="application/pdf, image/png, image/jpeg">
                        </div>
                    </div>
                </div>`;
    return html;
}

function construct_document_upload_details() {
    var html = '';
    html += `<div class="form-group" style=" display: flex; justify-content: center;">
                    <label class="col-md-4">Upload file</label>
                    <div class="col-md-6 ">
                        <input type="file" class="form-control" onchange="upload_admission_documents_path()" id="re_upload_document">
                        <span id="re_percentage_doc_completed" style="font-size: 15px; display: none;">0 %</span>
                        <div id="re_afterSuccessUploadShow">
                        </div>
                    </div>
                </div>`;
    return html;
}

function construct_document_upload() {
    var html = '';
    html += `<div class="col-md-12">
                    <label class="col-md-4">Upload file <font style="color:red"> *</font></label>
                    <div class="col-md-6 ">
                        <input type="file" class="form-control" name="document_file" accept="application/pdf, image/png, image/jpeg" required>
                    </div>
                </div>`;
    return html;
}

function show_aadhar_details(e) {
    if (e == 1) {
        $('#show_aadhar_data').show();
        $('#no_aadhar_document').hide();
        $('#show_acknowledgement_data').hide();
        $('#download_aadhar_acknowledgement').hide();
        $('#name_in_aadhar').attr('required', 'required');
        $('#aadhar_number').attr('required', 'required');
        $('#aadhar_doc_file').attr('required', 'required');
        $('#acknowledgement_file').removeAttr('required');
        $('#aadhar_declaration').removeAttr('required');
    } else {
        $('#no_aadhar_document').show();
        $('#show_aadhar_data').hide();
        $('#show_acknowledgement_data').hide();
        $('#download_aadhar_acknowledgement').hide();
        $('#name_in_aadhar').removeAttr('required');
        $('#aadhar_number').removeAttr('required');
        $('#aadhar_doc_file').removeAttr('required');
    }
}

function upload_acknowledgement_details(e) {
    if (e == 1) {
        $('#show_acknowledgement_data').show();
        $('#download_aadhar_acknowledgement').hide();
        $('#show_aadhar_data').hide();
        $('#acknowledgement_file').attr('required', 'required');
        $('#aadhar_declaration').removeAttr('required');
    } else {
        $('#download_aadhar_acknowledgement').show();
        $('#show_acknowledgement_data').hide();
        $('#show_aadhar_data').hide();
        $('#acknowledgement_file').removeAttr('required');
        $('#aadhar_declaration').attr('required', 'required');
    }
}

function show_pan_details(e) {
    if (e == 1) {
        $('#show_pancard_data').show();
        $('#no_pan_document').hide();
        $('#show_pan_acknowledgement_data').hide();
        $('#download_pancard_acknowledgement').hide();
        $('#pan_card_number').attr('required', 'required');
        $('#pancard_doc_file').attr('required', 'required');
        $('#pan_acknowledgement').removeAttr('required');
        $('#pan_declaration').removeAttr('required');
    } else {
        $('#no_pan_document').show();
        $('#show_pancard_data').hide();
        $('#show_pan_acknowledgement_data').hide();
        $('#download_pancard_acknowledgement').hide();
        $('#pan_card_number').removeAttr('required');
        $('#pancard_doc_file').removeAttr('required');
    }
}

function upload_pan_acknowledgement_details(e) {
    if (e == 1) {
        $('#show_pan_acknowledgement_data').show();
        $('#download_pancard_acknowledgement').hide();
        $('#show_pancard_data').hide();
        $('#pan_acknowledgement').attr('required', 'required');
        $('#pan_declaration').removeAttr('required');
    } else {
        $('#download_pancard_acknowledgement').show();
        $('#show_pan_acknowledgement_data').hide();
        $('#show_pancard_data').hide();
        $('#pan_declaration').attr('required', 'required');
        $('#pan_acknowledgement').removeAttr('required');
    }
}

function upload_admission_documents_path() {
    var document_id = $('#upload_document_id').val();
    var file = event.target.files[0];
    completed_promises = 0;
    current_percentage = 0;
    total_promises = 1;
    in_progress_promises = total_promises;
    var af_id = '<?php echo $afId ?>';
    reupload_saveFileToStorage(file, af_id, document_id);
    // $('.file-preview').css('opacity', '0.3');
}

function reupload_saveFileToStorage(file, af_id, document_id) {
    $('#re_percentage_doc_completed').show();
    $('#re_upload_document').attr('disabled', 'disabled');
    $("#re_upload_btn").prop('disabled', true);
    $.ajax({
        url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {
            'filename': file.name,
            'file_type': file.type,
            'folder': 'profile'
        },
        success: function(response) {
            // console.log('Response: ',response)
            single_file_progress(0);
            response = JSON.parse(response);
            var path = response.path;
            var signedUrl = response.signedUrl;
            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type,
                    "x-amz-acl": "public-read"
                },
                processData: false,
                data: file,
                xhr: function() {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function(e) {
                        // For uploads
                        if (e.lengthComputable) {
                            reupload_single_file_progress(e.loaded / e.total * 100 | 0);
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    // return false;
                    $('#re_percentage_doc_completed').hide();
                    update_admission_documents(path, af_id, document_id);
                    $('#doc-upload' + document_id).removeAttr('disabled');
                    $('.file-preview').css('opacity', '1');
                    $("#document_submit").prop('disabled', false);
                    // resolve({path:path, name:file.name, type:file.type});
                    // increaseLoading();
                },
                error: function(err) {
                    // console.log(err);
                    reject(err);
                }
            });
        },
        error: function(err) {
            reject(err);
        }
    });

}

function reupload_single_file_progress(percentage) {
    if (percentage == 100) {
        in_progress_promises--;
        if (in_progress_promises == 0) {
            current_percentage = percentage;
        }
    } else {
        if (current_percentage < percentage) {
            current_percentage = percentage;
        }
    }
    $("#re_percentage_doc_completed").html(`${current_percentage} %`);
    return false;
}

function update_admission_documents(path, af_id, document_id) {
    $.ajax({
        url: '<?php echo site_url('admission_process/reupdate_documents_new'); ?>',
        type: 'post',
        data: {
            'path': path,
            'af_id': af_id,
            'document_id': document_id
        },
        success: function(data) {
            var docrowId = data.trim();
            if (docrowId != 0) {
                $('#re_upload_document').attr('disabled', 'disabled');
                $('#re_upload_btn').removeAttr('disabled');
                var html = '';
                html += '<a style="margin-top: 1rem;" id="successmessageId' + document_id +
                    '" class="btn btn-success btn-sm"> Uploaded <i class="fa fa-check-circle"></i></a>';
                html += '<a style="margin-top: 1rem;" onclick="delete_document_path(' + document_id +
                    ')" id="removeButtonId' + document_id +
                    '" class="remove btn btn-danger  btn-sm"><i class="fa fa-trash-o"></i></a>';
                $('#re_afterSuccessUploadShow').show();
                $('#re_afterSuccessUploadShow').html(html);
            }
        }
    });
}

function upload_admission_documents() {
    var af_id = '<?php echo $afId?>';
    $('#af_id').val(af_id);
    var view_type = $('#view_type').val();
    if (view_type == '') {
        $('#upload_document').modal('hide');
        get_admission_document_by_id();
    } else if (view_type == 'aadhar' || view_type == 'pan') {
        var has_document = $('input[type=radio][name=has_document]:checked').val();
        var applied_for_document = $('input[type=radio][name=applied_form_document]:checked').val();
        if (has_document == undefined) {
            alert('Upload the Document');
            return false;
        } else if (has_document == 0 && applied_for_document == undefined) {
            alert('Upload the Document');
            return false;
        }
        // var doc_sl_no = $('#document_sl_no').val();
        var $form = $('#upload_document_form');
        if ($form.parsley().validate()) {
            $('#re_upload_btn').html('Please wait').attr('disabled', 'disabled');
            var form = $('#upload_document_form')[0];
            var formData = new FormData(form);
            $.ajax({
                url: '<?php echo site_url('admission_process/submit_admission_documents'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                // async: false,
                success: function(data) {
                    parsed_data = $.parseJSON(data);
                    if (parsed_data) {
                        $('#upload_document').modal('hide');
                        get_admission_document_by_id();
                    }
                },
                complete: function() {
                    $('#re_upload_btn').html('Upload').removeAttr('disabled');
                    get_admission_document_by_id()
                }
            });
        }
    }
}

function delete_document_path(document_id) {
    $.ajax({
        url: '<?php echo site_url('admission_process/delete_document_path'); ?>',
        type: 'post',
        data: {
            'document_id': document_id
        },
        success: function(data) {
            var docrowId = data.trim();
            if (docrowId != 0) {
                $('#upload_document form').trigger("reset");
                $('#re_upload_btn').removeAttr('disabled');
                $('#re_upload_document').removeAttr('disabled');
                $('#reupload_file_path').val('');
                $('#re_afterSuccessUploadShow').html('');
            }
        }
    });
}

function delete_receipt(admission_id) {
    bootbox.prompt({
        inputType: 'text',
        placeholder: 'Enter Remarks',
        title: "Deleting Admission Receipt. Are you Sure?",
        className: 'widthadjust',
        buttons: {
            confirm: {
                label: 'Yes',
                className: 'btn-success'
            },
            cancel: {
                label: 'No',
                className: 'btn-danger'
            }
        },
        callback: function(remarks) {
            if (remarks == '') {
                return false;
            }
            if (remarks) {
                $.ajax({
                    url: '<?php echo site_url('admission_process/delete_receipt'); ?>',
                    type: 'post',
                    data: {
                        'admission_id': admission_id,
                        'remarks': remarks
                    },
                    success: function(data) {
                        if (data) {
                            new PNotify({
                                title: 'Success',
                                text: 'Successfully Canceled',
                                type: 'success',
                            });
                            $('.canceled').show();
                        } else {
                            new PNotify({
                                title: 'Error',
                                text: 'Something went wrong',
                                type: 'Error',
                            });
                        }
                    }
                });
            }
        }
    });
}

function payment_modes() {
    var modes = ($('#paymentModes').val()).split('_', 1);
    if (modes == '1') {
        $('.hideshow1').hide();
        $('.hideshow').hide();
        $('.hideshow').hide();
        $('.hideshow').hide();
        $('.hideshow2').hide();
        $('.netbanking').hide();
        $('.paymentcard').hide();
        $('.cardCharge').hide();
        $('#datetimepicker1').show();
        $("#cardCharge").prop('disabled', true);
        $("#cd_no").attr('required', false);
        $("#nb_rn").attr('required', false);
        $("#currentdate").attr('required', false);
        $("#chq_no").attr('required', false);
        $("#dd_no").attr('required', true);
    } else if (modes == '4') {
        $('.hideshow').show();
        $('.hideshow').show();
        $('.hideshow').show();
        $('.hideshow1').show();
        $('.hideshow2').hide();
        $('.netbanking').hide();
        $('.paymentcard').hide();
        $('.cardCharge').hide();

        $('.date').show();
        $('#datetimepicker1').show();
        $("#cardCharge").prop('disabled', true);
        $("#cd_no").attr('required', false);
        $("#nb_rn").attr('required', false);
        $("#currentdate").attr('required', true);
        $("#chq_no").attr('required', true);
        $("#dd_no").attr('required', false);
    } else if (modes == '5') {
        $('.hideshow').show();
        $('.hideshow').show();
        $('.hideshow').show();
        $('.hideshow1').show();
        $('.hideshow2').hide();
        $('.netbanking').hide();
        $('.paymentcard').hide();
        $('.cardCharge').hide();

        $('.date').show();
        $('#datetimepicker1').show();
        $("#cardCharge").prop('disabled', true);
        $("#cd_no").attr('required', false);
        $("#nb_rn").attr('required', false);
        $("#currentdate").attr('required', true);
        $("#chq_no").attr('required', true);
        $("#dd_no").attr('required', false);
    } else if (modes == '6') {
        $('.hideshow').show();
        $('.hideshow').show();
        $('.hideshow').show();
        $('.hideshow1').show();
        $('.hideshow2').hide();
        $('.netbanking').hide();
        $('.paymentcard').hide();
        $('.cardCharge').hide();

        $('.date').show();
        $('#datetimepicker1').show();
        $("#cardCharge").prop('disabled', true);
        $("#cd_no").attr('required', false);
        $("#nb_rn").attr('required', false);
        $("#currentdate").attr('required', true);
        $("#chq_no").attr('required', true);
        $("#dd_no").attr('required', false);
    } else if (modes == '7') {
        $('.hideshow2').hide();
        $('.hideshow').show();
        $('.hideshow').show();
        $('.hideshow').show();
        $('.hideshow1').show();
        $('.paymentcard').show();
        $('.cardCharge').show();
        $('.netbanking').hide();
        $('.date').hide();
        $("#cd_no").attr('required', true);
        $("#nb_rn").attr('required', false);
        $("#currentdate").attr('required', false);
        $("#cardCharge").prop('disabled', false);
        $("#chq_no").attr('required', false);
        $("#dd_no").attr('required', false);
    } else if (modes == '2') {
        $('.hideshow2').hide();
        $('.hideshow').show();
        $('.hideshow').show();
        $('.hideshow').show();
        $('.hideshow1').show();
        $('.paymentcard').show();
        $('.cardCharge').show();
        $('.netbanking').hide();
        $('.date').show();
        $("#cd_no").attr('required', true);
        $("#nb_rn").attr('required', false);
        $("#currentdate").attr('required', false);
        $("#cardCharge").prop('disabled', false);
        $("#chq_no").attr('required', false);
        $("#dd_no").attr('required', false);
    } else if (modes == '3') {
        $('.hideshow2').hide();
        $('.hideshow').show();
        $('.hideshow').show();
        $('.hideshow').show();
        $('.hideshow1').show();
        $('.paymentcard').show();
        $('.cardCharge').show();
        $('.netbanking').hide();
        $('.date').show();
        $("#cd_no").attr('required', true);
        $("#nb_rn").attr('required', false);
        $("#currentdate").attr('required', false);
        $("#cardCharge").prop('disabled', false);
        $("#chq_no").attr('required', false);
        $("#dd_no").attr('required', false);
    } else if (modes == '8' || modes == '32') {
        $('.hideshow2').hide();
        $('.hideshow').hide();
        $('.hideshow').hide();
        $('.hideshow').hide();
        $('.hideshow1').hide();
        $('.netbanking').show();
        $('#bankname').show();
        $('.paymentcard').hide();
        $('.date').show();
        $('.cardCharge').hide();
        $("#cardCharge").prop('disabled', true);
        $("#cd_no").attr('required', false);
        $("#nb_rn").attr('required', true);
        $("#currentdate").attr('required', true);
        $("#chq_no").attr('required', false);
        $("#dd_no").attr('required', false);
    } else if (modes == '11') {
        $('.hideshow2').hide();
        $('.hideshow').show();
        $('.hideshow').show();
        $('.hideshow').show();
        $('.hideshow1').hide();
        $('.card').show();
        $('.cardCharge').show();
        $('.netbanking').hide();
        $('.date').hide();
        $("#cd_no").attr('required', false);
        $("#nb_rn").attr('required', false);
        $("#currentdate").attr('required', false);
        $("#cardCharge").prop('disabled', false);
        $("#chq_no").attr('required', false);
        $("#dd_no").attr('required', false);
    } else {
        var amount = "";
        $('.hideshow').hide();
        $('.hideshow').hide();
        $('.hideshow').hide();
        $('.hideshow1').hide();
        $('.hideshow2').hide();
        $('.paymentcard').hide();
        $('.netbanking').hide();
        $('.cardCharge').hide();
        $("#cardCharge").prop('disabled', true);
        $("#cd_no").attr('required', false);
        $("#nb_rn").attr('required', false);
        $("#currentdate").attr('required', false);
        $("#chq_no").attr('required', false);
        $("#dd_no").attr('required', false);
    }
}

function close_application_popup() {
    var af_id = '<?php echo $afId ?>';
    var closer_reason = $('#closer_reason').val();
    if (closer_reason == '') {
        $('#closer_reason').attr('required', 'required');
        Swal.fire({
            // position: "top-end",
            icon: "Enter the reason",
            title: "Enter the reason",
            showConfirmButton: false,
            timer: 1500
        });
        return false;
    }
    bootbox.confirm({
        title: "Close Application. Are you Sure?",
        message: "Are you sure to close this application?",
        className: 'widthadjust',
        buttons: {
            confirm: {
                label: 'Yes',
                className: 'btn-success'
            },
            cancel: {
                label: 'No',
                className: 'btn-danger'
            }
        },
        callback: function(result) {
            if (result) {
                $.ajax({
                    url: '<?php echo site_url('admission_process/close_application'); ?>',
                    type: 'post',
                    data: {
                        'af_id': af_id,
                        'reason': closer_reason
                    },
                    success: function(data) {
                        if (data) {
                            new PNotify({
                                title: 'Success',
                                text: 'Successfully Closed',
                                type: 'success',
                            });
                            $('#currentStatus').html('Closed-not interested');
                            close_application();
                        } else {
                            new PNotify({
                                title: 'Error',
                                text: 'Something went wrong',
                                type: 'Error',
                            });
                        }
                    }
                });
            }
        }
    });
}

function deactivate_credentials(user_id, relation_type) {
    bootbox.confirm({
        title: `Confirmation`,
        message: `Are you sure to Deactivate ${relation_type} credential for this student?`,
        className: "medium",
        buttons: {
            confirm: {
                label: 'Yes',
                className: 'btn-success'
            },
            cancel: {
                label: 'No',
                className: 'btn-danger'
            }
        },
        callback: function(result) {
            if (result) {
                $.ajax({
                    url: '<?php echo site_url('admission_process/deactivate_credentials'); ?>',
                    type: 'post',
                    data: {
                        'user_id': user_id
                    },
                    success: function(data) {
                        if (data) {
                            $(function() {
                                new PNotify({
                                    title: 'Success',
                                    text: 'Successfully done',
                                    type: 'success',
                                });
                            });

                        } else {
                            $(function() {
                                new PNotify({
                                    title: 'Error',
                                    text: 'Something went wrong',
                                    type: 'error',
                                });
                            });
                        }
                    }
                });
            }
        }
    });
}

function acad_year_change_for_students_added_to_erp(af_id) {
    // var acad_year ='<?php //echo $this->acad_year->getAcadYearID() ?>';
    $.ajax({
        url: '<?php echo site_url('admission_flow/get_move_to_erp_details'); ?>',
        type: 'post',
        data: {
            'admission_form_id': af_id
        },
        success: function(data) {
            var res_data = $.parseJSON(data);
            if (res_data.fees) {
                $('#student_admid').val(res_data.student.student_id);
                get_classes();
            } else {
                $('#body_content').html('Fees assinged. Clear Fees then move');
            }
            // if(res_data.credentials || res_data.feeDetails){
            //   console.log(1);
            // }else{
            //   get_classes(acad_year);
            // }
        }
    })
}

function get_classes() {
    var academic_year = $('#academic_year_move').val();
    $.ajax({
        url: '<?php echo site_url('student/student_controller/getAcadClassess/'); ?>',
        type: 'post',
        data: {
            'acad_year': academic_year
        },
        success: function(data) {
            var class_st = $.parseJSON(data);
            // console.log(class_st)
            var output = '';
            output += '<option value="">-Select Class-</option>';
            var len = class_st.length;
            for (var i = 0, j = len; i < j; i++) {
                output += '<option value="' + class_st[i].id + '">' + class_st[i].class_name + '</option>';
            }
            $('#classID').html(output);
            get_Classsection();
        }
    });
}

function get_Classsection() {
    $('#classSectionId').html('');
    var classid = $("#classID").val();
    var acad_year = $('#academic_year_move').val();
    $.ajax({
        url: '<?php echo site_url('student/student_controller/getClassess/'); ?>',
        type: 'post',
        data: {
            'classid': classid,
            'acad_year': acad_year
        },
        success: function(data) {
            // console.log(data);
            var class_st = $.parseJSON(data);
            var output = '';
            output += '<option value="">-Select Section-</option>';
            var len = class_st.length;
            for (var i = 0, j = len; i < j; i++) {
                output += '<option value="' + class_st[i].id + '">' + class_st[i].section_name +
                '</option>';
            }
            $('#class_section_ID').html(output);
        }
    });
}

function change_acad_year() {
    var student_admission_id = $('#student_admid').val();
    var moveAcadyear = $('#academic_year_move').val();
    var moveClassId = $('#classID').val();
    var moveSectionId = $('#class_section_ID').val();
    var afId = '<?php echo $afId ?>';
    $.ajax({
        url: '<?php echo site_url('student/student_controller/change_to_acad_year/'); ?>',
        type: 'post',
        data: {
            'student_admission_id': student_admission_id,
            'moveAcadyear': moveAcadyear,
            'moveClassId': moveClassId,
            'moveSectionId': moveSectionId,
            'afId': afId
        },
        success: function(data) {
            if (data) {
                window.location.href = '<?php echo site_url('admission_process/application_view') ?>';
            }
        }
    });

}

function onselect_document() {
    var selected_document = $('#selected_document').val();
    if (selected_document === '') {
        return false;
    }

    var parts = selected_document.split('_');
    var document_type = parts[parts.length - 1].toLowerCase();

    var adm_setting_id = '<?php echo $admission_setting_id; ?>';

    if (document_type === 'aadhar') {
        $('#document_table').html(construct_aadhar_details(adm_setting_id));
    } else if (document_type === 'pan') {
        $('#document_table').html(construct_pan_details(adm_setting_id));
    } else {
        $('#document_table').html(construct_document_upload());
    }
}
</script>

<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/summernote/summernote.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<style>
#guardian_signature_edit,
#father_signature_edit,
#father_photo_edit,
#mother_photo_edit,
#family_photo_edit,
#stud_sign_photo_edit {
    color: gray;
    font-size: 2.5rem;
}
</style>