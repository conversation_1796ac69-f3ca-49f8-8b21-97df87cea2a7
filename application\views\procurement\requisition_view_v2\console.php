<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li>Procurement</li>
</ul>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            Procurement Management
          </h3>   
        </div>
      </div>
    </div>

    <div class="panel-body">

    <?php if(!empty($sara_tiles)) { foreach($sara_tiles as $key => $val) { ?>


      <?php if($val['title'] == 'Indent Management') { ?>
        <div class="col-md-12">
            <div class="m-0 d-flex">
                <div class="mr-5"><p style="font-size: 18px;font-weight: bold;color: #1e428a">Purchase Flows</p></div>
                <div class="mt-1 flex-fill"><hr></div>
            </div>
        </div>
      <?php } ?>
      <?php if($val['title'] == 'Budget Management') { ?>
        <div class="col-md-12">
            <div class="m-0 d-flex">
                <div class="mr-5"><p style="font-size: 18px;font-weight: bold;color: #1e428a">Administration</p></div>
                <div class="mt-1 flex-fill"><hr></div>
            </div>
        </div>
      <?php } ?>


      <div class='col-md-3'>
        <a class='' href='<?php echo $val['url']; ?>' target=''>
          <div class='widget widget-default widget-item-icon new_height animate__animated animate__fadeIn'> 
              <div class='widget-item-left' style='width:52px;'>
                <span class='animate__animated animate__fadeIn'>
                  <?php $this->load->view($val['icon']) ?>
                </span>
              </div>
              <div class='widget-data' style='padding-left:78px;'>
                <div class='widget-title' style='padding-top:20px; text-transform: capitalize;'><?php echo $val['title']; ?></div>
              </div>
          </div>
        </a>
      </div>
      
    <?php }} ?>

    </div>


    <!-- <div class="card-body pt-1">
      <?php 
        // $data['tile_list'] = $tiles;
        // $data['heading'] = 'Master';
        // echo $this->load->view('commons/secondary_dashboard_tiles.php', $data, true); 
      ?>
    </div>



    <div class="card-body pt-1">
      <?php 
        // $data['tile_list'] = $transaction_tiles;
        // $data['heading'] = 'Transaction';
        // echo $this->load->view('commons/secondary_dashboard_tiles.php', $data, true); 
      ?>
    </div>




    <div class="card-body pt-1">
        <?php 
        // $data['tile_list'] = $admin_tiles;
        // $data['heading'] = 'Administration';
        // echo $this->load->view('commons/secondary_dashboard_tiles.php', $data, true); 
        ?>
    </div>

    <div class="card-body pt-1">
      <?php 
        // $data['tile_list'] = $report_tiles;
        // $data['heading'] = 'Reports';
        // echo $this->load->view('commons/secondary_dashboard_tiles.php', $data, true); 
      ?>
    </div> -->

  </div>
</div>

<div class="visible-sm visible-xs visible-md">
  <a href="<?php echo site_url('dashboard');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>